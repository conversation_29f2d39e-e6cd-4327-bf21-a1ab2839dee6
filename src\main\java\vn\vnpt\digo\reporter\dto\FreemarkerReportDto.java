/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 *
 * <AUTHOR>
 */

@Data
@ToString
@AllArgsConstructor
public class FreemarkerReportDto implements Serializable {
    
    private Map<String, Object> parameters;
    private Map<String, String> model;
    
}
