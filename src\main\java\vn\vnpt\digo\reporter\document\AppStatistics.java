/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.document;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "appStatistics")
public class AppStatistics {
    @Id
    private ObjectId id;
    
    private int iosDownloadAmount;
    
    private int androidDownloadAmount;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;
    
    private ObjectId deploymentId;
    
    public void increaseIosDownload () {
        this.iosDownloadAmount = this.iosDownloadAmount + 1;
        this.updatedDate = new Date();
    }
    
    public void increaseAndroidDownload () {
        this.androidDownloadAmount = this.androidDownloadAmount + 1;
        this.updatedDate = new Date();
    }
}
