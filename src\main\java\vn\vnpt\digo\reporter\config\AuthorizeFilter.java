package vn.vnpt.digo.reporter.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;

import org.apache.commons.collections4.CollectionUtils;
import vn.vnpt.digo.reporter.util.Permission;

@Slf4j
@Component
public class AuthorizeFilter extends HandlerInterceptorAdapter {

    @Autowired
    private Environment env;

    @Autowired
    Microservice microservice;

    @Value("${vnpt.permission.api.allow}")
    private List<String> allowApis;

    @Value("${vnpt.permission.credentials.allow}")
    private List<String> credentialAllowApis;

    @Value("${vnpt.permission.interceptor.enable:false}")
    private boolean enableInterceptor;

    @Value("${vnpt.permission.rbac.enable:false}")
    private boolean enableRbac;

    @Value("${digo.permission.rbac.secret:VnptRbac@2022}")
    private String rbacSecret;

    private final String serviceName = "reporter";

    // return true is bypass check permistion
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // bypass check permission if interceptor is not enable

            String preferredUsername = Context.getJwtParameterValue("preferred_username");
            if(preferredUsername == null){
                return true;
            }
            if (enableInterceptor == false) {
                return true;
            }
            // if err then overide err response
            if (Objects.equals(request.getRequestURI(), "/error")) {
                return true;
            }
            // get api definition
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            ApiDefinition apiDef = getRequestMapping(request, handlerMethod);
            // check anotation exist then bypass api or else check permission by bussiness
            // belows
            if (Objects.isNull(apiDef.getAnotation())) {
                return true;
            }
            // check token is service account
            if (Objects.nonNull(preferredUsername) && preferredUsername.startsWith("service-account")) {
                if (this.apiIsAllow(apiDef, 2)) { // if apiIsAllows then return true;
                    return true;
                }
            } else if (this.apiIsAllow(apiDef, 0)) { // if apiIsAllows then return true;
                return true;
            } else if (this.checkRbac(apiDef)) { // else if Rbac isAllows then return true;
                return true;
            }
            this.checkAnnotation(apiDef.getAnotation()); // else if Permission isAllows then return true;
            return true;
        } catch (DigoHttpException digoe) {
            throw digoe; // else throws 403 err
        } catch (Exception e) {
            log.info("Cant check permission by {}", e.getMessage());
            return true; // return true if anything wrong code
        }
    }

    // define function check API allows
    boolean apiIsAllow(ApiDefinition apiDef, int strategy) {
        // strategy
        // 0: check normal allowed APIs
        // 1: check credential allowed APIs
        // 2: check either 0 or 1 is true
        try {
            String path = apiDef.getMethod().name() + ":" + apiDef.getPath();
            switch (strategy) {
                case (1): {
                    return (credentialAllowApis.contains(path));
                }
                case (2): {
                    return (credentialAllowApis.contains(path) || allowApis.contains(path));
                }
                default: {
                    return allowApis.contains(path);
                }
            }
        } catch (Exception e) {
            return false;
        }
    }

    // define function check RBAC
    boolean checkRbac(ApiDefinition apiDef) {
        // check rbac is not enable then return false
        if (enableRbac == false) {
            return false;
        }
        // check role by api
        try {
            boolean hasRole = false;
            hasRole = this.getRoleByApi(apiDef.getMethod(), apiDef.getPath());
            return hasRole;
        } catch (DigoHttpException digoe) {
            throw digoe;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean getRoleByApi(HttpMethod method, String path) {
        try {
            String rolebaseApiUrl = microservice.sysmanUri("/rolebase-api/--by-api").toUriString();
            InputCheckApiIsAllowsDto body = new InputCheckApiIsAllowsDto(rbacSecret, serviceName, method, path);
            String[] role = MicroserviceExchange.post(rolebaseApiUrl, body, String[].class);
            if (role.length > 0) {
                try {
                    String realmAccess = Context.getJwtParameterValue("realm_access");
                    Gson gson = new Gson();
                    RealmRolesDto realmRoles = gson.fromJson(realmAccess, RealmRolesDto.class);
                    List<String> lstRole = Arrays.asList(role);
                    boolean roleChecked = lstRole.stream().anyMatch(element -> realmRoles.getRoles().contains(element));
                    if (roleChecked) {
                        return roleChecked;
                    } else {
                        throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                    }
                } catch (Exception e) {
                    throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                }
            } else {
                return false;
            }
        } catch (DigoHttpException digoe) {
            throw digoe;
        } catch (Exception e) {
            return false;
        }
    }

    // define function check Permission
    private void checkAnnotation(IcodeAuthorize annotation) {
        if (!hasPrivilege(annotation.value(), annotation.option())) {
            throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
        }
    }

    public boolean hasPrivilege(String permissionKey, String option) {
        String permission = env.getProperty(permissionKey);
        if (permission == null || permission.isBlank()) {
            return true;
        }
        String permissionStr = Context.getJwtParameterValue("permissions");
        if (permissionStr == null || permissionStr.isBlank()) {
            return false;
        }
        List<String> requiredPermissions = new ArrayList<>(Arrays.asList(permission.split(",")));
        List<String> userPermissions = new ArrayList<>();
        Type listType = new TypeToken<List<Permission>>() {
        }.getType();
        List<Permission> permissions = new Gson().fromJson(permissionStr, listType);
        for (var item : CollectionUtils.emptyIfNull(permissions)) {
            userPermissions.add(item.getPermission().getCode());
        }

        if (option.equalsIgnoreCase("any")) {
            return CollectionUtils.containsAny(userPermissions, requiredPermissions);
        }
        return CollectionUtils.containsAll(userPermissions, requiredPermissions);
    }

    // define function get RequestMapping and MethodMapping (return path and http
    // methods)
    private ApiDefinition getRequestMapping(HttpServletRequest request, HandlerMethod method) {
        String requestPath = "";
        HttpMethod requestMethod = null;
        IcodeAuthorize anotation = null;
        // get RequestMapping
        try {
            requestPath = String.join("",
                    method.getMethod().getDeclaringClass().getDeclaredAnnotation(RequestMapping.class).value());
        } catch (Exception e) {
            throw new DigoHttpException(10004);
        }
        // get MethodMapping
        try {
            String reqMethod = request.getMethod();
            switch (reqMethod) {
                case "GET": {
                    requestPath += String.join("", method.getMethodAnnotation(GetMapping.class).value());
                    requestMethod = HttpMethod.GET;
                    break;
                }
                case "POST": {
                    requestPath += String.join("", method.getMethodAnnotation(PostMapping.class).value());
                    requestMethod = HttpMethod.POST;
                    break;
                }
                case "PUT": {
                    requestPath += String.join("", method.getMethodAnnotation(PutMapping.class).value());
                    requestMethod = HttpMethod.PUT;
                    break;
                }
                case "DELETE": {
                    requestPath += String.join("", method.getMethodAnnotation(DeleteMapping.class).value());
                    requestMethod = HttpMethod.DELETE;
                    break;
                }
            }
        } catch (Exception e) {
            throw new DigoHttpException(10004);
        }
        try {
            anotation = method.getMethod().getDeclaringClass().getDeclaredAnnotation(IcodeAuthorize.class);
        } catch (Exception e) {
            log.info("Request is allow for citizens");
        }
        return new ApiDefinition(requestPath, requestMethod, anotation);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InputCheckApiIsAllowsDto implements Serializable {

        private String secret;

        private String microservice;

        private HttpMethod method;

        private String path;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RealmRolesDto implements Serializable {
        private List<String> roles;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiDefinition implements Serializable {
        private String path;
        private HttpMethod method;
        private IcodeAuthorize anotation;
    }
}
