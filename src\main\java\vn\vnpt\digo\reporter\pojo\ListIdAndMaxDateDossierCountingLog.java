package vn.vnpt.digo.reporter.pojo;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.document.DossierCountingLog;



@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListIdAndMaxDateDossierCountingLog {
  private List<String> listDossierId = new ArrayList<>();
  private Date maxDate ;
  public void getMaxDate(DossierCountingLog input){
    if (maxDate == null){
      maxDate = input.getAcceptedDate();
    } else{
      if (input.getAcceptedDate().after(maxDate)) maxDate = input.getAcceptedDate();
    }
    
  }
}
