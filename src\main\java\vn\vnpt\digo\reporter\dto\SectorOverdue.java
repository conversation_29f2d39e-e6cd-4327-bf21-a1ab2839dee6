package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import vn.vnpt.digo.reporter.pojo.NameDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;
import vn.vnpt.digo.reporter.util.Translator;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectorOverdue implements Serializable {
    @JsonProperty("_id")
    private String _id;
    
    @JsonProperty("sector")
    private SectorDossierByDay sector;
    
    @JsonProperty("procedure")
    private ProcedureDossierByDay procedure;
        
    @JsonProperty("unresolvedOverdue")
    private Integer unresolvedOverdue = 0;
    
    @Autowired
    @JsonIgnore
    private Translator trans;

    private static Translator translator;

    @PostConstruct
    private void initStaticDao() {
        translator = this.trans;
    }
    public static SectorOverdue fromDocument(SectorOverdue sec) {
        Short langId = translator.getCurrentLocaleId();
        SectorOverdue sector = new SectorOverdue();
        sector.setUnresolvedOverdue(sec.getUnresolvedOverdue());
        sector.set_id(sec.get_id());
        for (NameDossierByDay name : sec.getSector().getName()) {
            if (langId.equals(name.getLanguageId())) {
                sector.getSector().getName().add(name);
                sector.getSector().setId(sec.getSector().getId());
                break;
            }
        }
        for (NameDossierByDay name : sec.getProcedure().getTranslate()) {
            if (langId.equals(name.getLanguageId())) {
                sector.getProcedure().getTranslate().add(name);
                sector.getProcedure().setId(sec.getSector().getId());
                break;
            }
        }
        return sector;
    }
}
