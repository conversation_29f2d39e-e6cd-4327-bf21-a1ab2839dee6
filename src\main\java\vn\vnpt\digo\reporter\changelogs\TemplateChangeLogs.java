/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexOptions;
import org.bson.Document;

import vn.vnpt.digo.reporter.document.Template;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-10-21-11-48-00")
public class TemplateChangeLogs {

//   
    @ChangeSet(order = "2020-10-20-11-48-00", id = "TemplateChangeLogs::create", author = "Trong Thoai")
    public void create(DB db) {
        db.createCollection("template", null);

    }

    @ChangeSet(order = "2020-10-21-11-49-00", id = "TemplateChangeLogs::index", author = "Trong Thoai")
    public void createIndex(MongoDatabase db) {
        // create default objects
        Document doc = new Document();
        doc.put("name", 1);
        doc.put("deploymentId", 1);
        db.getCollection("template").createIndex(doc, new IndexOptions().unique(true).background(true));

    }

}
