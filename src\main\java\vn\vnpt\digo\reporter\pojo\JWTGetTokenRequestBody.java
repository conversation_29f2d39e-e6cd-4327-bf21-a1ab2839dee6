/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JWTGetTokenRequestBody implements Serializable {

    @JsonProperty("client_id")
    String client_id;
    @JsonProperty("grant_type")
    String grant_type;
    @JsonProperty("username")
    String username;
    @JsonProperty("password")
    String password;
}
