/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.util;

import vn.vnpt.digo.reporter.properties.DigoProperties;
import org.springframework.web.util.UriComponentsBuilder;

/**
 *
 * <AUTHOR>
 */
public class Microservice {

    public static final String HUMAN = "human";
    public static final String BASEDATA = "basedata";
    public static final String BASEDOC = "basedoc";
    public static final String BASEPAD = "basepad";
    public static final String BASECAT = "basecat";
    public static final String SYSMAN = "sysman";
    public static final String LOGMAN = "logman";
    public static final String FILEMAN = "fileman";
    public static final String ADAPTER = "adapter";
    public static final String SURFEED = "surfeed";
    public static final String PETITION = "petition";
    public static final String REPORTER = "reporter";
    public static final String MESSENGER = "messenger";
    public static final String SCHEDULER = "scheduler";
    public static final String QUEUEMAN = "queueman";
    public static final String BPM = "bpm";
    public static final String DOCMAN = "docman";
    public static final String POSTMAN = "postman";
    public static final String TOURIST = "tourist";
    public static final String SALEMAN = "saleman";
    public static final String PADMAN = "padman";
    public static final String ENVMON = "envmon";
    public static final String STORAGE = "storage";
    
    public static class CommunicationStrategy { 
        
        public static String CLUSTER = "cluster";
        public static String GATEWAY = "gateway";
        
    }
    
    private DigoProperties digoProperties;
    
    public void setDigoProperties(DigoProperties digoProperties) {
        this.digoProperties = digoProperties;
    }
    
    public String getPathCode(String code) {
        switch (code) {
            case HUMAN: return "hu";
            case BASEDATA: return "ba";
            case BASEDOC: return "bc";
            case BASEPAD: return "bd";
            case BASECAT: return "bt";
            case SYSMAN: return "sy";
            case LOGMAN: return "lo";
            case FILEMAN: return "fi";
            case ADAPTER: return "ad";
            case SURFEED: return "su";
            case PETITION: return "pe";
            case REPORTER: return "re";
            case MESSENGER: return "me";
            case SCHEDULER: return "sc";
            case QUEUEMAN: return "qu";
            case BPM: return "bp";
            case DOCMAN: return "do";
            case POSTMAN: return "po";
            case TOURIST: return "to";
            case SALEMAN: return "sa";
            case PADMAN: return "pa";
            case ENVMON: return "em";
            case STORAGE: return "storage";
        }
        return null;
    }

    private UriComponentsBuilder baseUri(String microservice, String path) {
        String url;
        if (CommunicationStrategy.GATEWAY.equals(digoProperties.getMicroservice().getCommunicationStrategy())) {
            url = digoProperties.getMicroservice().getGatewayUrl();
            url += "/" + getPathCode(microservice);
        } else {
            final String protocol = digoProperties.getMicroservice().getServiceDefaultProtocol();
            final String prefix = digoProperties.getMicroservice().getServiceNamePrefix();
            final int port = digoProperties.getMicroservice().getServiceDefaultPort();
            url = protocol == null || protocol.isEmpty() ? "http" : protocol;
            url += "://" + prefix + microservice;
            url += port == 80 ? "" : ":" + port;
        }
        url += path == null || path.isEmpty() ? "" : "/" + path;
        return UriComponentsBuilder.fromHttpUrl(url);
    }

    public UriComponentsBuilder humanUri(String path) {
        return baseUri(HUMAN, path);
    }

    public UriComponentsBuilder basedataUri(String path) {
        return baseUri(BASEDATA, path);
    }

    public UriComponentsBuilder basedocUri(String path) {
        return baseUri(BASEDOC, path);
    }

    public UriComponentsBuilder basepadUri(String path) {
        return baseUri(BASEPAD, path);
    }

    public UriComponentsBuilder basecatUri(String path) {
        return baseUri(BASECAT, path);
    }

    public UriComponentsBuilder sysmanUri(String path) {
        return baseUri(SYSMAN, path);
    }

    public UriComponentsBuilder logmanUri(String path) {
        return baseUri(LOGMAN, path);
    }

    public UriComponentsBuilder filemanUri(String path) {
        return baseUri(FILEMAN, path);
    }

    public UriComponentsBuilder adapterUri(String path) {
        return baseUri(ADAPTER, path);
    }

    public UriComponentsBuilder surfeedUri(String path) {
        return baseUri(SURFEED, path);
    }

    public UriComponentsBuilder petitionUri(String path) {
        return baseUri(PETITION, path);
    }

    public UriComponentsBuilder reporterUri(String path) {
        return baseUri(REPORTER, path);
    }

    public UriComponentsBuilder messengerUri(String path) {
        return baseUri(MESSENGER, path);
    }

    public UriComponentsBuilder schedulerUri(String path) {
        return baseUri(SCHEDULER, path);
    }

    public UriComponentsBuilder queuemanUri(String path) {
        return baseUri(QUEUEMAN, path);
    }

    public UriComponentsBuilder bpmUri(String path) {
        return baseUri(BPM, path);
    }

    public UriComponentsBuilder docmanUri(String path) {
        return baseUri(DOCMAN, path);
    }

    public UriComponentsBuilder postmanUri(String path) {
        return baseUri(POSTMAN, path);
    }

    public UriComponentsBuilder touristUri(String path) {
        return baseUri(TOURIST, path);
    }

    public UriComponentsBuilder salemanUri(String path) {
        return baseUri(SALEMAN, path);
    }

    public UriComponentsBuilder padmanUri(String path) {
        return baseUri(PADMAN, path);
    }

    public UriComponentsBuilder envmonUri(String path) {
        return baseUri(ENVMON, path);
    }
    public UriComponentsBuilder storageUri(String path) { return baseUri(STORAGE, path);
    }

}
