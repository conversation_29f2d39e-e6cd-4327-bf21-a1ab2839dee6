/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.pojo.TemplateType;
import vn.vnpt.digo.reporter.pojo.TemplateSubsystem;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PutTemplateDto  implements Serializable{

    @NotNull
    private String name;

    @NotNull
    private TemplateType type;

    @NotNull
    private TemplateFile file;

    private List<TemplateSubsystem> subsystem;

    private String listVariableString;

    private String deploymentId;

    private Integer signEnable = 0;

    private String code;
}
