package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.statistics.AnalyticReportDto;
import vn.vnpt.digo.reporter.service.StatisticBTTTTService;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistic")
@IcodeAuthorize("vnpt.permission.statistics")
public class StatisticBTTTTController {

    @Autowired
    private StatisticBTTTTService service;

    Logger logger = LoggerFactory.getLogger(StatisticBTTTTController.class);

    @GetMapping(value = "/--analytics-report")
    public AnalyticReportDto syncReport(HttpServletRequest request,
                                        @RequestParam(value = "propertyId") String propertyId,
                                        @RequestParam(value = "metrics") String metrics,
                                        @RequestParam(value = "startDate") String startDate,
                                        @RequestParam(value = "endDate") String endDate) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AnalyticReportDto listSector = service.analyticsReport(propertyId, metrics, startDate, endDate);
        logger.info("DIGO-Info: " + listSector);
        return listSector;
    }

}
