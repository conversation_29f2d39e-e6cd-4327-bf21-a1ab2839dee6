package vn.vnpt.digo.reporter.util;
public class HideSecurityInformationHelper {
    public static String setFullnameSecurity(String fullname) {
        try{
            if (fullname != null && !fullname.isEmpty() && fullname.length() > 0) { // N.T.T.Phu
                String[] hoTen = fullname.split(" ");
                StringBuilder tenChinhHoa = new StringBuilder();
                if(hoTen.length <= 1){
                    // trả về tên tắt
                    tenChinhHoa.append(hoTen);
                }else{
                    for(int i = 0; i < hoTen.length; i++){
                        String tem = null;
                        if(i==hoTen.length-1){
                            // tên cuối hiển thị
                            tem = hoTen[i];
                            tenChinhHoa.append(tem);
                        }else{
                            tem = hoTen[i].substring(0, 1) + ".";
                            tenChinhHoa.append(tem).append(" ");
                        }

                    }
                }
                fullname = tenChinhHoa.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fullname;
    }


    public static String maskPhoneNumber(String phone) {
        if (phone == null || phone.length() < 4) return "****";
        int len = phone.length();
        return phone.substring(0, 3) + "*****" + phone.substring(len - 2);
    }
}
