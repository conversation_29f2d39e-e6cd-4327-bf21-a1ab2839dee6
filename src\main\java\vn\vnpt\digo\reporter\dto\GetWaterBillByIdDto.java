package vn.vnpt.digo.reporter.dto;

import java.util.Date;
import javax.validation.constraints.NotNull;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetWaterBillByIdDto implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String billCode;

    @NotNull
    private int year;

    @NotNull
    private int month;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NotNull
    private Date startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NotNull
    private Date endDate;

    @NotNull
    private boolean paid;

    @NotNull
    private int oldIndex;

    @NotNull
    private int newIndex;

    @NotNull
    private int consumedAmount;

    @NotNull
    private float paymentAmount;

    @NotNull
    private String customerCode;

    @NotNull
    private String meterNumber;
}
