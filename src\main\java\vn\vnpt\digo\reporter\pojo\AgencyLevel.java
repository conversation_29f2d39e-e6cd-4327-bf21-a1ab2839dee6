/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AgencyLevel implements Serializable {
    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
}
