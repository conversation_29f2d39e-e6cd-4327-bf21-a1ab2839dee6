/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimeSheetGenPayloadDto {
    
    private Dossier dossier;
    
    private Timesheet timesheet;
    
    private Double duration;
    
    private Date startDate;
    
    private Date endDate;
    
    private Boolean checkOffDay;

    private String processingTimeUnit;

    private String offTime;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Dossier{
        
        @JsonSerialize(using = ToStringSerializer.class)
        private String id;

    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Timesheet{
        
        @JsonSerialize(using = ToStringSerializer.class)
        private String id;

    }

    public void setData(ObjectId dossierId, ObjectId timesheetId, double duration, Date startDate, String processingTimeUnit){
        Dossier dossier = new Dossier();
        dossier.setId(dossierId.toHexString());
        
        Timesheet timesheet = new Timesheet();
        timesheet.setId(timesheetId.toHexString());
        
        this.dossier = dossier;
        this.timesheet = timesheet;
        this.startDate = startDate;
        this.duration = duration;
        this.endDate = null;
        this.processingTimeUnit = processingTimeUnit;
    }

}
