debug=false

server.port=8022

management.server.port=9990
management.endpoints.enabled-by-default=false
management.endpoints.web.base-path=/igate-probes
management.endpoints.web.cors.allowed-origins=https://apitest.vnptigate.vn
management.endpoints.web.cors.allowed-methods=GET
management.endpoint.health.show-details=never
management.endpoint.health.roles=admin

management.endpoint.health.enabled=true
management.health.db.enabled=true
management.health.mongo.enabled=true
management.health.mail.enabled=false
management.health.ldap.enabled=false
spring.cloud.discovery.client.composite-indicator.enabled=false
telegram.bot.token=**********************************************
telegram.chat.id=-1002215146785
telegram.chat.topic=0

## gRPC
grpc.server.port=9090
grpc.client.svc-adapter.address=static://localhost:9090
grpc.client.svc-basecat.address=static://localhost:9090
grpc.client.svc-basedata.address=static://localhost:9090
grpc.client.svc-bpm.address=static://localhost:9090
grpc.client.svc-eform.address=static://localhost:9090
grpc.client.svc-fileman.address=static://localhost:9090
grpc.client.svc-human.address=static://localhost:9090
grpc.client.svc-logman.address=static://localhost:9091
grpc.client.svc-messenger.address=static://localhost:9090
grpc.client.svc-modeling.address=static://localhost:9090
grpc.client.svc-reporter.address=static://localhost:9090
grpc.client.svc-sysman.address=static://localhost:9090
grpc.client.svc-system.address=static://localhost:9090
grpc.client.svc-basepad.address=static://localhost:9090
grpc.client.svc-padman.address=static://localhost:9090
grpc.client.svc-rbonegate.address=static://localhost:9090
grpc.client.svc-storage.address=static://localhost:9090
grpc.client.svc-surfeed.address=static://localhost:9090
grpc.client.GLOBAL.negotiation-type=plaintext
## TLS
grpc.server.max-inbound-message-size=10485760
grpc.server.max-inbound-metadata-size=10485760
#grpc.server.max-inbound-message-size=10485760
#grpc.server.max-inbound-metadata-size=10485760
#grpc.server.security.enabled=true
#grpc.server.security.privateKey=file:/etc/grpc/ssl/tls.key
#grpc.server.security.certificateChain=file:/etc/grpc/ssl/tls.crt

#spring.data.mongodb.uri=mongodb://************:27017/digoReporter
spring.data.mongodb.uri=mongodb://*************:30004/qnisvcReporter
spring.data.rest.default-page-size=15
spring.data.rest.max-page-size=50
#spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://digo-sso.vnptigate.vn/oauth/certs
#spring.security.oauth2.resourceserver.jwt.issuer-uri=https://digo-oidc.vnptigate.vn/auth/realms/digo
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://sso.quangngai.gov.vn/auth/realms/digo

mongobee.enable=true
mongobee.dbname=digoReporter
mongobee.change-logs-scan-package=vn.vnpt.digo.reporter.changelogs
mongobee.change-log-collection-name=mongobeeChangeLogs
mongobee.lock-collection-name=mongobeeLock

digo.microservice.communication-strategy=gateway
#digo.microservice.gateway-url=https://digo-api.vnptioffice.vn
digo.microservice.gateway-url=https://apiigate.quangngai.gov.vn
digo.microservice.service-name-prefix=digo-api-
digo.microservice.service-default-port=8080
digo.microservice.service-default-protocol=http
digo.locale-code=vi
digo.supported-locales={"vi":228,"en":46,"zh":232}
digo.oauth2.resource-id=digo-api-reporter
digo.cors-mappings=[{"path-pattern":"/**","allowed-origins":"http://localhost:4200,http://localhost:8080,http://digo-swagger-ui.vnptioffice.vn,http://digo-swagger-editor.vnptioffice.vn"}]

spring.cloud.stream.kafka.binder.brokers=***********:9092
#spring.cloud.stream.kafka.binder.defaultBrokerPort=30185
spring.cloud.stream.bindings.agencyProducerRequestOut.destination=agency-statistic

spring.cloud.stream.bindings.agencyConsumerRequestIn.destination=agency-statistic
spring.cloud.stream.bindings.agencyConsumerRequestIn.group=svc-agency-statistic
spring.cloud.stream.bindings.ktmETLDossierRequestIn.destination=ktmETLDossierRequest

spring.cloud.stream.bindings.procedureProducerRequestOut.destination=procedure-statistic

spring.cloud.stream.bindings.procedureDossierQuantityRequestIn.destination=procedure-statistic
spring.cloud.stream.bindings.procedureDossierQuantityRequestIn.group=svc-procedure-statistic

digo.reporter.procedure.citizen=5f3113a8fd0ee6fddc48b004
digo.reporter.procedure.enterprise=5f3113a8fd0ee6fddc48b001

digo.petition-statistics.credentials-client-id=web-citizens-public
digo.petition-statistics.credentials-client-secret=c3517bcf-8361-4cbd-b628-d6cd327309b6


## Redis
#spring.cache.type=redis
#spring.session.store-type=redis
spring.cache.redis.time-to-live=900
#spring.cache.redis.cache-null-values=false
##spring.redis.cluster.nodes:ip:port,ip:port:ip:port
#spring.redis.host=ip
#spring.redis.port=port
#spring.redis.password=password
spring.cache.redis.key-prefix=svc-reporter

## MULTIPART (MultipartProperties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true
# Threshold after which files are written to disk.
spring.servlet.multipart.file-size-threshold=2KB
# Max file size.
spring.servlet.multipart.max-file-size=200MB
# Max Request Size
spring.servlet.multipart.max-request-size=215MB

## File Storage Properties
# All files uploaded through the REST API will be stored in this directory
file.upload-dir=/reporter/upload/
file.upload-malformed-regex=/....,/..,\....,\..
info.app.name=svc-reporter
info.app.description=Service reporter

vnpt.permission.debbuger.enable=true
vnpt.permission.interceptor.enable=true
vnpt.permission.rbac.enable=true
digo.permission.rbac.secret=VnptRbac@2022
vnpt.permission.agency=manageDigo
vnpt.permission.appstatistics=manageDigo
vnpt.permission.statistics=manageDigo
vnpt.permission.dossier=manageDigo
vnpt.permission.electricbill=manageDigo
vnpt.permission.electriccustomer=manageDigo
vnpt.permission.file=manageDigo
vnpt.permission.freemarker=manageDigo
vnpt.permission.jasper=manageDigo
vnpt.permission.procedure=manageDigo
vnpt.permission.subscriptiontype=manageDigo
vnpt.permission.telecomcost=manageDigo
vnpt.permission.template=manageDigo
vnpt.permission.waterbill=manageDigo
vnpt.permission.manageDigo=manageDigo
vnpt.permission.watercustomer=manageDigo
vnpt.permission.office-template=manageDigo
vnpt.permission.eresult-form=manageDigo
vnpt.permission.feature-statistics=manageDigo
vnpt.permission.petition-statistics=manageDigo
vnpt.permission.publish-late-documents=manageDigo
vnpt.permission.api.allow=GET:/eresult-form/--activation,GET:/procedure/--frequent,GET:/procedure/--frequent-v2,GET:/procedure-sector-agency/--procedure-quantity-by-tag,GET:/agency/--dossier,GET:/dbn-report/statistic/--by-year,POST:/office-template/--download-from-data,GET:/dossiercounting/--from-to-by-agency-v2,GET:/dossiercounting/--from-to-by-agency,GET:/dossiercounting/--by-v2,GET:/dossiercounting/--by,GET:/dossiercounting/--from-to-by-tag-agency,GET:/dossiercounting/--from-to-bundles,GET:/dossiercounting/--from-to-bundle,GET:/eresult-form/--activation,GET:/dossiercounting/--counting-detail,POST:/freemarker/--report,GET:/statistic/--analytics-report,GET:/general-report-qni/-detail/-all, PUT:/general-report-qni/-etl-dossier/-update-date, POST:/sample-report-qni/, GET:/publish-late-documents/--activation
vnpt.permission.credentials.allow=
vnpt.permission.print-report=manageDigo
server.max-http-header-size=480000

#Cache
digo.cache.security-key=Vnpt2021
digo.analytics.sync.clear.statistic=59 * * * * *

#actoutor
management.endpoints.web.exposure.include=*
#PTRP202311.092
digo.file.extension.list=jrxml,ftl,rptdesign,docx
digo.file.blacklist-keyword.allow=
# analytics
analytics=
analytics.hhg=

#QNI
digo.dossier.tag-level3=5f6b177a4e1bd312a6f3ae4a
digo.agency.sotnmt=62174f68378b3c2a75639f97
digo.dossier.tag-donvidl=6399e7ae35e1b72daaa5a422
spring.cloud.stream.bindings.qniETLDossierRequestIn.destination=qniETLDossierRequest
digo.dossier.off-time=15:00
digo.report.ignore-pause-extend-date=true
digo.report.re-digitizing-reporter=false
digo.oidc.client-id=svc-adapter
digo.oidc.client-secret=62ca3e28-2878-473c-8956-22f5cf288b3a
digo.payment.report.ignore-procedure-level=5f5b2c2b4e1bd312a6f3ae23
#SSRF
digo.ssrf.black-list-enable=true
digo.ssrf.black-list=../,/ROOT,.jsp,..
digo.ssrf.white-list=https://apitest.vnptigate.vn
digo.petition.callout.whitelist-url=^(https:\\/\\/apitest\\.vnptigate\\.vn/.*|https:\\/\\/apitest\\.vnptigate\\.vn\\/.*|http:\\/\\/vietmap\\.com\\/.*|https:\\/\\/gisapi\\.tphcm\\.gov\\.vn\\/.*)$


#IGATESUPP-89671 download file Object Storage
#enable module
vnpt.object-storage.moved-without-minio-access.recent=19/10/1019
#set config
vnpt.object-storage.download-file.bucket-name=test
vnpt.object-storage.download-file.access-key=local
vnpt.object-storage.download-file.secret-key=local
vnpt.object-storage.download-file.endpoint=https://local.vnpt.vn

#minio
upload-file-type=test
upload-path=upload/temp/

digo.microservice.client-id=test
digo.microservice.client-secret=21ed14ae-ab44-46b1-8cae-5dc84c844094

#timezone
TZ=Etc/GMT-7
#IGATESUPP-89251 T�n trang thai ho so: Tra do qu� han bo sung giay to -- site qni:  66c3023311fe147681979b1c -- site test: 66c409224a49230981cb663c
digo.dossier.qni.return-over-due-additional-dossier-status-task-id=66c3023311fe147681979b1c

digo.qni.agency-level.root=000000000191c4e1bd300048
digo.qni.agency-level.department=611e494a43b16972f62dfd36
digo.qni.agency-level.district=5f7dade4b80e603d5300dcc4
digo.qni.agency-level.commune=5f6b177a4e1bd312a6f3ae4a

digo.qni.digitizing.enable-check-is-reused=false
#qni:623849e3e63b54793b9ff57e   test:62f4b8f61a6d6b4ce7305267
digo.qni.timesheet-id=623849e3e63b54793b9ff57e
digo.qni.max-sleep-time=10

digo.qni.hiddenFeeReportNAST=false
digo.qni.is-hide-security-inf=true
digo.qni.ignore-hide-security-inf-permission=ignoreHideInfNAST
#Fix ATTT
net.sf.jasperreports.report.class.filter.enabled=true

#common-statistics
digo.common-statistics.procedure-level-4=5f5b2c564e1bd312a6f3ae25

vnpt.permission.role.admin=admin,SUPER_ADMIN,oidcAdminMaster
digo.enable.hide.fullname=false

digo.microservice.auth.grant-type=client_credentials
digo.microservice.auth.client-id=svc-reporter
digo.microservice.auth.username=demo
digo.microservice.auth.password=change-me-in-prod
digo.microservice.auth.client-secret=change-me-in-prod
digo.report.birtviewer.url=https://baocaodongtest.vnptigate.vn/

# ===================================================================
#poolexecutor: 1->AboutPolicy, 2->CallerRunsPolicy, 3->DiscardPolicy, 4->DiscardOldestPolicy
#corepoolsize: is the minimum number of workers to keep alive
#maxpoolsize: defines the maximum number of threads that can ever be created
# 1. If the number of threads is less than the corepoolsize, create a new Thread to run a new task
# 2. If the number of threads is equal (or greater than) the corepoolsize, put the task into the queue
# 3. If the queue is full, and the number of threads is less than the maxpoolsize, create a new thread to run tasks in
# 4. If the queue is full, and the number of threads is greater than of equal to maxpoolsize, reject the task follow rule of poolexecutor
# ===================================================================
thread.poolexecutor=2
thread.corepoolsize=50
thread.maxpoolsize=100
thread.queuecapacity=1000
thread.sleeptime=1000
digo.change-stream.enable=false
digo.thread.change-stream.enable=false
digo.thread.change-stream.max-retry=3
