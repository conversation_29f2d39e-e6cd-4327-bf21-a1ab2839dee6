package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusTotalDetail implements Serializable {

    @Id
    private IdTrans receptionMethod;

    private String receptionMethodName;

    private Long amount = 0L;

    private StatusTotal status;

}
