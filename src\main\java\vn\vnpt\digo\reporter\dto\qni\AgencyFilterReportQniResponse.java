package vn.vnpt.digo.reporter.dto.qni;

import vn.vnpt.digo.reporter.document.AgencyFilterReportQni;

public class AgencyFilterReportQniResponse {
    private String _id;
    private String agencyName;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getIdAgency() {
        return idAgency;
    }

    public void setIdAgency(String idAgency) {
        this.idAgency = idAgency;
    }

    public String getIdFilter() {
        return idFilter;
    }

    public void setIdFilter(String idFilter) {
        this.idFilter = idFilter;
    }

    private String idAgency;
    private String idFilter;

    // Default constructor
    public AgencyFilterReportQniResponse() {
    }

    public AgencyFilterReportQniResponse(AgencyFilterReportQni document) {
        this._id = document.get_id().toHexString();
        this.agencyName = document.getAgencyName();
        this.idAgency = document.getIdAgency();
        this.idFilter = document.getIdFilter();
    }
}
