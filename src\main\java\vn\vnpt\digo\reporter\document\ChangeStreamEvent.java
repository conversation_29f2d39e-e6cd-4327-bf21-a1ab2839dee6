package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.BsonDocument;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document("changeStreamEvent")
@CompoundIndex(
        name = "service_collection_type",
        def = "{'service': 1, 'collection': 1, 'type': 1}",
        background = true
)
public class ChangeStreamEvent implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private String service;
    
    private String collection;
    
    private String type;

    @Indexed(name = "itemId", unique = true, background = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId itemId;
    
    private Object data;
    
    private String resumeToken;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    // 0: không lỗi, 1: collection chưa định nghĩa (có thể xử lý lại), 2: Lỗi xử lý (Không xử lý lại), 3: Lỗi khác có thể xử lý lại
    private int errorType;
    
    private String errorMsg;
    
    private int retryCount = 0;

}
