package vn.vnpt.digo.reporter.dto;

import javax.validation.constraints.NotNull;
import org.bson.types.ObjectId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetListElectricBillDto implements Serializable {

	@JsonSerialize(using = ToStringSerializer.class)
	private ObjectId id;

	@NotNull
	private int month;

	@NotNull
	private int year;

	@NotNull
	private boolean paid;

	@NotNull
	private int consumedAmount;

	@NotNull
	private float paymentAmount;

}
