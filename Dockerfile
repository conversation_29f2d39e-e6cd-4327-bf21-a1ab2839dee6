FROM openjdk:11.0.16-jre-slim-buster
ADD build/libs/svc-reporter-1.0.0-beta.1.jar svc-reporter-1.0.0-beta.1.jar
ADD src/main/resources/db /db
ADD src/main/resources/libs src/main/resources/libs

## IDG APM integration info
ADD src/main/resources/libs/opentelemetry.jar opentelemetry-javaagent.jar
ENV OTEL_SERVICE_NAME=svc.reporter
## Disable SDK by default
ENV OTEL_SDK_DISABLED=true
## Alloy endpoint
ENV OTEL_EXPORTER_OTLP_ENDPOINT=http://10.168.10.10:31986
ENV OTEL_EXPORTER_OTLP_PROTOCOL=grpc
## Exporter type (log, trace, metrics): otlp|none
ENV OTEL_TRACES_EXPORTER=otlp
ENV OTEL_LOGS_EXPORTER=otlp
ENV OTEL_METRICS_EXPORTER=none

EXPOSE 8080
ENTRYPOINT ["java", "-javaagent:./opentelemetry-javaagent.jar", "-jar", "svc-reporter-1.0.0-beta.1.jar"]
