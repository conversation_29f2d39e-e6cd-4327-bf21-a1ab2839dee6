package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.dto.featurestatistics.FeatureStatisticsInputDto;
import vn.vnpt.digo.reporter.util.Context;

import java.util.Date;

/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "featureStatistics")
public class FeatureStatistics {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String appCode;
    private String featureCode;
    private Long counter;
    private ObjectId deploymentId;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate = new Date();

    public FeatureStatistics(FeatureStatisticsInputDto input) {
        this.appCode = input.getAppCode();
        this.featureCode = input.getFeatureCode();
        this.counter = input.getCounter();
        this.deploymentId = Context.getDeploymentId();
    }
}
