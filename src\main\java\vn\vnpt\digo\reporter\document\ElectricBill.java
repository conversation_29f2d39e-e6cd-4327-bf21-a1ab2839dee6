package vn.vnpt.digo.reporter.document;

import java.util.Date;

import javax.validation.constraints.NotNull;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import vn.vnpt.digo.reporter.dto.ElectricBillInputDto;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Document(collection = "electricBill")
@CompoundIndex(name = "unique_electricBill", def = "{'billCode': 1, 'userId': 1, 'customerCode': 1, 'year': 1, 'month': 1}", unique = true)
public class ElectricBill {

    @Id
    @JsonSerialize
    private ObjectId id;
    private String billCode;
    private ObjectId userId;

    @NotNull
    private String customerCode;

    @NotNull
    private String meterNumber;

    @NotNull
    private int year;

    @NotNull
    private int month;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NotNull
    private Date startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NotNull
    private Date endDate;

    @NotNull
    private int oldIndex;

    @NotNull
    private int newIndex;

    @NotNull
    private int consumedAmount;

    @NotNull
    private float paymentAmount;

    @NotNull
    private boolean paid;

    public ElectricBill(ElectricBillInputDto electricBillInputDto) {
        this.billCode = electricBillInputDto.getBillCode();
        this.userId = electricBillInputDto.getUserId();
        this.customerCode = electricBillInputDto.getCustomerCode();
        this.meterNumber = electricBillInputDto.getMeterNumber();
        this.year = electricBillInputDto.getYear();
        this.month = electricBillInputDto.getMonth();
        this.startDate = electricBillInputDto.getStartDate();
        this.endDate = electricBillInputDto.getEndDate();
        this.oldIndex = electricBillInputDto.getOldIndex();
        this.newIndex = electricBillInputDto.getNewIndex();
        this.consumedAmount = electricBillInputDto.getConsumedAmount();
        this.paymentAmount = electricBillInputDto.getPaymentAmount();
        this.paid = electricBillInputDto.isPaid();
    }
}
