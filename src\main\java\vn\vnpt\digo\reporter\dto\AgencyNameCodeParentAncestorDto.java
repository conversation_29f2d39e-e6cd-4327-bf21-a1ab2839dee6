/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.pojo.AgencyName;
import vn.vnpt.digo.reporter.pojo.Ancestor;
import vn.vnpt.digo.reporter.pojo.AncestorMap;
import vn.vnpt.digo.reporter.pojo.Name;
import vn.vnpt.digo.reporter.pojo.Parent;
import vn.vnpt.digo.reporter.pojo.TagAgency;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgencyNameCodeParentAncestorDto implements Serializable{
    private String id;

    private ArrayList<AgencyName> name;
    
    private String code;

    private Parent parent;

    private ArrayList<AncestorMap> ancestors;
    
    private List<TagAgency> tag;

}
