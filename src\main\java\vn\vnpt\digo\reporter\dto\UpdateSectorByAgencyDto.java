package vn.vnpt.digo.reporter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.AgencySector;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSectorByAgencyDto implements Serializable {

    @NotNull
    private ObjectId agencyId;

    private ArrayList<AgencySector> sector;
}
