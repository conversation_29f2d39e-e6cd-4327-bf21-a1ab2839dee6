package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import com.mongodb.DBCollection;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-08-20-14-10")
public class UpdateAgencyChangeLogs {

    private ObjectId changeLongToObjectId(String idTemplateString, Long id) {
        String placeId = id + "";
        int size = placeId.length();
        int maxLength = 24 - idTemplateString.length();
        if (maxLength >= size) {
            for (int i = 0; i < (maxLength - size); i++) {
                idTemplateString += "0";
            }
            idTemplateString += placeId;
        }
        return new ObjectId(idTemplateString);
    }

    @ChangeSet(order = "2020-08-20-14-10", id = "UpdateAgencyChangeLogs::updateAgency", author = "haimn")
    public void updateTagPetition(DB db) {
        DBCollection agencyCollection = db.getCollection("agency");
        agencyCollection.find().forEach(data -> {
            // Map to BasicDBObject
            BasicDBObject updateAgency = (BasicDBObject) data;
            // Set new originId
            updateAgency.put("originId", new ObjectId());
            // Set new parentId
            updateAgency.put("parentId", new ObjectId());
            // Set new placeId
            try {
                String idTemplate = "5def47c5f47614018c";
                updateAgency.put("placeId", this.changeLongToObjectId(idTemplate, updateAgency.getLong("placeId")));
            } catch (Exception e) {

            }
            // Set new ancestorPlaceId
            BasicDBList ancestorPlaceIds = (BasicDBList) updateAgency.get("ancestorPlaceId");
            List<ObjectId> newAncestorPlaceIds = new ArrayList<>();
            ancestorPlaceIds.forEach(id -> {
                try {
                    String ancestorPlaceId = id + "";
                    String idTemplate = "5def47c5f47614018c";
                    newAncestorPlaceIds.add(this.changeLongToObjectId(idTemplate, Long.parseLong(ancestorPlaceId)));
                } catch (Exception e) {

                }
            });
            updateAgency.put("ancestorPlaceId", newAncestorPlaceIds);
            // Set new sector
            BasicDBList sectors = (BasicDBList) updateAgency.get("sector");
            sectors.forEach(sectorItem -> {
                BasicDBObject sector = (BasicDBObject) sectorItem;
                String idTemplate = "5def47c5f47614018c";
                String sectorId = sector.getLong("id") + "";
                this.changeLongToObjectId(idTemplate, sector.getLong("id"));
                sector.put("id", this.changeLongToObjectId(idTemplate, sector.getLong("id")));
            });

            agencyCollection.save(updateAgency);
        });
    }
}
