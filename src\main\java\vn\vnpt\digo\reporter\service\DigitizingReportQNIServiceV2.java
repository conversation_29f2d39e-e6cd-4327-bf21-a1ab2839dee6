package vn.vnpt.digo.reporter.service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mongodb.MongoClient;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.dto.qni.AgencyFilterReportQniResponse;
import vn.vnpt.digo.reporter.dto.qni.DetailGeneralReportDto;
import vn.vnpt.digo.reporter.dto.qni.DigitizingReportDto;
import vn.vnpt.digo.reporter.dto.qni.GeneralReportDto;
import vn.vnpt.digo.reporter.util.*;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
public class DigitizingReportQNIServiceV2 {
    private static final RestTemplate restTemplate = new RestTemplate();
    private final Gson gson = new Gson();
    Logger logger = org.slf4j.LoggerFactory.getLogger(GeneralReportQNIService.class);
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    TimeZone timezone = TimeZone.getTimeZone("GMT");
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MongoConverter mongoConverter;
    @Autowired
    private Microservice microservice;
    @Autowired
    private Translator translator;
    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI)
    private Resource resourceTemplateDossierStatisticAssigneeQNI;
    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI_v2)
    private Resource resourceTemplateDossierStatisticAssigneeQNIV2;
    @Autowired
    private MongoClient client;
    @Value("${digo.dossier.off-time}")
    private String offTime;
    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;
    @Value("${digo.enable.hide.fullname}")
    private boolean enableHideName;
    @Autowired
    private AgencyFilterReportQniService agencyFilterReportQniService;

    public Document documentTiepNhan(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$appointmentDate", false))
        ));
    }
    public Document documentTiepNhanCoSoHoa(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$extendDigitizing.isHaveTPHS", false)),
                new Document("$eq", Arrays.asList("$extendDigitizing.isHaveTPHS", true))
        ));
    }

    public Document documentTiepNhanLuuKho(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$extendDigitizing.isComponentsStorages", false)),
                new Document("$eq", Arrays.asList("$extendDigitizing.isComponentsStorages", true))
        ));
    }

    public Document documentTiepNhanKhongCoSoHoa(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDigitizing.isHaveTPHS", false))),
                        new Document("$eq", Arrays.asList("$extendDigitizing.isHaveTPHS", false))
                ))
        ));
    }

    public Document documentHoSoKetQua(Date fromDate, Date toDate){
        return new Document("$and", Arrays.asList(
                new Document("ifNull",Arrays.asList("$completedDate", false)),
                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                ))
        ));
    }
    public Document documentHoSoKetQuaCoSoHoa(Date fromDate, Date toDate){
        return new Document("$and", Arrays.asList(
                new Document("ifNull",Arrays.asList("$completedDate", false)),
                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$extendDigitizing.isHaveAttachment", false)),
                new Document("$eq", Arrays.asList("$extendDigitizing.isHaveAttachment", true))
        ));
    }

    public Document documentHoSoKetQuaLuuKho(Date fromDate, Date toDate){
        return new Document("$and", Arrays.asList(
                new Document("ifNull",Arrays.asList("$completedDate", false)),
                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$extendDigitizing.isAttachmentStorages", false)),
                new Document("$eq", Arrays.asList("$extendDigitizing.isAttachmentStorages", true))
        ));
    }

    public Document documentHoSoKetQuaKhongCoSoHoa(Date fromDate, Date toDate){
        return new Document("$and", Arrays.asList(
                new Document("ifNull",Arrays.asList("$completedDate", false)),
                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDigitizing.isHaveAttachment", false))),
                        new Document("$eq", Arrays.asList("$extendDigitizing.isHaveAttachment", false))
                ))
        ));
    }
    public Document documentHoSoTaiSuDung(Date fromDate, Date toDate){
        return new Document("$and", Arrays.asList(
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                                new Document("$lte", Arrays.asList("$acceptedDate", toDate))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                                new Document("$lte", Arrays.asList("$completedDate", toDate))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$extendDigitizing.isReuseFile", false)),
                new Document("$eq", Arrays.asList("$extendDigitizing.isReuseFile", true))
        ));
    }
    public Document documentTaiSuDungCSDLDC(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$extendDigitizing.isUseCheckCitizen", false)),
                new Document("$eq", Arrays.asList("$extendDigitizing.isUseCheckCitizen", true))
        ));
    }
    public Document getTaiSuDungCSDLDC(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTaiSuDungCSDLDC(fromDate, toDate),
                1, 0)
        );
    }
    public Document getHoSoTaiSuDung(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoTaiSuDung(fromDate, toDate),
                1, 0)
        );
    }
    public Document getHoSoCoKetQuaKhongCoSoHoa(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoKetQuaKhongCoSoHoa(fromDate, toDate),
                1, 0)
        );
    }
    public Document getHoSoCoKetQuaCoSoHoa(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoKetQuaCoSoHoa(fromDate, toDate),
                1, 0)
        );
    }

    public Document getHoSoCoKetQuaLuuKho(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoKetQuaLuuKho(fromDate, toDate),
                1, 0)
        );
    }

    public Document getHoSoCoKetQua(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoKetQua(fromDate, toDate),
                1, 0)
        );
    }

    public Document getTiepNhan(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhan(fromDate, toDate),
                1, 0)
        );
    }

    public Document getTiepNhanCoSoHoa(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanCoSoHoa(fromDate, toDate),
                1, 0)
        );
    }

    public Document getTiepNhanLuuKho(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanLuuKho(fromDate, toDate),
                1, 0)
        );
    }

    public Document getTiepNhanKhongCoSoHoa(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanKhongCoSoHoa(fromDate, toDate),
                1, 0)
        );
    }

    public Document docunemtMatch(Integer type, Date fromDate, Date toDate) {

        Document document = new Document();

        switch (type) {
            case 1: // Tiếp nhận
                document = getTiepNhan(fromDate, toDate);
                break;
            case 2: // Số hồ sơ có số hóa thành phần HS
                document = getTiepNhanCoSoHoa(fromDate, toDate);
                break;
            case 5: // Số hồ sơ chưa số hóa TPHS
                document = getTiepNhanKhongCoSoHoa(fromDate, toDate);
                break;
            case 3: // Số hồ sơ đã giải quyết (ho_so_ket_qua)
                document = getHoSoCoKetQua(fromDate, toDate);
                break;
            case 4: // Số hồ sơ có số hóa kết quả ho_so_ket_qua_co_so_hoa
                document = getHoSoCoKetQuaCoSoHoa(fromDate, toDate);
                break;
            case 6: // Số hồ sơ chưa số hóa kết quả TTHC ho_so_ket_qua_khong_co_so_hoa
                document = getHoSoCoKetQuaKhongCoSoHoa(fromDate, toDate);
                break;
            case 7: // tai_su_dung
                document = getHoSoTaiSuDung(fromDate, toDate);
                break;
            case 8: // kết quả lưu kho
                document = getHoSoCoKetQuaLuuKho(fromDate, toDate);
                break;
            case 9: // tiếp nhận lưu kho
                document = getTiepNhanLuuKho(fromDate, toDate);
                break;
            case 10: // sử dụng scdldcqg
                document = getTaiSuDungCSDLDC(fromDate, toDate);
                break;
        }

        return document;
    }

    public Criteria buildCriteria(List<ObjectId> agencyIds) throws JSONException {
        List<Criteria> criteriaList = new ArrayList<>();
        Criteria criteriaAgency = null;

        criteriaAgency = new Criteria().orOperator(
                Criteria.where("agency._id").in(agencyIds)
        );
        criteriaList.add(criteriaAgency);

        Criteria criteria = new Criteria();
        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));

        return criteria;
    }

    public Aggregation getAggregation(Date fromDate, Date toDate, List<ObjectId> agencyIds, List<String> excludedProcedureIds) throws JSONException {

        String[] option = new String[6];

        AggregationOperation matchOperation = Aggregation.match(buildCriteria(agencyIds));

        if (excludedProcedureIds != null && !excludedProcedureIds.isEmpty()) {
            matchOperation = Aggregation.match(buildCriteriaV2IgnoreProcedure(agencyIds, excludedProcedureIds));
        }

        AggregationOperation projectOperation = context -> {
            Document projection = new Document();
            projection.put("tiep_nhan", getTiepNhan(fromDate, toDate));
            projection.put("ho_so_ket_qua", getHoSoCoKetQua(fromDate, toDate));
            projection.put("tiep_nhan_co_so_hoa", getTiepNhanCoSoHoa(fromDate, toDate));
            projection.put("tiep_nhan_khong_co_so_hoa", getTiepNhanKhongCoSoHoa(fromDate, toDate));
            projection.put("ho_so_ket_qua_co_so_hoa", getHoSoCoKetQuaCoSoHoa(fromDate, toDate));
            projection.put("ho_so_ket_qua_khong_co_so_hoa", getHoSoCoKetQuaKhongCoSoHoa(fromDate, toDate));
            projection.put("tai_su_dung", getHoSoTaiSuDung(fromDate, toDate));
            projection.put("ho_so_ket_qua_co_luu_kho", getHoSoCoKetQuaLuuKho(fromDate, toDate));
            projection.put("tiep_nhan_co_luu_kho", getTiepNhanLuuKho(fromDate, toDate));
            projection.put("tai_su_dung_csdldc", getTaiSuDungCSDLDC(fromDate, toDate));

            return new Document("$addFields", projection);
        };

        AggregationOperation groupOperation = Aggregation.group("agency._id")
                .sum("tiep_nhan").as("totalReceiver")
                .sum("tiep_nhan_co_so_hoa").as("totalReceiverHavingFile")
                .sum("tiep_nhan_khong_co_so_hoa").as("totalReceiverNopeFile")
                .sum("ho_so_ket_qua").as("totalComplete")
                .sum("ho_so_ket_qua_co_so_hoa").as("totalCompleteHavingFile")
                .sum("ho_so_ket_qua_khong_co_so_hoa").as("totalCompleteNopeFile")
                .sum("tai_su_dung").as("totalReused")
                .sum("ho_so_ket_qua_co_luu_kho").as("totalCompleteStorage")
                .sum("tiep_nhan_co_luu_kho").as("totalReceiverStorage")
                .sum("tai_su_dung_csdldc").as("totalReusedCSDLDC")
                .first("agency.name").as("ten_don_vi");

        AggregationOperation finalProjectOperation = Aggregation.project()
                .andExclude("_id")
                .and("_id").as("agency.idAgency")
                .and("ten_don_vi").as("agency.name")
                .and("totalReceiver").as("totalReceiver")
                .and("totalReceiverHavingFile").as("totalReceiverHavingFile")
                .and("totalReceiverNopeFile").as("totalReceiverNopeFile")
                .and("totalComplete").as("totalComplete")
                .and("totalCompleteHavingFile").as("totalCompleteHavingFile")
                .and("totalCompleteNopeFile").as("totalCompleteNopeFile")
                .and("totalCompleteStorage").as("totalCompleteStorage")
                .and("totalReceiverStorage").as("totalReceiverStorage")
                .and("totalReused").as("totalReused")
                .and("totalReusedCSDLDC").as("totalReusedCSDLDC");

        return Aggregation.newAggregation(
                matchOperation,
                projectOperation,
                groupOperation,
                finalProjectOperation
        );
    }

    private Set<ObjectId> uniqueObjectIdAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<ObjectId> uniqueObjectIds = new HashSet<>();
        Set<String> uniqueStringIds = new HashSet<>();
        for (String objectIdString : objectIdStrings) {
            try {
                ObjectId objectId = new ObjectId(objectIdString);
                uniqueObjectIds.add(objectId);
                uniqueStringIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    public List<DigitizingReportDto.CustomSummary> getDigitizingReportDto(List<String> agencyIds, List<String> excludedProcedureIds, String fromDateString, String toDateString) {
        List<GeneralReportDto.CustomSummary> results = new ArrayList<>();
        logger.info("Begin getGeneralReportByAgencyDto");

        try {

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }

            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);
            this.df.setTimeZone(this.timezone);

            Date fromDate = this.df.parse(fromDateString);
            Date toDate = this.df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            Aggregation aggregation = getAggregation(fromDate, toDate, agencyObjectIds, excludedProcedureIds);
            AggregationResults<DigitizingReportDto> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", DigitizingReportDto.class);
            List<DigitizingReportDto> generalReportDto = resultAggregation.getMappedResults();

            return getReportWithAgency(agencyIds, generalReportDto);

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportByAgencyDto");
        return null;
    }

    private List<String> getRootAgencies(List<String> agencyIds) throws JSONException {
        String getObjUrl;
        String jsonString;
        JSONArray jsonArr;

        List<String> rootAgencyIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(agencyIds)) {
            getObjUrl = "qni-agency/--find-root?id=" + String.join(",", agencyIds);
            getObjUrl = microservice.basedataUri(getObjUrl).toUriString();
            try {
                jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
                jsonArr = new JSONArray(jsonString);
                for (int i = 0; i < jsonArr.length(); i++) {
                    rootAgencyIds.add(jsonArr.getJSONObject(i).get("id").toString());
                }
            } catch (Exception ex) {
                logger.error("Can not find root agencies ", ex);
            }
        }
        return rootAgencyIds;
    }

    private Set<String> uniqueStringAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<String> uniqueObjectIds = new HashSet<>();

        for (String objectIdString : objectIdStrings) {
            try {
                uniqueObjectIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    private List<DigitizingReportDto.CustomSummary> getReportWithAgency(List<String> agencyIds, List<DigitizingReportDto> generalReportDto) throws JSONException {
        Comparator<DigitizingReportDto.CustomSummary> agencyComparator = Comparator.comparing(agency -> agency.getAgency().getName());
        var rootAgencys = getRootAgencies(agencyIds);
        var getObjUrl = microservice.basedataUri("agency/--by-parent-agency?arr-id=" + String.join(",", rootAgencys)).toUriString();
        var jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
        var jsonArr = new JSONArray(jsonString);

        List<DigitizingReportDto.Agency> agencyTransList = new ArrayList<>();

        if (Objects.nonNull(jsonArr)) {
            for (int i = 0; i < jsonArr.length(); i++) {
                String agencyIdTemp = jsonArr.getJSONObject(i).get("id").toString();
                var target = new DigitizingReportDto.Agency();
                target.setIdAgency(agencyIdTemp);
                target.setName(jsonArr.getJSONObject(i).get("name").toString());

                agencyTransList.add(target);
            }
        }

        List<DigitizingReportDto.CustomSummary> resultsAgency = new ArrayList<>();
        for (DigitizingReportDto.Agency agency : agencyTransList) {
            var target = new DigitizingReportDto.CustomSummary(agency, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0);
            for (DigitizingReportDto generalReport : generalReportDto) {

                AgencyFilterReportQniResponse agencyTemp = null;
                List<String> agencyTemps = null;
                try {
                    agencyTemp = agencyFilterReportQniService.getDocumentsByAgencyId(agency.getId());
                    if (agencyTemp != null) {
                        agencyTemps = new ArrayList<>(uniqueStringAgency(agencyTemp.getIdFilter()));
                    }
                } catch (Exception e) {
                    continue;
                }

                if (agencyTemps.contains(generalReport.getAgency().getId())) {
                    target.setTotalReceiver(target.getTotalReceiver() + generalReport.getTotalReceiver());
                    target.setTotalComplete(target.getTotalComplete() + generalReport.getTotalComplete());
                    target.setTotalReceiverHavingFile(target.getTotalReceiverHavingFile() + generalReport.getTotalReceiverHavingFile());
                    target.setTotalCompleteHavingFile(target.getTotalCompleteHavingFile() + generalReport.getTotalCompleteHavingFile());
                    target.setTotalReceiverNopeFile(target.getTotalReceiverNopeFile() + generalReport.getTotalReceiverNopeFile());
                    target.setTotalCompleteNopeFile(target.getTotalCompleteNopeFile() + generalReport.getTotalCompleteNopeFile());
                    target.setTotalReused(target.getTotalReused() + generalReport.getTotalReused());
                    if (target.getTotalReceiver() != 0){
                        target.setPercentTotalReceiverHavingFile((double) target.getTotalReceiverHavingFile() / target.getTotalReceiver() * 100);
                    }
                    if (target.getTotalComplete() != 0){
                        target.setPercentTotalCompleteHavingFile((double) target.getTotalCompleteHavingFile() / target.getTotalComplete() * 100);
                    }
                    target.setTotalCompleteStorage(target.getTotalCompleteStorage() + generalReport.getTotalCompleteStorage());
                    target.setTotalReceiverStorage(target.getTotalReceiverStorage() + generalReport.getTotalReceiverStorage());
                    target.setTotalReusedCSDLDC(target.getTotalReusedCSDLDC() + generalReport.getTotalReusedCSDLDC());
                }
            }

            resultsAgency.add(target);
        }

        return resultsAgency.stream().filter(x ->
                x.getTotalReceiver() > 0 ||
                        x.getTotalComplete() > 0 ||
                        x.getTotalReceiverHavingFile() > 0 ||
                        x.getTotalCompleteHavingFile() > 0 ||
                        x.getTotalReceiverNopeFile() > 0 ||
                        x.getTotalCompleteNopeFile() > 0 ||
                        x.getTotalReused() > 0 ||
                        x.getPercentTotalReceiverHavingFile() > 0 ||
                        x.getPercentTotalCompleteHavingFile() > 0
        ).sorted(
                agencyComparator
        ).collect(Collectors.toList());
    }

    public Page<DetailGeneralReportDto.PageResult> getGeneralDigitizingReportDetailDto(List<String> agencyIds,
                                                                                       List<String> excludedProcedureIds,
                                                                                       String fromDateString,
                                                                                       String toDateString,
                                                                                       Integer type, Pageable pageable) {
        logger.info("Begin getGeneralReportDetailDto");

        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }

            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, excludedProcedureIds, type, pageable, false);

            AggregationResults<DetailGeneralReportDto> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", DetailGeneralReportDto.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults().get(0).getPagedResults();
            long totalCount = 0;
            if (!results.isEmpty()) {
                totalCount = resultAggregation.getMappedResults().get(0).getTotalCount().get(0).getTotalCount();
            }
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                // Ẩn thông tin bảo mật và gán số thứ tự
                int startIndex = pageable.getPageNumber() * pageable.getPageSize();
                for (int i = 0; i < results.size(); i++) {
                    DetailGeneralReportDto.PageResult item = results.get(i);
                    item.setHideSecurityInformation(item.getApplicantOwnerFullName(), item.getAssigneeFullname());
                    item.setNo(startIndex + i + 1);
                }
            }else{
            IntStream.range(0, results.size())
                    .forEach(i -> results.get(i).setNo((pageable.getPageNumber() * pageable.getPageSize()) + i + 1));
            }
            return new PageImpl<>(results, pageable, totalCount);

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportDetailDto");
        return null;
    }

    public Aggregation getAggregationDetail(Date fromDate,
                                            Date toDate,
                                            List<ObjectId> agencyIds,
                                            List<String> excludedProcedureIds,
                                            Integer type,
                                            Pageable pageable,
                                            Boolean ignorePagination) throws JSONException {
        AggregationOperation matchOperation = Aggregation.match(buildCriteria(agencyIds));
        if (excludedProcedureIds!= null && !excludedProcedureIds.isEmpty()){
            matchOperation = Aggregation.match(buildCriteriaV2IgnoreProcedure(agencyIds,excludedProcedureIds));
        }
        Document documentExpr = new Document("$expr", docunemtMatch(type, fromDate, toDate));
        AggregationOperation matchCondition = context -> {
            return new Document("$match", documentExpr);
        };

        AggregationOperation groupOperation = Aggregation.group("_id")
                .first("code").as("dossierCode")
                .first("acceptedDate").as("acceptedDate")
                .first("appointmentDate").as("appointmentDate")
                .first("completedDate").as("completedDate")
                .first("applicant.ownerFullName").as("ownerFullName")
                .first("applicant.noiDungYeuCauGiaiQuyet").as("noiDungYeuCauGiaiQuyet")
                .first("applicant.phoneNumber").as("phoneNumber")
                .first("currentTask.assignee.fullname").as("assigneeFullname")
                .first("sector.name").as("sectorName")
                .first("procedure.name").as("procedureName")
                .first("currentTask.agency.name").as("curTaskAgencyName")
                .first("dossierStatus.name").as("dossierStatusName")
                .first("applyMethod.name").as("applyMethod");


        AggregationOperation finalProjectOperation = Aggregation.project()
                .andInclude("_id")
                .and(DateOperators.DateToString.dateOf("acceptedDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("acceptedDate")
                .and(DateOperators.DateToString.dateOf("appointmentDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("appointmentDate")
                .and(DateOperators.DateToString.dateOf("completedDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("completedDate")
                .and("dossierCode").as("dossierCode")
                .and("ownerFullName").as("applicantOwnerFullName")
                .and("noiDungYeuCauGiaiQuyet").as("noiDungYeuCauGiaiQuyet")
                .and("phoneNumber").as("applicantPhoneNumber")
                .and("assigneeFullname").as("assigneeFullname")
                .and("sectorName").as("sectorName")
                .and("procedureName").as("procedureName")
                .and("curTaskAgencyName").as("curTaskAgencyName")
                .and("dossierStatusName").as("dossierStatusName")
                .and("applyMethod").as("applyMethod");

        List<AggregationOperation> aggregationOperations = null;
        if (ignorePagination) {
            aggregationOperations = Arrays.asList(
                    matchOperation,
                    matchCondition,
                    groupOperation,
                    finalProjectOperation
            );
        } else {
            AggregationOperation skipStage = Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize());
            AggregationOperation limitStage = Aggregation.limit(pageable.getPageSize());

            AggregationOperation totalCountGroupOperation = Aggregation.group().count().as("totalCount");
            AggregationOperation totalCountProjectOperation = Aggregation.project()
                    .andExclude("_id")
                    .andInclude("totalCount");

            FacetOperation pagedFacet = new FacetOperation().and(groupOperation, finalProjectOperation, skipStage, limitStage).as("pagedResults")
                    .and(totalCountGroupOperation, totalCountProjectOperation).as("totalCount");

            aggregationOperations = Arrays.asList(
                    matchOperation,
                    matchCondition,
                    pagedFacet
            );
        }

        AggregationOptions options = AggregationOptions.builder()
                .allowDiskUse(true)
                .build();
        return Aggregation.newAggregation(aggregationOperations).withOptions(options);
    }

    public ResponseEntity<Object> exportGeneralReportDetail(String fromDate, String toDate,
                                                            List<String> agencyIds,
                                                            List<String> excludedProcedureIds,
                                                            Integer type) {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfTimestamp = new SimpleDateFormat("yyyyMMddHHmm");
        DateFormat dfCurrentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dfTimestamp.setTimeZone(timezone);
        dfCurrentDate.setTimeZone(timezone);
        String timestamp = dfTimestamp.format(date);
        String currentDate = dfCurrentDate.format(date);
        String fromDateReport = fromDate.substring(8, 10) + "/" + fromDate.substring(5, 7) + "/" + fromDate.substring(0, 4);
        String toDateReport = toDate.substring(8, 10) + "/" + toDate.substring(5, 7) + "/" + toDate.substring(0, 4);
        String filename = timestamp + "-danh-sach-ho-so.xlsx";
        byte[] resource = new byte[0];
        try {
            InputStream is = resourceTemplateDossierStatisticAssigneeQNIV2.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            var itemDtos = getDataExcelDto(agencyIds, excludedProcedureIds, fromDate, toDate, type);

            context.putVar("textBanner", translator.toLocale("lang.word.gov"));
            context.putVar("subtextBanner", translator.toLocale("lang.word.ifh"));

            context.putVar("title", translator.toLocale("lang.word.dossier-statistic-title"));
            context.putVar("subTitle", translator.toLocale("lang.word.dossier-statistic-fromdate-todate", new String[]{fromDateReport, toDateReport}));
            context.putVar("currentDate", translator.toLocale("lang.word.dossier-statistic-current-date", new String[]{currentDate}));
            context.putVar("no", translator.toLocale("lang.word.no"));
            context.putVar("dossierCode", translator.toLocale("lang.word.dossier-statistic-dossier-code"));
            context.putVar("procedureName", translator.toLocale("lang.word.dossier-statistic-procedure-name"));
            context.putVar("sectorName", translator.toLocale("lang.word.dossier-statistic-sector-name"));
            context.putVar("noiDungYeuCauGiaiQuyet", translator.toLocale("lang.word.dossier-statistic-noidungyeucaugiaiquyet"));
            context.putVar("acceptedDate", translator.toLocale("lang.word.dossier-statistic-accepted-date"));
            context.putVar("appointmentDate", translator.toLocale("lang.word.dossier-statistic-appointment-date"));
            context.putVar("completedDate", translator.toLocale("lang.word.dossier-statistic-completed-date"));
            context.putVar("applicantOwnerFullName", translator.toLocale("lang.word.dossier-statistic-applicant-ownerfullname"));
            context.putVar("applicantPhoneNumber", translator.toLocale("lang.word.dossier-statistic-applicant-phonenumber"));
            context.putVar("assigneeFullname", translator.toLocale("lang.word.dossier-statistic-assignee-fullname"));
            context.putVar("dossierStatusName", translator.toLocale("lang.word.dossier-statistic-status-name"));
            context.putVar("applyMethod", translator.toLocale("lang.word.dossier-statistic-applied-method"));
            context.putVar("writer", translator.toLocale("lang.word.dossier-statistic-writer"));
            context.putVar("reporter", translator.toLocale("lang.word.dossier-statistic-reporter"));
            context.putVar("itemDtos", itemDtos);

            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            resource = outputStream.toByteArray();
        } catch (Exception ex) {
            logger.info("exportDossierStatistic012020Detail error:" + ex.getMessage());
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(resource);
    }

    public List<DetailGeneralReportDto.PageResult> getDataExcelDto(List<String> agencyIds,
                                                                   List<String> excludedProcedureIds,
                                                                   String fromDateString,
                                                                   String toDateString,
                                                                   Integer type) {
        logger.info("Begin getGeneralReportDetailDto");

        try {

            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, excludedProcedureIds, type, null, true);

            AggregationResults<DetailGeneralReportDto.PageResult> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", DetailGeneralReportDto.PageResult.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults();
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                // Ẩn thông tin bảo mật và gán số thứ tự
                for (int i = 0; i < results.size(); i++) {
                    DetailGeneralReportDto.PageResult item = results.get(i);
                    item.setHideSecurityInformation(item.getApplicantOwnerFullName(), item.getAssigneeFullname());
                    item.setNo(i + 1);
                }
            }else{
            IntStream.range(0, results.size())
                    .forEach(i -> results.get(i).setNo(i + 1));
            }

            return results;

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportDetailDto");
        return null;
    }

    private List<ObjectId> buildStringToListObject(List<String> stringIds){
        if (stringIds == null || stringIds.isEmpty()) {
            return Collections.emptyList();
        }

        return stringIds.stream()
                .filter(id -> id != null && ObjectId.isValid(id))
                .distinct()
                .map(ObjectId::new)
                .collect(Collectors.toList());
    }

    private List<ObjectId> getExcludedProcedure(List<String> agencyIds, List<String> listProcedureCode){
        List<ObjectId> result = new ArrayList<>();
        try{
            String url = microservice.basepadUri("/procedure/--procedure-id-list-qni?procedure-code=" + String.join(",", listProcedureCode)).toUriString();
//            String url = "http://localhost:8091/procedure/--procedure-id-list-qni?procedure-code=" + String.join(",", listProcedureCode);
            String rawResponse = MicroserviceExchange.get(restTemplate, url, String.class);
            JsonArray excludedProcedure = gson.fromJson(rawResponse, JsonArray.class);
            for (JsonElement jsonElement : excludedProcedure) {
                result.add(new ObjectId(jsonElement.getAsString()));
            }
        } catch (Exception e) {
            logger.error("Error getExcludedProcedure qni digitizing report: {}", e.getMessage());
        }
        return result;
    }

    public Criteria buildCriteriaV2IgnoreProcedure(List<ObjectId> agencyIds, List<String> listProcedureCode){
        Criteria criteria = new Criteria();
        //IGATESUPP-105204 Loại trừ 1 số thủ tục không tính vào báo cáo số hóa theo CV 2768VPTTHC
        List<String> strAgencies =  agencyIds.stream()
                .map(ObjectId::toHexString)
                .collect(Collectors.toList());
        List<ObjectId> excludedProcedureIds = this.getExcludedProcedure(strAgencies, listProcedureCode);
        criteria = criteria.andOperator(
                Criteria.where("agency._id").in(agencyIds),
                Criteria.where("procedure._id").nin(excludedProcedureIds)
        );
        return criteria;
    }
}
