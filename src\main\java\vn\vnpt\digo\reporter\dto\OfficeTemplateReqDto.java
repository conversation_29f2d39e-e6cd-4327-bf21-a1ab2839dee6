package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import org.bson.types.ObjectId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.util.ParamName;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OfficeTemplateReqDto implements Serializable {
    
    @ParamName("id")
    private ObjectId id;

    @ParamName("api")
    private String api;

    @ParamName("query")
    private String query;

    @ParamName("save-format")
    private String saveFormat = "pdf";
}
