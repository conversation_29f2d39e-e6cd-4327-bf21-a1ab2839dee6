#!/bin/bash

echo "----------------"
echo "ENTRYPOINT START"
echo "----------------"
echo ""

CERT_DIR="./cert"

if [ -d "$CERT_DIR" ]
then
  for crtFile in $(ls $CERT_DIR);
     do
       echo "$crtFile"
       keytool -importcert -file "$CERT_DIR/$crtFile" -alias "$crtFile" -cacerts -storepass changeit -noprompt
     done
else
  echo "No external cert found. ($CERT_DIR not found)"
fi

exec java -jar svc-reporter-1.0.0-beta.1.jar
