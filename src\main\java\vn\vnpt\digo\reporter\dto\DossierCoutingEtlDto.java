package vn.vnpt.digo.reporter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.Place;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierCoutingEtlDto {
  private DossierSumEtlDto.CountingData countingData;


  private DossierAgencyDto agency;
  //  private DossierSectorDto sector;
  private long previousPeriod; // kì trướ<PERSON> chuyển sang


  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class CountingData {
    private long received;// da tiep nhan
    private long completed;// da hoan thanh (cả trể và ko trể)
    private long completedOnTime; // hs hoàn thành dung han
    private long completedEarly;// hs hoàn thành som han
    private long completedOutOfDue; // hs hoàn thành quá hạn
    private long onlineReceived;// tiếp nhận trực tuyến
    private long directReceived;// tiếp nhận trực tiếp

    private long inProgress;// dang xu ly
    private long inProgressAndOnTime;// hs chua den han, dang xu ly
    private long inProgressAndOutOfDue; // hs đang xử lý và quá hạn



  }
  private DossierSectorDto sector;

}




