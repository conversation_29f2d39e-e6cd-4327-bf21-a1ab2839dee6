package vn.vnpt.digo.reporter.service;

import org.json.JSONObject;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.dto.statistics.CommonStatisticsDto;
import vn.vnpt.digo.reporter.dto.statistics.GeneralStatisticsRequestDto;
import vn.vnpt.digo.reporter.dto.statistics.GeneralStatisticsResponseDto;
import vn.vnpt.digo.reporter.dto.statistics.ThongKe;
import vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class CommonStatisticsService {
    @Autowired
    private Microservice microservice;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${digo.common-statistics.procedure-level-4}")
    private ObjectId procedureLevel4;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String oidcPropertiesUrl;

    @Value("${digo.oidc.client-id}")
    private String client;

    @Value("${digo.oidc.client-secret}")
    private String secret;

    private static String oidcUrl;
    private static String clientId;
    private static String clientSecret;

    @PostConstruct
    void init() {
        clientId = client;
        clientSecret = secret;
        oidcUrl = oidcPropertiesUrl;
    }


    public String getToken() {
        String token = null;
        try{
            JwtAuthenticationToken claims = Context.getJwtAuthenticationToken();
            token = claims.getToken().getTokenValue();
        } catch (Exception ex) {}

        if(Objects.isNull(token)){
            try{
                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("grant_type", "client_credentials");
                map.add("client_id", clientId);
                map.add("client_secret", clientSecret);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
                String url = oidcUrl + "/protocol/openid-connect/token";
                System.out.println(url);
                ResponseEntity<JWTGetTokenResponseBody> result = restTemplate.exchange(url, HttpMethod.POST, request, JWTGetTokenResponseBody.class);
                token = result.getBody().getAccessToken();
            } catch (Exception ex) {}
        }
        return token;
    }

    public Object summaryStatistics () {
        // todo tổng hợp thống kê
        return null;
    }

    public GeneralStatisticsResponseDto generalStatistics (GeneralStatisticsRequestDto input) {
        // thống kê tổng quan của từng deployment

        GeneralStatisticsResponseDto response = new GeneralStatisticsResponseDto();
        ArrayList<ThongKe> arrayThongKe = new ArrayList<>();

        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Ho_Chi_Minh"));
        String formattedDate = simpleDateFormat.format(new Date());

        CommonStatisticsDto commonStatisticsPadman = commonStatisticsPadman(String.join(",", input.getLoaiThongKe()));

        if (input.getLoaiThongKe().contains("thongKeDvc")) {
            // 1. Thống kê dịch vụ công
            arrayThongKe.add(thongkeDVC(formattedDate));
        }

        if (input.getLoaiThongKe().contains("thongKeTongHoSo")) {
            // 2. Thống kê tổng hồ sơ
            arrayThongKe.add(new ThongKe("thongKeTongHoSo", formattedDate, commonStatisticsPadman.getThongKeTongHoSo()));
        }

        if (input.getLoaiThongKe().contains("thongKeTongHoSoTheoNam")) {
            // 3. Thống kê tổng hồ sơ theo năm
            arrayThongKe.add(new ThongKe("thongKeTongHoSoTheoNam", formattedDate, commonStatisticsPadman.getThongKeTongHoSoTheoNam()));
        }

        if (input.getLoaiThongKe().contains("thongKeTongHoSoTheoNam")) {
            // 4. Thống kê kho dữ liệu
            HashMap<String, Object> thongKeKhoDuLieu = new HashMap<>();
            thongKeKhoDuLieu.put("tongHsLuuTphsVaoKho", commonStatisticsPadman.getTongHsLuuTphsVaoKho());
            thongKeKhoDuLieu.put("tongTphsLuuVaoKho", commonStatisticsPadman.getTongTphsLuuVaoKho());
            thongKeKhoDuLieu.put("tongHsSuDungGiayToTuKho", commonStatisticsPadman.getTongHsSuDungGiayToTuKho());
            thongKeKhoDuLieu.put("tongTphsSuDungGiayToTuKho", commonStatisticsPadman.getTongTphsSuDungGiayToTuKho());
            thongKeKhoDuLieu.put("tongHsLuuKetQuaVaoKho", commonStatisticsPadman.getTongHsLuuKetQuaVaoKho());
            arrayThongKe.add(thongKeKhoDuLieu(formattedDate, thongKeKhoDuLieu));
        }

        if (input.getLoaiThongKe().contains("thongKeThanhToanTrucTuyen")) {
            // 5. Thống kê thanh toán trực tuyến
            arrayThongKe.add(new ThongKe("thongKeThanhToanTrucTuyen", formattedDate, commonStatisticsPadman.getThongKeThanhToanTrucTuyen()));
        }

        if (input.getLoaiThongKe().contains("thongKeSms")) {
            // 6. Thống kê sms
            arrayThongKe.add(thongKeSms(formattedDate));
        }

        if (input.getLoaiThongKe().contains("thongKeSoHoa")) {
            // 7. Thống kê số hóa
            arrayThongKe.add(new ThongKe("thongKeSoHoa", formattedDate, commonStatisticsPadman.getThongKeSoHoa()));
        }

        if (input.getLoaiThongKe().contains("thongKeTaiKhoan")) {
            // 8. Thống kê tài khoản
            arrayThongKe.add(thongKeTaiKhoan(formattedDate));
        }

        if (input.getLoaiThongKe().contains("thongKeTruyCap")) {
            // 9. Thống kê truy cập
            arrayThongKe.add(thongKeTruyCap(formattedDate));
        }


        response.setArrayThongKe(arrayThongKe);
        return response;
    }

    private ThongKe thongkeDVC (String date) {
        // 1. Thống kê dịch vụ công

        ThongKe thongKe = new ThongKe("thongkeDVC", date, null);
        HashMap<String, Object> duLieuThongKe = new HashMap<>();

        try {
            String basepadUrl = microservice.basepadUri("procedure?&spec=page&page=0&sort=createdDate,desc&represent=true&size=1&procedure-level-id=").toUriString();
            Integer tongSoDvc = getTotalElementsFromRequest(basepadUrl);
            Integer dvcToanTrinh = getTotalElementsFromRequest(basepadUrl + procedureLevel4);
            duLieuThongKe.put("tongSoDvc", tongSoDvc);
            duLieuThongKe.put("soDvcToanTrinh", dvcToanTrinh);
        } catch (Exception e) {
            duLieuThongKe.put("ERR", e);
        }

        thongKe.setDuLieuThongKe(duLieuThongKe);
        return thongKe;
    }

    private Integer getTotalElementsFromRequest (String url) {
        // Lấy totalElements từ response dạng Slice
        String response = MicroserviceExchange.get(getToken(), restTemplate, url,  String.class);
        JSONObject responseObj = new JSONObject(response);
        return Integer.parseInt(responseObj.get("totalElements").toString());
    }
    

    private CommonStatisticsDto commonStatisticsPadman (String statistics) {
        try {
            String padmanUrl = microservice.padmanUri("/dossier-statistic/common-statistics?statistics=" + statistics).toUriString();
            return MicroserviceExchange.get(getToken(), restTemplate, padmanUrl,  CommonStatisticsDto.class);
        } catch (Exception e) {
            return new CommonStatisticsDto();
        }
    }

    private ThongKe thongKeKhoDuLieu (String date, HashMap<String, Object> data) {
        // 4. Thống kê kho dữ liệu

        ThongKe thongKe = new ThongKe("thongKeKhoDuLieu", date, null);
        
        try {
            String storageUrl = microservice.storageUri("/statistics-report/--common-statistics").toUriString();
            CommonStatisticsDto thongKeKhoDuLieu = MicroserviceExchange.get(getToken(), restTemplate, storageUrl,  CommonStatisticsDto.class);
            data.put("tongNguoiDanCoGiayToTrongKho", thongKeKhoDuLieu.getTongNguoiDanCoGiayToTrongKho());
            data.put("tongGiayToTrongKho", thongKeKhoDuLieu.getTongGiayToTrongKho());
            data.put("tongDungLuongKhoDaSuDung", thongKeKhoDuLieu.getTongDungLuongKhoDaSuDung());
            data.put("ngayDungKho", thongKeKhoDuLieu.getNgayDungKho());
        } catch (Exception e) {
        }
        
        thongKe.setDuLieuThongKe(data);
        return thongKe;
    }

    private ThongKe thongKeSms (String date) {
        // 6. Thống kê SMS

        ThongKe thongKe = new ThongKe("thongKeSms", date, null);
        HashMap<String, Object> duLieuThongKe = new HashMap<>();
        
        try {
            String adapterUrl = microservice.adapterUri("integrated-logs/--count-sms-log").toUriString();
            Long tongSoSmsDaGui = MicroserviceExchange.get(getToken(), restTemplate, adapterUrl,  Long.class);;
            duLieuThongKe.put("tongSoSmsDaGui", tongSoSmsDaGui);
            duLieuThongKe.put("soSmsDaGuiDan", tongSoSmsDaGui);
        } catch (Exception e) {
            duLieuThongKe.put("ERR", e);
        }

        thongKe.setDuLieuThongKe(duLieuThongKe);
        return thongKe;
    }

    private ThongKe thongKeTaiKhoan (String date) {
        // 8. Thống kê tài khoản

        ThongKe thongKe = new ThongKe("thongKeTaiKhoan", date, null);
        HashMap<String, Object> duLieuThongKe = new HashMap<>();

        try {
            String humanUrl = microservice.humanUri("user?ldap=0&page=0&size=1&sortType=asc").toUriString();
            Integer soTaiKhoanCongDan = getTotalElementsFromRequest(humanUrl + "&type=1");
            Integer soTaiKhoanDoanhNghiep = getTotalElementsFromRequest(humanUrl + "&type=2");
            Integer soTaiKhoanCanBo = getTotalElementsFromRequest(humanUrl + "&type=3");
            duLieuThongKe.put("soTaiKhoanCongDan", soTaiKhoanCongDan);
            duLieuThongKe.put("soTaiKhoanDoanhNghiep", soTaiKhoanDoanhNghiep);
            duLieuThongKe.put("soTaiKhoanCanBo", soTaiKhoanCanBo);
        } catch (Exception e) {
            duLieuThongKe.put("ERR", e);
        }

        thongKe.setDuLieuThongKe(duLieuThongKe);
        return thongKe;
    }

    private ThongKe thongKeTruyCap (String date) {
        // 9. Thống kê truy cập

        ThongKe thongKe = new ThongKe("thongKeTruyCap", date, null);
        
        try {
            String logmanUrl = microservice.logmanUri("user-events-log/--common-statistics").toUriString();
            Object thongKeTruyCap = MicroserviceExchange.get(getToken(), restTemplate, logmanUrl,  Object.class);
            thongKe.setDuLieuThongKe(thongKeTruyCap);
        } catch (Exception e) {
            HashMap<String, Object> duLieuThongKe = new HashMap<>();
            duLieuThongKe.put("ERR", e);
            thongKe.setDuLieuThongKe(duLieuThongKe);
        }
        
        return thongKe;
    }

}
