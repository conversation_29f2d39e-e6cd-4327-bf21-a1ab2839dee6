package vn.vnpt.digo.reporter.document;

import java.util.Date;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;


@Data
@NoArgsConstructor
@Document(collection = "waterCustomer")
@CompoundIndex(name = "unique_waterCustomer", def = "{'userId': 1, 'customerCode': 1}", unique = true)
public class WaterCustomer {
    @Id
    private ObjectId id;
    private ObjectId userId;
    private String customerCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    public WaterCustomer (ObjectId userId, String customerCode) {

        this.userId = userId;
        this.customerCode = customerCode;
        this.updatedDate = new Date();
    }

    public void setUpdateDate(){
        this.updatedDate = new Date();
    }
}