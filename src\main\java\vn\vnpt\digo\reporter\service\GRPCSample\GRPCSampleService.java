package vn.vnpt.digo.reporter.service.GRPCSample;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.reporter.grpc.util.GrpcMicroservice;


@Service
public class GRPCSampleService {
    Logger logger = LoggerFactory.getLogger(GRPCSampleService.class);

    @Autowired
    private GrpcMicroservice grpcMicroservice;

    //Hàm để các service khác gọi sang
    public String testGRPCSample(String input){
        return "[LOGMAN] Bạn vừa nhập " + input;
    }

    //Hàm gọi sang adapter
    public String testGRPCAdapter(String input){
        String result = null;
        String URL = "/sample/test-grpc-adapter";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCAdapter: " + URL);
            json = grpcMicroservice.adapterUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCAdapter: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCBasecat(String input){
        String result = null;
        String URL = "/sample/test-grpc-basecat";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCBasecat: " + URL);
            json = grpcMicroservice.basecatUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCBasecat: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCBasedata(String input){
        String result = null;
        String URL = "/sample/test-grpc-basedata";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCBasedata: " + URL);
            json = grpcMicroservice.basedataUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCBasedata: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCBpm(String input){
        String result = null;
        String URL = "/sample/test-grpc-bpm";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCBpm: " + URL);
            json = grpcMicroservice.bpmUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCBpm: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCEform(String input){
        String result = null;
        String URL = "/sample/test-grpc-eform";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCEform: " + URL);
            json = grpcMicroservice.eformUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCEform: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCFileman(String input){
        String result = null;
        String URL = "/sample/test-grpc-fileman";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCFileman: " + URL);
            json = grpcMicroservice.filemanUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCFileman: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCHuman(String input){
        String result = null;
        String URL = "/sample/test-grpc-human";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCHuman: " + URL);
            json = grpcMicroservice.humanUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCHuman: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCLogman(String input){
        String result = null;
        String URL = "/sample/test-grpc-logman";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCLogman: " + URL);
            json = grpcMicroservice.logmanUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCLogman: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCMessenger(String input){
        String result = null;
        String URL = "/sample/test-grpc-messenger";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCMessenger: " + URL);
            json = grpcMicroservice.reporterUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCMessenger: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCModeling(String input){
        String result = null;
        String URL = "/sample/test-grpc-modeling";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCModeling: " + URL);
            json = grpcMicroservice.modelingUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCModeling: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCReporter(String input){
        String result = null;
        String URL = "/sample/test-grpc-reporter";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCReporter: " + URL);
            json = grpcMicroservice.reporterUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCReporter: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCSysman(String input){
        String result = null;
        String URL = "/sample/test-grpc-sysman";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCSysman: " + URL);
            json = grpcMicroservice.sysmanUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCSysman: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCSystem(String input){
        String result = null;
        String URL = "/sample/test-grpc-system";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCSystem: " + URL);
            json = grpcMicroservice.systemUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCSystem: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCBasepad(String input){
        String result = null;
        String URL = "/sample/test-grpc-basepad";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCBasepad: " + URL);
            json = grpcMicroservice.basepadUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCBasepad: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCPadman(String input){
        String result = null;
        String URL = "/sample/test-grpc-padman";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCPadman: " + URL);
            json = grpcMicroservice.padmanUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCPadman: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCRbonegate(String input){
        String result = null;
        String URL = "/sample/test-grpc-rbonegate";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCRbonegate: " + URL);
            json = grpcMicroservice.rbonegateUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCRbonegate: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCStorage(String input){
        String result = null;
        String URL = "/sample/test-grpc-storage";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCStorage: " + URL);
            json = grpcMicroservice.storageUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCStorage: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

    public String testGRPCSurfeed(String input){
        String result = null;
        String URL = "/sample/test-grpc-surfeed";
        URL += "?input=" + input;

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        String json = null;
        try{
            logger.info("testGRPCSurfeed: " + URL);
            json = grpcMicroservice.surfeedUri(uriBuilder, "GET");
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(json, new TypeReference<String>(){});
        } catch(Exception ex){
            logger.info("testGRPCSurfeed: " + URL);
            logger.info("Error: " + ex.getMessage());
        }
        return result;
    }

}
