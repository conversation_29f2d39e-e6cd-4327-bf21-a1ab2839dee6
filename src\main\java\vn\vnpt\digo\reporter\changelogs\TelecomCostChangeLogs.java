package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-05-20-11-16")
public class TelecomCostChangeLogs {

    @ChangeSet(order = "2020-05-20-11-16", id = "TelecomCostChangeLogs::create", author = "haimn")
    public void create(DB db) {
        db.createCollection("telecomCost", null);
    }
        
}
