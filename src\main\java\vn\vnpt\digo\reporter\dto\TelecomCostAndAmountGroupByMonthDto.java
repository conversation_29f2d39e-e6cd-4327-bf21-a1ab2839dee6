/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TelecomCostAndAmountGroupByMonthDto implements Serializable {

    private Integer month;

    private double totalAmount = 0;

    public void setTotalAmount(double amount) {
        totalAmount += amount;
    }

    public static void setTotalAmount(Integer month, double amount, List<TelecomCostAndAmountGroupByMonthDto> list) {

        list.stream().filter((item) -> (item.getMonth().equals(month))).forEachOrdered((item) -> {
            item.setTotalAmount(amount);
        });
    }

    public static boolean existMonth(Integer month, List<TelecomCostAndAmountGroupByMonthDto> list) {

        return list.stream().anyMatch((item) -> (item.getMonth().equals(month)));
    }
}
