package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Attachment implements Serializable {

		@Field("id")
		@JsonSerialize(using = ToStringSerializer.class)
		private ObjectId id;

		private String filename;

		private long size;

		private String extension;

		private UUID uuid;

		@JsonSerialize(using = ToStringSerializer.class)
		private ObjectId group;

}
