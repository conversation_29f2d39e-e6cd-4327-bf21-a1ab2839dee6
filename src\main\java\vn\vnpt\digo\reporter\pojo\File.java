package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class File implements Serializable {
	@Field("id")
	@JsonSerialize(using = ToStringSerializer.class)
	private ObjectId id;
	private String filename;
	private Integer size;
}
