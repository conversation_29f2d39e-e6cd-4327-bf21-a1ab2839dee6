package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.service.GeneralReportIgnorePauseExtendQNIService;
import vn.vnpt.digo.reporter.service.GeneralReportQNIService;
import vn.vnpt.digo.reporter.service.QNIETLDossierService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/general-report-qni")
@IcodeAuthorize("vnpt.permission.generalreportqni")
public class GeneralReportQNIController {

    Logger logger = LoggerFactory.getLogger(GeneralReportQNIController.class);
    @Autowired
    private GeneralReportQNIService generalReportQNIService;

    @Autowired
    private QNIETLDossierService qniETLDossierService;

    @Value("${digo.report.ignore-pause-extend-date}")
    private Boolean ignorePauseExtendDate;

    @Autowired
    private GeneralReportIgnorePauseExtendQNIService generalReportIgnorePauseExtendQNIService;

    @GetMapping(value = "")
    public List<GeneralReportDto.CustomSummary> getProcedureQuantityByTag(HttpServletRequest request,
                                                                          @RequestParam(value = "from-date", required = true) String fromDate,
                                                                          @RequestParam(value = "to-date", required = true) String toDate,
                                                                          @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                                          @RequestParam(value = "report-type-id", required = false) Integer reportTypeId,
                                                                          @RequestParam(value = "sector-id", required = false) List<String> sectorId,
                                                                          @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
                                                                          @RequestParam(value = "is-dvc", required = false) Boolean isDVC,
                                                                          @RequestParam(value = "include-inactive", required = false) Boolean includeInactiveAgency) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GeneralReportDto.CustomSummary> reportResult = null;
        if (ignorePauseExtendDate) {
            reportResult = generalReportIgnorePauseExtendQNIService.getGeneralReportDto(agencyIds, fromDate, toDate, reportTypeId, sectorId, procedureId, isDVC, includeInactiveAgency);
        } else {
            reportResult = generalReportQNIService.getGeneralReportDto(agencyIds, fromDate, toDate, reportTypeId, sectorId, procedureId);
        }

        logger.info("DIGO-Info: " + reportResult.size());
        return reportResult;
    }

    @GetMapping(value = "/--detail")
    public Page<DetailGeneralReportDto.PageResult> getProcedureQuantityByTag(HttpServletRequest request,
                                                                             @RequestParam(value = "from-date", required = true) String fromDate,
                                                                             @RequestParam(value = "to-date", required = true) String toDate,
                                                                             @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                                             @RequestParam(value = "report-type-id", required = false) Integer reportTypeId,
                                                                             @RequestParam(value = "sector-id", required = false) List<String> sectorId,
                                                                             @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
                                                                             @RequestParam(value = "type", required = false) Integer type,
                                                                             Pageable pageable
    ) {
        try {
            String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
            logger.info("DIGO-Info: " + requestPath);

            Page<DetailGeneralReportDto.PageResult> results = null;
            if (ignorePauseExtendDate) {
                results = generalReportIgnorePauseExtendQNIService.getGeneralReportDetailDto(agencyIds,
                        fromDate, toDate,
                        reportTypeId, sectorId,
                        procedureId, type, pageable);
            } else {
                results = generalReportQNIService.getGeneralReportDetailDto(agencyIds,
                        fromDate, toDate,
                        reportTypeId, sectorId,
                        procedureId, type, pageable);
            }

            logger.info("DIGO-Info: " + results.getTotalElements());
            return results;
        } catch (Exception e) {
            logger.error("DIGO-Info: " + e.getMessage());
        }
        return null;
    }

    @GetMapping(value = "/--detail/--all")
    public List<DetailGeneralReportDto.PageResult> getAllProcedureQuantityByTag(HttpServletRequest request,
                                                                             @RequestParam(value = "from-date", required = true) String fromDate,
                                                                             @RequestParam(value = "to-date", required = true) String toDate,
                                                                             @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                                             @RequestParam(value = "report-type-id", required = false) Integer reportTypeId,
                                                                             @RequestParam(value = "sector-id", required = false) List<String> sectorId,
                                                                             @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
                                                                             @RequestParam(value = "type", required = false) Integer type) {
        try {
            String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
            logger.info("DIGO-Info: " + requestPath);

            List<DetailGeneralReportDto.PageResult> results = null;
            if (ignorePauseExtendDate) {
                results = generalReportIgnorePauseExtendQNIService.getAllGeneralReportDetailDto(agencyIds,
                        fromDate, toDate,
                        reportTypeId, sectorId,
                        procedureId, type);
            } else {
                results = generalReportQNIService.getAllGeneralReportDetailDto(agencyIds,
                        fromDate, toDate,
                        reportTypeId, sectorId,
                        procedureId, type);
            }
            return results;
        } catch (Exception e) {
            logger.error("DIGO-Info: " + e.getMessage());
        }
        return null;
    }

    @GetMapping("/--detail/--export")
    public ResponseEntity<Object> exportDossierStatisticGeneralDetail(
            HttpServletRequest request,
            @RequestParam(value = "from-date") String fromDate,
            @RequestParam(value = "to-date") String toDate,
            @RequestParam(value = "agency-id") List<String> agencyId,
            @RequestParam(value = "sector-id", required = false) List<String> sectorId,
            @RequestParam(value = "procedure-id", required = false) List<String> procedureId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "report-type-id", required = false) Integer reportTypeId
    ) throws JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        ResponseEntity<Object> result = null;

        if (ignorePauseExtendDate) {
            result = generalReportIgnorePauseExtendQNIService.exportGeneralReportDetail(fromDate, toDate, agencyId, sectorId, procedureId, type, reportTypeId);
        } else {
            result = generalReportQNIService.exportGeneralReportDetail(fromDate, toDate, agencyId, sectorId, procedureId, type, reportTypeId);
        }
        return result;
    }

    @GetMapping("/--etl-dossier-id-list")
    public ResponseEntity<List<IdPojo>> getETLDossierIdList(HttpServletRequest request,
                                                            @RequestParam(value = "from-date", required = true) String fromDate,
                                                            @RequestParam(value = "to-date", required = true) String toDate) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<IdPojo> result = generalReportQNIService.getETLDossierIdList(fromDate, toDate);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--etl-dossier-by-id")
    public ResponseEntity<QNIETLDossier> getETLDossier(HttpServletRequest request,
                                                       @RequestParam(value = "dossier-id", required = true) String id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        QNIETLDossier result = generalReportQNIService.getETLDossier(id);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--etl-dossier-list")
    public Slice<QNIETLDossier> getETLDossierList(HttpServletRequest request,
                                                  @RequestParam(value = "from-date", required = true) String fromDate,
                                                  @RequestParam(value = "to-date", required = true) String toDate,
                                                  Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<QNIETLDossier> result = generalReportQNIService.getETLDossierList(fromDate, toDate, pageable);
        return result;
    }

    @PutMapping("/--etl-dossier/--excel")
    public ResponseEntity importDataUpdateCompletedDate(
            HttpServletRequest request,
            @ModelAttribute InputFileDto input
    ) {
        return ResponseEntity.ok(generalReportQNIService.importFromExcelUpdateCompletedDate(input.getFile()));
    }

    @PutMapping("/--etl-dossier/--update-sector-procedure-name-empty")
    public ResponseEntity upDateNameSectorOrProcedure(
            HttpServletRequest request,
            @RequestParam(value = "collection-name", required = true) String collectionName,
            @RequestParam(value = "update-sector-by-date", required = false) Boolean updateSectorByDate,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate
    ) {
        return ResponseEntity.ok(generalReportQNIService.upDateNameSectorOrProcedureEmpty(collectionName, updateSectorByDate, fromDate, toDate));
    }

    @PutMapping("/--etl-dossier/--update-sector-procedure-name-long-text")
    public ResponseEntity upDateNameSectorOrProcedureLongText(
            HttpServletRequest request,
            @RequestParam(value = "collection-name", required = true) String collectionName,
            @ModelAttribute InputFileDto input
    ) {
        return ResponseEntity.ok(generalReportQNIService.updateProcedureOrSectorLongtext(input.getFile(), collectionName));
    }

    @DeleteMapping("/--etl-dossier/--dossier-test")
    public ResponseEntity deleteDossierTest(
            HttpServletRequest request,
            @RequestParam(value = "text", required = true) String text
    ) {
        return ResponseEntity.ok(generalReportQNIService.deleteDossierTest(text));
    }

    @DeleteMapping("/--etl-dossier/--dossier-vbdlis-cancel")
    public ResponseEntity deleteDossierVbdlisCancel(
            HttpServletRequest request
    ) {
        return ResponseEntity.ok(generalReportQNIService.deleteDossierVbdlisCancel());
    }

    @PutMapping("/--etl-dossier/--update-appointment-date")
    public ResponseEntity updateAppointmentDate(
            @RequestParam(value = "from-date", required = true) String fromDate,
            @RequestParam(value = "to-date", required = true) String toDate,
            HttpServletRequest request
    ) {
        return ResponseEntity.ok(generalReportQNIService.updateAppointmentDate(fromDate, toDate));
    }

    @GetMapping("/--etl-dossier-list-in-valid")
    public List<QNIETLDossier> getEQnietlDossierInvalids(HttpServletRequest request,
                                                  @RequestParam(value = "field-in-valid", required = true) String fieldInvalid) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<QNIETLDossier> result = generalReportQNIService.getETLDossierListInvalid(fieldInvalid);
        return result;
    }

    @DeleteMapping("/--etl-dossier/--dossier-vbdlis-not-accept")
    public ResponseEntity deleteDossierNotaccept(
            HttpServletRequest request
    ) {
        return ResponseEntity.ok(generalReportQNIService.deleteDossierNotaccept());
    }

    @PutMapping("/--etl-dossier/--update-agency-null")
    public ResponseEntity updateAgencyNull(
            HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return ResponseEntity.ok(generalReportQNIService.updateAgencyNull());
    }

    @PutMapping("/--etl-dossier/--update-withdraw-date")
    public ResponseEntity updateWithdrawDate(
            HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return ResponseEntity.ok(generalReportQNIService.updateWithdrawDate());
    }

    @PutMapping("/--etl-dossier/--update-completed-date")
    public UpdateDateDossierResponseQni updateCompletedDate(
            HttpServletRequest request,
            @RequestParam(value = "form-date") String fromDate,
            @RequestParam(value = "to-date") String toDate
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return generalReportQNIService.updateCompletedDate(fromDate, toDate);
    }

    @PutMapping("/--etl-dossier/--update-date")
    public ResponseEntity updateDate(
            HttpServletRequest request,
            @RequestParam(value = "field-name", required = true) String fieldName
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return ResponseEntity.ok(generalReportQNIService.updateMissDate(fieldName));
    }

    @GetMapping("/--update-awaiting-financial-bbligations")
    public ImportResponseDto updateQniETLDossiersWithFinancialObligations(
            HttpServletRequest request,
            @RequestParam(value = "fromDate", required = true) String fromDate,
            @RequestParam(value = "toDate", required = true) String toDate,
            @RequestParam(value = "option") Integer option
    ){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return qniETLDossierService.insertFieldAwaitingFinancialObligations(fromDate, toDate, option);
    }
}
