package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;

import org.bson.types.ObjectId;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import vn.vnpt.digo.reporter.document.EvaluationResultForm;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationResultDto implements Serializable{
  @Id
  @JsonSerialize(using = ToStringSerializer.class)
	private ObjectId id;

  private File file;
  private String title;
  @JsonProperty("isActive")
	private boolean isActive;
  public static EvaluationResultDto fromDocument(EvaluationResultForm input) {
		EvaluationResultDto result = new EvaluationResultDto();
    result.setId(input.getId());
		result.setFile(input.getFile());
    result.setActive(input.isActive());
    result.setTitle(input.getTitle());
		return result;
	}
  public EvaluationResultDto(File file, String title,  boolean isActive){
    this.setFile(file);
    this.setActive(isActive);
    this.setTitle(title);
  }
}
