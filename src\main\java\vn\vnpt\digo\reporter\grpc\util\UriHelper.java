package vn.vnpt.digo.reporter.grpc.util;

import org.bson.types.ObjectId;
import org.springframework.web.util.UriComponents;

import java.net.URLDecoder;
import java.util.List;

public class UriHelper {
    public static String getStringParam(UriComponents uriComponents, String param) {
        try {
            return URLDecoder.decode(uriComponents.getQueryParams().getFirst(param), "UTF-8");
        } catch (Exception ex) {
        }
        return null;
    }

    public static ObjectId getObjectIdParam(UriComponents uriComponents, String param) {
        String id = UriHelper.getStringParam(uriComponents, param);
        try {
            return new ObjectId(id);
        } catch (Exception ex) {
        }
        return null;
    }

    public static List<String> getListStringParam(UriComponents uriComponents, String param) {
        try {
            List<String> params = uriComponents.getQueryParams().get(param);
            return params;
        } catch (Exception ex) {
        }
        return null;
    }
}
