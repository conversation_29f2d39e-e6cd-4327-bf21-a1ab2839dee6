/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InputTelecomCostDto implements Serializable {

    @NotNull
    private String phoneNumber;

    @NotNull
    private Integer month;

    @NotNull
    private Integer year;

    private String paymentCode;

    @NotNull
    private String subscriptionCode;

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subscriptionTypeId;

    @NotNull
    private String subscriptionTypeName;

    @NotNull
    private Double amount;
}
