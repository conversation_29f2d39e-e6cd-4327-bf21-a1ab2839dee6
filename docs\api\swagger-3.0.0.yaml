swagger: '2.0'
info:
  description: Service reporter
  version: 1.0.0
  title: svc-reporter
  contact: {}
  license: {}
host: 'digo-api.vnptigate.vn:80'
basePath: /
tags:
  - name: agency-controller
    description: Agency Controller
  - name: dossier-by-day-controller
    description: <PERSON>ssier By Day Controller
  - name: electric-bill-controller
    description: Electric Bill Controller
  - name: electric-customer-controller
    description: Electric Customer Controller
  - name: file-controller
    description: File Controller
  - name: freemarker-controller
    description: Freemarker Controller
  - name: procedure-controller
    description: Procedure Controller
  - name: subscription-type-controller
    description: Subscription Type Controller
  - name: telecom-cost-controller
    description: Telecom Cost Controller
  - name: template-controller
    description: Template Controller
  - name: water-bill-controller
    description: Water Bill Controller
  - name: water-customer-controller
    description: Water Customer Controller
  - name: petition-statistics-controller
    description: Petition Statistics Controller
  - name: feature-statistics-controller
    description: Feature Statistics Controller
paths:
  /agency:
    post:
      tags:
        - agency-controller
      summary: addNewAgency
      operationId: addNewAgencyUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: body
          description: body
          required: true
          schema:
            $ref: '#/definitions/PutAgencyDto'
            originalRef: PutAgencyDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--active-procedure:
    get:
      tags:
        - agency-controller
      summary: getListByActiveProcedure
      operationId: getListByActiveProcedureUsingGET
      produces:
        - '*/*'
      parameters:
        - name: place-id
          in: query
          description: place-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/AgencyNameDto'
              originalRef: AgencyNameDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--count-agency:
    get:
      tags:
        - agency-controller
      summary: getCountAgency
      operationId: getCountAgencyUsingGET
      produces:
        - '*/*'
      parameters:
        - name: deployment-id
          in: query
          description: deployment-id
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«GetTypeAgencyDto»'
            originalRef: Page«GetTypeAgencyDto»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--dossier:
    get:
      tags:
        - agency-controller
      summary: getDossier
      operationId: getDossierUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: agency-type
          in: query
          description: agency-type
          required: false
          type: string
        - name: month
          in: query
          description: month
          required: false
          type: integer
          maximum: 12
          exclusiveMaximum: false
          minimum: 1
          exclusiveMinimum: false
          format: int32
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: year
          in: query
          description: year
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Slice«GetDossierByAgencyDto»'
            originalRef: Slice«GetDossierByAgencyDto»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--for-online:
    get:
      tags:
        - agency-controller
      summary: getListByOnlineProcedure
      operationId: getListByOnlineProcedureUsingGET
      produces:
        - '*/*'
      parameters:
        - name: place-id
          in: query
          description: place-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/AgencyNameDto'
              originalRef: AgencyNameDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--procedure-quantity-by-sector:
    get:
      tags:
        - agency-controller
      summary: getProcedureQuantityBySector
      operationId: getProcedureQuantityBySectorUsingGET
      produces:
        - '*/*'
      parameters:
        - name: place-id
          in: query
          description: place-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/ProcedureQuantityBySectorDto'
              originalRef: ProcedureQuantityBySectorDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--procedure-quantity-by-tag:
    get:
      tags:
        - agency-controller
      summary: getProcedureQuantityByTag
      operationId: getProcedureQuantityByTagUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: ancestor-id
          in: query
          description: ancestor-id
          required: false
          type: string
        - name: place-id
          in: query
          description: place-id
          required: false
          type: string
        - name: tag-id
          in: query
          description: tag-id
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/ProcedureQuantityBySectorDto'
              originalRef: ProcedureQuantityBySectorDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--sector:
    get:
      tags:
        - agency-controller
      summary: getListFieldBySector
      operationId: getListFieldBySectorUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/SectorByAgencyReturnDto'
              originalRef: SectorByAgencyReturnDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/--update-procedure-sector:
    put:
      tags:
        - agency-controller
      summary: updateProcedureSector
      operationId: updateProcedureSectorUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: procedureAgencySectorDto
          description: procedureAgencySectorDto
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/ProcedureAgencySectorDto'
              originalRef: ProcedureAgencySectorDto
        - name: site-url
          in: query
          description: site-url
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /agency/test-kafka:
    get:
      tags:
        - agency-controller
      summary: testKafka
      operationId: testKafkaUsingGET
      produces:
        - '*/*'
      responses:
        '200':
          description: OK
          schema:
            type: boolean
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/agency/{id}/':
    put:
      tags:
        - agency-controller
      summary: updateAgency
      operationId: updateAgencyUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: body
          description: body
          required: true
          schema:
            $ref: '#/definitions/PutAgencyDto'
            originalRef: PutAgencyDto
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/agency/{id}/--dossier':
    put:
      tags:
        - agency-controller
      summary: updateAgencyDossier
      operationId: updateAgencyDossierUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: body
          description: body
          required: true
          schema:
            $ref: '#/definitions/PutAgencyDossierDto'
            originalRef: PutAgencyDossierDto
        - name: id
          in: path
          description: id
          required: true
          type: string
        - name: month
          in: query
          description: month
          required: true
          type: integer
          maximum: 12
          exclusiveMaximum: false
          minimum: 1
          exclusiveMinimum: false
          format: int32
        - name: year
          in: query
          description: year
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/agency/{id}/--procedure':
    put:
      tags:
        - agency-controller
      summary: updateAgencyProcedure
      operationId: updateAgencyProcedureUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: body
          description: body
          required: true
          schema:
            $ref: '#/definitions/PutAgencyDossierDto'
            originalRef: PutAgencyDossierDto
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by--unresolved-overdue:
    get:
      tags:
        - dossier-by-day-controller
      summary: getSectorOverdue
      operationId: getSectorOverdueUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SectorOverdue»'
            originalRef: Page«SectorOverdue»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-6a:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportDossierByDay
      operationId: getReportDossierByDayUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SimpleDossier»'
            originalRef: Page«SimpleDossier»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-province-6a:
    get:
      tags:
        - dossier-by-day-controller
      summary: Mẫu 6a báo cáo thống kê toàn tỉnh  (getReportDossierByProvince6a)
      operationId: getReportDossierByProvince6aUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SimpleDossier»'
            originalRef: Page«SimpleDossier»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-6b:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportDossierBy6b
      operationId: getReportDossierBy6bUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SimpleDossier»'
            originalRef: Page«SimpleDossier»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-province-6b:
    get:
      tags:
        - dossier-by-day-controller
      summary: Mẫu 6b báo cáo thống kê toàn tỉnh (getReportDossierByProvince6b)
      operationId: getReportDossierByProvince6bUsingGET
      description: Mẫu 6b báo cáo thống kê toàn tỉnh.
      parameters:
        - name: Accept-Language 
          in: header
          description: Ngôn ngữ trả về
          required: false
          type: string
          schema:
            type: string
            example: "vi"
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
        - name: agency-level-commune
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị phường/xã (agency-level-commune-id)
        - name: agency-level-district
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị quận/huyện (agency-level-district-id)
        - in: query
          name: tag-id
          description: Mã loại đơn vị
          type: array
          schema:
              type: array
              items:
                type: string
                format: ObjectId
              example:
                - 5f6b177a4e1bd312a6f3ae4a
                - 5f7dade4b80e603d5300dcc4
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              type: object
              properties:
                agencyLevelName: 
                  type: string
                  format: string
                  example: "Tình hình, kết quả giải quyết TTHC thuộc phạm vi thẩm quyền của UBND cấp huyện"
                noRoman:
                  type: string
                  format: string
                  example: "I"
                  description: "Thứ tự la mã"
                itemDetails:
                  type: array
                  items:
                    type: object
                    properties:
                      sector:
                        type: string
                        format: string
                        example: "Đất đai"
                      sectorId:
                        type: string
                        format: string
                        example: "5f9a9b3503d7c7706ec3dd3a"
                      sum1:
                        type: number
                        format: long
                        example: 6
                        description: "Tổng số hồ sơ tiếp nhận"
                      acceptedOnl:
                        type: number
                        format: long
                        example: 6
                        description: "Số hồ sơ tiếp nhận trực tuyến"
                      receivedDirect:
                        type: number
                        format: long
                        example: 0
                        description: "Số hồ sơ tiếp nhận trực tiếp"
                      acceptedDirect:
                        type: number
                        format: long
                        example: 0
                        description: "Số hồ sơ tiếp nhận dịch vụ bưu chính"
                      pastDossier:
                        type: number
                        format: long
                        example: 0
                        description: "Số hồ sơ tiếp nhận từ kỳ trước"
                      sum2:
                        type: number
                        format: long
                        example: 12
                        description: "Tổng số lượng hồ sơ đã giải quyết"
                      resolvedEarly:
                        type: number
                        format: long
                        example: 12
                        description: "Số lượng hồ sơ đã giải quyết trước hạn"
                      returnedOnTime:
                        type: number
                        format: long
                        example: 0
                        description: "Số lượng hồ sơ đã giải quyết đúng hạn"
                      resolvedOverdue:
                        type: number
                        format: long
                        example: 0
                        description: "Số lượng hồ sơ đã giải quyết quá hạn"
                      sum3:
                        type: number
                        format: long
                        example: 6
                        description: "Tổng số hồ sơ đang giải quyết"
                      unresolvedNoTime:
                        type: number
                        format: long
                        example: 3
                        description: "Số hồ sơ đang giải quyết trong hạn"
                      unresolvedOvertime:
                        type: number
                        format: long
                        example: 3
                        description: "Số hồ sơ đang giải quyết quá hạn"
                      no:
                        type: number
                        format: long
                        example: 1
                        description: "Số thứ tự"
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-province-6c:
    get:
      tags:
        - dossier-by-day-controller
      summary: Mẫu 6c báo cáo thống kê toàn tỉnh (getReportDossierByProvince6c)
      operationId: getReportDossierByProvince6cUsingGET
      description: Mẫu 6c báo cáo thống kê toàn tỉnh.
      parameters:
        - name: Accept-Language 
          in: header
          description: Ngôn ngữ trả về
          required: false
          type: string
          schema:
            type: string
            example: "vi"
        - name: agency-id
          in: query
          description: agency-id
          required: false
          schema:
            type: string
            example: "5f7dbb26e4293c0424be70ff"
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
          schema:
            type: string
            example: "2021-01-01T00:00:00"
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
          schema:
            type: string
            example: "2022-02-07T23:59:59"
        - name: agency-level-commune
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị phường/xã (agency-level-commune-id)
          schema:
            type: string
            example: "5febfe2295002b5c79f0fc9f"
        - name: agency-level-district
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị quận/huyện (agency-level-district-id)
          schema:
            type: string
            example: "5fe16943c229f60186af8ccb"
        - name: agency-level-province
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị tỉnh (agency-level-province-id)
          schema:
            type: string
            example: "5f39f4335224cf235e134c5b"
        - in: query
          name: tag-id
          description: Mã loại đơn vị
          type: array
          schema:
              type: array
              items:
                type: string
                format: ObjectId
              example:
                - 5f6b177a4e1bd312a6f3ae4a
                - 5f7dade4b80e603d5300dcc4
      responses:
        '200':
          description: OK
          schema:
            type: object
            properties:
              province:    
                  type: array
                  description: "Tình hình, kết quả giải quyết TTHC thuộc phạm vi thẩm quyền của UBND cấp tỉnh"
                  items:
                    type: object
                    properties:
                      noRoman: 
                        format: string
                        type: string
                        example: "I"
                        description: "Số thứ tự"
                      agencyName: 
                        type: string
                        format: string
                        example: "TTHC do Ủy ban nhân dân Huyện Cái Bè tiếp nhận, giải quyết TTHC"
                      itemDetails:
                        type: array
                        items:
                          type: object
                          properties:
                            sector:
                              type: string
                              format: string
                              example: "Đất đai"
                            sectorId:
                              type: string
                              format: string
                              example: "5f9a9b3503d7c7706ec3dd3a"
                            sum1:
                              type: number
                              format: long
                              example: 6
                              description: "Tổng số hồ sơ tiếp nhận"
                            acceptedOnl:
                              type: number
                              format: long
                              example: 6
                              description: "Số hồ sơ tiếp nhận trực tuyến"
                            receivedDirect:
                              type: number
                              format: long
                              example: 0
                              description: "Số hồ sơ tiếp nhận trực tiếp"
                            acceptedDirect:
                              type: number
                              format: long
                              example: 0
                              description: "Số hồ sơ tiếp nhận dịch vụ bưu chính"
                            pastDossier:
                              type: number
                              format: long
                              example: 0
                              description: "Số hồ sơ tiếp nhận từ kỳ trước"
                            sum2:
                              type: number
                              format: long
                              example: 12
                              description: "Tổng số lượng hồ sơ đã giải quyết"
                            resolvedEarly:
                              type: number
                              format: long
                              example: 12
                              description: "Số lượng hồ sơ đã giải quyết trước hạn"
                            returnedOnTime:
                              type: number
                              format: long
                              example: 0
                              description: "Số lượng hồ sơ đã giải quyết đúng hạn"
                            resolvedOverdue:
                              type: number
                              format: long
                              example: 0
                              description: "Số lượng hồ sơ đã giải quyết quá hạn"
                            sum3:
                              type: number
                              format: long
                              example: 6
                              description: "Tổng số hồ sơ đang giải quyết"
                            unresolvedNoTime:
                              type: number
                              format: long
                              example: 3
                              description: "Số hồ sơ đang giải quyết trong hạn"
                            unresolvedOvertime:
                              type: number
                              format: long
                              example: 3
                              description: "Số hồ sơ đang giải quyết quá hạn"
                            no:
                              type: number
                              format: long
                              example: 1
                              description: "Số thứ tự"
              district:
                type: array
                description: "Tình hình, kết quả giải quyết TTHC thuộc phạm vi thẩm quyền của UBND cấp huyện"
                items:
                  type: object
                  properties:
                    sector:
                      type: string
                      format: string
                      example: "Đất đai"
                    sectorId:
                      type: string
                      format: string
                      example: "5f9a9b3503d7c7706ec3dd3a"
                    sum1:
                      type: number
                      format: long
                      example: 6
                      description: "Tổng số hồ sơ tiếp nhận"
                    acceptedOnl:
                      type: number
                      format: long
                      example: 6
                      description: "Số hồ sơ tiếp nhận trực tuyến"
                    receivedDirect:
                      type: number
                      format: long
                      example: 0
                      description: "Số hồ sơ tiếp nhận trực tiếp"
                    acceptedDirect:
                      type: number
                      format: long
                      example: 0
                      description: "Số hồ sơ tiếp nhận dịch vụ bưu chính"
                    pastDossier:
                      type: number
                      format: long
                      example: 0
                      description: "Số hồ sơ tiếp nhận từ kỳ trước"
                    sum2:
                      type: number
                      format: long
                      example: 12
                      description: "Tổng số lượng hồ sơ đã giải quyết"
                    resolvedEarly:
                      type: number
                      format: long
                      example: 12
                      description: "Số lượng hồ sơ đã giải quyết trước hạn"
                    returnedOnTime:
                      type: number
                      format: long
                      example: 0
                      description: "Số lượng hồ sơ đã giải quyết đúng hạn"
                    resolvedOverdue:
                      type: number
                      format: long
                      example: 0
                      description: "Số lượng hồ sơ đã giải quyết quá hạn"
                    sum3:
                      type: number
                      format: long
                      example: 6
                      description: "Tổng số hồ sơ đang giải quyết"
                    unresolvedNoTime:
                      type: number
                      format: long
                      example: 3
                      description: "Số hồ sơ đang giải quyết trong hạn"
                    unresolvedOvertime:
                      type: number
                      format: long
                      example: 3
                      description: "Số hồ sơ đang giải quyết quá hạn"
                    no:
                      type: number
                      format: long
                      example: 1
                      description: "Số thứ tự"
              commune:
                type: array
                description: "Tình hình, kết quả giải quyết TTHC thuộc phạm vi thẩm quyền của UBND cấp xã"
                items:
                  type: object
                  properties:
                    sector:
                      type: string
                      format: string
                      example: "Đất đai"
                    sectorId:
                      type: string
                      format: string
                      example: "5f9a9b3503d7c7706ec3dd3a"
                    sum1:
                      type: number
                      format: long
                      example: 6
                      description: "Tổng số hồ sơ tiếp nhận"
                    acceptedOnl:
                      type: number
                      format: long
                      example: 6
                      description: "Số hồ sơ tiếp nhận trực tuyến"
                    receivedDirect:
                      type: number
                      format: long
                      example: 0
                      description: "Số hồ sơ tiếp nhận trực tiếp"
                    acceptedDirect:
                      type: number
                      format: long
                      example: 0
                      description: "Số hồ sơ tiếp nhận dịch vụ bưu chính"
                    pastDossier:
                      type: number
                      format: long
                      example: 0
                      description: "Số hồ sơ tiếp nhận từ kỳ trước"
                    sum2:
                      type: number
                      format: long
                      example: 12
                      description: "Tổng số lượng hồ sơ đã giải quyết"
                    resolvedEarly:
                      type: number
                      format: long
                      example: 12
                      description: "Số lượng hồ sơ đã giải quyết trước hạn"
                    returnedOnTime:
                      type: number
                      format: long
                      example: 0
                      description: "Số lượng hồ sơ đã giải quyết đúng hạn"
                    resolvedOverdue:
                      type: number
                      format: long
                      example: 0
                      description: "Số lượng hồ sơ đã giải quyết quá hạn"
                    sum3:
                      type: number
                      format: long
                      example: 6
                      description: "Tổng số hồ sơ đang giải quyết"
                    unresolvedNoTime:
                      type: number
                      format: long
                      example: 3
                      description: "Số hồ sơ đang giải quyết trong hạn"
                    unresolvedOvertime:
                      type: number
                      format: long
                      example: 3
                      description: "Số hồ sơ đang giải quyết quá hạn"
                    no:
                      type: number
                      format: long
                      example: 1
                      description: "Số thứ tự"    
              sum:
                type: object
                description: "Tổng cộng"
                properties:
                  sector:
                    type: string
                    format: string
                    example: "Tổng số"
                  sectorId:
                    type: string
                    format: string
                    example: ""
                  sum1:
                    type: number
                    format: long
                    example: 18
                    description: "Tổng số hồ sơ tiếp nhận"
                  acceptedOnl:
                    type: number
                    format: long
                    example: 18
                    description: "Số hồ sơ tiếp nhận trực tuyến"
                  receivedDirect:
                    type: number
                    format: long
                    example: 0
                    description: "Số hồ sơ tiếp nhận trực tiếp"
                  acceptedDirect:
                    type: number
                    format: long
                    example: 0
                    description: "Số hồ sơ tiếp nhận dịch vụ bưu chính"
                  pastDossier:
                    type: number
                    format: long
                    example: 0
                    description: "Số hồ sơ tiếp nhận từ kỳ trước"
                  sum2:
                    type: number
                    format: long
                    example: 36
                    description: "Tổng số lượng hồ sơ đã giải quyết"
                  resolvedEarly:
                    type: number
                    format: long
                    example: 36
                    description: "Số lượng hồ sơ đã giải quyết trước hạn"
                  returnedOnTime:
                    type: number
                    format: long
                    example: 0
                    description: "Số lượng hồ sơ đã giải quyết đúng hạn"
                  resolvedOverdue:
                    type: number
                    format: long
                    example: 0
                    description: "Số lượng hồ sơ đã giải quyết quá hạn"
                  sum3:
                    type: number
                    format: long
                    example: 18
                    description: "Tổng số hồ sơ đang giải quyết"
                  unresolvedNoTime:
                    type: number
                    format: long
                    example: 9
                    description: "Số hồ sơ đang giải quyết trong hạn"
                  unresolvedOvertime:
                    type: number
                    format: long
                    example: 9
                    description: "Số hồ sơ đang giải quyết quá hạn"
                  no:
                    type: number
                    format: long
                    example: 0
                    description: "Số thứ tự"
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--export:
    get:
      tags:
        - dossier-by-day-controller
      summary: Xuất báo cáo thống kê toàn tỉnh
      operationId: exportExcel
      description: Xuất báo cáo thống kê toàn tỉnh.
      parameters:
        - name: Accept-Language 
          in: header
          description: Ngôn ngữ trả về
          required: false
          type: string
          schema:
            type: string
            example: "vi"
        - name: agency-id
          in: query
          description: Mã đơn vị
          required: false
          schema:
            type: string
            example: "5f7dbb26e4293c0424be70ff"
        - name: agency-name
          in: query
          description: tên đơn vị
          required: false
          schema:
            type: string
            example: "Sở Công Thương - Tỉnh Lào Cai"
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
          schema:
            type: string
            example: "2021-01-01T00:00:00"
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
          schema:
            type: string
            example: "2022-02-07T23:59:59"
        - name: agency-level-commune
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị phường/xã (agency-level-commune-id)
          schema:
            type: string
            example: "5febfe2295002b5c79f0fc9f"
        - name: agency-level-district
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị quận/huyện (agency-level-district-id)
          schema:
            type: string
            example: "5fe16943c229f60186af8ccb"
        - name: agency-level-province
          in: query
          required: false
          type: string
          description: Mã cấp đơn vị tỉnh (agency-level-province-id)
          schema:
            type: string
            example: "5f39f4335224cf235e134c5b"
        - name: report-type
          in: query
          required: false
          type: integer
          description: "Loại báo cáo muốn xuất: [5: Mẫu 6a báo cáo thống kê toàn tỉn, 6: Mẫu 6b báo cáo thống kê toàn tỉnh, 7: Mẫu 6c báo cáo thống kê toàn tỉnh]"
          schema:
            type: integer
            example: 5
        - in: query
          name: tag-id
          description: Mã loại đơn vị
          type: array
          schema:
              type: array
              items:
                type: string
                format: ObjectId
              example:
                - 5f6b177a4e1bd312a6f3ae4a
                - 5f7dade4b80e603d5300dcc4
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
      responses:
        '200':
          description: OK
          content:
            application/vnd.ms-excel:
              schema:
                type: string
                format: binary
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-6d:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportDossierBy6d
      operationId: getReportDossierBy6dUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: from-date
          in: query
          description: from-date
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
        - name: to-date
          in: query
          description: to-date
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SimpleDossier»'
            originalRef: Page«SimpleDossier»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-day:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportDossierByDay
      operationId: getReportDossierByDayUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: fromDate
          in: query
          description: fromDate
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: procedure-level
          in: query
          description: procedure-level
          required: false
          type: string
        - name: sector-id
          in: query
          description: sector-id
          required: false
          type: string
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
        - name: toDate
          in: query
          description: toDate
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Slice«GetDossierByDay»'
            originalRef: Slice«GetDossierByDay»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/statistic/--by-procedure:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportDossierByDayProcedure
      operationId: getReportDossierByDayProcedureUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: accepted-from
          in: query
          description: Từ ngày
          required: false
          type: string
        - name: accepted-to
          in: query
          description: Đến ngày
          required: false
          type: string
        - name: procedure-level
          in: query
          description: Mức độ
          required: false
          type: string
        - name: sector-id
          in: query
          description: Lĩnh vực
          required: false
          type: string
        - name: procedure-check
          in: query
          description: Thống kê tất cả các thủ tục (có hoặc không có phát sinh hồ sơ)
          required: false
          type: boolean
        - name: status-procedure
          in: query
          description: Trạng thái thủ tục
          required: false
          type: string
        - name: keyword
          in: query
          description: Từ khóa
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Slice«GetDossierByDay»'
            originalRef: Slice«GetDossierByDay»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/statistic/--by-procedure-all:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportDossierByDayProcedureAll
      operationId: getReportDossierByDayProcedureAllUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: accepted-from
          in: query
          description: Từ ngày
          required: false
          type: string
        - name: accepted-to
          in: query
          description: Đến ngày
          required: false
          type: string
        - name: procedure-level
          in: query
          description: Mức độ
          required: false
          type: string
        - name: sector-id
          in: query
          description: Lĩnh vực
          required: false
          type: string
        - name: procedure-check
          in: query
          description: Thống kê tất cả các thủ tục (có hoặc không có phát sinh hồ sơ)
          required: false
          type: boolean
        - name: status-procedure
          in: query
          description: Trạng thái thủ tục
          required: false
          type: string
        - name: keyword
          in: query
          description: Từ khóa
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/GetDossierByDay'
              originalRef: GetDossierByDay
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-deployment:
    get:
      tags:
        - dossier-by-day-controller
      summary: getReportForDeployment
      operationId: getReportForDeploymentUsingGET
      produces:
        - '*/*'
      parameters:
        - name: deployment-id
          in: query
          description: deployment-id
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SimpleDossier»'
            originalRef: Page«SimpleDossier»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /dossier/--by-procedure:
    get:
      tags:
        - dossier-by-day-controller
      summary: getProcedure
      operationId: getProcedureUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: page
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Page«SectorOverdue»'
            originalRef: Page«SectorOverdue»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /electric-bill:
    post:
      tags:
        - electric-bill-controller
      summary: addNewBillForCustomer
      operationId: addNewBillForCustomerUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: electricBillInputDto
          description: electricBillInputDto
          required: true
          schema:
            $ref: '#/definitions/ElectricBillInputDto'
            originalRef: ElectricBillInputDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /electric-bill/--months:
    get:
      tags:
        - electric-bill-controller
      summary: getElectricBillByMonth
      operationId: getElectricBillByMonthUsingGET
      produces:
        - '*/*'
      parameters:
        - name: customer-id
          in: query
          description: customer-id
          required: true
          type: string
        - name: month
          in: query
          description: month
          required: false
          type: integer
          format: int32
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: year
          in: query
          description: year
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Slice«GetListElectricBillDto»'
            originalRef: Slice«GetListElectricBillDto»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/electric-bill/{id}':
    get:
      tags:
        - electric-bill-controller
      summary: getElectricBill
      operationId: getElectricBillUsingGET
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/GetElectricBillDto'
            originalRef: GetElectricBillDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /electric-customer/--insert-or-update:
    put:
      tags:
        - electric-customer-controller
      summary: insertOrUpdateElectricCustomer
      operationId: insertOrUpdateElectricCustomerUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: electricCustomerInputDto
          description: electricCustomerInputDto
          required: true
          schema:
            $ref: '#/definitions/ElectricCustomerInputDto'
            originalRef: ElectricCustomerInputDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /electric-customer/customer-code/--by-user-id:
    get:
      tags:
        - electric-customer-controller
      summary: getCustomerCodeByUserId
      operationId: getCustomerCodeByUserIdUsingGET
      produces:
        - '*/*'
      parameters:
        - name: user-id
          in: query
          description: user-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/GetCustomerCodeByUserIdDto'
              originalRef: GetCustomerCodeByUserIdDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /file:
    post:
      tags:
        - file-controller
      summary: uploadTemplateFile
      operationId: uploadTemplateFileUsingPOST
      consumes:
        - multipart/form-data
      produces:
        - '*/*'
      parameters:
        - in: body
          name: file
          description: file
          required: true
          schema:
            type: string
            format: binary
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/TemplateFile'
            originalRef: TemplateFile
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    delete:
      tags:
        - file-controller
      summary: deleteTemplateFileByPath
      operationId: deleteTemplateFileByPathUsingDELETE
      produces:
        - '*/*'
      parameters:
        - name: path
          in: query
          description: path
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: string
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /freemarker/--report:
    post:
      tags:
        - freemarker-controller
      summary: reportFreemarker
      operationId: reportFreemarkerUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: freemarkerReportDto
          description: freemarkerReportDto
          required: true
          schema:
            $ref: '#/definitions/FreemarkerReportDto'
            originalRef: FreemarkerReportDto
        - name: path
          in: query
          description: path
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: string
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /freemarker/--send-email:
    post:
      tags:
        - freemarker-controller
      summary: sendEmailFreemarker
      operationId: sendEmailFreemarkerUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: freemarkerMailDto
          description: freemarkerMailDto
          required: true
          schema:
            $ref: '#/definitions/FreemarkerMailDto'
            originalRef: FreemarkerMailDto
        - name: path
          in: query
          description: path
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: string
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /procedure/--frequent:
    get:
      tags:
        - procedure-controller
      summary: getFrequentList
      operationId: getFrequentListUsingGET
      produces:
        - '*/*'
      parameters:
        - name: agency-id
          in: query
          description: agency-id
          required: false
          type: string
        - name: citizen-id
          in: query
          description: citizen-id
          required: true
          type: string
        - name: enterprise-id
          in: query
          description: enterprise-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/FrequentProcedureDto'
            originalRef: FrequentProcedureDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - procedure-controller
      summary: addProcedureFrequent
      operationId: addProcedureFrequentUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: body
          description: body
          required: true
          schema:
            $ref: '#/definitions/PutProcedureDto'
            originalRef: PutProcedureDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/procedure/{id}/--frequent':
    put:
      tags:
        - procedure-controller
      summary: updateProcedureFrequent
      operationId: updateProcedureFrequentUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
        - in: body
          name: putBody
          description: putBody
          required: true
          schema:
            $ref: '#/definitions/PutProcedureDto'
            originalRef: PutProcedureDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    delete:
      tags:
        - procedure-controller
      summary: deleteProcedureFrequent
      operationId: deleteProcedureFrequentUsingDELETE
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  '/procedure/{id}/--frequent-quantity':
    put:
      tags:
        - procedure-controller
      summary: updateProcedureFrequentQuantity
      operationId: updateProcedureFrequentQuantityUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
        - name: quantity
          in: query
          description: quantity
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /subscription-type:
    get:
      tags:
        - subscription-type-controller
      summary: getListSubscriptionType
      operationId: getListSubscriptionTypeUsingGET
      produces:
        - '*/*'
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/SubscriptionTypeDto'
              originalRef: SubscriptionTypeDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /telecom-cost:
    post:
      tags:
        - telecom-cost-controller
      summary: postTelecomCost
      operationId: postTelecomCostUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: postTelecomCostData
          description: postTelecomCostData
          required: true
          schema:
            $ref: '#/definitions/InputTelecomCostDto'
            originalRef: InputTelecomCostDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /telecom-cost/--month:
    get:
      tags:
        - telecom-cost-controller
      summary: getTelecomCostByMonth
      operationId: getTelecomCostByMonthUsingGET
      produces:
        - '*/*'
      parameters:
        - name: month
          in: query
          description: month
          required: true
          type: integer
          format: int32
        - name: subscription-type-id
          in: query
          description: subscription-type-id
          required: false
          type: string
        - name: year
          in: query
          description: year
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/TelecomCostAndAmountByMonthDto'
            originalRef: TelecomCostAndAmountByMonthDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /telecom-cost/--year:
    get:
      tags:
        - telecom-cost-controller
      summary: getTelecomCostByYear
      operationId: getTelecomCostByYearUsingGET
      produces:
        - '*/*'
      parameters:
        - name: subscription-type-id
          in: query
          description: subscription-type-id
          required: false
          type: string
        - name: year
          in: query
          description: year
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/TelecomCostAndAmountByYearDto'
            originalRef: TelecomCostAndAmountByYearDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /template:
    get:
      tags:
        - template-controller
      summary: getListTemplate
      operationId: getListTemplateUsingGET
      produces:
        - '*/*'
      parameters:
        - name: keyword
          in: query
          description: keyword
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: spec
          in: query
          description: spec
          required: false
          type: string
          default: slice
        - name: subsystem-id
          in: query
          description: subsystem-id
          required: false
          type: string
        - name: type-id
          in: query
          description: type-id
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Slice«GetTemplateDto»'
            originalRef: Slice«GetTemplateDto»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - template-controller
      summary: createTemplate
      operationId: createTemplateUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: templateDto
          description: templateDto
          required: true
          schema:
            $ref: '#/definitions/PostTemplateDto'
            originalRef: PostTemplateDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/IdResponse'
            originalRef: IdResponse
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/template/{id}':
    get:
      tags:
        - template-controller
      summary: getDetailTemplate
      operationId: getDetailTemplateUsingGET
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/GetTemplateByIdDto'
            originalRef: GetTemplateByIdDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    put:
      tags:
        - template-controller
      summary: updateTemplate
      operationId: updateTemplateUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
        - in: body
          name: templatePayload
          description: templatePayload
          required: true
          schema:
            $ref: '#/definitions/PutTemplateDto'
            originalRef: PutTemplateDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    delete:
      tags:
        - template-controller
      summary: deleteTemplate
      operationId: deleteTemplateUsingDELETE
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/AffectedRowsDto'
            originalRef: AffectedRowsDto
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  '/template/{id}/--file':
    get:
      tags:
        - template-controller
      summary: downloadTemplateFileById
      operationId: downloadTemplateFileByIdUsingGET
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Resource'
            originalRef: Resource
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /water-bill:
    post:
      tags:
        - water-bill-controller
      summary: addNewBillForCustomer
      operationId: addNewBillForCustomerUsingPOST_1
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: waterBillInputDto
          description: waterBillInputDto
          required: true
          schema:
            $ref: '#/definitions/WaterBillInputDto'
            originalRef: WaterBillInputDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /water-bill/--months:
    get:
      tags:
        - water-bill-controller
      summary: getWaterBillByMonth
      operationId: getWaterBillByMonthUsingGET
      produces:
        - '*/*'
      parameters:
        - name: customer-id
          in: query
          description: customer-id
          required: true
          type: string
        - name: month
          in: query
          description: month
          required: false
          type: integer
          format: int32
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: size
          in: query
          required: false
          type: integer
          format: int32
        - name: sort
          in: query
          required: false
          type: string
        - name: year
          in: query
          description: year
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Slice«GetWaterBillDto»'
            originalRef: Slice«GetWaterBillDto»
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  '/water-bill/{id}':
    get:
      tags:
        - water-bill-controller
      summary: getWaterBillById
      operationId: getWaterBillByIdUsingGET
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/GetWaterBillByIdDto'
            originalRef: GetWaterBillByIdDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /water-customer/--insert-or-update:
    put:
      tags:
        - water-customer-controller
      summary: insertOrUpdateWaterCustomer
      operationId: insertOrUpdateWaterCustomerUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: waterCustomerInputDto
          description: waterCustomerInputDto
          required: true
          schema:
            $ref: '#/definitions/WaterCustomerInputDto'
            originalRef: WaterCustomerInputDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /water-customer/customer-code/--by-user-id:
    get:
      tags:
        - water-customer-controller
      summary: getCustomerCodeByUserId
      operationId: getCustomerCodeByUserIdUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: user-id
          in: query
          description: user-id
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/GetCustomerCodeByUserIdDto'
              originalRef: GetCustomerCodeByUserIdDto
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /petition-statistics/--today:
    get:
      tags:
        - petition-statistics-controller
      summary: getTodayPetitionStatistics
      operationId: getTodayPetitionStatistics
      produces:
        - 'application/json'
      responses:
        '200':
          description: OK
          schema:
            type: object
            properties:
              id:
                type: string
                format: ObjectId
                example: 6155e5a30144025f4853ee17
              statusStatistics:
                type: array
                items:
                  type: object
                  properties:
                    status:
                      type: object
                      properties:
                        statusNumber:
                          type: number
                        statusDescription:
                          type: string
                    statistics:
                      type: object
                      properties:
                        statusNumber:
                          type: number
                        statusDescription:
                          type: string
                example:
                - status:
                    statusNumber: 1
                    statusDescription: Chờ tiếp nhận
                  statistics:
                    today: 1
                    thisMonth: 57
                    systemTotal: 131337
                - status:
                    statusNumber: 2
                    statusDescription: Đang xử lý
                  statistics:
                    today: 2
                    thisMonth: 177
                    systemTotal: 905
                - status:
                    statusNumber: 3
                    statusDescription: Hoàn thành
                  statistics:
                    today: 0
                    thisMonth: 104
                    systemTotal: 255
                - status:
                    statusNumber: null
                    statusDescription: Tổng cộng
                  statistics:
                    today: 3
                    thisMonth: 338
                    systemTotal: 132497
              receptionMethodStatistics:
                type: array
                items:
                  type: object
                  properties:
                    receptionMethod:
                      type: object
                      properties:
                        id:
                          type: string
                          format: ObjectId
                        trans:
                          type: array
                          items:
                            type: object
                            properties:
                              languageId:
                                type: number
                              name:
                                type: string
                        transName:
                          type: string
                    statistics:
                      type: object
                      properties:
                        today:
                          type: number
                        thisMonth:
                          type: number
                        systemTotal:
                          type: number
                example:
                  - receptionMethod:
                      id: 0004891c4e1bd312a6f00004
                      trans:
                        - languageId: 228
                          name: Tổng đài
                        - languageId: 46
                          name: Call center
                      transName: Tổng đài
                    statistics:
                      today: 0
                      thisMonth: 0
                      systemTotal: 3
                  - receptionMethod:
                      id: 0004891c4e1bd312a6f00001
                      trans:
                        - languageId: 228
                          name: App công dân
                        - languageId: 46
                          name: Citizens' App
                      transName: App công dân
                    statistics:
                      today: 0
                      thisMonth: 19
                      systemTotal: 74339
              fieldStatistics:
                type: array
                items:
                  type: object
                  properties:
                    field:
                      type: object
                      properties:
                        id:
                          type: string
                          format: ObjectId
                        trans:
                          type: array
                          items:
                            type: object
                            properties:
                              languageId:
                                type: number
                              name:
                                type: string
                        transName:
                          type: string
                    statistics:
                      type: object
                      properties:
                        today:
                          type: number
                        thisMonth:
                          type: number
                        systemTotal:
                          type: number
                example:
                  - field:
                      id: 6119154a6e2ccc0de8c033f6
                      trans:
                        - languageId: 228
                          name: Tester_HCM_15082021
                        - languageId: 46
                          name: Tester_HCM_15082021
                      transName: Tester_HCM_15082021
                    statistics:
                      today: 0
                      thisMonth: 64
                      systemTotal: 143
                  - field:
                      id: 5fd9bfe8bcabe1343065cbf7
                      trans:
                        - languageId: 228
                          name: Test
                        - languageId: 46
                          name: Tester
                      transName: Test
                    statistics:
                      today: 0
                      thisMonth: 8
                      systemTotal: 39
              deploymentId:
                type: string
                format: ObjectId
                example: 5ee091507d567c9fe29f82fa
              creadDate:
                type: string
                format: Date
                example: 2019-10-02T13:33:42.165+0100
              updatedDate:
                type: string
                format: Date
                example: 2019-10-02T13:33:42.165+0100
        '401':
          description: Unauthorized
        '404':
          description: Not Found
  /feature-statistics:
    post:
      tags:
        - feature-statistics-controller
      summary: Thêm mới thông tin thống kê sử dụng chức năng
      operationId: addNewFeatureStatistics
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: featureStatisticsInputDto
          description: featureStatisticsInputDto
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/FeatureStatisticsInputDto'
              originalRef: FeatureStatisticsInputDto
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/PostResponseDto'
            originalRef: PostResponseDto
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    get:
      tags:
        - feature-statistics-controller
      summary: Thống kê số lần sửa dụng chức năng của ứng dụng
      operationId: statisticsFeature
      produces:
        - '*/*'
      parameters:
        - name: app-code
          in: query
          description: app-code
          required: true
          type: string
        - name: start-date
          in: query
          description: start-date
          required: false
          type: string
        - name: end-date
          in: query
          description: end-date
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              type: object
              properties:
                featureCode:
                  type: string
                counter:
                  type: number
            example:
              - featureCode: petition
                counter: 123
              - featureCode: environment
                counter: 124
              - featureCode: covid
                counter: 125
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

definitions:
  AffectedRowsDto:
    type: object
    properties:
      affectedRows:
        type: integer
        format: int32
    title: AffectedRowsDto
  AgencyDossier:
    type: object
    properties:
      month:
        $ref: '#/definitions/AgencyDossierMonth'
        originalRef: AgencyDossierMonth
      year:
        type: integer
        format: int32
    title: AgencyDossier
  AgencyDossierByDay:
    type: object
    properties:
      id:
        type: string
      name:
        type: array
        items:
          $ref: '#/definitions/NameDossierByDay'
          originalRef: NameDossierByDay
    title: AgencyDossierByDay
  AgencyDossierMonth:
    type: object
    properties:
      m1:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m10:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m11:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m12:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m2:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m3:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m4:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m5:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m6:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m7:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m8:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
      m9:
        $ref: '#/definitions/AgencyDossierMonthData'
        originalRef: AgencyDossierMonthData
    title: AgencyDossierMonth
  AgencyDossierMonthData:
    type: object
    properties:
      canceled:
        type: integer
        format: int32
      early:
        type: integer
        format: int32
      onTime:
        type: integer
        format: int32
      overdue:
        type: integer
        format: int32
      received:
        type: integer
        format: int32
      resolved:
        type: integer
        format: int32
    title: AgencyDossierMonthData
  AgencyLevel:
    type: object
    properties:
      id:
        type: string
    title: AgencyLevel
  AgencyName:
    type: object
    properties:
      languageId:
        type: integer
        format: int32
      name:
        type: string
    title: AgencyName
  AgencyNameDto:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
    title: AgencyNameDto
  AgencyProcedure:
    type: object
    properties:
      activeQuantity:
        type: integer
        format: int32
      fourthLevelQuantity:
        type: integer
        format: int32
      secondLevelQuantity:
        type: integer
        format: int32
      thirdLevelQuantity:
        type: integer
        format: int32
    title: AgencyProcedure
  AgencySectorReq:
    type: object
    properties:
      id:
        type: string
      name:
        type: integer
        format: int32
      procedureQuantity:
        type: integer
        format: int32
      transSector:
        type: array
        items:
          $ref: '#/definitions/AgencyTransSector'
          originalRef: AgencyTransSector
    title: AgencySectorReq
  AgencySectorRes:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      procedureQuantity:
        type: integer
        format: int32
      transSector:
        type: array
        items:
          $ref: '#/definitions/AgencyTransSector'
          originalRef: AgencyTransSector
    title: AgencySectorRes
  AgencyTransSector:
    type: object
    properties:
      languageId:
        type: integer
        format: int32
      name:
        type: string
    title: AgencyTransSector
  ElectricBillInputDto:
    type: object
    required:
      - consumedAmount
      - customerCode
      - endDate
      - meterNumber
      - month
      - newIndex
      - oldIndex
      - paid
      - paymentAmount
      - startDate
      - year
    properties:
      billCode:
        type: string
      consumedAmount:
        type: integer
        format: int32
      customerCode:
        type: string
      endDate:
        type: string
        format: date-time
      meterNumber:
        type: string
      month:
        type: integer
        format: int32
      newIndex:
        type: integer
        format: int32
      oldIndex:
        type: integer
        format: int32
      paid:
        type: boolean
      paymentAmount:
        type: number
        format: float
      startDate:
        type: string
        format: date-time
      userId:
        type: string
      year:
        type: integer
        format: int32
    title: ElectricBillInputDto
  ElectricCustomerInputDto:
    type: object
    required:
      - customerCode
      - userId
    properties:
      customerCode:
        type: string
      userId:
        type: string
    title: ElectricCustomerInputDto
  FreemarkerMailDto:
    type: object
    properties:
      fileDinhKem:
        type: string
      model:
        type: object
        additionalProperties:
          type: string
      noiDungXuLy:
        type: string
      phieuTiepNhan:
        type: string
      soHoSo:
        type: string
      thamSo:
        type: object
      trangThai:
        type: string
    title: FreemarkerMailDto
  FreemarkerReportDto:
    type: object
    properties:
      model:
        type: object
        additionalProperties:
          type: string
      parameters:
        type: object
    title: FreemarkerReportDto
  FrequentProcedureDto:
    type: object
    properties:
      agencyId:
        type: string
      citizen:
        type: array
        items:
          $ref: '#/definitions/ProcedureDto'
          originalRef: ProcedureDto
      enterprise:
        type: array
        items:
          $ref: '#/definitions/ProcedureDto'
          originalRef: ProcedureDto
    title: FrequentProcedureDto
  GetCustomerCodeByUserIdDto:
    type: object
    required:
      - id
    properties:
      id:
        type: string
    title: GetCustomerCodeByUserIdDto
  GetDossierByAgencyDto:
    type: object
    properties:
      agencyId:
        type: string
      canceled:
        type: integer
        format: int32
      deploymentId:
        type: string
      early:
        type: integer
        format: int32
      earlyRate:
        type: string
      fourthLevel:
        type: integer
        format: int32
      id:
        type: string
      level:
        type: string
      month:
        type: string
      name:
        type: string
      onTime:
        type: integer
        format: int32
      onTimeRate:
        type: string
      overdue:
        type: integer
        format: int32
      overdueRate:
        type: string
      received:
        type: integer
        format: int32
      resolved:
        type: integer
        format: int32
      secondLevel:
        type: integer
        format: int32
      thirdLevel:
        type: integer
        format: int32
      year:
        type: integer
        format: int32
    title: GetDossierByAgencyDto
  GetDossierByDay:
    type: object
    properties:
      agency:
        $ref: '#/definitions/AgencyDossierByDay'
        originalRef: AgencyDossierByDay
      agencyLevel:
        $ref: '#/definitions/AgencyDossierByDay'
        originalRef: AgencyDossierByDay
      appliedOnline:
        type: integer
        format: int32
      cancelled:
        type: integer
        format: int32
      day:
        type: integer
        format: int32
      deleted:
        type: integer
        format: int32
      deploymentId:
        type: string
      month:
        type: integer
        format: int32
      procedure:
        $ref: '#/definitions/ProcedureDossierByDay'
        originalRef: ProcedureDossierByDay
      procedureLevel:
        $ref: '#/definitions/ProcedureLevelDossierByDay'
        originalRef: ProcedureLevelDossierByDay
      received:
        type: integer
        format: int32
      receivedDirect:
        type: integer
        format: int32
      receivedOnline:
        type: integer
        format: int32
      resolved:
        type: integer
        format: int32
      resolvedEarly:
        type: integer
        format: int32
      resolvedOverdue:
        type: integer
        format: int32
      returnOnTime:
        type: integer
        format: int32
      returnOverdue:
        type: integer
        format: int32
      sector:
        $ref: '#/definitions/SectorDossierByDay'
        originalRef: SectorDossierByDay
      suspended:
        type: integer
        format: int32
      unresolved:
        type: integer
        format: int32
      unresolvedOverdue:
        type: integer
        format: int32
      updatedDate:
        type: string
        format: date-time
      year:
        type: integer
        format: int32
    title: GetDossierByDay
  GetElectricBillDto:
    type: object
    properties:
      billCode:
        type: string
      consumedAmount:
        type: integer
        format: int32
      customerCode:
        type: string
      endDate:
        type: string
        format: date-time
      id:
        type: string
      meterNumber:
        type: string
      month:
        type: integer
        format: int32
      newIndex:
        type: integer
        format: int32
      oldIndex:
        type: integer
        format: int32
      paid:
        type: boolean
      paymentAmount:
        type: number
        format: float
      startDate:
        type: string
        format: date-time
      year:
        type: integer
        format: int32
    title: GetElectricBillDto
  GetListElectricBillDto:
    type: object
    required:
      - consumedAmount
      - month
      - paid
      - paymentAmount
      - year
    properties:
      consumedAmount:
        type: integer
        format: int32
      id:
        type: string
      month:
        type: integer
        format: int32
      paid:
        type: boolean
      paymentAmount:
        type: number
        format: float
      year:
        type: integer
        format: int32
    title: GetListElectricBillDto
  GetTelecomCostByMonthDto:
    type: object
    required:
      - amount
      - subscriptionCode
      - subscriptionTypeId
      - subscriptionTypeName
    properties:
      amount:
        type: number
        format: double
      paymentCode:
        type: string
      subscriptionCode:
        type: string
      subscriptionTypeId:
        type: string
      subscriptionTypeName:
        type: string
    title: GetTelecomCostByMonthDto
  GetTemplateByIdDto:
    type: object
    properties:
      file:
        $ref: '#/definitions/TemplateFile'
        originalRef: TemplateFile
      id:
        type: string
      name:
        type: string
      subsystem:
        type: array
        items:
          $ref: '#/definitions/TemplateSubsystem'
          originalRef: TemplateSubsystem
      type:
        $ref: '#/definitions/TemplateType'
        originalRef: TemplateType
    title: GetTemplateByIdDto
  GetTemplateDto:
    type: object
    properties:
      deploymentId:
        type: string
      file:
        $ref: '#/definitions/TemplateFile'
        originalRef: TemplateFile
      id:
        type: string
      name:
        type: string
      subsystem:
        type: array
        items:
          $ref: '#/definitions/TemplateSubsystem'
          originalRef: TemplateSubsystem
      type:
        $ref: '#/definitions/TemplateType'
        originalRef: TemplateType
    title: GetTemplateDto
  GetTypeAgencyDto:
    type: object
    properties:
      count:
        type: integer
        format: int32
      deploymentId:
        type: string
      tag:
        $ref: '#/definitions/Tag'
        originalRef: Tag
    title: GetTypeAgencyDto
  GetWaterBillByIdDto:
    type: object
    required:
      - consumedAmount
      - customerCode
      - endDate
      - meterNumber
      - month
      - newIndex
      - oldIndex
      - paid
      - paymentAmount
      - startDate
      - year
    properties:
      billCode:
        type: string
      consumedAmount:
        type: integer
        format: int32
      customerCode:
        type: string
      endDate:
        type: string
        format: date-time
      id:
        type: string
      meterNumber:
        type: string
      month:
        type: integer
        format: int32
      newIndex:
        type: integer
        format: int32
      oldIndex:
        type: integer
        format: int32
      paid:
        type: boolean
      paymentAmount:
        type: number
        format: float
      startDate:
        type: string
        format: date-time
      year:
        type: integer
        format: int32
    title: GetWaterBillByIdDto
  GetWaterBillDto:
    type: object
    required:
      - consumedAmount
      - month
      - paid
      - paymentAmount
      - year
    properties:
      consumedAmount:
        type: integer
        format: int32
      id:
        type: string
      month:
        type: integer
        format: int32
      paid:
        type: boolean
      paymentAmount:
        type: number
        format: float
      year:
        type: integer
        format: int32
    title: GetWaterBillDto
  IdResponse:
    type: object
    properties:
      id:
        type: string
    title: IdResponse
  InputStream:
    type: object
    title: InputStream
  InputTelecomCostDto:
    type: object
    required:
      - amount
      - month
      - phoneNumber
      - subscriptionCode
      - subscriptionTypeId
      - subscriptionTypeName
      - year
    properties:
      amount:
        type: number
        format: double
      month:
        type: integer
        format: int32
      paymentCode:
        type: string
      phoneNumber:
        type: string
      subscriptionCode:
        type: string
      subscriptionTypeId:
        type: string
      subscriptionTypeName:
        type: string
      year:
        type: integer
        format: int32
    title: InputTelecomCostDto
  NameDossierByDay:
    type: object
    properties:
      languageId:
        type: integer
        format: int32
      name:
        type: string
    title: NameDossierByDay
  Pageable:
    type: object
    properties:
      page:
        type: integer
        format: int32
      size:
        type: integer
        format: int32
      sort:
        type: string
    title: Pageable
  Page«GetTypeAgencyDto»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/GetTypeAgencyDto'
          originalRef: GetTypeAgencyDto
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
      totalElements:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: Page«GetTypeAgencyDto»
  Page«SectorOverdue»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/SectorOverdue'
          originalRef: SectorOverdue
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
      totalElements:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: Page«SectorOverdue»
  Page«SimpleDossier»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/SimpleDossier'
          originalRef: SimpleDossier
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
      totalElements:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: Page«SimpleDossier»
  PostResponseDto:
    type: object
    properties:
      id:
        type: string
    title: PostResponseDto
  PostTemplateDto:
    type: object
    required:
      - file
      - name
      - type
    properties:
      file:
        $ref: '#/definitions/TemplateFile'
        originalRef: TemplateFile
      name:
        type: string
      subsystem:
        type: array
        items:
          $ref: '#/definitions/TemplateSubsystem'
          originalRef: TemplateSubsystem
      type:
        $ref: '#/definitions/TemplateType'
        originalRef: TemplateType
    title: PostTemplateDto
  ProcedureAgency:
    type: object
    properties:
      id:
        type: string
    title: ProcedureAgency
  ProcedureAgencySectorDto:
    type: object
    properties:
      idAgency:
        type: string
      name:
        type: array
        items:
          $ref: '#/definitions/TranslateName'
          originalRef: TranslateName
      sector:
        type: array
        items:
          $ref: '#/definitions/AgencySectorReq'
          originalRef: AgencySectorReq
    title: ProcedureAgencySectorDto
  ProcedureDossierByDay:
    type: object
    properties:
      code:
        type: string
      id:
        type: string
      translate:
        type: array
        items:
          $ref: '#/definitions/NameDossierByDay'
          originalRef: NameDossierByDay
    title: ProcedureDossierByDay
  ProcedureDto:
    type: object
    properties:
      dossierQuantity:
        type: integer
        format: int32
      id:
        type: string
      name:
        type: string
      order:
        type: integer
        format: int32
      originId:
        type: string
    title: ProcedureDto
  ProcedureImplementer:
    type: object
    properties:
      id:
        type: string
    title: ProcedureImplementer
  ProcedureLevelDossierByDay:
    type: object
    properties:
      id:
        type: string
      name:
        type: array
        items:
          $ref: '#/definitions/NameDossierByDay'
          originalRef: NameDossierByDay
    title: ProcedureLevelDossierByDay
  ProcedureQuantityBySectorDto:
    type: object
    properties:
      agencyId:
        type: string
      agencyName:
        type: string
      sector:
        type: array
        items:
          $ref: '#/definitions/AgencySectorRes'
          originalRef: AgencySectorRes
    title: ProcedureQuantityBySectorDto
  ProcedureSector:
    type: object
    properties:
      id:
        type: string
    title: ProcedureSector
  ProcedureTranslate:
    type: object
    properties:
      languageId:
        type: integer
        format: int32
      name:
        type: string
    title: ProcedureTranslate
  PutAgencyDossierDto:
    type: object
    properties:
      number:
        type: integer
        format: int32
      type:
        type: integer
        format: int32
    title: PutAgencyDossierDto
  PutAgencyDto:
    type: object
    properties:
      ancestorPlaceId:
        type: array
        items:
          $ref: 'Error-ModelName{namespace=''org.bson.types'', name=''ObjectId''}'
          originalRef: 'Error-ModelName{namespace=''org.bson.types'', name=''ObjectId''}'
      deploymentId:
        type: string
      dossier:
        type: array
        items:
          $ref: '#/definitions/AgencyDossier'
          originalRef: AgencyDossier
      id:
        type: string
      level:
        $ref: '#/definitions/AgencyLevel'
        originalRef: AgencyLevel
      name:
        type: array
        items:
          $ref: '#/definitions/AgencyName'
          originalRef: AgencyName
      originId:
        type: string
      parentId:
        type: string
      placeId:
        type: string
      procedure:
        $ref: '#/definitions/AgencyProcedure'
        originalRef: AgencyProcedure
      sector:
        type: array
        items:
          $ref: '#/definitions/AgencySectorReq'
          originalRef: AgencySectorReq
      tag:
        $ref: '#/definitions/Tag'
        originalRef: Tag
    title: PutAgencyDto
  PutProcedureDto:
    type: object
    properties:
      agency:
        type: array
        items:
          $ref: '#/definitions/ProcedureAgency'
          originalRef: ProcedureAgency
      deploymentId:
        type: string
      dossierQuantity:
        type: integer
        format: int32
      implementer:
        type: array
        items:
          $ref: '#/definitions/ProcedureImplementer'
          originalRef: ProcedureImplementer
      order:
        type: integer
        format: int32
      originId:
        type: string
      sector:
        $ref: '#/definitions/ProcedureSector'
        originalRef: ProcedureSector
      translate:
        type: array
        items:
          $ref: '#/definitions/ProcedureTranslate'
          originalRef: ProcedureTranslate
    title: PutProcedureDto
  PutTemplateDto:
    type: object
    required:
      - file
      - name
      - type
    properties:
      deploymentId:
        type: string
      file:
        $ref: '#/definitions/TemplateFile'
        originalRef: TemplateFile
      name:
        type: string
      subsystem:
        type: array
        items:
          $ref: '#/definitions/TemplateSubsystem'
          originalRef: TemplateSubsystem
      type:
        $ref: '#/definitions/TemplateType'
        originalRef: TemplateType
    title: PutTemplateDto
  Resource:
    type: object
    properties:
      description:
        type: string
      file:
        type: file
      filename:
        type: string
      inputStream:
        $ref: '#/definitions/InputStream'
        originalRef: InputStream
      open:
        type: boolean
      readable:
        type: boolean
      uri:
        type: string
        format: uri
      url:
        type: string
        format: url
    title: Resource
  SectorByAgencyReturnDto:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
    title: SectorByAgencyReturnDto
  SectorDossierByDay:
    type: object
    properties:
      id:
        type: string
      name:
        type: array
        items:
          $ref: '#/definitions/NameDossierByDay'
          originalRef: NameDossierByDay
    title: SectorDossierByDay
  SectorOverdue:
    type: object
    properties:
      _id:
        type: string
      procedure:
        $ref: '#/definitions/ProcedureDossierByDay'
        originalRef: ProcedureDossierByDay
      sector:
        $ref: '#/definitions/SectorDossierByDay'
        originalRef: SectorDossierByDay
      unresolvedOverdue:
        type: integer
        format: int32
    title: SectorOverdue
  SimpleDossier:
    type: object
    properties:
      _id:
        type: string
      agency:
        $ref: '#/definitions/AgencyDossierByDay'
        originalRef: AgencyDossierByDay
      agencyLevel:
        $ref: '#/definitions/AgencyDossierByDay'
        originalRef: AgencyDossierByDay
      appliedOnline:
        type: integer
        format: int32
      cancelled:
        type: integer
        format: int32
      deleted:
        type: integer
        format: int32
      deploymentId:
        type: string
      received:
        type: integer
        format: int32
      receivedOnline:
        type: integer
        format: int32
      resolved:
        type: integer
        format: int32
      resolvedEarly:
        type: integer
        format: int32
      resolvedOverdue:
        type: integer
        format: int32
      returnOnTime:
        type: integer
        format: int32
      returnOverdue:
        type: integer
        format: int32
      sector:
        $ref: '#/definitions/SectorDossierByDay'
        originalRef: SectorDossierByDay
      suspended:
        type: integer
        format: int32
      unresolved:
        type: integer
        format: int32
      unresolvedOverdue:
        type: integer
        format: int32
    title: SimpleDossier
  Slice«GetDossierByAgencyDto»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/GetDossierByAgencyDto'
          originalRef: GetDossierByAgencyDto
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
    title: Slice«GetDossierByAgencyDto»
  Slice«GetDossierByDay»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/GetDossierByDay'
          originalRef: GetDossierByDay
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
    title: Slice«GetDossierByDay»
  Slice«GetListElectricBillDto»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/GetListElectricBillDto'
          originalRef: GetListElectricBillDto
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
    title: Slice«GetListElectricBillDto»
  Slice«GetTemplateDto»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/GetTemplateDto'
          originalRef: GetTemplateDto
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
    title: Slice«GetTemplateDto»
  Slice«GetWaterBillDto»:
    type: object
    properties:
      content:
        type: array
        items:
          $ref: '#/definitions/GetWaterBillDto'
          originalRef: GetWaterBillDto
      empty:
        type: boolean
      first:
        type: boolean
      last:
        type: boolean
      number:
        type: integer
        format: int32
      numberOfElements:
        type: integer
        format: int32
      pageable:
        $ref: '#/definitions/Pageable'
        originalRef: Pageable
      size:
        type: integer
        format: int32
      sort:
        $ref: '#/definitions/Sort'
        originalRef: Sort
    title: Slice«GetWaterBillDto»
  Sort:
    type: object
    properties:
      empty:
        type: boolean
      sorted:
        type: boolean
      unsorted:
        type: boolean
    title: Sort
  SubscriptionTypeDto:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
    title: SubscriptionTypeDto
  Tag:
    type: object
    properties:
      id:
        type: string
      translate:
        type: array
        items:
          $ref: '#/definitions/TagTranslate'
          originalRef: TagTranslate
    title: Tag
  TagTranslate:
    type: object
    properties:
      languageId:
        type: integer
        format: int32
      name:
        type: string
    title: TagTranslate
  TelecomCostAndAmountByMonthDto:
    type: object
    required:
      - telecomCost
      - total
    properties:
      telecomCost:
        type: array
        items:
          $ref: '#/definitions/GetTelecomCostByMonthDto'
          originalRef: GetTelecomCostByMonthDto
      total:
        type: number
        format: double
    title: TelecomCostAndAmountByMonthDto
  TelecomCostAndAmountByYearDto:
    type: object
    properties:
      telecomCost:
        type: array
        items:
          $ref: '#/definitions/TelecomCostAndAmountGroupByMonthDto'
          originalRef: TelecomCostAndAmountGroupByMonthDto
      total:
        type: number
        format: double
    title: TelecomCostAndAmountByYearDto
  TelecomCostAndAmountGroupByMonthDto:
    type: object
    properties:
      month:
        type: integer
        format: int32
      totalAmount:
        type: number
        format: double
    title: TelecomCostAndAmountGroupByMonthDto
  TemplateFile:
    type: object
    properties:
      filename:
        type: string
      path:
        type: string
      size:
        type: integer
        format: int64
    title: TemplateFile
  TemplateSubsystem:
    type: object
    properties:
      code:
        type: string
      id:
        type: string
      name:
        type: array
        items:
          $ref: '#/definitions/TagTranslate'
          originalRef: TagTranslate
    title: TemplateSubsystem
  TemplateType:
    type: object
    properties:
      code:
        type: string
      id:
        type: string
      name:
        type: array
        items:
          $ref: '#/definitions/TagTranslate'
          originalRef: TagTranslate
    title: TemplateType
  TranslateName:
    type: object
    properties:
      languageId:
        type: integer
        format: int32
      name:
        type: string
    title: TranslateName
  WaterBillInputDto:
    type: object
    required:
      - consumedAmount
      - customerCode
      - endDate
      - meterNumber
      - month
      - newIndex
      - oldIndex
      - paid
      - paymentAmount
      - startDate
      - year
    properties:
      billCode:
        type: string
      consumedAmount:
        type: integer
        format: int32
      customerCode:
        type: string
      endDate:
        type: string
        format: date-time
      meterNumber:
        type: string
      month:
        type: integer
        format: int32
      newIndex:
        type: integer
        format: int32
      oldIndex:
        type: integer
        format: int32
      paid:
        type: boolean
      paymentAmount:
        type: number
        format: float
      startDate:
        type: string
        format: date-time
      userId:
        type: string
      year:
        type: integer
        format: int32
    title: WaterBillInputDto
  WaterCustomerInputDto:
    type: object
    required:
      - customerCode
      - userId
    properties:
      customerCode:
        type: string
      userId:
        type: string
    title: WaterCustomerInputDto
  FeatureStatisticsInputDto:
    type: object
    required:
      - appCode
      - featureCode
      - counter
    properties:
      appCode:
        type: string
        example: mobile-citizens
      featureCode:
        type: string
        example: petition
      counter:
        type: number
        example: 10
    title: FeatureStatisticsInputDto