package vn.vnpt.digo.reporter.dto.qni;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class FinancialObligationDto {
    private String id;
    private int stt;
    private String code;
    private String procedure;
    private String noiDungYeuCauGiaiQuyet;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;

    private String dossierStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date awaitingFinancialObligations;

    private Long awaitingFinancialCompareAcceptedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date financialObligationsDate;

    private Long financialCompareAcceptedDate;

    private static final SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

    public String getAcceptedDateFormatted() {
        return acceptedDate != null ? dateFormatter.format(acceptedDate) : null;
    }

    public String getAppointmentDateFormatted() {
        return appointmentDate != null ? dateFormatter.format(appointmentDate) : null;
    }

    public String getCompletedDateFormatted() {
        return completedDate != null ? dateFormatter.format(completedDate) : null;
    }

    public String getAwaitingFinancialObligationsFormatted() {
        return awaitingFinancialObligations != null ? dateFormatter.format(awaitingFinancialObligations) : null;
    }

    public String getFinancialObligationsDateFormatted() {
        return financialObligationsDate != null ? dateFormatter.format(financialObligationsDate) : null;
    }
}
