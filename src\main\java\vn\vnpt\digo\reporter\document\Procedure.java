package vn.vnpt.digo.reporter.document;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.pojo.ProcedureAgency;
import vn.vnpt.digo.reporter.pojo.ProcedureImplementer;
import vn.vnpt.digo.reporter.pojo.ProcedureSector;
import vn.vnpt.digo.reporter.pojo.ProcedureTranslate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "procedure")
@CompoundIndex(name = "unique_origin_id_agency_id_sector_id_and_deployment_id", def = "{'originId': 1, 'agency.id': 1, 'sector.id': 1, 'deploymentId': 1 }", unique = true)
public class Procedure {

    @Id
    private ObjectId id;

    private ObjectId originId;

    private ArrayList<ProcedureAgency> agency;

    private ProcedureSector sector;

    private List<ProcedureTranslate> translate;

    private Integer dossierQuantity;

    private List<ProcedureImplementer> implementer;

    private ObjectId deploymentId;

    private Integer order = 1000;

    public void setTranslateName(String name){
        ProcedureTranslate translate = new ProcedureTranslate();
        translate.setName(name);
        translate.setLanguageId((short)228);
        List<ProcedureTranslate> translateList = new ArrayList<>();
        translateList.add(translate);
        this.translate = translateList;
    }
}
