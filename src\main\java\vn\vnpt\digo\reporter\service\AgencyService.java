package vn.vnpt.digo.reporter.service;

import com.google.gson.Gson;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import vn.vnpt.digo.reporter.dto.AgencyNameDto;
import vn.vnpt.digo.reporter.dto.ProcedureQuantityBySectorDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.Agency;
import vn.vnpt.digo.reporter.dto.GetDossierByAgencyDto;
import vn.vnpt.digo.reporter.dto.GetTypeAgencyDto;
import vn.vnpt.digo.reporter.dto.KafkaAgencyStatisticDto;
import vn.vnpt.digo.reporter.util.Translator;
import vn.vnpt.digo.reporter.repository.AgencyRepository;
import vn.vnpt.digo.reporter.dto.SectorByAgencyReturnDto;
import vn.vnpt.digo.reporter.dto.SectorByAgencyDto;
import vn.vnpt.digo.reporter.stream.AgencyProducerStream;
import vn.vnpt.digo.reporter.util.Context;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.PutAgencyDossierDto;
import vn.vnpt.digo.reporter.dto.PutAgencyDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.AgencyDossier;
import vn.vnpt.digo.reporter.pojo.AgencyDossierMonth;
import vn.vnpt.digo.reporter.pojo.AgencyDossierMonthData;
import vn.vnpt.digo.reporter.pojo.AgencyProcedure;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.AgencyDetailDto;
import vn.vnpt.digo.reporter.dto.AgencyNameCodeParentAncestorDto;
import vn.vnpt.digo.reporter.dto.ProcedureAgencySectorDto;
import vn.vnpt.digo.reporter.pojo.Ancestor;
import vn.vnpt.digo.reporter.pojo.AncestorMap;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.pojo.Tag;
import vn.vnpt.digo.reporter.pojo.TagAgency;
import vn.vnpt.digo.reporter.util.Microservice;

/**
 *
 * <AUTHOR>
 */
@Service
public class AgencyService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AgencyRepository agencyRepository;

    @Autowired
    private Translator translator;

    @Autowired
    private AgencyProducerStream agencyProducerStream;

    @Autowired
    private MongoTemplate mongoTemplate;

    Logger logger = org.slf4j.LoggerFactory.getLogger(ProcedureService.class);

    @Value("${digo.microservice.gateway-url}")
    private String gatewayURL;

    @Autowired
    private Microservice microservice;

    @Value("${digo.ssrf.black-list}")
    private List<String> SSRFBlacklist;

    @Value("${digo.ssrf.white-list}")
    private List<String> SSRFWhitelist;

    @Value("${digo.ssrf.black-list-enable}")
    private Boolean SSRFBlacklistEnable;

    @Transactional
    public AffectedRowsDto updateBySector(ArrayList<ProcedureAgencySectorDto> procedureAgencySectorDto, String url) {
        String token = Context.getJwtAuthenticationTokenValue();

        try {
            StringBuilder requestData = new StringBuilder(url);
            if (SSRFBlacklistEnable) {
                for (String blacklistItem : SSRFBlacklist) {
                    if (requestData.indexOf(blacklistItem) != -1) {
                        throw new DigoHttpException(12412, HttpServletResponse.SC_FORBIDDEN);
                    }
                }
                String regex = "src=['\"](.*?)['\"]";
                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(requestData.toString());
                while (matcher.find()) {
                    String srcLink = matcher.group(1);
                    boolean isWhitelisted = false;
                    for (String whitelistItem : SSRFWhitelist) {
                        if (srcLink.startsWith(whitelistItem)) {
                            isWhitelisted = true;
                            break;
                        }
                    }
                    if (!isWhitelisted) {
                        throw new DigoHttpException(12412, HttpServletResponse.SC_FORBIDDEN);
                    }
                }
            }
            
            for (ProcedureAgencySectorDto oneProcedureAgencySectorDto : procedureAgencySectorDto) {
                ArrayList<Agency> listAgency = agencyRepository.getQuantityBySectorAgency(oneProcedureAgencySectorDto.getIdAgency());
                if (listAgency.size() > 0) {
                    Agency item1 = listAgency.get(0);
                    System.out.println(item1);
                    item1.setSector(oneProcedureAgencySectorDto.getSector());
                    agencyRepository.save(item1);
                } else {
                    Agency item = new Agency();
                    item.setOriginId(oneProcedureAgencySectorDto.getIdAgency());
                    item.setSector(oneProcedureAgencySectorDto.getSector());
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.add("Authorization", "Bearer " + token);
                    HttpEntity<?> request = new HttpEntity<>(headers);
                    //Send request
                    AgencyDetailDto result = restTemplate.exchange(url + "/ba/agency/" + oneProcedureAgencySectorDto.getIdAgency(), HttpMethod.GET, request, AgencyDetailDto.class).getBody();
                    item.setParentId(result.getParent());
                    if(result.getAddress() != null && result.getAddress().getPlace() != null){
                        item.setPlaceId(result.getAddress().getPlace().getId());
                    }
                    item.setTagAgency(result.getTag());
                    item.setName(result.getName());
                    ArrayList<ObjectId> temp = new ArrayList<ObjectId>();
                    if(result.getAddress() != null && result.getAddress().getPlace() != null && result.getAddress().getPlace().getAncestor() != null){
                        for (IdPojo id : result.getAddress().getPlace().getAncestor()) {
                            temp.add(id.getId());
                        }
                    }
                    item.setAncestorPlaceId(temp);
                    agencyRepository.save(item);
                }
            }
            return new AffectedRowsDto(1);
        } catch (Exception e) {
            System.out.println("Update failed! " + e.getMessage());
            if (e instanceof DigoHttpException) {
                DigoHttpException digoException = (DigoHttpException) e;
                if(digoException.getDigoHttpExceptionDto().getCode()==12412){
                    throw new DigoHttpException(12412, HttpServletResponse.SC_FORBIDDEN);
                }
            }
        }
        return new AffectedRowsDto(0);
    }

    public List<ProcedureQuantityBySectorDto> getListProcedureQuantityByTag(ObjectId placeId, ObjectId tagId, ObjectId agencyId, ObjectId ancestorId) {

        List<ProcedureQuantityBySectorDto> result = agencyRepository.getListProcedureQuantityByTag(placeId, tagId, agencyId, ancestorId);
        result.forEach(procedureQuantityBySector -> {

            procedureQuantityBySector.setAgencyName(translator.getCurrentLocaleId());
            procedureQuantityBySector.getSector().forEach(transSector -> {
                transSector.setName(translator.getCurrentLocaleId());
            });
        });

        return result;
    }

    //Digo 1216
    public List<AgencyNameDto> getListByActiveProcedure(ObjectId placeId) {
        List<AgencyNameDto> procedureDto;
        //Get list agency have active procedure

        procedureDto = agencyRepository.getListByActiveProcedure(placeId);
        procedureDto.forEach((AgencyNameDto item) -> {
            item.setNameAgency(translator.getCurrentLocaleId());
        });
        return procedureDto;
    }

    //Digo 1206  
    public List<AgencyNameDto> getListByOnlineProcedure(ObjectId placeId) {
        List<AgencyNameDto> procedureDto;
        // Get list agency have procedure level 3 or level 4
        procedureDto = agencyRepository.getListByOnlineProcedure(placeId, translator.getCurrentLocaleId());
        System.out.println(procedureDto);
        //Set name for agency
        procedureDto.forEach((item) -> {
            item.setNameAgency(translator.getCurrentLocaleId());
        });
        return procedureDto;
    }

    public List<SectorByAgencyReturnDto> getListSectorBySector(ObjectId id) {

        List<SectorByAgencyReturnDto> sectors = new ArrayList<>();

        List<SectorByAgencyDto> sectorByAgencys = agencyRepository.getListSectorBySector(id);

        sectorByAgencys.forEach((sectorByAgency) -> {
            sectorByAgency.getSector().forEach((sector) -> {
                sector.getTransSector().forEach((name) -> {
                    if (name.getLanguageId().equals(translator.getCurrentLocaleId())) {
                        sectors.add(new SectorByAgencyReturnDto(sector.getId(), name.getName()));
                    }
                });
            });
        });

        return sectors;
    }

    public List<ProcedureQuantityBySectorDto> getListProcedureQuantityBySector(ObjectId placeId) {

        List<ProcedureQuantityBySectorDto> result = agencyRepository.getListProcedureQuantityBySector(placeId);
        result.forEach(procedureQuantityBySector -> {

            procedureQuantityBySector.setAgencyName(translator.getCurrentLocaleId());
            procedureQuantityBySector.getSector().forEach(transSector -> {
                transSector.setName(translator.getCurrentLocaleId());
            });
        });

        return result;
    }

    public Agency getAgency(ObjectId originId, ObjectId deploymentId) {
        return agencyRepository.getAgency(originId, deploymentId);
    }

    public Agency save(Agency agency) {
        return agencyRepository.save(agency);
    }

    @Cacheable(value = "Agency::getAgencyDossier", 
            key = "{#agencyId,#agencyType,#year,#month,#parentId,#pageable.getPageNumber(),#pageable.getPageSize(),#pageable.getSort().toString()}")
    public Slice<GetDossierByAgencyDto> getAgencyDossier(ObjectId agencyId, ObjectId agencyType, Integer year, Integer month, ObjectId parentId, Pageable pageable) {
        ObjectId deploymentId = Context.getDeploymentId();
        Slice<GetDossierByAgencyDto> ret = this.agencyRepository.getAgencyDossier(agencyId, agencyType, deploymentId, parentId, year, pageable);
        if (Objects.isNull(month) || month < 1 || month > 12) {
            ret.getContent().forEach(item -> {
                item.setData(translator.getCurrentLocaleId());
                item.setDossierDetail(year);
            });
        } else {
            ret.getContent().forEach(item -> {
                item.setData(translator.getCurrentLocaleId());
                item.setDossierDetail(year, "m" + month);
            });
        }
        return ret;
    }

    public Page<GetTypeAgencyDto> getCountAgency(Pageable pageable, ObjectId deploymentId, String spec) {
        Aggregation agg = null;
        if (deploymentId != null) {
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("deploymentId").is(deploymentId)
                    )),
                    group("tag.id")
                            .first("tag").as("tag")
                            .first("deploymentId").as("deploymentId")
                            .count().as("count")
            );
        } else {
            agg = (Aggregation) newAggregation(
                    group("tag.id", "deploymentId")
                            .first("tag").as("tag")
                            .first("deploymentId").as("deploymentId")
                            .count().as("count")
            );
        }
        AggregationResults<GetTypeAgencyDto> results;
        results = mongoTemplate.aggregate(agg, "agency", GetTypeAgencyDto.class);
        List<GetTypeAgencyDto> res = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<GetTypeAgencyDto> page = PageableExecutionUtils.getPage(res, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), GetTypeAgencyDto.class));
        return page;
    }

    public PostResponseDto addNewAgency(PutAgencyDto postAgency) {
        ObjectId deploymentId = Context.getDeploymentId();
        if (deploymentId != null) {
            Agency agency = agencyRepository.getAgency(postAgency.getOriginId(), deploymentId);
            if (agency == null) {
                Agency newAgency = new Agency();
                if (Objects.nonNull(postAgency.getOriginId())) {
                    newAgency.setOriginId(postAgency.getOriginId());
                }
                if (Objects.nonNull(postAgency.getParentId())) {
                    newAgency.setParentId(postAgency.getParentId());
                }
                if (Objects.nonNull(postAgency.getPlaceId())) {
                    newAgency.setPlaceId(postAgency.getPlaceId());
                }
                if (Objects.nonNull(postAgency.getAncestorPlaceId())) {
                    newAgency.setAncestorPlaceId(postAgency.getAncestorPlaceId());
                }
                if (Objects.nonNull(postAgency.getName())) {
                    newAgency.setName(postAgency.getName());
                }
                if (Objects.nonNull(postAgency.getSector())) {
                    newAgency.setSector(postAgency.getSector());
                }
                if (Objects.nonNull(postAgency.getProcedure())) {
                    newAgency.setProcedure(postAgency.getProcedure());
                }
                if (Objects.nonNull(postAgency.getTag())) {
                    newAgency.setTag(postAgency.getTag());
                }
                if (Objects.nonNull(postAgency.getTagAgency())) {
                    newAgency.setTagAgency(postAgency.getTagAgency());
                }
                if (Objects.nonNull(postAgency.getLevel())) {
                    newAgency.setLevel(postAgency.getLevel());
                }
                if (Objects.nonNull(postAgency.getDossier())) {
                    newAgency.setDossier(postAgency.getDossier());
                }
                if (Objects.nonNull(postAgency.getAncestors())) {
                    newAgency.setAncestors(postAgency.getAncestors());
                }
                newAgency.setDeploymentId(deploymentId);
                try {
                    agencyRepository.save(newAgency);
                    PostResponseDto res = new PostResponseDto(newAgency.getId());
                    return res;
                } catch (Exception ex) {
                    throw new ExceptionInInitializerError(ex.getMessage());
                }
            } else {
                throw new DigoHttpException(10007, new String[]{translator.toLocale("lang.word.agency")}, HttpServletResponse.SC_BAD_REQUEST);
            }
        } else {
            throw new DigoHttpException(10000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    public AffectedRowsDto updateAgency(ObjectId originId, PutAgencyDto putAgency) {
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        ObjectId deploymentId = Context.getDeploymentId();
        Agency agency = agencyRepository.getAgency(originId, deploymentId);
        if (agency != null) {
            try {
                if (Objects.nonNull(putAgency.getParentId())) {
                    agency.setParentId(putAgency.getParentId());
                }
                if (Objects.nonNull(putAgency.getPlaceId())) {
                    agency.setPlaceId(putAgency.getPlaceId());
                }
                if (Objects.nonNull(putAgency.getAncestorPlaceId())) {
                    agency.setAncestorPlaceId(putAgency.getAncestorPlaceId());
                }
                if (Objects.nonNull(putAgency.getName())) {
                    agency.setName(putAgency.getName());
                }
                if (Objects.nonNull(putAgency.getSector())) {
                    agency.setSector(putAgency.getSector());
                }
                if (Objects.nonNull(putAgency.getProcedure())) {
                    agency.setProcedure(putAgency.getProcedure());
                }
                if (Objects.nonNull(putAgency.getTag())) {
                    agency.setTag(putAgency.getTag());
                }
                if (Objects.nonNull(putAgency.getLevel())) {
                    agency.setLevel(putAgency.getLevel());
                }
                if (Objects.nonNull(putAgency.getDossier())) {
                    agency.setDossier(putAgency.getDossier());
                }
                if (Objects.nonNull(putAgency.getAncestors())) {
                    agency.setAncestors(putAgency.getAncestors());
                }
                agency.setDeploymentId(deploymentId);
                agencyRepository.save(agency);
                affectedRows.setAffectedRows(1);
            } catch (Exception e) {
                logger.info("DIGO-Info: " + e.toString());
            }
        }
        return affectedRows;
    }

    public AffectedRowsDto updateAgencyDossier(ObjectId originId, Integer year, Integer month, PutAgencyDossierDto putBody) {
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        ObjectId deploymentId = Context.getDeploymentId();
        if (deploymentId != null) {
            Agency agency = agencyRepository.getAgency(originId, deploymentId);
            if (agency != null) {
                try {
                    System.out.println("-----------DIGO-Info: Update Agency from updateAgencyDossier-----------");
                    String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/" + originId + "/--fully";
                    String token = Context.getJwtAuthenticationTokenValue();
                    Map<String, Object> params = new HashMap<>();
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("Content-Type", "application/json");
                    headers.setBearerAuth(token);
                    HttpEntity<String> request = new HttpEntity<>(headers);
                    ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.GET, request, String.class, params);
                    String data = result.getBody();
                    Gson g = new Gson();
                    AgencyNameCodeParentAncestorDto agencyTemp = g.fromJson(data, AgencyNameCodeParentAncestorDto.class);
                    if (agencyTemp!= null && agencyTemp.getTag() != null) {
                        ArrayList<ObjectId> tagAgency = new ArrayList<>();
                        for (TagAgency tag : agencyTemp.getTag()) {
                            tagAgency.add(new ObjectId(tag.getId()));
                        }
                        agency.setAncestorPlaceId(tagAgency);
                        agency.setTagAgency(tagAgency);
                    }
                } catch (Exception e) {
                    logger.info("DIGO-Info-SAVE:" + e.toString());
                }
                ArrayList<AgencyDossier> dossier = agency.getDossier();
                if (dossier.size() > 0) {
                    if (dossier.stream().filter(d -> d.getYear().equals(year)).findFirst().isPresent()) {
                        for (AgencyDossier agencyDossier : dossier) {
                            if (agencyDossier.getYear().equals(year)) {
                                AgencyDossierMonth dossierMonth = agencyDossier.getMonth();
                                try {
                                    Method method = dossierMonth.getClass().getMethod("getM" + month, null);
                                    AgencyDossierMonthData mData = (AgencyDossierMonthData) method.invoke(dossierMonth, null);

                                    switch (putBody.getType()) {
                                        case 1:
                                            mData.setReceived(mData.getReceived() + putBody.getNumber());
                                            break;
                                        case 2:
                                            mData.setResolved(mData.getResolved() + putBody.getNumber());
                                            break;
                                        case 3:
                                            mData.setEarly(mData.getEarly() + putBody.getNumber());
                                            break;
                                        case 4:
                                            mData.setOnTime(mData.getOnTime() + putBody.getNumber());
                                            break;
                                        case 5:
                                            mData.setOverdue(mData.getOverdue() + putBody.getNumber());
                                            break;
                                        case 6:
                                            mData.setCanceled(mData.getCanceled() + putBody.getNumber());
                                            break;
                                    }
                                    agencyRepository.save(agency);
                                    affectedRows.setAffectedRows(1);
                                } catch (Exception e) {
                                    logger.info("DIGO-Info-SAVE:" + e.toString());
                                }
                            }
                        }
                    } else {
                        AgencyDossier newAgencyDossier = this.addNewYear(year);
                        agency.getDossier().add(newAgencyDossier);
                        agencyRepository.save(agency);
                        affectedRows.setAffectedRows(1);
                        this.updateAgencyDossier(originId, year, month, putBody);
                    }
                } else {
                    AgencyDossier newAgencyDossier = this.addNewYear(year);
                    agency.getDossier().add(newAgencyDossier);
                    agencyRepository.save(agency);
                    affectedRows.setAffectedRows(1);
                    this.updateAgencyDossier(originId, year, month, putBody);
                }
            } else {
                // add new agency
                System.out.println("-----------DIGO-Info: Add new Agency from updateAgencyDossier-----------");
                String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/" + originId + "/--fully";
                String token = Context.getJwtAuthenticationTokenValue();
                Map<String, Object> params = new HashMap<>();
                HttpHeaders headers = new HttpHeaders();
                headers.add("Content-Type", "application/json");
                headers.setBearerAuth(token);
                HttpEntity<String> request = new HttpEntity<>(headers);
                ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.GET, request, String.class, params);
                String data = result.getBody();
                Gson g = new Gson();  
                AgencyNameCodeParentAncestorDto agencyTemp = g.fromJson(data, AgencyNameCodeParentAncestorDto.class); 
                logger.info("DIGO-Info-GET-BASEDATE:" + agencyTemp);
                PutAgencyDto putAgencyDto = new PutAgencyDto();
                putAgencyDto.setOriginId(originId);
                putAgencyDto.setParentId(new ObjectId(agencyTemp.getParent().getId()));
                putAgencyDto.setName(agencyTemp.getName());
                if(agencyTemp.getAncestors() != null){
                    ArrayList<Ancestor> arrAncestors = new ArrayList<Ancestor>();
                    for (AncestorMap ancestorTemp : agencyTemp.getAncestors()) {
                        Ancestor ancestor= new Ancestor();
                        ancestor.setId(new ObjectId(ancestorTemp.getId()));
                        ancestor.setName(ancestorTemp.getName());
                        arrAncestors.add(ancestor);
                    }
                    putAgencyDto.setAncestors(arrAncestors);
                }
                if(agencyTemp.getTag() != null){
                    ArrayList<ObjectId> tagAgency = new ArrayList<>();
                    for (TagAgency tag : agencyTemp.getTag()) {
                        tagAgency.add(new ObjectId(tag.getId()));
                    }
                    putAgencyDto.setAncestorPlaceId(tagAgency);
                    putAgencyDto.setTagAgency(tagAgency);
                }
                putAgencyDto.setDeploymentId(deploymentId);
                AgencyDossier newAgencyDossier = this.addNewYear(year);
                ArrayList<AgencyDossier> dossier = new ArrayList();
                dossier.add(newAgencyDossier);
                putAgencyDto.setDossier(dossier);
                AgencyProcedure agencyProcedure = new AgencyProcedure();
                switch(putBody.getTypeProcedure()){
                    case 2:
                        agencyProcedure.setActiveQuantity(agencyProcedure.getActiveQuantity() + 1);
                        agencyProcedure.setSecondLevelQuantity(agencyProcedure.getSecondLevelQuantity() + 1);
                        break;
                    case 3:
                        agencyProcedure.setActiveQuantity(agencyProcedure.getActiveQuantity() + 1);
                        agencyProcedure.setThirdLevelQuantity(agencyProcedure.getThirdLevelQuantity() + 1);
                        break;
                    case 4:
                        agencyProcedure.setActiveQuantity(agencyProcedure.getActiveQuantity() + 1);
                        agencyProcedure.setFourthLevelQuantity(agencyProcedure.getFourthLevelQuantity() + 1);
                        break;
                    default:
                        agencyProcedure.setActiveQuantity(agencyProcedure.getActiveQuantity() + 1);
                        agencyProcedure.setThirdLevelQuantity(agencyProcedure.getThirdLevelQuantity() + 1);
                        break;
                }
                putAgencyDto.setProcedure(agencyProcedure);
                logger.info("DIGO-Info-GET-BASEDATE:" + putAgencyDto);

                PostResponseDto postResponseDto =  addNewAgency(putAgencyDto);
                if(postResponseDto.getId()!= null){
                    this.updateAgencyDossier(originId, year, month, putBody);
                    affectedRows.setAffectedRows(1);
                }else{
                    affectedRows.setAffectedRows(0);
                }
                System.out.println("-----------DIGO-Info: End new Agency-----------");
            }
        } else {
            throw new DigoHttpException(10000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return affectedRows;
    }
    
    String addAgency(ObjectId originId){
        ObjectId deploymentId = Context.getDeploymentId();
        System.out.println("-----------DIGO-Info: Add new Agency from updateAgencyDossier-----------");
        String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/" + originId + "/--fully";
        String token = Context.getJwtAuthenticationTokenValue();
        Map<String, Object> params = new HashMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.setBearerAuth(token);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.GET, request, String.class, params);
        String data = result.getBody();
        Gson g = new Gson();  
        AgencyNameCodeParentAncestorDto agencyTemp = g.fromJson(data, AgencyNameCodeParentAncestorDto.class); 
        logger.info("DIGO-Info-GET-BASEDATE:" + agencyTemp);
        PutAgencyDto putAgencyDto = new PutAgencyDto();
        putAgencyDto.setOriginId(originId);
        putAgencyDto.setParentId(new ObjectId(agencyTemp.getParent().getId()));
        putAgencyDto.setName(agencyTemp.getName());
        if(agencyTemp.getAncestors() != null){
            ArrayList<Ancestor> arrAncestors = new ArrayList<Ancestor>();
            for (AncestorMap ancestorTemp : agencyTemp.getAncestors()) {
                Ancestor ancestor= new Ancestor();
                ancestor.setId(new ObjectId(ancestorTemp.getId()));
                ancestor.setName(ancestorTemp.getName());
                arrAncestors.add(ancestor);
            }
            putAgencyDto.setAncestors(arrAncestors);
        }
        if(agencyTemp.getTag() != null){
            ArrayList<ObjectId> tagAgency = new ArrayList<>();
            for (TagAgency tag : agencyTemp.getTag()) {
                tagAgency.add(new ObjectId(tag.getId()));
            }
            putAgencyDto.setAncestorPlaceId(tagAgency);
            putAgencyDto.setTagAgency(tagAgency);
        }
        putAgencyDto.setDeploymentId(deploymentId);
//        AgencyDossier newAgencyDossier = this.addNewYear(year);
        ArrayList<AgencyDossier> dossier = new ArrayList();
//        dossier.add(newAgencyDossier);
        putAgencyDto.setDossier(dossier);
        putAgencyDto.setProcedure(new AgencyProcedure());
        logger.info("DIGO-Info-GET-BASEDATE:" + putAgencyDto);

        PostResponseDto postResponseDto =  addNewAgency(putAgencyDto);
        if(postResponseDto.getId()!= null){
            return "1";
        }else{
            return "0";
        }
//        System.out.println("-----------DIGO-Info: End new Agency-----------");
    }
    
    public AffectedRowsDto updateAgencyProcedure(ObjectId originId, PutAgencyDossierDto putBody) {
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        ObjectId deploymentId = Context.getDeploymentId();
        if (deploymentId != null) {
            Agency agency = agencyRepository.getAgency(originId, deploymentId);
            if (agency != null) {
                try {
                    AgencyProcedure procedure = agency.getProcedure();
                    procedure.setActiveQuantity(procedure.getActiveQuantity() + putBody.getNumber());
                    switch (putBody.getType()) {
                        case 2:
                            procedure.setSecondLevelQuantity(procedure.getSecondLevelQuantity() + putBody.getNumber());
                            break;
                        case 3:
                            procedure.setThirdLevelQuantity(procedure.getThirdLevelQuantity() + putBody.getNumber());
                            break;
                        case 4:
                            procedure.setFourthLevelQuantity(procedure.getFourthLevelQuantity() + putBody.getNumber());
                            break;
                    }
                    agencyRepository.save(agency);
                    affectedRows.setAffectedRows(1);
                } catch (Exception e) {
                    logger.info("DIGO-Info-SAVE:" + e.toString());
                }
            } else {
                String value = this.addAgency(originId);
                if(value.equals("1")){
                    this.updateAgencyProcedure(originId, putBody);
                }
            }
        } else {
            throw new DigoHttpException(10000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return affectedRows;
    }
    
    

    public AgencyDossier addNewYear(Integer year) {
        AgencyDossier newAgencyDossier = new AgencyDossier();
        newAgencyDossier.setYear(year);
        AgencyDossierMonth newMonth = new AgencyDossierMonth();
        newMonth.setM1(new AgencyDossierMonthData());
        newMonth.setM2(new AgencyDossierMonthData());
        newMonth.setM3(new AgencyDossierMonthData());
        newMonth.setM4(new AgencyDossierMonthData());
        newMonth.setM5(new AgencyDossierMonthData());
        newMonth.setM6(new AgencyDossierMonthData());
        newMonth.setM7(new AgencyDossierMonthData());
        newMonth.setM8(new AgencyDossierMonthData());
        newMonth.setM9(new AgencyDossierMonthData());
        newMonth.setM10(new AgencyDossierMonthData());
        newMonth.setM11(new AgencyDossierMonthData());
        newMonth.setM12(new AgencyDossierMonthData());
        newAgencyDossier.setMonth(newMonth);
        return newAgencyDossier;
    }

    public boolean test() {
        KafkaAgencyStatisticDto input = new KafkaAgencyStatisticDto();
        input.setOriginId(new ObjectId("5f1fff521ed71d2e600a2260"));
        input.setDeploymentId(new ObjectId("5edf04e6edac937ec2ab011a"));
        KafkaAgencyStatisticDto.DossierData dossierData = new KafkaAgencyStatisticDto.DossierData();
        dossierData.setResolved(1);
        dossierData.setEarly(1);
        dossierData.setCanceled(-1);
        KafkaAgencyStatisticDto.Dossier dossier = new KafkaAgencyStatisticDto.Dossier(2020, 2, dossierData);
        input.setDossier(dossier);
        return agencyProducerStream.push(input);
    }
}
