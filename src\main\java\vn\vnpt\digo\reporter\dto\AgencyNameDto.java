package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.AgencyName;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyNameDto implements Serializable {

    @JsonProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId originId;

    @JsonProperty("name")
    private String agencyName;
    @JsonIgnore
    private List<AgencyName> name;

    public void setNameAgency(Short localeId) {
        name.forEach(tran -> {
            if (tran.getLanguageId().equals(localeId)) {
                this.agencyName = tran.getName();
            }
        });
    }
}
