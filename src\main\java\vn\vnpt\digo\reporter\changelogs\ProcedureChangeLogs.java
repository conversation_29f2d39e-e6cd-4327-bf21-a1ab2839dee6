package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-08-10-03-40")
public class ProcedureChangeLogs {

    @ChangeSet(order = "2020-08-10-03-40", id = "ProcedureChangeLogs::create", author = "haimn")
    public void create(DB db) {
        db.createCollection("procedure", null);
    }

    @ChangeSet(order = "2020-08-10-03-40", id = "ProcedureChangeLogs::createUniqueIndex", author = "haimn")
    public void createUniqueIndex(DB db) {
        db.getCollection("procedure").createIndex(new BasicDBObject("originId", 1), new BasicDBObject("unique", true));
    }
}
