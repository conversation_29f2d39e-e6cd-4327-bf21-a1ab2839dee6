package vn.vnpt.digo.reporter.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.document.EvaluationResultForm;

@Repository
public interface EvaluationResultFormRepository extends MongoRepository<EvaluationResultForm, ObjectId>{
  public Integer deleteEvaluationResultFormById(ObjectId id);
}
