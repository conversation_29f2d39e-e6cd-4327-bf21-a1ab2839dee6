apiVersion: v1
kind: ConfigMap
metadata:
  name: svc-reporter.env
data:
  SERVER_PORT: '8080'
  #
  # Digo
  DIGO_LOCALE_CODE: 'vi'
  DIGO_SUPPORTED_LOCALES: '{"vi":228,"en":46,"zh":232}'
  DIGO_MICROSERVICE_COMMUNICATION_STRATEGY: 'cluster' # cluster or gateway
  DIGO_MICROSERVICE_GATEWAY_URL: '{{microserviceGateway}}' # example: https://digo-test-api.vnptigate.vn
  DIGO_MICROSERVICE_SERVICE_NAME_PREFIX: 'svc-'
  DIGO_MICROSERVICE_SERVICE_DEFAULT_PORT: '8080'
  DIGO_MICROSERVICE_SERVICE_DEFAULT_PROTOCOL: 'http'
  DIGO_REPORTER_PROCEDURE_CITIZEN: '5f3113a8fd0ee6fddc48b004'
  DIGO_REPORTER_PROCEDURE_ENTERPRISE: '5f3113a8fd0ee6fddc48b001'
  #
  # Spring
  SPRING_DATA_WEB_PAGEABLE_DEFAULT_PAGE_SIZE: '15'
  SPRING_DATA_WEB_PAGEABLE_MAX_PAGE_SIZE: '50'
  SPRING_CACHE_TYPE: 'hazelcast'
  SPRING_HAZELCAST_CONFIG: 'file:usr/local/digo/hazelcast-config.xml'
  SPRING_DATA_MONGODB_URI: '{{mongodbUri}}' # example: mongodb://mongo-0.mongodb:27017,mongo-1.mongodb:27017,mongo-2.mongodb:27017/testReporter
  SPRING_SECURITY_OAUTH2_RESOURCESERVER_JWT_ISSUER_URI: '{{oauth2IssuerUri}}' # example: https://digo-test-oidc.vnptigate.vn/auth/realms/digo
  #
  # Mongobee
  MONGOBEE_ENABLE: 'true'
  MONGOBEE_CHANGE_LOGS_SCAN_PACKAGE: 'vn.vnpt.digo.reporter.changelogs'
  #
  # Spring Cloud
  SPRING_CLOUD_STREAM_KAFKA_BINDER_BROKERS: '10.82.14.228'
  SPRING_CLOUD_STREAM_KAFKA_BINDER_DEFAULTBROKERPORT: '31241'