package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.service.DigitizingReportQNIService;
import vn.vnpt.digo.reporter.service.GeneralReportIgnorePauseExtendQNIService;
import vn.vnpt.digo.reporter.service.GeneralReportQNIService;
import vn.vnpt.digo.reporter.service.DigitizingReportQNIServiceV2;


import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/digitizing-report-qni")
@IcodeAuthorize("vnpt.permission.digitizingreportqni")
public class DigitizingReportQNIController {

    Logger logger = LoggerFactory.getLogger(DigitizingReportQNIController.class);
    @Autowired
    private DigitizingReportQNIService generalReportQNIService;

    @Autowired
    private DigitizingReportQNIServiceV2 digitizingReportQNIServiceV2;

    @Value("${digo.report.re-digitizing-reporter}")
    private Boolean isReDigitizingReporter;

    @GetMapping(value = "")
    public List<DigitizingReportDto.CustomSummary> getProcedureQuantityByTag(HttpServletRequest request,
                                                                             @RequestParam(value = "from", required = true) String fromDate,
                                                                             @RequestParam(value = "to", required = true) String toDate,
                                                                             @RequestParam(value = "arr-agency", required = true) List<String> agencyIds,
                                                                             @RequestParam(value = "excluded-procedure", required = false) List<String> excludedProcedure) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<DigitizingReportDto.CustomSummary> reportResult = null;
        if (isReDigitizingReporter){
            reportResult = digitizingReportQNIServiceV2.getDigitizingReportDto(agencyIds, excludedProcedure, fromDate, toDate);
        }else{
            reportResult = generalReportQNIService.getDigitizingReportDto(agencyIds, fromDate, toDate);
        }
        logger.info("DIGO-Info: " + reportResult.size());
        return reportResult;
    }

    @GetMapping(value = "/--detail")
    public Page<DetailGeneralReportDto.PageResult> getProcedureByTagDetail(HttpServletRequest request,
                                                                             @RequestParam(value = "from", required = true) String fromDate,
                                                                             @RequestParam(value = "to", required = true) String toDate,
                                                                             @RequestParam(value = "agency-id", required = true) List<String> agencyIds,
                                                                             @RequestParam(value = "excluded-procedure", required = false) List<String> excludedProcedure,
                                                                             @RequestParam(value = "type", required = true) Integer type,
                                                                             Pageable pageable
    ) {
        try {
            String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
            logger.info("DIGO-Info: " + requestPath);
            Page<DetailGeneralReportDto.PageResult> results = null;
            if (isReDigitizingReporter){
                results = digitizingReportQNIServiceV2.getGeneralDigitizingReportDetailDto(agencyIds, excludedProcedure, fromDate, toDate, type, pageable);
            }else{
                results = generalReportQNIService.getGeneralDigitizingReportDetailDto(agencyIds, fromDate, toDate, type, pageable);
            }
            logger.info("DIGO-Info: " + results.getTotalElements());
            return results;
        } catch (Exception e) {
            logger.error("DIGO-Info: " + e.getMessage());
        }
        return null;
    }

    @GetMapping("/--detail/--export")
    public ResponseEntity<Object> exportDossierStatisticGeneralDetail(
            HttpServletRequest request,
            @RequestParam(value = "from") String fromDate,
            @RequestParam(value = "to") String toDate,
            @RequestParam(value = "agency-id") List<String> agencyId,
            @RequestParam(value = "excluded-procedure", required = false) List<String> excludedProcedure,
            @RequestParam(value = "type") Integer type
    ) throws JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        ResponseEntity<Object> result = null;
        if (isReDigitizingReporter){
            result = digitizingReportQNIServiceV2.exportGeneralReportDetail(fromDate, toDate, agencyId, excludedProcedure, type);
        }else{
            result = generalReportQNIService.exportGeneralReportDetail(fromDate, toDate, agencyId, type);
        }
        return result;
    }

}
