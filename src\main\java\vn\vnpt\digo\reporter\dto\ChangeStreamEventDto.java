package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChangeStreamEventDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String service;

    private String collection;

    private String type;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId itemId;

    private Object data;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    // 0: không lỗi, 1: collection chưa định nghĩa (có thể xử lý lại), 2: Lỗi xử lý (<PERSON>h<PERSON>ng xử lý lại), 3: Lỗi khác có thể xử lý lại
    private int errorType;

    private String errorMsg;

    private int retryCount = 0;
}
