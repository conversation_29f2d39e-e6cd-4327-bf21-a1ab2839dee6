/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.service.AppStatisticsService;
import vn.vnpt.digo.reporter.dto.AppStatisticsOutputDto;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app-statistics")
@IcodeAuthorize("vnpt.permission.appstatistics")
public class AppStatisticsController {
    @Autowired
    private AppStatisticsService appStatisticsService;

    Logger logger = LoggerFactory.getLogger(AppStatisticsController.class);
    
    @PutMapping("/--insert-or-update")
    public boolean insertOrUpdateAppStatistics(
            HttpServletRequest request, @RequestParam(value = "type", required = true, defaultValue = "0") String type
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() 
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        
        boolean result = appStatisticsService.insertOrUpdateAppStatistics(type);
        return result;
    }
    
    @GetMapping("/--get-amount")
    public AppStatisticsOutputDto getAmount(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        AppStatisticsOutputDto result = appStatisticsService.getAmount();
        return result;
    }
}
