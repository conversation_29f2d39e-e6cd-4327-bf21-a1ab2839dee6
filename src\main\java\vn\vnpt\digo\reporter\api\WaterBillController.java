package vn.vnpt.digo.reporter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.GetWaterBillByIdDto;
import vn.vnpt.digo.reporter.dto.GetWaterBillDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.WaterBillInputDto;
import vn.vnpt.digo.reporter.service.WaterBillService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/water-bill")
@IcodeAuthorize("vnpt.permission.waterbill")
public class WaterBillController {

    @Autowired
    public WaterBillService waterBillService;

    Logger logger = LoggerFactory.getLogger(WaterBillController.class);

    @PostMapping()
    public PostResponseDto addNewBillForCustomer(HttpServletRequest request,
            @Valid @RequestBody @ModelAttribute WaterBillInputDto waterBillInputDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PostResponseDto responseIdDto = waterBillService.addNewBillForCustomer(waterBillInputDto);

        logger.info("DIGO-Info: " + responseIdDto);
        return responseIdDto;
    }

    // Digo 2464
    @GetMapping("/{id}")
    public GetWaterBillByIdDto getWaterBillById(HttpServletRequest request, @PathVariable("id") ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        GetWaterBillByIdDto waterBillData = waterBillService.getWaterBillById(id);
        logger.info(waterBillData.toString());

        return waterBillData;
    }

    // Digo 2514
    @GetMapping("/--months")
    public Slice<GetWaterBillDto> getWaterBillByMonth(HttpServletRequest request,
            @RequestParam(value = "customer-id", required = true) String customerCode,
            @RequestParam(value = "month", required = false) Integer month,
            @RequestParam(value = "year", required = false) Integer year, Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<GetWaterBillDto> waterBill = waterBillService.getWaterBillByMonth(customerCode, month, year, pageable);
        logger.info(waterBill.getNumberOfElements() + "");

        return waterBill;
    }

}
