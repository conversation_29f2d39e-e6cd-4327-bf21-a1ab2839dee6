package vn.vnpt.digo.reporter.api;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hazelcast.internal.json.ParseException;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;

import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.pojo.EvaluationResultDto;
import vn.vnpt.digo.reporter.service.EvaluationResultFormService;

@RestController
@RequestMapping("/eresult-form")
@IcodeAuthorize("vnpt.permission.eresult-form")
public class EvaluationResultFormController {
  
  @Autowired
  private EvaluationResultFormService evaluationResultFormService;

  Logger logger = LoggerFactory.getLogger(DossierByDayController.class);

  @PostMapping("")
  public ResponseEntity<PostResponseDto> postEvaluationResultForm(HttpServletRequest request,
                                                                  @RequestBody EvaluationResultDto input
  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    PostResponseDto result = evaluationResultFormService.postEvaluationResultForm(input);
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--list")
  public ResponseEntity<Slice<EvaluationResultDto>> getListEvaluationResultForm(HttpServletRequest request,
                                                                              @RequestParam(value = "keyword",required = false) String keyword,
                                                                              Pageable pageable
  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    Slice<EvaluationResultDto> result = evaluationResultFormService.getListEvaluationResultForm( keyword, pageable);
    return ResponseEntity.ok(result);
  }


  @GetMapping("/{id}")
  public ResponseEntity<EvaluationResultDto> getEvaluationResultFormById(HttpServletRequest request,
          @PathVariable(value = "id", required = true) ObjectId id
  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    EvaluationResultDto result = evaluationResultFormService.getEvaluationResultFormById(id);
    return ResponseEntity.ok(result);
  }

  @PutMapping("/{id}")
  public ResponseEntity<AffectedRowsDto> updateEvaluationResultFormById(HttpServletRequest request,
                                                                        @PathVariable(value = "id", required = true) ObjectId id,
                                                                        @RequestBody EvaluationResultDto input
  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result = evaluationResultFormService.updateEvaluationResultFormById(id,input);
    return ResponseEntity.ok(result);
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<AffectedRowsDto> deleteEvaluationResultFormById(HttpServletRequest request,
          @PathVariable(value = "id", required = true) ObjectId id
  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result = evaluationResultFormService.deleteEvaluationResultFormById(id);
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--activation")
  public ResponseEntity<EvaluationResultDto> getEvaluationResultFormById(HttpServletRequest request) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    EvaluationResultDto result = evaluationResultFormService.getEvaluationResultFormActive();
    return ResponseEntity.ok(result);
  }
}
