package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierTaskStatus implements Serializable{
    
    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private ArrayList<TranslateName> name;
}
