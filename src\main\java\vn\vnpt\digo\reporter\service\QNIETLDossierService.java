package vn.vnpt.digo.reporter.service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mongodb.bulk.BulkWriteResult;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.dto.qni.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.qni.DossierHavingFinancialObligationsDTO;
import vn.vnpt.digo.reporter.dto.qni.ImportResponseDto;
import vn.vnpt.digo.reporter.dto.qni.UpdateExtendDigitizingDto;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static vn.vnpt.digo.reporter.util.MicroserviceExchange.postNoAuth;


@Service
public class QNIETLDossierService {

    private final MongoTemplate mongoTemplate;

    @Autowired
    public QNIETLDossierService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Autowired
    private Microservice microservice;

    @Autowired
    private RestTemplate restTemplate;

    private Gson gson = new Gson();

    Logger logger = LoggerFactory.getLogger(QNIETLDossierService.class);

    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    TimeZone timezone = TimeZone.getTimeZone("GMT");

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    @Value(value = "${digo.dossier.qni.return-over-due-additional-dossier-status-task-id}")
    private String returnOverDueAdditionalDossierStatusTaskId;

    @Value(value = "${digo.qni.digitizing.enable-check-is-reused}")
    private boolean enableCheckReusedFileExtend;

    @Value(value = "${digo.qni.hiddenFeeReportNAST}")
    private boolean hiddenFeeReportNAST;

    public boolean addQNIETLDossierData(List<QNIETLDossier> listQNIETLDossier) {
        if(listQNIETLDossier.size()>0){
            for(int i = 0; i <listQNIETLDossier.size(); i++ ){
                var item = listQNIETLDossier.get(i);
                var extendDigitizing = new QNIETLDossier.ExtendDigitizing();
                QNIETLDossier qnietlDossier = mongoTemplate.findById(new ObjectId(item.getId()), QNIETLDossier.class);
                if (Objects.nonNull(qnietlDossier)){
                    if (Objects.nonNull(qnietlDossier.getExtendDigitizing())){
                        extendDigitizing = qnietlDossier.getExtendDigitizing();
                    }
                    if (qnietlDossier.getISlowToReceive() != null){
                        item.setISlowToReceive(qnietlDossier.getISlowToReceive());
                    }
                    if (qnietlDossier.getDeadlineAcceptedDate() != null){
                        item.setDeadlineAcceptedDate(qnietlDossier.getDeadlineAcceptedDate());
                    }
                    if (qnietlDossier.getProcedureHavePayment() != null){
                        item.setProcedureHavePayment(qnietlDossier.getProcedureHavePayment());
                    }
                    if (qnietlDossier.getIsPaymentOnline() != null){
                        item.setIsPaymentOnline(qnietlDossier.getIsPaymentOnline());
                    }
                    if(Objects.equals(item.getDossierTaskStatus().getId(), this.returnOverDueAdditionalDossierStatusTaskId)){
                        List<Date> additionalDates = item.getAdditionalDate();
                        if (additionalDates != null && !additionalDates.isEmpty()){
                            Date lastAdditionalDate = additionalDates.get(additionalDates.size() - 1);

                            item.setCompletedDate(lastAdditionalDate);
                        }
                    }
                }
                try {
                    extendDigitizing.setIsHaveAttachment(item.getAttachment() != null && item.getAttachment().get(0).getId() != null);
                    extendDigitizing.setIsHaveTPHS(!getDossierFormFile(item.getId()).isJsonNull() && getDossierFormFile(item.getId()).getAsJsonArray().size() > 0);

                    JsonObject dossierDetailData = getDossierDetailNoAuth(item.getId());
                    extendDigitizing.setIsAttachmentStorages(isNonEmptyArray(dossierDetailData, "attachmentStorages"));
                    extendDigitizing.setIsComponentsStorages(isNonEmptyArray(dossierDetailData, "componentsStorages"));
                    if (hiddenFeeReportNAST && dossierDetailData.has("listDossierReceipt") && dossierDetailData.get("listDossierReceipt").getAsJsonArray().size() > 0){
                        item.setIsPaymentOnline(true);
                    }
                    updateFinancialObligationsDateVbdlis(dossierDetailData, item);
                    if (enableCheckReusedFileExtend){
                        this.checkIsReuseFileExtend(dossierDetailData, extendDigitizing);
                    }
                } catch (Exception e)
                {
                    logger.error(e.getMessage());
                }
                item.setExtendDigitizing(extendDigitizing);
                mongoTemplate.save(item, "qniETLDossier");
            }
        }

        return true;
    }
    public UpdateExtendDigitizingDto updateExtendDigitizing(String _fromDate, String _toDate, String fieldName) {
        UpdateExtendDigitizingDto updateExtendDigitizingDto = new UpdateExtendDigitizingDto();
        AtomicInteger successRow = new AtomicInteger(0);
        List<String> updateDossierCode = new ArrayList<>();
        List<String> updateSuccessDossierCode = new ArrayList<>();
        List<String> updateErrorMessages = new ArrayList<>();

        Criteria criteria = new Criteria();
        String field = "extendDigitizing." + fieldName;
        criteria.orOperator(
                Criteria.where(field).exists(false),
                Criteria.where(field).is(false)
        );
        List<String> attachmentFields = Arrays.asList("isAttachmentStorages", "isSignsAttachment", "isHaveAttachment");
        if (attachmentFields.contains(fieldName)){
            criteria.and("completedDate").gte(parseDate(_fromDate)).lte(parseDate(_toDate));
        }else {
            criteria.and("acceptedDate").gte(parseDate(_fromDate)).lte(parseDate(_toDate));
        }
        Query query = new Query(criteria);
        List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);
        list.forEach(element -> {
            updateDossierCode.add(element.getCode());
            try{
                switch (fieldName) {
                    case "isAttachmentStorages":
                        updateIsAttachmentStorages(element, successRow, updateSuccessDossierCode);
                        break;
                    case "isComponentsStorages":
                        updateIsComponentsStorages(element, successRow, updateSuccessDossierCode);
                        break;
                    case "isSignsTPHS":
                        updateIsSignsTPHS(element, successRow, updateSuccessDossierCode);
                        break;
                    case "isReuseFile":
                        updateIsReuseFileStatus(element, successRow, updateSuccessDossierCode);
                        break;
                    case "isSignsAttachment":
                        updateIsSignsAttachment(element, successRow, updateSuccessDossierCode);
                        break;
                    case "isHaveAttachment":
                        updateIsHaveAttachment(element, successRow, updateSuccessDossierCode);
                        break;
                    case "isHaveTPHS":
                        updateIsHaveTPHS(element, successRow, updateSuccessDossierCode);
                        break;
                    default:
                        break;
                }
            }catch (Exception ex){
                updateErrorMessages.add(String.format("Error message: %s Code: %s",
                        ex.getMessage(), element.getCode()));
            }

        });
        updateExtendDigitizingDto.setUpdateErrorMessage(updateErrorMessages);
        updateExtendDigitizingDto.setSuccessRows(successRow.get());
        updateExtendDigitizingDto.setUpdateSuccessDossierCode(updateSuccessDossierCode);
        updateExtendDigitizingDto.setUpdateDossierCode(updateDossierCode);
        return updateExtendDigitizingDto;
    }

    public UpdateExtendDigitizingDto updateIsPaymentOnline(String fromDate, String toDate) {
        UpdateExtendDigitizingDto result = new UpdateExtendDigitizingDto();
        AtomicInteger successRow = new AtomicInteger(0);
        List<String> updateDossierCode = new ArrayList<>();
        List<String> updateSuccessDossierCode = new ArrayList<>();
        List<String> updateErrorMessages = new ArrayList<>();

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("isPaymentOnline").exists(false),
                Criteria.where("isPaymentOnline").is(false)
        );

        criteria.and("acceptedDate").gte(parseDate(fromDate)).lte(parseDate(toDate));
        Query query = new Query(criteria);
        List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);

        list.forEach(element -> {
            try{
                this.updateIsPaymentOnline(element, successRow, updateSuccessDossierCode);
            }catch (Exception ex){
                updateErrorMessages.add(String.format("Error message: %s Code: %s",
                        ex.getMessage(), element.getCode()));
            }
        });

        result.setUpdateErrorMessage(updateErrorMessages);
        result.setSuccessRows(successRow.get());
        result.setUpdateSuccessDossierCode(updateSuccessDossierCode);
        result.setUpdateDossierCode(updateDossierCode);

        return result;

    }

    public UpdateExtendDigitizingDto updateIsPaymentOnlineV2(String fromDate, String toDate, Boolean checkExist) {
        UpdateExtendDigitizingDto result = new UpdateExtendDigitizingDto();
        AtomicInteger successRow = new AtomicInteger(0);
        List<String> updateDossierCode = new ArrayList<>();
        List<String> updateSuccessDossierCode = new ArrayList<>();
        List<String> updateErrorMessages = new ArrayList<>();

        Query query = new Query();

        query.addCriteria(Criteria.where("acceptedDate").gte(parseDate(fromDate)).lte(parseDate(toDate)));

        if (!checkExist) {
            query.addCriteria(Criteria.where("isPaymentOnline").exists(false));
        }

        query.addCriteria(new Criteria().andOperator(
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null)
        ));

        try{
            //Lấy thông tin dossier
            List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);
            List<String> ids = list.stream()
                    .map(QNIETLDossier::getId)
                    .collect(Collectors.toList());
            if (ids.isEmpty()){
                result.setSuccessRows(successRow.get());
                return result;
            }
            //Kiểm tra các dossier có thanh toán trực tuyến => danh sách dossierId có thanh toán trực tuyến
            JsonArray checkDossierPaymentResult = this.checkDossierPayment(ids);

            List<ObjectId> updateDossierIds = new ArrayList<>();
            List<String> updateDossierIdsString = new ArrayList<>();
            for (JsonElement element : checkDossierPaymentResult) {
                updateDossierIds.add(new ObjectId(element.getAsString()));
                updateDossierIdsString.add(element.getAsString());
            }

            Query queryDossierUpdate = new Query();
            queryDossierUpdate.addCriteria(Criteria.where("_id").in(updateDossierIds));

            Update update = new Update();
            update.set("isPaymentOnline", true);
            successRow.set(updateDossierIds.size());

            mongoTemplate.updateMulti(queryDossierUpdate, update, QNIETLDossier.class);

            if (checkExist){
                List<String> ignoreUpdateIds = ids.stream()
                        .filter(num -> !updateDossierIdsString.contains(num))
                        .collect(Collectors.toList());

                this.updateFieldToNullData(ignoreUpdateIds, "isPaymentOnline");
            }
        } catch (Exception e) {
            logger.error("Update payment online error: {}", e.getMessage());
        }

        result.setUpdateErrorMessage(updateErrorMessages);
        result.setSuccessRows(successRow.get());
        result.setUpdateSuccessDossierCode(updateSuccessDossierCode);
        result.setUpdateDossierCode(updateDossierCode);

        return result;
    }

    public UpdateExtendDigitizingDto updateIsReuseExtend(String _fromDate, String _toDate, Boolean ignoreExist) {
        UpdateExtendDigitizingDto updateExtendDigitizingDto = new UpdateExtendDigitizingDto();
        AtomicInteger successRow = new AtomicInteger(0);
        List<String> updateSuccessDossierCode = new ArrayList<>();
        List<String> updateErrorMessages = new ArrayList<>();

        Criteria criteria = new Criteria();
        criteria.and("acceptedDate").gte(parseDate(_fromDate)).lte(parseDate(_toDate));
        if (!ignoreExist){
            criteria.orOperator(
                    Criteria.where("extendDigitizing.isUseCheckCitizen").exists(false)
            );
        }

        Query query = new Query(criteria);
        List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);
        list.forEach(element -> {
            try{
                JsonObject dossierData = this.getDossierDetailNoAuth(element.getId());
                this.checkIsReuseFileExtend(dossierData, element.getExtendDigitizing());
                if (element.getExtendDigitizing().getIsUseCheckCitizen() != null && element.getExtendDigitizing().getIsUseCheckCitizen()){
                    updateSuccessDossierCode.add(element.getCode());
                    successRow.getAndIncrement();
                }
                mongoTemplate.save(element);
            }catch (Exception ex){
                updateErrorMessages.add(String.format("Error message: %s Code: %s",
                        ex.getMessage(), element.getCode()));
            }

        });
        updateExtendDigitizingDto.setUpdateErrorMessage(updateErrorMessages);
        updateExtendDigitizingDto.setSuccessRows(successRow.get());
        updateExtendDigitizingDto.setUpdateSuccessDossierCode(updateSuccessDossierCode);
        return updateExtendDigitizingDto;
    }

    public UpdateExtendDigitizingDto updateFinancialDateVbdlis(String fromDate, String toDate) {
        UpdateExtendDigitizingDto result = new UpdateExtendDigitizingDto();
        AtomicInteger successRow = new AtomicInteger(0);
        List<String> updateDossierCode = new ArrayList<>();
        List<String> updateSuccessDossierCode = new ArrayList<>();
        List<String> updateErrorMessages = new ArrayList<>();
        Criteria criteria = new Criteria().andOperator(
                Criteria.where("acceptedDate").gte(parseDate(fromDate)).lte(parseDate(toDate))
//                Criteria.where("financialObligationsDate").exists(false)
        );
        Query query = new Query(criteria);
        List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);

        list.forEach(element -> {
            try{
                JsonObject dossierData = this.getDossierDetailNoAuth(element.getId());
                this.updateFinancialObligationsDateVbdlis(dossierData, element);
                mongoTemplate.save(element);
            }catch (Exception ex){
                updateErrorMessages.add(String.format("Error message: %s Code: %s",
                        ex.getMessage(), element.getCode()));
            }
        });

        result.setUpdateErrorMessage(updateErrorMessages);
        result.setSuccessRows(successRow.get());
        result.setUpdateSuccessDossierCode(updateSuccessDossierCode);
        result.setUpdateDossierCode(updateDossierCode);

        return result;
    }

    private JsonObject getDossierDetail(String dossierId){
        String getDossierDetailTemplateUrl = microservice.padmanUri("dossier/" + dossierId + "/--online").toUriString();
        String getDossierDetailRawResponse = MicroserviceExchange.get(restTemplate, getDossierDetailTemplateUrl, String.class);
        return gson.fromJson(getDossierDetailRawResponse, JsonObject.class);
    }

    private JsonObject getDossierDetailNoAuth(String dossierId){
        String getDossierDetailTemplateUrl = microservice.padmanUri("dossier/" + dossierId + "/--online").toUriString();
        String getDossierDetailRawResponse = MicroserviceExchange.getNoAuth(this.getRestTemplate(), getDossierDetailTemplateUrl, String.class);
        return gson.fromJson(getDossierDetailRawResponse, JsonObject.class);
    }

    private JsonArray getDossierFormFile(String dossierId){
        String getFileTemplateUrl = microservice.padmanUri("dossier-form-file/" + dossierId + "/--get-all-file").toUriString();
        String getFileRawResponse = MicroserviceExchange.getNoAuth(this.getRestTemplate(), getFileTemplateUrl, String.class);
        return gson.fromJson(getFileRawResponse, JsonArray.class);
    }

    private RestTemplate getRestTemplate(){
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

    private JsonArray getSignFile (String fileId){
        String getSignFileTemplateUrl = microservice.filemanUri("file/" + fileId + "/--signs").toUriString();
        String getSignFileRawResponse = MicroserviceExchange.get(restTemplate, getSignFileTemplateUrl, String.class);
        return gson.fromJson(getSignFileRawResponse, JsonArray.class);
    }
    private void updateIsReuseFileStatus(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        JsonArray data = getDossierFormFile(qnietlDossier.getId());
        for (int i = 0; i < data.size(); i++) {
            JsonObject fileObject = data.get(i).getAsJsonObject();
            JsonElement reusedElement = fileObject.get("reused");
            if (reusedElement != null && (reusedElement.getAsInt() == 1 || reusedElement.getAsInt() == 2)) {
                successRow.addAndGet(1);
                updateSuccessDossierCode.add(qnietlDossier.getCode());
                extendDigitizing.setIsReuseFile(true);
                qnietlDossier.setExtendDigitizing(extendDigitizing);
                mongoTemplate.save(qnietlDossier);
                break;
            }
        }
    }

    private JsonArray checkDossierPayment(List<String> listDossierId){
        String url = microservice.padmanUri("/dossier-payment/--check-dossier-payment-qni").toUriString();
//        String url = "http://localhost:8081/dossier-payment/--check-dossier-payment-qni";
        String rawResponse = MicroserviceExchange.postJson(restTemplate, url, listDossierId, String.class);
        return gson.fromJson(rawResponse, JsonArray.class);
    }

    private void updateIsSignsTPHS(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        JsonArray data = getDossierFormFile(qnietlDossier.getId());
        for (int i = 0; i < data.size(); i++) {
            JsonObject fileObject = data.get(i).getAsJsonObject();
            String fileId = fileObject.get("id").getAsString();
            if (fileId != null) {
                JsonArray getSignFileData = getSignFile(fileId);
                if (getSignFileData.size() > 0){
                    successRow.addAndGet(1);
                    updateSuccessDossierCode.add(qnietlDossier.getCode());
                    extendDigitizing.setIsSignsTPHS(true);
                    qnietlDossier.setExtendDigitizing(extendDigitizing);
                    mongoTemplate.save(qnietlDossier);
                    break;
                }
            }
        }
    }
    private void updateIsAttachmentStorages(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        JsonObject dossierDetailData = getDossierDetail(qnietlDossier.getId());
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        if (isNonEmptyArray(dossierDetailData, "attachmentStorages")) {
            successRow.addAndGet(1);
            updateSuccessDossierCode.add(qnietlDossier.getCode());
            extendDigitizing.setIsAttachmentStorages(true);
            qnietlDossier.setExtendDigitizing(extendDigitizing);
            mongoTemplate.save(qnietlDossier);
        }
    }
    private void updateIsComponentsStorages(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        JsonObject dossierDetailData = getDossierDetail(qnietlDossier.getId());
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        if (isNonEmptyArray(dossierDetailData, "componentsStorages")) {
            successRow.addAndGet(1);
            updateSuccessDossierCode.add(qnietlDossier.getCode());
            extendDigitizing.setIsComponentsStorages(true);
            qnietlDossier.setExtendDigitizing(extendDigitizing);
            mongoTemplate.save(qnietlDossier);
        }
    }
    private void updateIsSignsAttachment(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        if (qnietlDossier.getAttachment() != null && !qnietlDossier.getAttachment().isEmpty()){
            for (int i = 0; i < qnietlDossier.getAttachment().size(); i++){
                JsonArray getSignFileData = getSignFile(qnietlDossier.getAttachment().get(i).getId());
                if (getSignFileData.size() > 0){
                    successRow.addAndGet(1);
                    updateSuccessDossierCode.add(qnietlDossier.getCode());
                    extendDigitizing.setIsSignsAttachment(true);
                    qnietlDossier.setExtendDigitizing(extendDigitizing);
                    mongoTemplate.save(qnietlDossier);
                    break;
                }

            };
        }
    }
    private void updateIsHaveTPHS(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        JsonArray data = getDossierFormFile(qnietlDossier.getId());
        if (!data.isJsonNull() && data.getAsJsonArray().size() > 0){
            successRow.addAndGet(1);
            updateSuccessDossierCode.add(qnietlDossier.getCode());
            extendDigitizing.setIsHaveTPHS(true);
            qnietlDossier.setExtendDigitizing(extendDigitizing);
            mongoTemplate.save(qnietlDossier);
        };
    }
    private void updateIsHaveAttachment(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        QNIETLDossier.ExtendDigitizing extendDigitizing = new QNIETLDossier.ExtendDigitizing();
        if (qnietlDossier.getExtendDigitizing() != null){
            extendDigitizing = qnietlDossier.getExtendDigitizing();
        }
        if (qnietlDossier.getAttachment() != null && !qnietlDossier.getAttachment().isEmpty()){
            successRow.addAndGet(1);
            updateSuccessDossierCode.add(qnietlDossier.getCode());
            extendDigitizing.setIsHaveAttachment(true);
            qnietlDossier.setExtendDigitizing(extendDigitizing);
            mongoTemplate.save(qnietlDossier);
        }
    }
    private void updateIsPaymentOnline(QNIETLDossier qnietlDossier, AtomicInteger successRow, List<String> updateSuccessDossierCode){
        JsonObject dossierDetailData = getDossierDetail(qnietlDossier.getId());
        if (dossierDetailData.has("listDossierReceipt")) {
            if (dossierDetailData.get("listDossierReceipt").getAsJsonArray().size() > 0){
                successRow.addAndGet(1);
                updateSuccessDossierCode.add(qnietlDossier.getCode());
                qnietlDossier.setIsPaymentOnline(true);
                mongoTemplate.save(qnietlDossier);
            }
        }
    }
    private void checkIsReuseFileExtend(JsonObject dossierDetailData, QNIETLDossier.ExtendDigitizing extendDigitizing){
        if(dossierDetailData.has("applicant")
                && (extendDigitizing.getIsReuseFile() == null || !extendDigitizing.getIsReuseFile())){
            JsonObject applicant = dossierDetailData.getAsJsonObject("applicant");
            if (applicant.has("data")){
                JsonObject data = applicant.getAsJsonObject("data");
                if (data.has("checkCitizen")){
                    extendDigitizing.setIsUseCheckCitizen(data.get("checkCitizen").getAsBoolean());
                }
            }
        }
    }
    private void updateFinancialObligationsDateVbdlis(JsonObject dossierDetailData, QNIETLDossier qnietlDossier) throws ParseException {
        if(dossierDetailData.has("extendQNI")){
            JsonObject extendQNI = dossierDetailData.getAsJsonObject("extendQNI");
            if (extendQNI.has("isVbdlis")){
                if (extendQNI.get("isVbdlis").getAsBoolean()){
                    if (dossierDetailData.has("task")){
                        JsonArray task = dossierDetailData.getAsJsonArray("task");
                        if (task.size() > 3){
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
                            ZonedDateTime zonedDateTime = ZonedDateTime.parse(task.get(3).getAsJsonObject().get("assignedDate").getAsString(), formatter);
                            // Chuyển đổi từ ZonedDateTime sang Date
                            Date date = Date.from(zonedDateTime.toInstant());
                            qnietlDossier.setFinancialObligationsDate(date);
                        }
                    }
                }
            }
        }
    }
    private static boolean isNonEmptyArray(JsonObject data, String key) {
        try{
            return Objects.nonNull(data) &&
                    data.has(key) &&
                    data.getAsJsonArray(key).size() > 0;
        }catch (Exception ex){
            return false;
        }

    }
    private Date parseDate(String dateStr) {
        try {
            this.df.setTimeZone(this.timezone);
            return this.df.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public UpdateExtendDigitizingDto updateExtendDigitizingV2(String fromDate, String toDate, String fieldName, Boolean checkExist) {
        UpdateExtendDigitizingDto result = new UpdateExtendDigitizingDto();
        AtomicInteger successRow = new AtomicInteger(0);

        Query query = new Query();

        query.addCriteria(Criteria.where("acceptedDate").gte(parseDate(fromDate)).lte(parseDate(toDate)));

        String field = "extendDigitizing." + fieldName;

        if (!checkExist) {
            query.addCriteria(Criteria.where(field).exists(false));
        }

        query.addCriteria(new Criteria().andOperator(
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null)
        ));

        try{
            //Lấy thông tin dossier
            List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);
            List<String> ids = list.stream()
                    .map(QNIETLDossier::getId)
                    .collect(Collectors.toList());
            if (ids.isEmpty()){
                result.setSuccessRows(successRow.get());
                return result;
            }
            JsonArray checkResult = null;
            switch (fieldName) {
                case "isReuseFile":
                    checkResult = this.checkDossierIsReuseFile(ids);
                    break;
                case "isUseCheckCitizen":
                    checkResult= this.checkDossierIsUseCheckCitizen(ids);
                    break;
                default:
                    break;
            }

            if (checkResult == null){
                result.setSuccessRows(successRow.get());
                return result;
            }
            List<ObjectId> updateDossierIds = new ArrayList<>();
            List<String> updateDossierIdsString = new ArrayList<>();
            for (JsonElement element : checkResult) {
                updateDossierIds.add(new ObjectId(element.getAsString()));
                updateDossierIdsString.add(element.getAsString());
            }

            Query queryDossierUpdate = new Query();
            queryDossierUpdate.addCriteria(Criteria.where("_id").in(updateDossierIds));

            Update update = new Update();
            update.set(field, true);
            successRow.set(updateDossierIds.size());

            mongoTemplate.updateMulti(queryDossierUpdate, update, QNIETLDossier.class);

            if (checkExist){
                List<String> ignoreUpdateIds = ids.stream()
                        .filter(num -> !updateDossierIdsString.contains(num))
                        .collect(Collectors.toList());

                this.updateFieldToNullData(ignoreUpdateIds, field);
            }
        } catch (Exception e) {
            logger.error("UpdateIsUseCheckCitizen: {}", e.getMessage());
        }
        result.setSuccessRows(successRow.get());
        return result;
    }

    private JsonArray checkDossierIsUseCheckCitizen(List<String> listDossierId){
        String url = microservice.padmanUri("/digitizing-report-job-qni/--check-is-check-citizen").toUriString();
//        String url = "http://localhost:8081/digitizing-report-job-qni/--check-is-check-citizen";
        String rawResponse = MicroserviceExchange.postJson(restTemplate, url, listDossierId, String.class);
        return gson.fromJson(rawResponse, JsonArray.class);
    }

    private JsonArray checkDossierIsReuseFile(List<String> listDossierId){
        String url = microservice.padmanUri("/digitizing-report-job-qni/--check-is-reuse-file").toUriString();
//        String url = "http://localhost:8081/digitizing-report-job-qni/--check-is-reuse-file";
        String rawResponse = MicroserviceExchange.postJson(restTemplate, url, listDossierId, String.class);
        return gson.fromJson(rawResponse, JsonArray.class);
    }

    private void updateFieldToNullData(List<String> dossierIds, String field){
        try{
            if (dossierIds == null || dossierIds.isEmpty()) {
                return;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where("_id").in(dossierIds));

            Update update = new Update();
            update.unset(field);

            mongoTemplate.updateMulti(query, update, QNIETLDossier.class);
        } catch (Exception e) {
            logger.error("Lỗi khi cập nhật field '{}' thành null: {}", field, e.getMessage(), e);
        }
    }

    /**
     * Insert awaitingFinancialObligations field for dossiers matching date criteria and options
     *
     * @param fromDateString Start date in ISO format (yyyy-MM-dd'T'HH:mm:ss.SSS'Z')
     * @param toDateString End date in ISO format (yyyy-MM-dd'T'HH:mm:ss.SSS'Z')
     * @param option Filter option: 1-All, 2-Without field, 3-With field already (field awaitingFinancialObligations)
     * @return ImportResponseDto containing results of the operation
     */
    public ImportResponseDto insertFieldAwaitingFinancialObligations(String fromDateString, String toDateString, Integer option) {
        logger.info("Starting insertFieldAwaitingFinancialObligations with dateRange: {} to {}, option: {}",
                fromDateString, toDateString, option);

        ImportResponseDto response = new ImportResponseDto();

        try {
            // Parse date strings to Date objects
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            // Build query
            Query query = new Query();
            query.addCriteria(Criteria.where("acceptedDate").gte(fromDate).lte(toDate));

            // Apply filtering based on option parameter
            switch (option) {
                case 2:
                    query.addCriteria(Criteria.where("awaitingFinancialObligations").exists(false));
                    break;
                case 3:
                    query.addCriteria(Criteria.where("awaitingFinancialObligations").exists(true));
                    break;
                default:
                    // Option 1 or invalid option - no additional criteria
                    break;
            }

            // Add complex criteria for dossier status and appointment date
            query.addCriteria(new Criteria().andOperator(
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));

            // Execute query to find matching dossiers and extract IDs
            logger.debug("Executing MongoDB query to find matching dossiers");
            List<QNIETLDossier> dossiers;
            try {
                dossiers = mongoTemplate.find(query, QNIETLDossier.class);
                logger.info("Found {} dossiers matching the criteria", dossiers.size());
            } catch (Exception e) {
                logger.error("Error querying dossiers from database", e);
                response.setSuccessRows(0);
                response.setInsertedRows("0");
                response.getErrorMessages().add("Lỗi truy vấn cơ sở dữ liệu: " + e.getMessage());
                return response;
            }

            // If no dossiers found, return early
            if (dossiers.isEmpty()) {
                logger.info("No dossiers found matching the query criteria");
                response.setSuccessRows(0);
                response.setInsertedRows("0");
                response.setSuccessMessage("Không tìm thấy dossier nào phù hợp với điều kiện tìm kiếm");
                return response;
            }

            // Extract IDs for processing
            List<String> dossierIds = dossiers.stream()
                    .map(QNIETLDossier::getId)
                    .collect(Collectors.toList());

            // Call function to update dossiers with financial obligations
            logger.info("Calling updateQniETLDossiersWithFinancialObligations with {} dossier IDs", dossierIds.size());
            response = updateQniETLDossiersWithFinancialObligations(dossierIds);

            return response;

        } catch (ParseException e) {
            logger.error("Error parsing date strings: {} or {}", fromDateString, toDateString, e);
            response.setSuccessRows(0);
            response.setInsertedRows("0");
            response.getErrorMessages().add("Lỗi định dạng ngày tháng: " + e.getMessage());
            return response;
        } catch (Exception e) {
            logger.error("Unexpected error in insertFieldAwaitingFinancialObligations", e);
            response.setSuccessRows(0);
            response.setInsertedRows("0");
            response.getErrorMessages().add("Lỗi hệ thống: " + e.getMessage());
            return response;
        }
    }

    public ImportResponseDto updateQniETLDossiersWithFinancialObligations(List<String> dossierIds) {
        logger.info("Starting updateQniETLDossiersWithFinancialObligations with {} dossier IDs", dossierIds.size());

        ImportResponseDto response = new ImportResponseDto();

        try {
            String url = microservice.padmanUri("dossier-qni/financial-obligations/optimized").toUriString();
            //String url = "http://localhost:8081/dossier-qni/financial-obligations/optimized";
            // 1. Call the financial obligations API
            DossierHavingFinancialObligationsDTO[] apiResponse = postNoAuth(this.getRestTemplate(), url, dossierIds, DossierHavingFinancialObligationsDTO[].class);

            DossierHavingFinancialObligationsDTO[] dossierObligations = apiResponse;

            if (dossierObligations == null || dossierObligations.length == 0) {
                logger.warn("No financial obligations found for the provided dossier IDs");
                response.setSuccessRows(0);
                response.setInsertedRows("0");
                response.setSuccessMessage("No financial obligations found for the provided dossier IDs");
                return response;
            }

            // 2. Set the successRows as the number of returned items
            response.setSuccessRows(dossierObligations.length);
            logger.info("Found {} dossiers with financial obligations", dossierObligations.length);

            // 3. Create a map of id to assignedDate for quick lookup
            Map<String, Date> idToAssignedDateMap = new HashMap<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

            for (DossierHavingFinancialObligationsDTO obligation : dossierObligations) {
                try {
                    String assignedDateStr = obligation.getAssignedDate().toString();
                    Date assignedDate = sdf.parse(assignedDateStr); // Parse chuỗi thành Date UTC
                    idToAssignedDateMap.put(obligation.getId(), assignedDate);
                } catch (Exception e) {
                    logger.error("Error parsing assignedDate for dossier ID: {}, value: {}",
                            obligation.getId(), obligation.getAssignedDate(), e);
                    continue; // Bỏ qua ID này nếu parse lỗi
                }
            }

            // 4. Get the IDs to query
            List<String> obligationIds = new ArrayList<>(idToAssignedDateMap.keySet());

            // 5. First query to find which IDs exist in the database
            Query existingDocsQuery = new Query();
            existingDocsQuery.addCriteria(Criteria.where("_id").in(obligationIds));
            existingDocsQuery.fields().include("_id");
            List<QNIETLDossier> existingDossiers = mongoTemplate.find(existingDocsQuery, QNIETLDossier.class);

            Set<String> existingIds = existingDossiers.stream()
                    .map(QNIETLDossier::getId)
                    .collect(Collectors.toSet());

            // 6. Track successful updates and errors
            int successCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 7. Add error messages for non-existing IDs
            for (String id : obligationIds) {
                if (!existingIds.contains(id)) {
                    String errorMsg = "_id: " + id + " không tồn tại trong collection QniETLDossier";
                    errorMessages.add(errorMsg);
                    logger.error(errorMsg);
                }
            }

            // 8. Use bulk operations for better performance
            if (!existingIds.isEmpty()) {
                BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, QNIETLDossier.class);

                for (String id : existingIds) {
                    Query query = new Query(Criteria.where("_id").is(id));
                    Update update = new Update().set("awaitingFinancialObligations", idToAssignedDateMap.get(id));
                    bulkOps.updateOne(query, update);
                }

                BulkWriteResult bulkResult = bulkOps.execute();
                successCount = bulkResult.getModifiedCount();

                // Log any existing IDs that weren't modified (may have already been updated)
                if (successCount < existingIds.size()) {
                    for (String id : existingIds) {
                        // Query to check if the document has the value we just tried to set
                        Query checkQuery = new Query(Criteria.where("_id").is(id)
                                .and("awaitingFinancialObligations").is(idToAssignedDateMap.get(id)));

                        if (mongoTemplate.exists(checkQuery, QNIETLDossier.class)) {
                            String warningMsg = "_id: " + id + " không cần cập nhật hoặc đã được cập nhật trước đó";
                            errorMessages.add(warningMsg);
                            logger.warn(warningMsg);
                        }
                    }
                }
            }

            // 9. Complete the response
            response.setInsertedRows(String.valueOf(successCount));
            response.setErrorMessages(errorMessages);

            if (successCount == obligationIds.size()) {
                response.setSuccessMessage("All dossiers successfully updated with financial obligations");
            } else {
                response.setSuccessMessage("Updated " + successCount + " out of " + obligationIds.size() + " dossiers with financial obligations");
            }

            logger.info("Completed updateQniETLDossiersWithFinancialObligations: {} out of {} updates successful",
                    successCount, obligationIds.size());

            return response;

        } catch (Exception e) {
            logger.error("Error in updateQniETLDossiersWithFinancialObligations", e);
            response.setSuccessRows(0);
            response.setInsertedRows("0");
            response.getErrorMessages().add("Lỗi hệ thống: " + e.getMessage());
            return response;
        }
    }
}
