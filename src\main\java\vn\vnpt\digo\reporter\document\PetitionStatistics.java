package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.FieldPetitionStatistics;
import vn.vnpt.digo.reporter.pojo.ReceptionMethodPetitionStatistics;
import vn.vnpt.digo.reporter.pojo.StatusPetitionStatistics;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "petitionStatistics")
public class PetitionStatistics implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private List<StatusPetitionStatistics> statusStatistics;
    
    private List<ReceptionMethodPetitionStatistics> receptionMethodStatistics;
    
    private List<FieldPetitionStatistics> fieldStatistics;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

}
