package vn.vnpt.digo.reporter.stream;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.Procedure;
import vn.vnpt.digo.reporter.dto.KafkaProcedureDossierQuantityDto;
import vn.vnpt.digo.reporter.service.ProcedureService;
import vn.vnpt.digo.reporter.stream.messaging.ProcedureDossierQuantity;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(ProcedureDossierQuantity.class)
public class ProcedureDossierQuantityStream {

    @Autowired
    private ProcedureService procedureService;

    Logger logger = LoggerFactory.getLogger(ProcedureDossierQuantity.class);

    @StreamListener(ProcedureDossierQuantity.INPUT)
    public void updateProcedureDossierQuantity(KafkaProcedureDossierQuantityDto input) {
        System.out.println("PROCEDURE_UPDATE_DATA");
        try {
            KafkaProcedureDossierQuantityDto kafka = input;
            Procedure updateProcedure = procedureService.getProcedure(kafka.getOriginId());
            if (Objects.nonNull(updateProcedure)) {
                logger.info("Update procedure: " + input);
                updateProcedure.setDossierQuantity(updateProcedure.getDossierQuantity() + kafka.getDossierQuantity());
            }
            procedureService.save(updateProcedure);
            logger.info("Dossier quantity updated successfully");
        } catch (Exception e) {
            logger.info("Update procedure: " + input);
            logger.info("Dossier quantity updated failed with exception:  " + e.getMessage());
        }
    }
}
