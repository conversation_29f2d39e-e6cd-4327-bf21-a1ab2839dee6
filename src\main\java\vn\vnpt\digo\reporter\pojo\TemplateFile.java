/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.reporter.dto.MinioAccessDto;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TemplateFile implements Serializable {
    
    private String filename;
    
    private long size;
    
    private String path;

    private MinioAccessDto minioAccess;

    public TemplateFile(String filename, long size, String path) {
        this.filename = filename;
        this.size = size;
        this.path = path;
    }

    public TemplateFile(String filename, long size, String path, MinioAccessDto minioAccess) {
        this.filename = filename;
        this.size = size;
        this.path = path;
        this.minioAccess = minioAccess;
    }

}
