package vn.vnpt.digo.reporter.service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.PetitionStatistics;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Translator;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import vn.vnpt.digo.reporter.exception.DigoHttpException;

/**
 *
 * <AUTHOR>
 */
@Service
public class PetitionStatisticsService {

    @Autowired
    private Translator translator;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    Logger logger = LoggerFactory.getLogger(PetitionStatisticsService.class);

    @Cacheable("getPetitionStatistics")
    public PetitionStatistics getTodayPetitionStatistics() {
        ObjectId deploymentId = Context.getDeploymentId();
        Date today = new Date();
        today.setHours(0);
        today.setMinutes(0);
        today.setSeconds(0);
        Query query = new Query();
        query.addCriteria(Criteria.where("deploymentId").is(deploymentId));
        query.addCriteria(Criteria.where("createdDate").gte(today));
        
        PetitionStatistics todayStats = mongoTemplate.findOne(query, PetitionStatistics.class);
        if (Objects.isNull(todayStats)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.petition-statistics")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return todayStats;
    }
    
    @Cacheable("getTodayPetitionStatisticsByDay")
    public PetitionStatistics getTodayPetitionStatisticsByDay(String wday){
        ObjectId deploymentId = Context.getDeploymentId();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
       
        Query query = new Query();
        if ((wday != null)) {
            try {
                Date wdayDateFormat = dateFormat.parse(wday);
                query.addCriteria(Criteria.where("deploymentId").is(deploymentId));
                query.addCriteria(Criteria.where("createdDate").gte(wdayDateFormat));
            } catch (ParseException parse) {
                    logger.info(null, parse);
            }   
        }
        
        PetitionStatistics withDayStats = mongoTemplate.findOne(query, PetitionStatistics.class);
        if (Objects.isNull(withDayStats)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.petition-statistics-by-day")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return withDayStats;
    }
}