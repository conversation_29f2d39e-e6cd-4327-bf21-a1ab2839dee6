package vn.vnpt.digo.reporter.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.AgencyName;
import vn.vnpt.digo.reporter.pojo.AgencySector;
import vn.vnpt.digo.reporter.pojo.Tag;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetTypeAgencyDto implements Serializable {

    @JsonProperty("tag")
    private Tag tag;
    
    @JsonProperty("count")
    private Integer count;
    
    @JsonProperty("deploymentId")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    
}
