package vn.vnpt.digo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import vn.vnpt.digo.reporter.properties.TemplateFileStorageProperties;

@SpringBootApplication
@EnableConfigurationProperties({
        TemplateFileStorageProperties.class
})
@EnableScheduling
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
