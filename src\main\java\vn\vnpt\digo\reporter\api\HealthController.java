package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.service.HealthService;

import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/health-service")
@IcodeAuthorize("vnpt.permission.manageDigo")
public class HealthController {
    @Autowired
    private HealthService healthService;

    Logger logger = LoggerFactory.getLogger(HealthController.class);

    @GetMapping()
    public Object getHealthDetails(
            HttpServletRequest request,
            @RequestParam(value = "ci", required = false) String chatId,
            @RequestParam(value = "tp", required = false) String topic
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return healthService.getAndLogHealth(chatId, topic);
    }
}
