package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.AgencyDossier;
import vn.vnpt.digo.reporter.pojo.AgencyLevel;
import vn.vnpt.digo.reporter.pojo.AgencyName;
import vn.vnpt.digo.reporter.pojo.AgencyProcedure;
import vn.vnpt.digo.reporter.pojo.AgencySector;
import vn.vnpt.digo.reporter.pojo.Ancestor;
import vn.vnpt.digo.reporter.pojo.Tag;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PutAgencyDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private ObjectId originId;

    private ObjectId parentId;

    private ObjectId placeId;

    private ArrayList<ObjectId> ancestorPlaceId;
    
    private ArrayList<ObjectId> tagAgency;
    
    private ArrayList<Ancestor> ancestors;

    private ArrayList<AgencyName> name;

    private ArrayList<AgencySector> sector;

    private AgencyProcedure procedure;

    private Tag tag;

    private AgencyLevel level;

    private ArrayList<AgencyDossier> dossier;

    private ObjectId deploymentId;

}
