package vn.vnpt.digo.reporter.service;

import java.util.List;
import java.util.Objects;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.ElectricCustomer;
import vn.vnpt.digo.reporter.dto.ElectricCustomerInputDto;
import vn.vnpt.digo.reporter.dto.GetCustomerCodeByUserIdDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.repository.ElectricCustomerRepository;

/**
 *
 * <AUTHOR>
 */
@Service
public class ElectricCustomerService {

    @Autowired
    private ElectricCustomerRepository electricCustomerRepository;

    public PostResponseDto insertOrUpdateElectricCustomer(ElectricCustomerInputDto electricCustomerInputDto) {
        ElectricCustomer customerMapping = electricCustomerRepository.findUserIdAndCustomerCode(electricCustomerInputDto.getUserId(),
                electricCustomerInputDto.getCustomerCode());
        if (Objects.equals(customerMapping, null)) {
            customerMapping = new ElectricCustomer(electricCustomerInputDto.getUserId(),
                    electricCustomerInputDto.getCustomerCode());
            customerMapping = electricCustomerRepository.save(customerMapping);
        } else {
            customerMapping.setUpdateDate();
            customerMapping = electricCustomerRepository.save(customerMapping);
        }
        return new PostResponseDto(customerMapping.getId());
    }

    public List<GetCustomerCodeByUserIdDto> getCustomerCodeByUserId(ObjectId userId) {
        List<GetCustomerCodeByUserIdDto> customerCode = electricCustomerRepository.getCustomerCodeByUserId(userId);

        return customerCode;
    }
}
