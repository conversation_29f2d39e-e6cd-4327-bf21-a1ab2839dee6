/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.repository;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.DossierByDay;
import vn.vnpt.digo.reporter.dto.GetDossierByDay;

@Repository
public interface DossierByDayRepository extends PagingAndSortingRepository<DossierByDay, ObjectId> {

    @Query(value = "{ $and: [ "
            + "{'updatedDate': {$gte : :#{#startDate}}},"
            + "{'updatedDate': {$lte : :#{#endDate}}},"
            + "{$or: [{$expr:{$eq: [:#{#procedureId}, null ] }} ,{$and: [{'procedure.id': { $exists: true }},{'procedure.id': :#{#procedureId} }]}]},"
            + "{$or: [{$expr:{$eq: [:#{#sectorId}, null ] }} ,{$and: [{'sector.id': { $exists: true }},{'sector.id': :#{#sectorId} }]}]},"
            + "{$or: [{$expr:{$eq: [:#{#agencyId}, null ] }} ,{$and: [{'agency.id': { $exists: true }},{'agency.id': :#{#agencyId} }]}]},"
            + "{$or: [{$expr:{$eq: [:#{#procedureLevel}, null ] }} ,{$and: [{'procedureLevel.id': { $exists: true }},{'procedureLevel.id': :#{#procedureLevel} }]}]},"
            + "]}")
    Page<GetDossierByDay> getPageReportDossierByDay(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("procedureId") ObjectId procedureId, @Param("sectorId") ObjectId sectorId, @Param("agencyId") ObjectId agencyId, @Param("procedureLevel") ObjectId procedureLevel, Pageable pageable);

    @Query(value = "{'procedure.id': ?0,'sector.id': ?1,'agency.id': ?2,'procedureLevel.id': ?3}")
    Page<GetDossierByDay> getPageReportDossierWithoutDay(@Param("procedureId") ObjectId procedureId, @Param("sectorId") ObjectId sectorId, @Param("agencyId") ObjectId agencyId, @Param("procedureLevel") ObjectId procedureLevel, Pageable pageable);

    @Query(value = "{"
            + "'agency.id': {:#{#agencyId != null ? '$eq' : '$ne'} : :#{#agencyId != null ? #agencyId : '0'}},"
            + "'sector.id': {:#{#sectorId != null ? '$eq' : '$ne'} : :#{#sectorId != null ? #sectorId : '0'}},"
            + "'procedureLevel.id': {:#{#procedureLevel != null ? '$eq' : '$ne'} : :#{#procedureLevel != null ? #procedureLevel : '0'}}"
            + "}")
    Page<GetDossierByDay> getPageListProcedure(
            Pageable pageable,
            @Param("sectorId") ObjectId sectorId,
            @Param("agencyId") ObjectId agencyId,
            @Param("procedureLevel") ObjectId procedureLevel
    );

    @Query(value = "{"
            + "'agency.id': {:#{#agencyId != null ? '$eq' : '$ne'} : :#{#agencyId != null ? #agencyId : '0'}},"
            + "'service.id': {:#{#serviceId != null ? '$eq' : '$ne'} : :#{#serviceId != null ? #serviceId : '0'}},"
            + "'procedureLevel.id': {:#{#procedureLevel != null ? '$eq' : '$ne'} : :#{#procedureLevel != null ? #procedureLevel : '0'}},"
            + "}", sort = "{'updateDate': -1}")
    Slice<GetDossierByDay> getSliceListProcedure(
            Pageable pageable,
            @Param("sectorId") ObjectId sectorId,
            @Param("agencyId") ObjectId agencyId,
            @Param("procedureLevel") ObjectId procedureLevel
    );

    @Query(value = "{$and: ["
            + "{ 'year': ?0 }, "
            + "{ 'month': ?1 }, "
            + "{ 'day': ?2 }, "
            + "{ 'sector.id': ?3 }, "
            + "{ 'agency.id': ?4 }, "
            + "{ 'procedure.id': ?5 }, "
            + "{ 'deploymentId': ?6 } "
            + "]}")
    DossierByDay getDossierByDayInfo(Integer year, Integer month, Integer day, ObjectId sectorId, ObjectId agencyId, ObjectId procedureId, ObjectId deploymentId);

    @Query(value = "{$and:["
            + "?#{ [0] == null ? { $where : 'true'} : {'deploymentId':[0]}},"
            + "?#{ [3] == null ? { $where : 'true'} : {'agency.id': [3]}},"
            + "?#{ [1] == null ? { $where : 'true'} : {'updatedDate': {$gte : [1]}}},"
            + "?#{ [2] == null ? { $where : 'true'} : {'updatedDate': {$lte : [2]}}},"
            + "]}")
    List<GetDossierByDay> getListByDeploymentId(@Param("deploymentId") ObjectId deploymentId, @Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("agencyId") ObjectId agencyId);

    @Query(value = "{$and:["
            + "?#{ [0] == null ? { $where : 'true'} : {'deploymentId':[0]}},"
            + "?#{ [3] == null ? { $where : 'true'} : {'agency.id': [3]}},"
            + "?#{ [4] == null ? { $where : 'true'} : {'sector.id': [4]}},"
            + "?#{ [5] == null ? { $where : 'true'} : {'procedure.id': [5]}},"
            + "?#{ [1] == null ? { $where : 'true'} : {'updatedDate': {$gte : [1]}}},"
            + "?#{ [2] == null ? { $where : 'true'} : {'updatedDate': {$lte : [2]}}},"
            + "]}")
    List<GetDossierByDay> getListDossierBySector(
            @Param("deploymentId") ObjectId deploymentId, 
            @Param("fromDate") Date fromDate, 
            @Param("toDate") Date toDate, 
            @Param("agencyId") ObjectId agencyId,
            @Param("sectorId") ObjectId sectorId,
            @Param("procedureId") ObjectId procedureId
            );
    
    @Query(value = "{$and:["
            + "?#{ [0] == null ? { $where : 'true'} : {'deploymentId':[0]}},"
            + "?#{ [3] == null ? { $where : 'true'} : {'agency.id': [3]}},"
            + "?#{ [4] == null ? { $where : 'true'} : {'sector.id': [4]}},"
            + "?#{ [5] == null ? { $where : 'true'} : {'procedure.id': [5]}},"
            + "?#{ [6] == null ? { $where : 'true'} : {'agency.ancestors.id': [6]}},"
            + "?#{ [1] == null ? { $where : 'true'} : {'updatedDate': {$gte : [1]}}},"
            + "?#{ [2] == null ? { $where : 'true'} : {'updatedDate': {$lte : [2]}}},"
            + "]}")
    List<GetDossierByDay> getListDossierByAncestorId(
            @Param("deploymentId") ObjectId deploymentId, 
            @Param("fromDate") Date fromDate, 
            @Param("toDate") Date toDate, 
            @Param("agencyId") ObjectId agencyId,
            @Param("sectorId") ObjectId sectorId,
            @Param("procedureId") ObjectId procedureId,
            @Param("ancestorId") ObjectId ancestorId
            );
    
    @Query(value = "{$and:["
            + "?#{ [0] == null ? { $where : 'true'} : {'deploymentId':[0]}},"
            + "?#{ [3] == null ? { $where : 'true'} : {'agency.id': [3]}},"
            + "?#{ [4] == null ? { $where : 'true'} : {'agency.ancestors.id': [4]}},"
            + "?#{ [1] == null ? { $where : 'true'} : {'updatedDate': {$gte : [1]}}},"
            + "?#{ [2] == null ? { $where : 'true'} : {'updatedDate': {$lte : [2]}}},"
            + "]}")
    List<GetDossierByDay> getListByDeploymentIdWithAncestor(@Param("deploymentId") ObjectId deploymentId, @Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("agencyId") ObjectId agencyId, @Param("ancestorId") ObjectId ancestorId);

}

