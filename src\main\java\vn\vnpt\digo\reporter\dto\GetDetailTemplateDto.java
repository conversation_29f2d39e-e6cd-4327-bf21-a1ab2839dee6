/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.pojo.TemplateType;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class GetDetailTemplateDto implements Serializable {
    
    Long id;
    String name;
    TemplateType type;
    TemplateFile file;
    
    public GetDetailTemplateDto(Long id, String name, ObjectId typeId, String typeCode, String typeName, String fileFileName, Long fileSize, String filePath ) {
        this.id = id;
        this.name = name;
    }
    
}
