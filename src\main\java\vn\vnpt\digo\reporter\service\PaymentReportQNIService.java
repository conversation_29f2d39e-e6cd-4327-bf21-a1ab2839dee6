package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mongodb.MongoClient;
import org.bson.types.ObjectId;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.util.*;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
public class PaymentReportQNIService {
    private static final RestTemplate restTemplate = new RestTemplate();
    private final Gson gson = new Gson();
    Logger logger = org.slf4j.LoggerFactory.getLogger(PaymentReportQNIService.class);
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    TimeZone timezone = TimeZone.getTimeZone("GMT");
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MongoConverter mongoConverter;
    @Autowired
    private Microservice microservice;
    @Autowired
    private Translator translator;
    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI)
    private Resource resourceTemplateDossierStatisticAssigneeQNI;
    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI_v2)
    private Resource resourceTemplateDossierStatisticAssigneeQNIV2;
    @Autowired
    private MongoClient client;
    @Value("${digo.dossier.off-time}")
    private String offTime;
    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;
    @Value("${digo.enable.hide.fullname}")
    private boolean enableHideName;
    @Autowired
    private AgencyFilterReportQniService agencyFilterReportQniService;
    @Value("${digo.qni.is-hide-security-inf}")
    private boolean isHideSecurityInf;
    @Value("${digo.qni.ignore-hide-security-inf-permission}")
    private String ignoreHideSecurityInfPermission;

    private Set<ObjectId> uniqueObjectIdAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<ObjectId> uniqueObjectIds = new HashSet<>();
        Set<String> uniqueStringIds = new HashSet<>();
        for (String objectIdString : objectIdStrings) {
            try {
                ObjectId objectId = new ObjectId(objectIdString);
                uniqueObjectIds.add(objectId);
                uniqueStringIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    private List<String> getRootAgencies(List<String> agencyIds) throws JSONException {
        String getObjUrl;
        String jsonString;
        JSONArray jsonArr;

        List<String> rootAgencyIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(agencyIds)) {
            getObjUrl = "qni-agency/--find-root?id=" + String.join(",", agencyIds);
            getObjUrl = microservice.basedataUri(getObjUrl).toUriString();
            try {
                jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
                jsonArr = new JSONArray(jsonString);
                for (int i = 0; i < jsonArr.length(); i++) {
                    rootAgencyIds.add(jsonArr.getJSONObject(i).get("id").toString());
                }
            } catch (Exception ex) {
                logger.error("Can not find root agencies ", ex);
            }
        }
        return rootAgencyIds;
    }

    private Set<String> uniqueStringAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<String> uniqueObjectIds = new HashSet<>();

        for (String objectIdString : objectIdStrings) {
            try {
                uniqueObjectIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    public List<PaymentReportDto> findDossiersWithConditions(List<String> agencyIds, String fromDateString, String toDateString, List<String> ignoreProcedureIds) {
        Query query = new Query();
        logger.info("Begin findDossiersWithConditions");

        try {
            // format Date
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);

            // get Agency
            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            // Điều kiện dựa trên agency và khoảng thời gian
            query.addCriteria(Criteria.where("agency._id").in(agencyObjectIds));
            query.addCriteria(Criteria.where("acceptedDate").gte(fromDate).lte(toDate));

            // Điều kiện cho financialObligationOnline
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("procedureHavePayment").is(true),
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));

            if (ignoreProcedureIds != null && !ignoreProcedureIds.isEmpty()) {
                List<ObjectId> ignoreProcedureObjIds = this.mapListStringToObjectId(ignoreProcedureIds);
                if (!ignoreProcedureObjIds.isEmpty()){
                    query.addCriteria(Criteria.where("procedureLevel._id").nin(ignoreProcedureObjIds));
                }
            }

            logger.info("End getGeneralReportDetailDto");
            var dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            // Tính toán cộng lại theo agency cơ quan cha được cấu hình
            List<DigitizingReportDto.Agency> agencyTransList = new ArrayList<>();
            var rootAgencys = getRootAgencies(agencyIds);
            var getObjUrl = microservice.basedataUri("agency/--by-parent-agency?arr-id=" + String.join(",", rootAgencys)).toUriString();
            var jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
            var jsonArr = new JSONArray(jsonString);

            if (Objects.nonNull(jsonArr)) {
                for (int i = 0; i < jsonArr.length(); i++) {
                    String agencyIdTemp = jsonArr.getJSONObject(i).get("id").toString();
                    var target = new DigitizingReportDto.Agency();
                    target.setIdAgency(agencyIdTemp);
                    target.setName(jsonArr.getJSONObject(i).get("name").toString());

                    agencyTransList.add(target);
                }
            }

            //var resultDB = aggregateResults(dossiers);
            //var aggregateResultsFinal = aggregateResultsOptimized(dossiers, agencyTransList);

            var resultDB = aggregateResults(dossiers);
            var aggregateResultsFinal = aggregateResultsFinal(resultDB, agencyTransList);

            return aggregateResultsFinal;
        } catch (Exception e) {
            logger.error("Error findDossiersWithConditions: " + e.getMessage());
        }

        return null;
    }

    public PageImpl<DetailGeneralReportDto.PageResult> getPaymentReportDetail(int type, List<String> agencyIds, String procedureName, String fromDateString, String toDateString, List<String> ignoreProcedureIds, Pageable pageable) {
        logger.info("Begin getPaymentReportDetail");
        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);
            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());
            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);
            Criteria criteria;
            switch (type) {
                case 5: // trực tiếp
                    criteria = this.buildFinancialObligationDirectCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 6: // trực tuyến
                    criteria = this.buildFinancialObligationOnlineCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 7: // số hồ sơ thanh toán trực tuyến
                    criteria = this.buildPaymentOnlineCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                default:
                    criteria = this.buildFinancialObligationCriteria(fromDate, toDate, agencyObjectIds);
            }

            if (procedureName != null){
                criteria.and("procedure.name").is(procedureName);
            }

            if (ignoreProcedureIds != null && !ignoreProcedureIds.isEmpty()) {
                List<ObjectId> ignoreProcedureObjIds = this.mapListStringToObjectId(ignoreProcedureIds);
                if (!ignoreProcedureObjIds.isEmpty()){
                    criteria.and("procedureLevel._id").nin(ignoreProcedureObjIds);
                }
            }

            Query query = new Query().addCriteria(criteria).with(pageable);
            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            List<DetailGeneralReportDto.PageResult> qniEtlDossiers = IntStream.range(0, dossiers.size())
                    .mapToObj(i -> this.mapToDossierResponse(dossiers.get(i), i))
                    .collect(Collectors.toList());

            long count = mongoTemplate.count(query.skip(-1).limit(-1), QNIETLDossier.class);

            return new PageImpl<>(qniEtlDossiers, pageable, count);

        } catch (Exception e) {
            logger.error("Error getPaymentReportDetail: " + e.getMessage());
        }

        logger.info("End getPaymentReportDetail");
        return null;
    }

    public List<PaymentReportDto> aggregateResults(List<QNIETLDossier> dossiers) {
        try {
            // Bản đồ lưu trữ kết quả gộp theo agencyId và procedureId
            Map<String, Map<String, Object>> aggregatedResults = new HashMap<>();

            // Tập hợp để theo dõi những procedureId đã đếm cho procedureUsingPaymentOnline
            Set<String> countedProcedureIds = new HashSet<>();

            // Lặp qua danh sách Dossier để gộp kết quả
            for (QNIETLDossier item : dossiers) {
                String agencyId = item.getAgency().getId();  // Lấy agencyId
                String procedureId = item.getProcedure().getId();  // Lấy procedureId
                String procedureName = item.getProcedure().getName();

                // Tạo key cho nhóm theo agencyId
                String agencyKey = agencyId + "_" + item.getAgency().getName();

                // Kiểm tra nếu key chưa tồn tại trong aggregatedResults thì khởi tạo
                if (!aggregatedResults.containsKey(agencyKey)) {
                    Map<String, Object> agencyData = new HashMap<>();
                    agencyData.put("agencyIdAgency", agencyId);
                    agencyData.put("agencyName", item.getAgency().getName());
                    agencyData.put("procedurePayment", new ArrayList<Map<String, Object>>());
                    agencyData.put("procedureUsingPayment", 0);
                    agencyData.put("procedureUsingPaymentOnline", 0); // Thêm trường mới

                    aggregatedResults.put(agencyKey, agencyData);
                }

                // Lấy dữ liệu gộp cho agency
                Map<String, Object> agencyData = aggregatedResults.get(agencyKey);

                // Tìm kiếm nếu procedure đã tồn tại trong danh sách procedurePayment
                List<Map<String, Object>> procedurePayments = (List<Map<String, Object>>) agencyData.get("procedurePayment");
                boolean procedureExists = false;

                for (Map<String, Object> payment : procedurePayments) {
                    if (payment.get("procedureName").equals(procedureName)) {
                        // Cập nhật thông tin nếu procedureName đã tồn tại
                        payment.put("financialObligationOnline", (Integer) payment.get("financialObligationOnline") + (item.getFinancialObligationOnlineCondition() ? 1 : 0));
                        payment.put("financialObligationDirect", (Integer) payment.get("financialObligationDirect") + (item.getFinancialObligationDirectCondition() ? 1 : 0));
                        payment.put("paymentOnline", (Integer) payment.get("paymentOnline") + (item.getPaymentOnlineCondition() ? 1 : 0));

                        // Cập nhật financialObligation
                        int financialObligation = (Integer) payment.get("financialObligation");
                        if (item.getFinancialObligationOnlineCondition()) {
                            financialObligation++;
                        }
                        if (item.getFinancialObligationDirectCondition()) {
                            financialObligation++;
                        }
                        payment.put("financialObligation", financialObligation);

                        // Tính percentPaymentOnline
                        Integer paymentOnlineValue = (Integer) payment.get("paymentOnline");
                        Double financialObligationValue = payment.get("financialObligation") instanceof Integer
                                ? ((Integer) payment.get("financialObligation")).doubleValue()
                                : (Double) payment.get("financialObligation");

                        if (paymentOnlineValue != null && financialObligationValue != null && financialObligationValue > 0) {
                            double percentPaymentOnline = (double) paymentOnlineValue / financialObligationValue * 100;
                            payment.put("percentPaymentOnline", percentPaymentOnline);
                        } else {
                            payment.put("percentPaymentOnline", 0.0);
                        }

                        procedureExists = true;
                        break;
                    }
                }

                // Nếu procedureId không tồn tại, thêm mới vào danh sách
                if (!procedureExists) {
                    Map<String, Object> newPayment = new HashMap<>();
                    newPayment.put("financialObligationOnline", item.getFinancialObligationOnlineCondition() ? 1 : 0);
                    newPayment.put("financialObligationDirect", item.getFinancialObligationDirectCondition() ? 1 : 0);
                    newPayment.put("paymentOnline", item.getPaymentOnlineCondition() ? 1 : 0);
                    newPayment.put("agencyBabyIdAgency", item.getAgency().getId());
                    newPayment.put("agencyBabyName", item.getAgency().getName());
                    newPayment.put("procedureName", item.getProcedure().getName());
                    newPayment.put("procedureId", procedureId);

                    int financialObligation = (item.getFinancialObligationOnlineCondition() ? 1 : 0) + (item.getFinancialObligationDirectCondition() ? 1 : 0);
                    newPayment.put("financialObligation", financialObligation);

                    double percentPaymentOnline = (financialObligation > 0)
                            ? ((Integer) newPayment.get("paymentOnline")).doubleValue() / (double) financialObligation * 100
                            : 0.0;
                    newPayment.put("percentPaymentOnline", percentPaymentOnline);

                    procedurePayments.add(newPayment);
                }

                // Nếu paymentOnline > 0 và chưa được đếm, tăng procedureUsingPaymentOnline
                if (item.getPaymentOnlineCondition() && !countedProcedureIds.contains(procedureId)) {
                    agencyData.put("procedureUsingPaymentOnline", (Integer) agencyData.get("procedureUsingPaymentOnline") + 1);
                    countedProcedureIds.add(procedureId);
                }
            }

            // Chuyển đổi kết quả gộp thành danh sách PaymentReportDto
            List<PaymentReportDto> finalResult = new ArrayList<>();
            for (Map<String, Object> item : aggregatedResults.values()) {
                PaymentReportDto reportDto = new PaymentReportDto();
                reportDto.setAgencyIdAgency((String) item.get("agencyIdAgency"));
                reportDto.setAgencyName((String) item.get("agencyName"));
                reportDto.setProcedurePayment((List<PaymentReportDto.ProcedurePaymentDto>) item.get("procedurePayment"));
                reportDto.setProcedureUsingPayment(reportDto.getProcedurePayment().size());
                reportDto.setProcedureUsingPaymentOnline((Integer) item.get("procedureUsingPaymentOnline"));
                reportDto.setPercentProcedureUsingPayment(reportDto.getProcedureUsingPayment() > 0 ? (double) reportDto.getProcedureUsingPaymentOnline() / reportDto.getProcedureUsingPayment() * 100 : 0.0);
                finalResult.add(reportDto);
            }

            return finalResult;
        } catch (Exception e) {
            logger.error("error aggregateResults: ", e);
        }

        return null;
    }

    public List<PaymentReportDto> aggregateResultsFinal(List<PaymentReportDto> paymentReports, List<DigitizingReportDto.Agency> agencyTransList) {
        // Bản đồ agencyId -> agencyName
        Map<String, String> agencyMap = new HashMap<>();
        for (DigitizingReportDto.Agency agency : agencyTransList) {
            agencyMap.put(agency.getId(), agency.getName());
        }

        // Danh sách lưu trữ kết quả cuối cùng
        List<PaymentReportDto> finalResult = new ArrayList<>();

        // Lặp qua từng agency trong agencyMap
        for (Map.Entry<String, String> entry : agencyMap.entrySet()) {
            String agencyId = entry.getKey();
            String agencyName = entry.getValue();

            // Lấy danh sách agencyTemp cho agency hiện tại (list Id)
            AgencyFilterReportQniResponse agencyTemp = null;
            List<String> agencyTemps = null;
            try {
                agencyTemp = agencyFilterReportQniService.getDocumentsByAgencyId(agencyId);
                if (agencyTemp != null) {
                    agencyTemps = new ArrayList<>(uniqueStringAgency(agencyTemp.getIdFilter()));
                }
            } catch (Exception e) {
                continue;
            }

            // Lọc danh sách PaymentReportDto hiện tại theo agencyTemp
            List<String> finalAgencyTemps = agencyTemps;
            List<PaymentReportDto> filteredReports = paymentReports.stream()
                    .filter(report -> finalAgencyTemps.contains(report.getAgencyIdAgency()))
                    .collect(Collectors.toList());

            // Bản đồ gộp theo procedureId
            Map<String, PaymentReportDto.ProcedurePaymentDto> aggregatedProcedurePayments = new HashMap<>();

            ObjectMapper objectMapper = new ObjectMapper(); // Sử dụng ObjectMapper để chuyển đổi dữ liệu
            Set<String> countedProcedureNames = new HashSet<>(); // Để kiểm tra procedureId đã đếm chưa
            int procedureUsingPaymentOnline = 0; // Đếm số procedure với paymentOnline > 0

            // Lặp qua danh sách filteredReports để gộp các procedurePayment
            for (PaymentReportDto report : filteredReports) {
                for (Object procedurePayment : report.getProcedurePayment()) {
                    PaymentReportDto.ProcedurePaymentDto procedurePaymentDto;

                    // Kiểm tra xem procedurePayment có phải là HashMap không
                    if (procedurePayment instanceof HashMap) {
                        // Chuyển đổi từ HashMap sang ProcedurePaymentDto
                        procedurePaymentDto = objectMapper.convertValue(procedurePayment, PaymentReportDto.ProcedurePaymentDto.class);
                    } else {
                        // Nếu không phải HashMap, thì casting sang ProcedurePaymentDto
                        procedurePaymentDto = (PaymentReportDto.ProcedurePaymentDto) procedurePayment;
                    }

                    String procedureName = procedurePaymentDto.getProcedureName();

                    // Nếu đã có procedureId trong bản đồ, gộp kết quả
                    if (aggregatedProcedurePayments.containsKey(procedureName)) {
                        PaymentReportDto.ProcedurePaymentDto existingProcedurePayment = aggregatedProcedurePayments.get(procedureName);
                        existingProcedurePayment.setFinancialObligationOnline(
                                existingProcedurePayment.getFinancialObligationOnline() + procedurePaymentDto.getFinancialObligationOnline());
                        existingProcedurePayment.setFinancialObligationDirect(
                                existingProcedurePayment.getFinancialObligationDirect() + procedurePaymentDto.getFinancialObligationDirect());
                        existingProcedurePayment.setPaymentOnline(
                                existingProcedurePayment.getPaymentOnline() + procedurePaymentDto.getPaymentOnline());
                        existingProcedurePayment.setFinancialObligation(
                                existingProcedurePayment.getFinancialObligation() + procedurePaymentDto.getFinancialObligation());
                        existingProcedurePayment.setPercentPaymentOnline(existingProcedurePayment.getFinancialObligation() > 0 ? existingProcedurePayment.getPaymentOnline() / existingProcedurePayment.getFinancialObligation() * 100 : 0.0);
                    } else {
                        // Nếu chưa có, thêm mới procedurePayment
                        aggregatedProcedurePayments.put(procedureName, procedurePaymentDto);
                    }

                    // Nếu paymentOnline > 0 và procedureId chưa được đếm, tăng biến procedureUsingPaymentOnline
                    if (procedurePaymentDto.getPaymentOnline() > 0 && !countedProcedureNames.contains(procedureName)) {
                        procedureUsingPaymentOnline++;
                        countedProcedureNames.add(procedureName); // Đánh dấu procedureId đã được đếm
                    }
                }
            }

            // Tạo PaymentReportDto cho agency hiện tại
            PaymentReportDto aggregatedReport = new PaymentReportDto();
            aggregatedReport.setAgencyIdAgency(agencyId);
            aggregatedReport.setAgencyName(agencyName);
            aggregatedReport.setProcedurePayment(new ArrayList<>(aggregatedProcedurePayments.values()));
            aggregatedReport.setProcedureUsingPayment(aggregatedReport.getProcedurePayment().size());
            aggregatedReport.setProcedureUsingPaymentOnline(procedureUsingPaymentOnline);

            // Tính phần trăm procedure sử dụng Payment Online
            if (aggregatedReport.getProcedureUsingPayment() > 0) {
                aggregatedReport.setPercentProcedureUsingPayment(
                        (double) aggregatedReport.getProcedureUsingPaymentOnline() / aggregatedReport.getProcedureUsingPayment() * 100);
            } else {
                aggregatedReport.setPercentProcedureUsingPayment(0);
            }

            // Thêm kết quả vào finalResult
            finalResult.add(aggregatedReport);
        }

        return finalResult;
    }

    private DetailGeneralReportDto.PageResult mapToDossierResponse(QNIETLDossier qnietlDossier, int index){
        try{
            TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            df.setTimeZone(timezone);
            DetailGeneralReportDto.PageResult result = new DetailGeneralReportDto.PageResult();
            result.setNo(index + 1);
            if (qnietlDossier.getAcceptedDate() != null) {
                result.setAcceptedDate(df.format(qnietlDossier.getAcceptedDate()));
            }
            result.setApplicantOwnerFullName(qnietlDossier.getApplicant().getOwnerFullName());
            result.setDossierCode(qnietlDossier.getCode());
            result.setId(qnietlDossier.getId());
            result.setApplicantPhoneNumber(qnietlDossier.getApplicant().getPhoneNumber());
            if (qnietlDossier.getAppointmentDate() != null) {
                result.setAppointmentDate(df.format(qnietlDossier.getAppointmentDate()));
            }
            if(qnietlDossier.getCompletedDate() != null){
                result.setCompletedDate(df.format(qnietlDossier.getCompletedDate()));
            }
            result.setNoiDungYeuCauGiaiQuyet(qnietlDossier.getApplicant().getNoiDungYeuCauGiaiQuyet());
            result.setProcedureName(qnietlDossier.getProcedure().getName());
            result.setSectorName(qnietlDossier.getSector().getName());
            result.setCurTaskAgencyName(
                    qnietlDossier.getCurrentTask() != null ? qnietlDossier.getCurrentTask().getAgency().getName() : ""
            );
            result.setAssigneeFullname(
                    qnietlDossier.getCurrentTask() != null ? qnietlDossier.getCurrentTask().getAssignee().getFullname(): ""
            );
            result.setDossierStatusName(
                    qnietlDossier.getDossierStatus() != null ? qnietlDossier.getDossierStatus().getName() : ""
            );
            result.setApplyMethod(qnietlDossier.getApplyMethod().getName());
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                result.setHideSecurityInformation(result.getApplicantOwnerFullName(), result.getAssigneeFullname());
            }
            if (this.isHideSecurityInf && Context.getPermission(this.ignoreHideSecurityInfPermission) == null && !isAdmin){
                result.setApplicantPhoneNumber(HideSecurityInformationHelper.maskPhoneNumber(qnietlDossier.getApplicant().getPhoneNumber()));
            }
            return result;
        }catch (Exception e){
            logger.error("Error with code: {}", qnietlDossier.getCode());
            return null;
        }
    }

    public ResponseEntity<Object> exportDossierPaymentOnlineDetail(String fromDate, String toDate,
                                                            List<String> agencyIds,
                                                            Integer type, String procedureName, List<String> ignoreProcedureIds) {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfTimestamp = new SimpleDateFormat("yyyyMMddHHmm");
        DateFormat dfCurrentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dfTimestamp.setTimeZone(timezone);
        dfCurrentDate.setTimeZone(timezone);
        String timestamp = dfTimestamp.format(date);
        String currentDate = dfCurrentDate.format(date);
        String fromDateReport = fromDate.substring(8, 10) + "/" + fromDate.substring(5, 7) + "/" + fromDate.substring(0, 4);
        String toDateReport = toDate.substring(8, 10) + "/" + toDate.substring(5, 7) + "/" + toDate.substring(0, 4);
        String filename = timestamp + "-danh-sach-ho-so.xlsx";
        byte[] resource = new byte[0];
        try {
            InputStream is = resourceTemplateDossierStatisticAssigneeQNIV2.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            var itemDtos = getDataExcelDto(fromDate, toDate, agencyIds, type, procedureName, ignoreProcedureIds);

            context.putVar("textBanner", translator.toLocale("lang.word.gov"));
            context.putVar("subtextBanner", translator.toLocale("lang.word.ifh"));

            context.putVar("title", translator.toLocale("lang.word.dossier-statistic-title"));
            context.putVar("subTitle", translator.toLocale("lang.word.dossier-statistic-fromdate-todate", new String[]{fromDateReport, toDateReport}));
            context.putVar("currentDate", translator.toLocale("lang.word.dossier-statistic-current-date", new String[]{currentDate}));
            context.putVar("no", translator.toLocale("lang.word.no"));
            context.putVar("dossierCode", translator.toLocale("lang.word.dossier-statistic-dossier-code"));
            context.putVar("procedureName", translator.toLocale("lang.word.dossier-statistic-procedure-name"));
            context.putVar("sectorName", translator.toLocale("lang.word.dossier-statistic-sector-name"));
            context.putVar("noiDungYeuCauGiaiQuyet", translator.toLocale("lang.word.dossier-statistic-noidungyeucaugiaiquyet"));
            context.putVar("acceptedDate", translator.toLocale("lang.word.dossier-statistic-accepted-date"));
            context.putVar("appointmentDate", translator.toLocale("lang.word.dossier-statistic-appointment-date"));
            context.putVar("completedDate", translator.toLocale("lang.word.dossier-statistic-completed-date"));
            context.putVar("applicantOwnerFullName", translator.toLocale("lang.word.dossier-statistic-applicant-ownerfullname"));
            context.putVar("applicantPhoneNumber", translator.toLocale("lang.word.dossier-statistic-applicant-phonenumber"));
            context.putVar("assigneeFullname", translator.toLocale("lang.word.dossier-statistic-assignee-fullname"));
            context.putVar("dossierStatusName", translator.toLocale("lang.word.dossier-statistic-status-name"));
            context.putVar("applyMethod", translator.toLocale("lang.word.dossier-statistic-applied-method"));
            context.putVar("writer", translator.toLocale("lang.word.dossier-statistic-writer"));
            context.putVar("reporter", translator.toLocale("lang.word.dossier-statistic-reporter"));
            context.putVar("itemDtos", itemDtos);

            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            resource = outputStream.toByteArray();
        } catch (Exception ex) {
            logger.info("exportDossierStatistic012020Detail error:" + ex.getMessage());
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(resource);
    }

    public List<DetailGeneralReportDto.PageResult> getDataExcelDto(String fromDateString,
                                                                   String toDateString,
                                                                   List<String> agencyIds,
                                                                   Integer type,
                                                                   String procedureName,
                                                                   List<String> ignoreProcedureIds) {
        logger.info("Begin getPaymentReportDetail");
        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);
            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());
            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);
            Criteria criteria;
            switch (type) {
                case 5: // trực tiếp
                    criteria = this.buildFinancialObligationDirectCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 6: // trực tuyến
                    criteria = this.buildFinancialObligationOnlineCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 7: // số hồ sơ thanh toán trực tuyến
                    criteria = this.buildPaymentOnlineCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                default: // tổng số hồ sơ có yêu cầu nghĩa vụ tài chính
                    criteria = this.buildFinancialObligationCriteria(fromDate, toDate, agencyObjectIds);
            }

            if (procedureName != null){
                criteria.and("procedure.name").is(procedureName);
            }

            if (ignoreProcedureIds != null && !ignoreProcedureIds.isEmpty()) {
                List<ObjectId> ignoreProcedureObjIds = this.mapListStringToObjectId(ignoreProcedureIds);
                if (!ignoreProcedureObjIds.isEmpty()){
                    criteria.and("procedureLevel._id").nin(ignoreProcedureObjIds);
                }
            }

            Query query = new Query().addCriteria(criteria);

            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            return IntStream.range(0, dossiers.size())
                    .mapToObj(i -> this.mapToDossierResponse(dossiers.get(i), i))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Error export getPaymentReportDetail: " + e.getMessage());
        }

        logger.info("End export getPaymentReportDetail");
        return null;
    }

    private Criteria buildFinancialObligationCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        return new Criteria().andOperator(
                Criteria.where("acceptedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null),
                Criteria.where("procedureHavePayment").is(true)
//                new Criteria().orOperator(
//                        Criteria.where("financialObligationsDate").exists(true),
//                        Criteria.where("isPaymentOnline").is(true)
//                )
        );
    }

    private Criteria buildFinancialObligationDirectCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        return new Criteria().andOperator(
                Criteria.where("acceptedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null),
                Criteria.where("applyMethod._id").is(1),
                Criteria.where("procedureHavePayment").is(true)
//                new Criteria().orOperator(
//                        Criteria.where("financialObligationsDate").exists(true),
//                        Criteria.where("isPaymentOnline").is(true)
//                )
        );
    }

    private Criteria buildFinancialObligationOnlineCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        return new Criteria().andOperator(
                Criteria.where("acceptedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null),
                Criteria.where("applyMethod._id").is(0),
                Criteria.where("procedureHavePayment").is(true)
//                new Criteria().orOperator(
//                        Criteria.where("financialObligationsDate").exists(true),
//                        Criteria.where("isPaymentOnline").is(true)
//                )
        );
    }

    private Criteria buildPaymentOnlineCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        return new Criteria().andOperator(
                Criteria.where("acceptedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null),
                Criteria.where("procedureHavePayment").is(true),
                Criteria.where("isPaymentOnline").is(true)
        );
    }

    public ImportResponseDto updateIsPaymentProcedure(String fromDateString, String toDateString, Boolean ignoreExist) {
        Query query = new Query();
        logger.info("Begin update procedure have payment");
        var results = new ImportResponseDto();
        AtomicInteger successRow = new AtomicInteger();
        List<String> errorMessages = new ArrayList<>();
        List<String> listProcedureIdHavePayment = new ArrayList<>();
        try{
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);
            query.addCriteria(Criteria.where("appliedDate").gte(fromDate).lte(toDate));
            if (!ignoreExist) {
                query.addCriteria(Criteria.where("procedureHavePayment").exists(false));
            }
            query.addCriteria(new Criteria().andOperator(
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));
            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);
            for ( QNIETLDossier dossier : dossiers ) {
                try{
                    String procedureId = dossier.getProcedure().getId();
                    if (listProcedureIdHavePayment.contains(procedureId)){
                        dossier.setProcedureHavePayment(true);
                        mongoTemplate.save(dossier);
                        successRow.getAndIncrement();
                    }else{
                        Boolean checkProcedureHavePayment = this.checkIsProcedureHavePayment(dossier);
                        if (checkProcedureHavePayment != null){
                            if (checkProcedureHavePayment){
                                listProcedureIdHavePayment.add(procedureId);
                            }
                            dossier.setProcedureHavePayment(checkProcedureHavePayment);
                            mongoTemplate.save(dossier);
                            successRow.getAndIncrement();
                        }else {
                            errorMessages.add(dossier.getCode() + " : " + "Check procedure have payment error");
                        }
                    }
                } catch (Exception e) {
                    errorMessages.add(dossier.getCode() + " : " + e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("Error in update procedure have payment: " + e.getMessage(), e);
            errorMessages.add(e.getMessage());
        }
        results.setSuccessRows(successRow.get());
        results.setErrorMessages(errorMessages);
        logger.info("End update procedure have payment");
        return results;
    }

    private Boolean checkIsProcedureHavePayment (QNIETLDossier dossier){
        try{
            String url = microservice.basepadUri("procost/?spec=page&status=1&page=0&size=10&procedure-id=" + dossier.getProcedure().getId()).toUriString();
            String rawResponse = MicroserviceExchange.get(restTemplate, url, String.class);
            JsonObject procedureCost = gson.fromJson(rawResponse, JsonObject.class);
            if (procedureCost.has("content")){
                return procedureCost.get("content").getAsJsonArray().size() > 0;
            }
            return false;
        }catch (Exception e){
            logger.error("Error in checkIsProcedureHavePayment: " + e.getMessage(), e);
            return null;
        }
    }

    public ImportResponseDto updateIsPaymentProcedureV2(String fromDateString, String toDateString, Boolean ignoreExist) {
        Query query = new Query();
        logger.info("Begin update procedure have paymentV2");
        var results = new ImportResponseDto();
        AtomicInteger successRow = new AtomicInteger();
        List<String> errorMessages = new ArrayList<>();
        try{
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);
            query.addCriteria(Criteria.where("appliedDate").gte(fromDate).lte(toDate));
            if (!ignoreExist) {
                query.addCriteria(Criteria.where("procedureHavePayment").exists(false));
            }
            query.addCriteria(new Criteria().andOperator(
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));
            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            if (dossiers.isEmpty()){
                results.setSuccessRows(0);
                return results;
            }

            List<String> ids = dossiers.stream()
                    .map(QNIETLDossier::getId)
                    .collect(Collectors.toList());

            JsonArray checkDossierFeeResult = this.checkDossierFee(ids);

            List<ObjectId> updateDossierIds = new ArrayList<>();
            List<String> updateDossierIdsString = new ArrayList<>();
            for (JsonElement element : checkDossierFeeResult) {
                updateDossierIds.add(new ObjectId(element.getAsString()));
                updateDossierIdsString.add(element.getAsString());
            }

            Query queryDossierUpdate = new Query();
            queryDossierUpdate.addCriteria(Criteria.where("_id").in(updateDossierIds));

            Update update = new Update();
            update.set("procedureHavePayment", true);

            successRow.set(updateDossierIds.size());

            mongoTemplate.updateMulti(queryDossierUpdate, update, QNIETLDossier.class);

            if (ignoreExist){
                List<String> ignoreUpdateIds = ids.stream()
                        .filter(num -> !updateDossierIdsString.contains(num))
                        .collect(Collectors.toList());

                this.updateFieldToNullData(ignoreUpdateIds, "procedureHavePayment");
            }


        } catch (Exception e) {
            logger.error("Error in update procedure have payment: " + e.getMessage(), e);
            errorMessages.add(e.getMessage());
        }
        results.setSuccessRows(successRow.get());
        results.setErrorMessages(errorMessages);
        logger.info("End update procedure have payment V2");
        return results;
    }

    private JsonArray checkDossierFee(List<String> listDossierId){
        String url = microservice.padmanUri("/dossier-fee/--check-dossier-fee-qni").toUriString();
//        String url = "http://localhost:8081/dossier-fee/--check-dossier-fee-qni";
        String rawResponse = MicroserviceExchange.postJson(restTemplate, url, listDossierId, String.class);
        return gson.fromJson(rawResponse, JsonArray.class);
    }

    private void updateFieldToNullData(List<String> dossierIds, String field){
        try{
            if (dossierIds == null || dossierIds.isEmpty()) {
                return;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where("_id").in(dossierIds));

            Update update = new Update();
            update.unset(field);

            mongoTemplate.updateMulti(query, update, QNIETLDossier.class);
        } catch (Exception e) {
            logger.error("Lỗi khi cập nhật field '{}' thành null: {}", field, e.getMessage(), e);
        }
    }

    private List<ObjectId> mapListStringToObjectId(List<String> listStringIds){
        try{
            return listStringIds.stream()
                    .filter(ObjectId::isValid)
                    .map(ObjectId::new)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error in mapListStringToObjectId: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
}
