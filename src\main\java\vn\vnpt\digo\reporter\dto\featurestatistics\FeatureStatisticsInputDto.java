package vn.vnpt.digo.reporter.dto.featurestatistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeatureStatisticsInputDto {

    @NonNull
    @NotBlank
    private String appCode;

    @NonNull
    @NotBlank
    private String featureCode;

    @NonNull
    @Min(0)
    private Long counter;

}
