package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusStatisticsByTag implements Serializable {

    private long total;

    private StatusTotal status;

    private List<StatusStatisticsByTagTotalDetail> detail;

    public void setTotal() {
        this.total = this.status.getWaitingReception() + this.status.getInprogress() + this.status.getCompleted() + this.status.getCanceled();
    }
}
