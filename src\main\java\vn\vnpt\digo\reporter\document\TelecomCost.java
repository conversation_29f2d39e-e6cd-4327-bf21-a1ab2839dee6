package vn.vnpt.digo.reporter.document;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.pojo.SubscriptionType;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "telecomCost")
@CompoundIndex(name = "unique_telecom_cost", def = "{'subscriptionCode': 1, 'month': 1, 'year': 1, 'subscriptionType': 1}", unique = true)
public class TelecomCost {

    @Id
    private ObjectId id;

    @NotNull
    private String phoneNumber;

    @NotNull
    private Integer month;

    @NotNull
    private Integer year;

    private String paymentCode;

    @NotNull
    private String subscriptionCode;

    @NotNull
    private SubscriptionType subscriptionType;

    @NotNull
    private Double amount;

    public TelecomCost(String phoneNumber, Integer year, Integer month, String paymentCode, String subscriptionCode, SubscriptionType subscriptionType, Double amount) {
        this.phoneNumber = phoneNumber;
        this.year = year;
        this.month = month;
        this.paymentCode = paymentCode;
        this.subscriptionCode = subscriptionCode;
        this.subscriptionType = subscriptionType;
        this.amount = amount;
    }
}
