/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.util;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Component
public class RequestUtils {

    @Autowired
    private HttpServletRequest httpServletRequest;

    private static HttpServletRequest request;

    @PostConstruct
    private void initStaticDao() {
        request = this.httpServletRequest;
    }

    public static <T extends Object> T getParammeter(String key, Class<T> type, Object defaultValue) {
        Object obj = null;
        if (type == ObjectId.class) {
            obj = (Objects.nonNull(request.getParameter(key))
                    && ObjectId.isValid(request.getParameter(key))) ? new ObjectId(request.getParameter(key)) : defaultValue;
        } else if (type == Integer.class) {
            obj = (Objects.nonNull(request.getParameter(key))
                    && !request.getParameter(key).isBlank()) ? Integer.parseInt(request.getParameter(key)) : defaultValue;
        } else if (type == Double.class) {
            obj = (Objects.nonNull(request.getParameter(key))
                    && !request.getParameter(key).isBlank()) ? Double.parseDouble(request.getParameter(key)) : defaultValue;
        } else if (type == Long.class) {
            obj = (Objects.nonNull(request.getParameter(key))
                    && !request.getParameter(key).isBlank()) ? Long.parseLong(request.getParameter(key)) : defaultValue;
        } else if (type == Boolean.class) {
            obj = (Objects.nonNull(request.getParameter(key))
                    && !request.getParameter(key).isBlank()) ? Boolean.valueOf(request.getParameter(key)) : defaultValue;
        } else {
            obj = (Objects.nonNull(request.getParameter(key))
                    && !request.getParameter(key).isBlank()) ? String.valueOf(request.getParameter(key)) : defaultValue;
        }
        return type.cast(obj);
    }

    public static <T extends Object> List<T> getParameterValues(String key, Class<T> type) {
        String[] innerArray = request.getParameterValues(key);
        List<String> outerArray = new ArrayList<>();
        List<T> returnArray = new ArrayList<>();
        if (innerArray != null && innerArray.length > 0) {
            for (var item : innerArray) {
                List<String> temp = Arrays.asList(item.trim().split("\\s*,\\s*"));
                outerArray.addAll(temp);
                outerArray.removeAll(Arrays.asList("", null));
            }
        }
        for (var item : outerArray) {
            Object obj = null;
            if (type == ObjectId.class) {
                obj = new ObjectId(item);
            } else if (type == String.class) {
                obj = item;
            } else if (type == Integer.class) {
                obj = Integer.parseInt(item);
            } else if (type == Double.class) {
                obj = Double.parseDouble(item);
            } else if (type == Long.class) {
                obj = Long.parseLong(item);
            } else if (type == Boolean.class) {
                obj = Boolean.valueOf(item);
            } else {
                obj = (item);
            }
            returnArray.add(type.cast(obj));
        }
        return returnArray;
    }

}
