/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import org.apache.commons.io.FilenameUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.service.TemplateFileStorageService;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.util.BlacklistKeyword;
import vn.vnpt.digo.reporter.util.Context;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/file")
@IcodeAuthorize("vnpt.permission.file")
public class FileController {

    @Value("${digo.file.extension.list}")
    private String extList;
    @Value("${digo.file.blacklist-keyword.allow}")
    private String allowKeyword;

    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private TemplateFileStorageService templateFileStorageService;

    // TODO
//    @DeleteMapping("")
//    public String deleteTemplateFileByPath(@RequestParam(value = "path", required = true) String path) {
//        return templateFileStorageService.deleteFileByPath(path);
//    }

    @PostMapping("")
    public TemplateFile uploadTemplateFile(@RequestParam("file") MultipartFile file) throws IOException {
        List<String> rolesToCheck = List.of(adminRoles.split(","));
        boolean  isAdmin  = Context.getListPemission(rolesToCheck);
        if(!isAdmin){
            // kiểm tra quyền admin
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
        }
        // Lay ten file và phan mo rong
        String filename = StringUtils.cleanPath(file.getOriginalFilename());
        String extension = FilenameUtils.getExtension(filename).toLowerCase();

        // Kiem tra phan mo rong file co nam trong danh sach cho phep khong
        if (!extList.contains(extension)) {
            logger.warn("Tu choi file do khong dung dinh dang: " + filename);
            return null;
        }

        // Kiem tra tu khoa nguy hiem
        boolean foundHarmfulKeyword = false;
        try {
            // Doc noi dung file
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);

            // Chuan hoa noi dung file (xoa khoang trang, ky tu dac biet, escape sequence)
            String sanitizedContent = BlacklistKeyword.sanitizeContent(content);

            // Dung regex de kiem tra tu khoa nguy hiem
            for (String word : BlacklistKeyword.BlacklistKeywordJava) {
                String regex = "\\b" + Pattern.quote(word) + "\\b\\s*\\(";  // Chi chan neu la goi ham
                if (Pattern.compile(regex).matcher(sanitizedContent).find() && !allowKeyword.contains(word)) {
                    foundHarmfulKeyword = true;
                    logger.warn("Phat hien tu khoa doc hai: " + word + " trong file: " + filename);
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error("Loi khi doc file: " + filename, ex);
            return null;
        }

        // Neu phat hien tu khoa nguy hiem, tu choi file
        if (foundHarmfulKeyword) {
            return null;
        } else {
            TemplateFile templateFile = templateFileStorageService.storeFile(file);
            return templateFile;
        }
    }

//    @PostMapping("")
//    public TemplateFile uploadTemplateFile(@RequestParam("file") MultipartFile file) throws IOException {
//        String filename = StringUtils.cleanPath(file.getOriginalFilename());
//        boolean foundHamfullKeyword = false;
//        String extention = FilenameUtils.getExtension(file.getOriginalFilename());
//        if(extList.contains(extention.toLowerCase())){
//            try {
//                String content = new String(file.getBytes(), StandardCharsets.UTF_8);
//
//                for ( String word : BlacklistKeyword.BlacklistKeywordJava) {
//                    if (content.contains(word) && !allowKeyword.contains(word)) {
//                        foundHamfullKeyword = true;
//                        break;
//                    }
//                }
//
//            }catch (Exception ex){
//                logger.info("Có lỗi khi đọc file");
//            }
//
//        }
//        if (foundHamfullKeyword){
//            return null;
//        } else {
//            TemplateFile templateFile = templateFileStorageService.storeFile(file);
//            return templateFile;
//        }
//    }

}
