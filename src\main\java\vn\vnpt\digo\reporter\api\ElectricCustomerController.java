package vn.vnpt.digo.reporter.api;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.ElectricCustomerInputDto;
import vn.vnpt.digo.reporter.dto.GetCustomerCodeByUserIdDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.service.ElectricCustomerService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/electric-customer")
@IcodeAuthorize("vnpt.permission.electriccustomer")
public class ElectricCustomerController {

    @Autowired
    private ElectricCustomerService electricCustomerService;

    Logger logger = LoggerFactory.getLogger(WaterCustomerController.class);

    @PutMapping("/--insert-or-update")
    public PostResponseDto insertOrUpdateElectricCustomer(HttpServletRequest request,
            @Valid @RequestBody @ModelAttribute ElectricCustomerInputDto electricCustomerInputDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PostResponseDto result = electricCustomerService.insertOrUpdateElectricCustomer(electricCustomerInputDto);
        logger.info("DIGO-Info: " + result.getId());

        return result;
    }

    // Digo 2516
    @GetMapping("/customer-code/--by-user-id")
    public List<GetCustomerCodeByUserIdDto> getCustomerCodeByUserId(HttpServletRequest request, @RequestParam(value = "user-id") ObjectId userId) {

        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetCustomerCodeByUserIdDto> customerCode = electricCustomerService.getCustomerCodeByUserId(userId);
        logger.info("DIGO-Info: " + customerCode.size());
        return customerCode;
    }
}
