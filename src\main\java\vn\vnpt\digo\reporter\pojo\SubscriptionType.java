/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionType implements Serializable {

    @Field("id")
    private ObjectId id;

    @JsonProperty("name")
    private String subscriptTypeName;

    @JsonIgnore
    private List<SubscriptionTypeName> name;

    public SubscriptionType(ObjectId id, List<SubscriptionTypeName> name) {
        this.id = id;
        this.name = name;
    }

    public SubscriptionType(ObjectId id, String subscriptTypeName) {
        this.id = id;
        this.subscriptTypeName = subscriptTypeName;
    }

    public void setName(Short localeId) {
        name.forEach(item -> {
            if (item.getLanguageId().equals(localeId)) {
                this.subscriptTypeName = item.getName();
            }
        });
    }
    
}
