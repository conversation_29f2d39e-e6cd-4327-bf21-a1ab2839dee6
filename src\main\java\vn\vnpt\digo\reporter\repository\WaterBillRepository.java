package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.WaterBill;
import vn.vnpt.digo.reporter.dto.GetWaterBillDto;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface WaterBillRepository extends MongoRepository<WaterBill, ObjectId> {

    @Query(value = "{'customerCode': ?0, 'month': ?1, 'year': ?2}", fields = "{'id': 1, 'month': 1, 'year': 1, 'paid': 1, 'consumedAmount': 1, 'paymentAmount': 1}")
    public Slice<GetWaterBillDto> getWaterBillByMonth(String customerCode, int month, int year,
            Pageable pageable);

    @Query(value = "{'customerCode': ?0}", fields = "{'id': 1, 'month': 1, 'year': 1, 'paid': 1, 'consumedAmount': 1, 'paymentAmount': 1}", sort = "{'month': -1, 'year': -1}")
    public Slice<GetWaterBillDto> getWaterBillByMonthAndYearDesc(String customerCode, Pageable pageable);

}
