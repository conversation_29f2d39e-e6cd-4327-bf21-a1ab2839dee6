/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.config;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Permission;

/**
 *
 * <AUTHOR>
 */

@Component
public class IcodeAuthorizationImpl {
    
    @Autowired
    private Environment env;
    
    public boolean hasPrivilege(String permissionKey, String attribute, String option) {
        String permission = env.getProperty(permissionKey);
        if(permission == null || permission.isBlank()){
            return true;
        }
        String permissionStr = Context.getJwtParameterValue("permissions");
        if(permissionStr == null || permissionStr.isBlank()){
            return false;
        }
        Type listType = new TypeToken<List<Permission>>() {}.getType();
        List<Permission> permissions = new Gson().fromJson(permissionStr, listType);
        for(var item : CollectionUtils.emptyIfNull(permissions)){
            if(item.getPermission().getCode().equalsIgnoreCase(permission)){
                if(Objects.isNull(item.getAttributes())){
                    return false;
                }
                JSONObject permissionAttribute = new Gson().fromJson(item.getAttributes(), JSONObject.class);
                JSONObject requiredAttribute = new Gson().fromJson(attribute, JSONObject.class);
                Iterator<String> keys = requiredAttribute.keys();
                while(keys.hasNext()) {
                    String key = keys.next();
                    
                    try{
                        if ( Objects.equals(requiredAttribute.get(key), permissionAttribute.get(key)) ){
                            continue;
                        }
                    }catch (JSONException ex){
                        return false;
                    }
                }
                return true;
            }
        }
        return false;
    }
    
    public boolean hasPrivilege(String permissionKey,  String option) {
        String permission = env.getProperty(permissionKey);
        if(permission == null || permission.isBlank()){
            return true;
        }
        String permissionStr = Context.getJwtParameterValue("permissions");
        if(permissionStr == null || permissionStr.isBlank()){
            return false;
        }
        List<String> requiredPermissions = new ArrayList<>(Arrays.asList(permission.split(",")));
        List<String> userPermissions = new ArrayList<>();
        Type listType = new TypeToken<List<Permission>>() {}.getType();
        List<Permission> permissions = new Gson().fromJson(permissionStr, listType);
        for(var item : CollectionUtils.emptyIfNull(permissions)){
            userPermissions.add(item.getPermission().getCode());
        }

        if(option.equalsIgnoreCase("any")){
            return CollectionUtils.containsAny(userPermissions, requiredPermissions);
        }
        return CollectionUtils.containsAll(userPermissions, requiredPermissions);
    } 
}

