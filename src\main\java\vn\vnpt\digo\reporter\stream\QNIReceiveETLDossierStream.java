package vn.vnpt.digo.reporter.stream;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.service.QNIETLDossierService;
import vn.vnpt.digo.reporter.stream.messaging.KTMReceiveETLDossierRequest;
import vn.vnpt.digo.reporter.stream.messaging.QNIReceiveETLDossierRequest;

import java.util.List;

@Component
@EnableBinding(QNIReceiveETLDossierRequest.class)
public class QNIReceiveETLDossierStream {
    @Autowired
    private QNIETLDossierService qnietlDossierService;
    @StreamListener(value = QNIReceiveETLDossierRequest.INPUT)
    public void qniDossierDataConsumerStream(Message<List<QNIETLDossier>> dossier) {
        List<QNIETLDossier> dossierMessage = dossier.getPayload();
        Gson gson = new Gson();
        String json = gson.toJson(dossierMessage);

        List<QNIETLDossier> dossierList = gson.fromJson(json, new TypeToken<List<QNIETLDossier>>() {}.getType());
        qnietlDossierService.addQNIETLDossierData(dossierList);
    }

}
