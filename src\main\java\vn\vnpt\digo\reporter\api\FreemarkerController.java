/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.service.FreemarkerService;
import vn.vnpt.digo.reporter.dto.FreemarkerReportDto;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/freemarker")
@IcodeAuthorize("vnpt.permission.freemarker")
public class FreemarkerController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateController.class);

    @Autowired
    private FreemarkerService freemarkerService;
    
    @PostMapping("/--report")
    public String reportFreemarker(@RequestParam(value = "id", required = true) String id, @RequestBody FreemarkerReportDto freemarkerReportDto) {
        return freemarkerService.report(id, freemarkerReportDto);
    }

    @GetMapping("/noitify")
    public String getNotify(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return "Gọi service thành công!";
    }
}
