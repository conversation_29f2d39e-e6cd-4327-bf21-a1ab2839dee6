package vn.vnpt.digo.reporter.service;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.Procedure;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.FrequentProcedureDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.ProcedureDto;
import vn.vnpt.digo.reporter.dto.PutProcedureDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.ProcedureImplementer;
import vn.vnpt.digo.reporter.pojo.ProcedureSector;
import vn.vnpt.digo.reporter.pojo.ProcedureTranslate;
import vn.vnpt.digo.reporter.repository.ProcedureRepository;
import vn.vnpt.digo.reporter.stream.ProcedureDossierQuantityStream;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class ProcedureService {

    @Autowired
    private Environment environment;

    @Autowired
    private Translator translator;

    @Autowired
    private ProcedureRepository procedureRepository;

    @Autowired
    ProcedureDossierQuantityStream procedureDossierQuantityStream;

    @Autowired
    private MongoTemplate mongoTemplate;

    Logger logger = org.slf4j.LoggerFactory.getLogger(ProcedureService.class);

    @Cacheable(value = "Procedure::getFrequentList")
    public FrequentProcedureDto getFrequentList(ObjectId agencyId, ObjectId citizenId, ObjectId enterpriseId) {
        ObjectId deploymentId = Context.getDeploymentId();
        Pageable pageReq = PageRequest.of(0, 3220);
        FrequentProcedureDto frequentProcedureDto = new FrequentProcedureDto();
        frequentProcedureDto.setAgencyId(agencyId);
        List<ProcedureDto> citizensTemp = procedureRepository.getProcedureFrequent(agencyId, citizenId, deploymentId, pageReq);
        List<ProcedureDto> enterprisesTemp = procedureRepository.getProcedureFrequent(agencyId, enterpriseId, deploymentId, pageReq);

        //Loc thu tuc trung ten
        List<ProcedureDto> citizens = filterDuplicate(citizensTemp);
        List<ProcedureDto> enterprises = filterDuplicate(enterprisesTemp);

        for (int i = 0; i < 10; i++) {
            citizens.get(i).setId(citizens.get(i).getOriginId());
            citizens.get(i).setTransName(translator.getCurrentLocaleId());
            citizens.get(i).setOrder(i + 1);
        }
        frequentProcedureDto.setCitizen(new ArrayList<>(citizens.subList(0,10)));
        for (int i = 0; i < 10; i++) {
            enterprises.get(i).setId(enterprises.get(i).getOriginId());
            enterprises.get(i).setTransName(translator.getCurrentLocaleId());
            enterprises.get(i).setOrder(i + 1);
        }
        frequentProcedureDto.setEnterprise(new ArrayList<>(enterprises.subList(0,10)));
        return frequentProcedureDto;
    }

    public List<ProcedureDto> filterDuplicate (List<ProcedureDto> result){
        Map<String, Integer> totalDossierQuantity = new HashMap<>();
        Map<String, ProcedureDto> uniqueProcedures = new HashMap<>();

        for (ProcedureDto procedure : result) {
            String name = procedure.getTranslate().get(0).getName();
            int currentQuantity = procedure.getDossierQuantity();

            // Cộng dồn dossierQuantity
            totalDossierQuantity.put(name, totalDossierQuantity.getOrDefault(name, 0) + currentQuantity);

            if (!uniqueProcedures.containsKey(name)) {
                uniqueProcedures.put(name, procedure);
            }
        }

        for (Map.Entry<String, ProcedureDto> entry : uniqueProcedures.entrySet()) {
            String name = entry.getKey();
            ProcedureDto procedure = entry.getValue();
            procedure.setDossierQuantity(totalDossierQuantity.get(name));
        }

        List<ProcedureDto> filteredResults = new ArrayList<>(uniqueProcedures.values());
        //sort lai giam dan
        filteredResults.sort(Comparator.comparingInt(ProcedureDto::getDossierQuantity).reversed());
        return filteredResults;
    }


    public List<ProcedureDto> getFrequentListV2() {
        ObjectId deploymentId = Context.getDeploymentId();
        Pageable pageReq = PageRequest.of(0, 3220);
        List<ProcedureDto>  frequentListTemp = procedureRepository.findAllSortDescending(deploymentId, pageReq);
        List<ProcedureDto> frequentList = filterDuplicate(frequentListTemp);
        for (int i = 0; i < 10; i++) {
            frequentList.get(i).setId(frequentList.get(i).getOriginId());
            if ( frequentList.get(i).getTranslate() != null && !frequentList.get(i).getTranslate().isEmpty() ) {
                frequentList.get(i).setTransName(translator.getCurrentLocaleId());
            };
            frequentList.get(i).setOrder(i + 1);
        }
        return new ArrayList<>(frequentList.subList(0,10));
    }

    @CacheEvict(value = {"getProcedureDossier"}, allEntries = true)
    public AffectedRowsDto updateProcedureFrequentQuantity(ObjectId procedureId, Integer quantity, String name) {
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        Procedure procedure = procedureRepository.getProcedure(procedureId);
        if (procedure != null) {
            try {
                int newQuantity = 0;
                newQuantity = procedure.getDossierQuantity() + quantity;
                procedure.setDossierQuantity(newQuantity);
                procedure.setTranslateName(name);
                procedureRepository.save(procedure);
                affectedRows.setAffectedRows(1);
            } catch (Exception e) {
                logger.info("DIGO-Info: " + e.toString());
            }
        }else{
            try{
                System.out.println("kimloannt "+name);
                PutProcedureDto procedureFrequent = new PutProcedureDto();
                procedureFrequent.setOriginId(procedureId);
                procedureFrequent.setAgency(new ArrayList<>());
                procedureFrequent.setSector(new ProcedureSector());
                procedureFrequent.setTranslateName(name);
                procedureFrequent.setDossierQuantity(1);
                procedureFrequent.setOrder(0);
                this.addProcedureFrequent(procedureFrequent);
            }catch (Exception e) {
                logger.info("DIGO-Info: " + e.toString());
            }
        }
        return affectedRows;
    }

    @CacheEvict(value = {"getProcedureDossier"}, allEntries = true)
    public AffectedRowsDto updateProcedureFrequent(ObjectId originId, PutProcedureDto putProcedure) {
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        Procedure procedure = procedureRepository.getProcedure(originId);
        List<ProcedureImplementer> implementerTemp = new ArrayList<>();
        implementerTemp.add(new ProcedureImplementer(new ObjectId("5f3f3c044e1bd312a6f3addf")));
        implementerTemp.add(new ProcedureImplementer(new ObjectId("5f3f3c2b4e1bd312a6f3ade0")));
        if (procedure != null) {
            try {
                if (Objects.nonNull(putProcedure.getAgency())) {
                    procedure.setAgency(putProcedure.getAgency());
                }
                if (Objects.nonNull(putProcedure.getSector())) {
                    procedure.setSector(putProcedure.getSector());
                }
                if (Objects.nonNull(putProcedure.getTranslate())) {
                    procedure.setTranslate(putProcedure.getTranslate());
                }
                if (Objects.nonNull(putProcedure.getDossierQuantity())) {
                    procedure.setDossierQuantity(putProcedure.getDossierQuantity());
                }
                if (Objects.nonNull(putProcedure.getImplementer()) && putProcedure.getImplementer().size() > 0) {
                    procedure.setImplementer(putProcedure.getImplementer());
                }else{
                    procedure.setImplementer(implementerTemp);
                }
                if (Objects.nonNull(putProcedure.getDeploymentId())) {
                    procedure.setDeploymentId(putProcedure.getDeploymentId());
                }
                if (Objects.nonNull(putProcedure.getOrder())) {
                    procedure.setOrder(putProcedure.getOrder());
                }
                procedureRepository.save(procedure);
                affectedRows.setAffectedRows(1);
            } catch (Exception e) {
                logger.info("DIGO-Info: " + e.toString());
            }
        }
        return affectedRows;
    }

    @CacheEvict(value = {"getProcedureDossier"}, allEntries = true)
    public PostResponseDto addProcedureFrequent(PutProcedureDto procedureFrequent) {
        ObjectId deploymentId = Context.getDeploymentId();
        List<ProcedureImplementer> implementerTemp = new ArrayList<>();
        implementerTemp.add(new ProcedureImplementer(new ObjectId("5f3f3c044e1bd312a6f3addf")));
        implementerTemp.add(new ProcedureImplementer(new ObjectId("5f3f3c2b4e1bd312a6f3ade0")));
        if (deploymentId != null) {
            Procedure procedure = procedureRepository.getProcedure(procedureFrequent.getOriginId());
            if (procedure == null) {
                Procedure newProcedure = new Procedure();
                newProcedure.setOriginId(procedureFrequent.getOriginId());
                newProcedure.setAgency(procedureFrequent.getAgency());
                newProcedure.setSector(procedureFrequent.getSector());
                newProcedure.setTranslate(procedureFrequent.getTranslate());
                newProcedure.setDossierQuantity(procedureFrequent.getDossierQuantity());
                if (Objects.nonNull(procedureFrequent.getImplementer()) && procedureFrequent.getImplementer().size() > 0) {
                    newProcedure.setImplementer(procedureFrequent.getImplementer());
                }else{
                    newProcedure.setImplementer(implementerTemp);
                }
                newProcedure.setOrder(procedureFrequent.getOrder());
                newProcedure.setDeploymentId(Context.getDeploymentId());
                try {
                    procedureRepository.save(newProcedure);
                    PostResponseDto res = new PostResponseDto(newProcedure.getId());
                    return res;
                } catch (Exception ex) {
                    throw new ExceptionInInitializerError(ex.getMessage());
                }
            } else {
                throw new DigoHttpException(10007, new String[]{translator.toLocale("lang.word.procedure")}, HttpServletResponse.SC_BAD_REQUEST);
            }
        } else {
            throw new DigoHttpException(10000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    @CacheEvict(value = {"getProcedureDossier"}, allEntries = true)
    public AffectedRowsDto deleteProcedureFrequent(ObjectId id) {
        Procedure procedure = procedureRepository.getProcedure(id);
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        if (procedure != null) {
            procedureRepository.deleteProcedureFrequentById(procedure.getId());
            affectedRows.setAffectedRows(1);
        }
        return affectedRows;
    }

    public Procedure getProcedure(ObjectId originId) {
        return procedureRepository.getProcedure(originId);
    }

    @CacheEvict(value = {"getProcedureDossier"}, allEntries = true)
    public Procedure save(Procedure procedure) {
        return procedureRepository.save(procedure);
    }

    @Cacheable("getProcedureDossier")
    public List<PutProcedureDto> getListAll() {
        Query query = new Query();
        query.addCriteria(Criteria.where("dossierQuantity").gte(1));
        List<Procedure> listProcedure = mongoTemplate.find(query, Procedure.class);
        List<PutProcedureDto> listResult = new ArrayList<>();
        for(Procedure item: listProcedure){
            PutProcedureDto procedure = new PutProcedureDto();
            procedure.setOriginId(item.getOriginId());
            procedure.setDossierQuantity(item.getDossierQuantity());
            listResult.add(procedure);
        }
        return listResult;
    }
}
