package vn.vnpt.digo.reporter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.ElectricBillInputDto;
import vn.vnpt.digo.reporter.dto.GetElectricBillDto;
import vn.vnpt.digo.reporter.dto.GetListElectricBillDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.service.ElectricBillService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/electric-bill")
@IcodeAuthorize("vnpt.permission.electricbill")
public class ElectricBillController {

    @Autowired
    public ElectricBillService electricBillService;

    Logger logger = LoggerFactory.getLogger(ElectricBillController.class);

    // Digo 2485
    @GetMapping("/--months")
    public Slice<GetListElectricBillDto> getElectricBillByMonth(HttpServletRequest request,
            @RequestParam(value = "customer-id", required = true) String customerId,
            @RequestParam(value = "month", required = false) Integer month,
            @RequestParam(value = "year", required = false) Integer year, Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<GetListElectricBillDto> electricBill = electricBillService.getElectricBillByMonth(customerId, month, year, pageable);
        logger.info(electricBill.getNumberOfElements() + "");

        return electricBill;
    }
    
    @GetMapping("/{id}")
    public GetElectricBillDto getElectricBill(HttpServletRequest request, @PathVariable(name = "id", required = true) ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        GetElectricBillDto electricBill = electricBillService.getElectricBillById(id);
        logger.info(electricBill.toString());
        return electricBill;
    }
    
    @PostMapping()
    public PostResponseDto addNewBillForCustomer(
            HttpServletRequest request,
            @Valid @RequestBody @ModelAttribute ElectricBillInputDto electricBillInputDto
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PostResponseDto responseIdDto = electricBillService.addNewBillForCustomer(electricBillInputDto);
        logger.info("DIGO-Info: " + responseIdDto);
        return responseIdDto;
    }

}
