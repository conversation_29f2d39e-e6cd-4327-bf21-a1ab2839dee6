package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.qni.DetailGeneralReportDto;
import vn.vnpt.digo.reporter.dto.qni.DigitizingReportDto;
import vn.vnpt.digo.reporter.dto.qni.ImportResponseDto;
import vn.vnpt.digo.reporter.dto.qni.PaymentReportDto;
import vn.vnpt.digo.reporter.service.DigitizingReportQNIService;
import vn.vnpt.digo.reporter.service.DigitizingReportQNIServiceV2;
import vn.vnpt.digo.reporter.service.PaymentReportQNIService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment-report-qni")
@IcodeAuthorize("vnpt.permission.paymentreportqni")
public class PaymentReportQNIController {

    Logger logger = LoggerFactory.getLogger(PaymentReportQNIController.class);

    @Autowired
    private PaymentReportQNIService paymentReportQNIService;

    @GetMapping(value = "")
    public List<PaymentReportDto> getProcedureQuantityByTag(HttpServletRequest request,
                                                            @RequestParam(value = "from", required = true) String fromDate,
                                                            @RequestParam(value = "to", required = true) String toDate,
                                                            @RequestParam(value = "arr-agency", required = true) List<String> agencyIds,
                                                            @RequestParam(value = "ignore-procedure", required = false) List<String> ignoreProcedureIds
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<PaymentReportDto> reportResult = null;
        reportResult = paymentReportQNIService.findDossiersWithConditions(agencyIds, fromDate, toDate, ignoreProcedureIds);
        logger.info("DIGO-Info: " + reportResult.size());
        return reportResult;
    }

    @GetMapping(value = "/--detail")
    public Page<DetailGeneralReportDto.PageResult> getPaymentReportDetail(HttpServletRequest request,
                                                                          @RequestParam(value = "from", required = true) String fromDate,
                                                                          @RequestParam(value = "to", required = true) String toDate,
                                                                          @RequestParam(value = "arr-agency", required = true) List<String> agencyIds,
                                                                          @RequestParam(value = "type", required = true) int type,
                                                                          @RequestParam(value = "procedure", required = false) String procedureId,
                                                                          @RequestParam(value = "ignore-procedure", required = false) List<String> ignoreProcedureIds,
                                                                          Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<DetailGeneralReportDto.PageResult> reportResult = null;
        reportResult = paymentReportQNIService.getPaymentReportDetail(type, agencyIds, procedureId, fromDate, toDate, ignoreProcedureIds, pageable);
        logger.info("DIGO-Info: " + reportResult.getTotalElements());
        return reportResult;
    }

    @GetMapping("/--detail/--export")
    public ResponseEntity<Object> exportDossierPaymentOnlineDetail(
            HttpServletRequest request,
            @RequestParam(value = "from", required = true) String fromDate,
            @RequestParam(value = "to", required = true) String toDate,
            @RequestParam(value = "arr-agency", required = true) List<String> agencyIds,
            @RequestParam(value = "type", required = true) int type,
            @RequestParam(value = "procedure", required = false) String procedureName,
            @RequestParam(value = "ignore-procedure", required = false) List<String> ignoreProcedureIds
    ){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        ResponseEntity<Object> result = null;
        result = paymentReportQNIService.exportDossierPaymentOnlineDetail(fromDate, toDate, agencyIds, type, procedureName, ignoreProcedureIds);
        return result;
    }

    @PutMapping("/--update--payment-procedure")
    public ImportResponseDto updateIsPaymentProcedure(
            HttpServletRequest request,
            @RequestParam(value = "form", required = true) String fromDate,
            @RequestParam(value = "to", required = true) String toDate,
            @RequestParam(value = "ignore-exist") Boolean checkExist,
            @RequestParam(value = "hiddenFeeReportNAST", defaultValue = "0") int hiddenFeeReportNAST
    ){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        if (hiddenFeeReportNAST == 1){
            return paymentReportQNIService.updateIsPaymentProcedureV2(fromDate, toDate, checkExist);
        }
        return paymentReportQNIService.updateIsPaymentProcedure(fromDate, toDate, checkExist);
    }

    @PutMapping("/--update--payment-procedure-v2")
    public ImportResponseDto updateIsPaymentProcedureV2(
            HttpServletRequest request,
            @RequestParam(value = "from", required = true) String fromDate,
            @RequestParam(value = "to", required = true) String toDate,
            @RequestParam(value = "ignore-exist") Boolean checkExist
    ){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return paymentReportQNIService.updateIsPaymentProcedureV2(fromDate, toDate, checkExist);
    }

}
