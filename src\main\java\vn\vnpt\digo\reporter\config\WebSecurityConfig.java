/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.web.cors.CorsConfigurationSource;
import vn.vnpt.digo.reporter.properties.DigoProperties;

/**
 *
 * <AUTHOR>
 */
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private DigoProperties digoProperties;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
//        http.cors().and().oauth2ResourceServer().jwt();
//        http.authorizeRequests(au -> au.anyRequest().authenticated());

        http.csrf().disable()
                .authorizeRequests()
                .antMatchers(HttpMethod.GET, "/cache/--clear-all").permitAll()
                .antMatchers(HttpMethod.GET, "/actuator/health").permitAll()
                .antMatchers(HttpMethod.GET, "/freemarker/noitify").permitAll()
                .antMatchers(HttpMethod.GET, "/igate-probes/health").permitAll()
                .antMatchers(HttpMethod.GET, "/health-service/**").permitAll()
                .anyRequest().authenticated()
                .and()
                .oauth2ResourceServer().jwt();
    }


    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        return digoProperties.getCorsConfigurationSource();
    }

    @Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring()
                .antMatchers("/webjars/**", "/actuator/health")
                .antMatchers("re/webjars/**", "re/actuator/health", "re/health-service/**");
    }

}
