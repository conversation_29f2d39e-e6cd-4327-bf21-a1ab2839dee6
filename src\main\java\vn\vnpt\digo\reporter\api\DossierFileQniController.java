package vn.vnpt.digo.reporter.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.qni.UpdateExtendDigitizingDto;
import vn.vnpt.digo.reporter.service.QNIETLDossierService;

@RestController
@IcodeAuthorize("vnpt.permission.digitizingreportqni")
@RequestMapping("/dossier-file-qni")
public class DossierFileQniController {
    @Autowired
    private QNIETLDossierService qnietlDossierService;

    @PutMapping("/--update-extend-digitizing")
    public UpdateExtendDigitizingDto updateExtendDigitizing (
            @RequestParam(value = "form-date") String fromDate,
            @RequestParam(value = "to-date") String toDate,
            @RequestParam(value = "fieldName") String fieldName){
        return qnietlDossierService.updateExtendDigitizing(fromDate, toDate, fieldName);
    }
    @PutMapping("/--update-is-payment-online")
    public UpdateExtendDigitizingDto updateIsPaymentOnline (
            @RequestParam(value = "form-date") String fromDate,
            @RequestParam(value = "to-date") String toDate){
        return qnietlDossierService.updateIsPaymentOnline(fromDate, toDate);
    }
    @PutMapping("/--update-is-payment-online-v2")
    public UpdateExtendDigitizingDto updateIsPaymentOnlineV2 (
            @RequestParam(value = "form-date") String fromDate,
            @RequestParam(value = "to-date") String toDate,
            @RequestParam(value = "ignore-exist") Boolean checkExist){
        return qnietlDossierService.updateIsPaymentOnlineV2(fromDate, toDate, checkExist);
    }
    @PutMapping("/--update-financial-date-vbdlis")
    public UpdateExtendDigitizingDto updateFinancialDateVbdlis (
            @RequestParam(value = "form-date") String fromDate,
            @RequestParam(value = "to-date") String toDate){
        return qnietlDossierService.updateFinancialDateVbdlis(fromDate, toDate);
    }
    @PutMapping("/--update-is-reuse-extend")
    public UpdateExtendDigitizingDto updateIsReuseExtend (
            @RequestParam(value = "form") String fromDate,
            @RequestParam(value = "to") String toDate,
            @RequestParam(value = "ignore-exist") Boolean fieldName){
        return qnietlDossierService.updateIsReuseExtend(fromDate, toDate, fieldName);
    }
    @PutMapping("/--update-extend-digitizing-v2")
    public UpdateExtendDigitizingDto updateExtendDigitizingV2 (
            @RequestParam(value = "from") String fromDate,
            @RequestParam(value = "to") String toDate,
            @RequestParam(value = "fieldName") String fieldName,
            @RequestParam(value = "ignore-exist") Boolean checkExist){
        return qnietlDossierService.updateExtendDigitizingV2(fromDate, toDate, fieldName, checkExist);
    }
}
