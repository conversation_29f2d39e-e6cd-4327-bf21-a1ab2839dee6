package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierCountingDto implements Serializable {
  private long count =0;
  private ArrayList<DossierSimpleDataDto> lDossierSimpleData;
  // 0 : Inprogress On Time
  // 1 : Inprogress Out Of Due
  // 2 : Completed  Early
  // 3 : Completed On Time
  // 4 : Completed Out Of Due
  // 5 : cancelled
  // 6 : suspended
  private int code;
}
