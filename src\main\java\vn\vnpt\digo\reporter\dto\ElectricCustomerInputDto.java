package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import org.bson.types.ObjectId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ElectricCustomerInputDto implements Serializable {

    @NotNull
    private ObjectId userId;

    @NotNull
    private String customerCode;
}
