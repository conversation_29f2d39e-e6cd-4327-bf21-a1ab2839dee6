package vn.vnpt.digo.reporter.util;

import java.util.ArrayList;
import java.util.List;

public class BlacklistKeyword {
    public static final String[] BlacklistKeywordJava = {
            // Thuc thi lenh he thong (RCE)
            "exec", "Runtime", "ProcessBuilder", "getRuntime", "process",

            // Doc/Ghi file
            "File", "FileInputStream", "FileOutputStream", "RandomAccessFile",
            "FileReader", "FileWriter", "BufferedReader", "BufferedWriter",
            "PrintWriter", "Files", "Paths", "newBufferedReader", "readAllBytes",

            // Truy cap mang
            "Socket", "ServerSocket", "InetAddress", "URL", "URLConnection", "openStream",

            // Reflection API (Tai class, goi ham an)
            "Class", "ClassLoader", "loadClass", "defineClass", "MethodHandles", "lookup", "MethodType",

            // Script engine (Thuc thi ma dong)
            "ScriptEngineManager", "eval", "invoke", "call",

            // System API (Thoat chuong trinh, thay doi quyen)
            "System", "exit", "setSecurityManager"
    };

    public static String sanitizeContent(String content) {
        return content.replaceAll("[\\s\\t\\n\\r]", "")  // Xoa khoang trang, xuong dong
                .replaceAll("\\\\u[0-9a-fA-F]{4}", "") // Xoa Unicode Escape Sequence (\u0065)
                .replaceAll("[^\\p{Print}]", ""); // Xoa ky tu khong in duoc
    }

}
