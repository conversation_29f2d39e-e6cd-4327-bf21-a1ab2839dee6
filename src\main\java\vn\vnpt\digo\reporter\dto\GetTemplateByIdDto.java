/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.pojo.TemplateSubsystem;
import vn.vnpt.digo.reporter.pojo.TemplateType;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
public class GetTemplateByIdDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private TemplateType type;

    private String name;

    private List<TemplateSubsystem> subsystem;

    private Object listVariable;

    private String listVariableString;

    private TemplateFile file;

    private Integer signEnable;
    
    private List<TemplateFileSignDto> fileSign;

    private String code;

}
