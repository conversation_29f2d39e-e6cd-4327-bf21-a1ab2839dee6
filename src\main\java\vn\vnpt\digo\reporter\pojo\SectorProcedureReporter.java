package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SectorProcedureReporter implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private Integer procedureQuantity;
    @JsonProperty("transSector")
    private ArrayList<TranslateName> name = new ArrayList<>();
}
