package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.Tag;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAgencyDto implements Serializable {
    @JsonProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("tag")
    private List<Tag> tag;
    @JsonProperty("name")
    private String name;
}
