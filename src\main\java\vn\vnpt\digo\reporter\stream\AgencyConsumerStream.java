/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.stream;

import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.Agency;
import vn.vnpt.digo.reporter.dto.KafkaAgencyStatisticDto;
import vn.vnpt.digo.reporter.pojo.AgencyDossier;
import vn.vnpt.digo.reporter.pojo.AgencyDossierMonthData;
import vn.vnpt.digo.reporter.service.AgencyService;
import vn.vnpt.digo.reporter.stream.messaging.AgencyConsumer;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(AgencyConsumer.class)
public class AgencyConsumerStream {

    @Autowired
    private AgencyService agencyService;

    Logger logger = LoggerFactory.getLogger(AgencyConsumer.class);

    @StreamListener(AgencyConsumer.INPUT)
    public void agencyStatistic(KafkaAgencyStatisticDto input) {
        System.out.println("AGENCY_STATISTIC");
        try {
            KafkaAgencyStatisticDto a = input;
            Agency updateAgency = agencyService.getAgency(input.getOriginId(), input.getDeploymentId());
            if (Objects.nonNull(updateAgency)) {
                logger.info("Update agency : " + input);
                logger.info("Dossier statistic updated on " + input.getDossier().getMonth() + "/" + input.getDossier().getYear());
                updateAgency.getDossier().forEach((AgencyDossier dossier) -> {
                    if (Objects.equals(input.getDossier().getYear(), dossier.getYear())) {
                        switch (input.getDossier().getMonth()) {
                            case 1:
                                if (Objects.isNull(dossier.getMonth().getM1())) {
                                    dossier.getMonth().setM1(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM1().setReceived(dossier.getMonth().getM1().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM1().setResolved(dossier.getMonth().getM1().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM1().setEarly(dossier.getMonth().getM1().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM1().setOnTime(dossier.getMonth().getM1().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM1().setOverdue(dossier.getMonth().getM1().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM1().setCanceled(dossier.getMonth().getM1().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 2:
                                if (Objects.isNull(dossier.getMonth().getM2())) {
                                    dossier.getMonth().setM2(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM2().setReceived(dossier.getMonth().getM2().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM2().setResolved(dossier.getMonth().getM2().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM2().setEarly(dossier.getMonth().getM2().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM2().setOnTime(dossier.getMonth().getM2().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM2().setOverdue(dossier.getMonth().getM2().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM2().setCanceled(dossier.getMonth().getM2().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 3:
                                if (Objects.isNull(dossier.getMonth().getM3())) {
                                    dossier.getMonth().setM3(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM3().setReceived(dossier.getMonth().getM3().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM3().setResolved(dossier.getMonth().getM3().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM3().setEarly(dossier.getMonth().getM3().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM3().setOnTime(dossier.getMonth().getM3().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM3().setOverdue(dossier.getMonth().getM3().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM3().setCanceled(dossier.getMonth().getM3().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 4:
                                if (Objects.isNull(dossier.getMonth().getM4())) {
                                    dossier.getMonth().setM4(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM4().setReceived(dossier.getMonth().getM4().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM4().setResolved(dossier.getMonth().getM4().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM4().setEarly(dossier.getMonth().getM4().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM4().setOnTime(dossier.getMonth().getM4().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM4().setOverdue(dossier.getMonth().getM4().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM4().setCanceled(dossier.getMonth().getM4().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 5:
                                if (Objects.isNull(dossier.getMonth().getM5())) {
                                    dossier.getMonth().setM5(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM5().setReceived(dossier.getMonth().getM5().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM5().setResolved(dossier.getMonth().getM5().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM5().setEarly(dossier.getMonth().getM5().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM5().setOnTime(dossier.getMonth().getM5().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM5().setOverdue(dossier.getMonth().getM5().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM5().setCanceled(dossier.getMonth().getM5().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 6:
                                if (Objects.isNull(dossier.getMonth().getM6())) {
                                    dossier.getMonth().setM6(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM6().setReceived(dossier.getMonth().getM6().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM6().setResolved(dossier.getMonth().getM6().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM6().setEarly(dossier.getMonth().getM6().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM6().setOnTime(dossier.getMonth().getM6().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM6().setOverdue(dossier.getMonth().getM6().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM6().setCanceled(dossier.getMonth().getM6().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 7:
                                if (Objects.isNull(dossier.getMonth().getM7())) {
                                    dossier.getMonth().setM7(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM7().setReceived(dossier.getMonth().getM7().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM7().setResolved(dossier.getMonth().getM7().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM7().setEarly(dossier.getMonth().getM7().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM7().setOnTime(dossier.getMonth().getM7().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM7().setOverdue(dossier.getMonth().getM7().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM7().setCanceled(dossier.getMonth().getM7().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 8:
                                if (Objects.isNull(dossier.getMonth().getM8())) {
                                    dossier.getMonth().setM8(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM8().setReceived(dossier.getMonth().getM8().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM8().setResolved(dossier.getMonth().getM8().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM8().setEarly(dossier.getMonth().getM8().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM8().setOnTime(dossier.getMonth().getM8().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM8().setOverdue(dossier.getMonth().getM8().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM8().setCanceled(dossier.getMonth().getM8().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 9:
                                if (Objects.isNull(dossier.getMonth().getM9())) {
                                    dossier.getMonth().setM9(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM9().setReceived(dossier.getMonth().getM9().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM9().setResolved(dossier.getMonth().getM9().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM9().setEarly(dossier.getMonth().getM9().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM9().setOnTime(dossier.getMonth().getM9().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM9().setOverdue(dossier.getMonth().getM9().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM9().setCanceled(dossier.getMonth().getM9().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 10:
                                if (Objects.isNull(dossier.getMonth().getM10())) {
                                    dossier.getMonth().setM10(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM10().setReceived(dossier.getMonth().getM10().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM10().setResolved(dossier.getMonth().getM10().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM10().setEarly(dossier.getMonth().getM10().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM10().setOnTime(dossier.getMonth().getM10().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM10().setOverdue(dossier.getMonth().getM10().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM10().setCanceled(dossier.getMonth().getM10().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 11:
                                if (Objects.isNull(dossier.getMonth().getM11())) {
                                    dossier.getMonth().setM11(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM11().setReceived(dossier.getMonth().getM11().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM11().setResolved(dossier.getMonth().getM11().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM11().setEarly(dossier.getMonth().getM11().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM11().setOnTime(dossier.getMonth().getM11().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM11().setOverdue(dossier.getMonth().getM11().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM11().setCanceled(dossier.getMonth().getM11().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                            case 12:
                                if (Objects.isNull(dossier.getMonth().getM12())) {
                                    dossier.getMonth().setM12(new AgencyDossierMonthData());
                                }
                                dossier.getMonth().getM12().setReceived(dossier.getMonth().getM12().getReceived() + input.getDossier().getData().getReceived());
                                dossier.getMonth().getM12().setResolved(dossier.getMonth().getM12().getResolved() + input.getDossier().getData().getResolved());
                                dossier.getMonth().getM12().setEarly(dossier.getMonth().getM12().getEarly() + input.getDossier().getData().getEarly());
                                dossier.getMonth().getM12().setOnTime(dossier.getMonth().getM12().getOnTime() + input.getDossier().getData().getOnTime());
                                dossier.getMonth().getM12().setOverdue(dossier.getMonth().getM12().getOverdue() + input.getDossier().getData().getOverdue());
                                dossier.getMonth().getM12().setCanceled(dossier.getMonth().getM12().getCanceled() + input.getDossier().getData().getCanceled());
                                break;
                        }
                    }
                });
                agencyService.save(updateAgency);
            }
        } catch (Exception e) {
            logger.info("Update agency : " + input);
            logger.info("Dossier statistic updated failed with exception:  " + e.getMessage());
        }
    }
}
