package vn.vnpt.digo.reporter.service;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import com.aspose.words.Document;
import com.aspose.words.ReportingEngine;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.Template;
import vn.vnpt.digo.reporter.dto.OfficeTemplateReqDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.repository.TemplateRepository;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;
import vn.vnpt.digo.reporter.util.OfficeUtils;
import vn.vnpt.digo.reporter.util.Translator;
import org.springframework.beans.factory.annotation.Value;

@Service
public class OfficeTemplateService {
    
    @Value("${file.upload-dir}")
    private String uploadDir;

    private Gson gson = new Gson();

    @Autowired
    private Translator translator;

    @Autowired
    private Microservice microservice;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TemplateRepository templateRepository;

    public Resource download(OfficeTemplateReqDto params){
        try {
            //get template
            Template template = templateRepository.findOneById(params.getId());
            //generate report from template
            Document document = this.generateWord(template, params);
            //save to resource
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            document.save(output, OfficeUtils.getFormat(params.getSaveFormat()));
            //return resource
            String filename = OfficeUtils.getSaveFilename(template.getFile().getFilename(), params.getSaveFormat());
            ByteArrayResource resource = new ByteArrayResource(output.toByteArray(), "report") {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
            return resource;
        } catch (DigoHttpException digoex){
            throw digoex;
        } catch (Exception ex){
            throw new DigoHttpException(11003, new String[]{ex.getMessage()}, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public Resource downloadFromData(OfficeTemplateReqDto params, String data){
        try {
            //get template
            Template template = templateRepository.findOneById(params.getId());
            //generate report from template
            Document document = this.generateWordFromData(template, params, data);
            //save to resource
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            document.save(output, OfficeUtils.getFormat(params.getSaveFormat()));
            //return resource
            String filename = OfficeUtils.getSaveFilename(template.getFile().getFilename(), params.getSaveFormat());
            ByteArrayResource resource = new ByteArrayResource(output.toByteArray(), "report") {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
            return resource;
        } catch (DigoHttpException digoex){
            throw digoex;
        } catch (Exception ex){
            throw new DigoHttpException(11003, new String[]{ex.getMessage()}, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public IdPojo generate(OfficeTemplateReqDto params){
        try {
            //get template
            Template template = templateRepository.findOneById(params.getId());
            //generate report from template
            Document document = this.generateWord(template, params);
            //upload to fileman
            //save to resource
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            document.save(output, OfficeUtils.getFormat(params.getSaveFormat()));
            //return resource
            String filename = OfficeUtils.getSaveFilename(this.uploadDir + template.getFile().getPath(), params.getSaveFormat());
            ByteArrayResource resource = new ByteArrayResource(output.toByteArray(), "abc") {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
            //return file id
            MultiValueMap<String, Object> reqBody = new LinkedMultiValueMap<>();
            reqBody.add("file", resource);
            String postFileUrl = microservice.filemanUri("file").toUriString();
            IdPojo res = MicroserviceExchange.postMultipart(restTemplate, postFileUrl, reqBody, IdPojo.class);
            return res;
        } catch (DigoHttpException digoex){
            throw digoex;
        } catch (Exception ex){
            throw new DigoHttpException(11003, new String[]{ex.getMessage()}, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private Document generateWord(Template template, OfficeTemplateReqDto params){
        try{
            //get template from file
            FileInputStream file = new FileInputStream( this.uploadDir + template.getFile().getPath());
            Document document = new Document(file);
            ReportingEngine engine = new ReportingEngine();
            //get data from api
            String endpoint = params.getApi();
            if(Objects.nonNull(params.getQuery()) && !Objects.equals(params.getQuery(), "")){
                endpoint += "?" + params.getQuery();
            }
            String rawResponse = MicroserviceExchange.get(restTemplate, endpoint, String.class);
            JsonObject data = gson.fromJson(rawResponse, JsonObject.class);
            //build report from template and data
            engine.buildReport(document, data, "data");
            return document;
        } catch (FileNotFoundException fnfex){
            throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_BAD_REQUEST);
        } catch (Exception ex){
            System.out.print(ex);
            throw new DigoHttpException(11003, new String[]{ex.getMessage()}, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    private Document generateWordFromData(Template template, OfficeTemplateReqDto params, String body){
        try{
            //get template from file
            FileInputStream file = new FileInputStream( this.uploadDir + template.getFile().getPath());
            Document document = new Document(file);
            ReportingEngine engine = new ReportingEngine();
            //convert data to JsonObject
            JsonObject data = gson.fromJson(body, JsonObject.class);
            //build report from template and data
            engine.buildReport(document, data, "data");
            return document;
        } catch (FileNotFoundException fnfex){
            throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_BAD_REQUEST);
        } catch (Exception ex){
            System.out.print(ex);
            throw new DigoHttpException(11003, new String[]{ex.getMessage()}, HttpServletResponse.SC_NOT_FOUND);
        }
    }
}
