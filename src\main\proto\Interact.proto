/**
 * <AUTHOR>
 */

syntax = "proto3";

option java_multiple_files = true;
option java_package = "vn.vnpt.igate.Interact";
option java_outer_classname = "InteractProto";
option objc_class_prefix = "ITR";

package Interact;

service Interacter {
  rpc SendRequest (Request) returns (Reply) {
  }
}

message Request {
  string url = 1;
  string pathVariable = 2;
  string requestParam = 3;
  string requestBody = 4;
  string httpMethod = 5;
}

message Reply {
  string message = 1;
}
