package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusStatisticsByTagTotalDetail implements Serializable {

    @Id
    private TagStatisticsPetitionTag tag;

    private String tagName;

    private Long total = 0L;

    private StatusTotal status;
    
    public void setTag(ObjectId id, List<TranslateName> trans, IdTrans parent) {
        this.tag = new TagStatisticsPetitionTag();
        this.tag.setId(id);
        this.tag.setTrans(trans);
        this.tag.setParent(parent);
    }

}
