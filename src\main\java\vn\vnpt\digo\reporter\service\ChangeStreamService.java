package vn.vnpt.digo.reporter.service;

import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.ChangeStreamEvent;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.ChangeStreamEventDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import com.mongodb.MongoException;
import org.springframework.dao.DuplicateKeyException;

@Service
public class ChangeStreamService {

    @Autowired
    private MongoTemplate mongoTemplate;

    ZoneId zoneId = ZoneId.of("Asia/Ho_Chi_Minh");

    public Page<ChangeStreamEventDto> getListChangeStreamEvent(
            String service,
            String collection,
            String type,
            Integer errorType,
            ObjectId itemId,
            String fromDate,
            String toDate,
            Pageable pageable
    ) {
        

        Query query = new Query();
        List<Criteria> criteriaList = initCriteria(service, collection, type, errorType, itemId, fromDate, toDate);
        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        long total = mongoTemplate.count(query, "changeStreamEvent");

        // Sort theo Pageable, nếu có
        if (pageable.getSort().isSorted()) {
            query.with(pageable.getSort());
        } else {
            query.with(Sort.by(Sort.Direction.DESC, "createdDate"));
        }

        query.skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize());

        List<ChangeStreamEventDto> content = mongoTemplate.find(query, ChangeStreamEventDto.class, "changeStreamEvent");

        return new PageImpl<>(content, pageable, total);
    }

    public AffectedRowsDto editChangeStreamData (
            ObjectId id,
            ObjectId itemId,
            Object newData
    ) {
        ChangeStreamEvent changeStreamEvent = mongoTemplate.findById(id, ChangeStreamEvent.class);
        if (Objects.isNull(changeStreamEvent)) {
            throw new DigoHttpException(10400, new String[]{"Không tìm thấy event!"}, HttpServletResponse.SC_BAD_REQUEST);
        } else if (Objects.nonNull(changeStreamEvent) && !changeStreamEvent.getItemId().equals(itemId)) {
            throw new DigoHttpException(10400, new String[]{"itemId không thuộc event này!"}, HttpServletResponse.SC_BAD_REQUEST);
        } else {
            try {
                if (Objects.nonNull(newData)) changeStreamEvent.setData(newData);
                changeStreamEvent.setRetryCount(0);
                changeStreamEvent.setErrorType(0);
                mongoTemplate.save(changeStreamEvent);
                return new AffectedRowsDto(1);
            } catch (DuplicateKeyException | MongoException ex) {
                throw new DigoHttpException(10401, new String[]{"Lỗi lưu dữ liệu!"}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (Exception e) {
                throw new DigoHttpException(10400, new String[]{"Dữ liệu không hợp lệ!"}, HttpServletResponse.SC_BAD_REQUEST);
            }
        }
    }
    
    public AffectedRowsDto retryEvent (
            String service,
            String collection,
            String type,
            Integer errorType,
            ObjectId itemId,
            String fromDate,
            String toDate
    ) {
        Query query = new Query();
        List<Criteria> criteriaList = initCriteria(service, collection, type, errorType, itemId, fromDate, toDate);
        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }
        Update update = new Update()
                .set("retryCount", 0)
                .set("errorType", 0);

        UpdateResult result = mongoTemplate.updateMulti(query, update, "changeStreamEvent");

        return new AffectedRowsDto((int) result.getModifiedCount());
    }
    
    private List<Criteria> initCriteria (
            String service,
            String collection,
            String type,
            Integer errorType,
            ObjectId itemId,
            String fromDate,
            String toDate
    ) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (service != null && !service.isBlank()) {
            criteriaList.add(Criteria.where("service").is(service));
        }

        if (collection != null && !collection.isBlank()) {
            criteriaList.add(Criteria.where("collection").is(collection));
        }

        if (type != null && !type.isBlank()) {
            criteriaList.add(Criteria.where("type").is(type));
        }

        if (errorType != null) {
            criteriaList.add(Criteria.where("errorType").is(errorType));
        }

        if (itemId != null) {
            criteriaList.add(Criteria.where("itemId").is(itemId));
        }

        if (fromDate != null && !fromDate.isBlank()) {
            Date from = Date.from(LocalDate.parse(fromDate).atStartOfDay(zoneId).toInstant());
            criteriaList.add(Criteria.where("createdDate").gte(from));
        }

        if (toDate != null && !toDate.isBlank()) {
            Date to = Date.from(LocalDate.parse(toDate).atStartOfDay(zoneId).plusDays(1).toInstant());
            criteriaList.add(Criteria.where("createdDate").lt(to));
        }
        
        return criteriaList;
        
    }
    
}
