package vn.vnpt.digo.reporter.grpc.client;

import net.devh.boot.grpc.client.inject.GrpcClient;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.grpc.auth.BearerToken;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.GsonUtils;
import vn.vnpt.igate.Interact.InteracterGrpc;
import vn.vnpt.igate.Interact.Reply;
import vn.vnpt.igate.Interact.Request;

/**
 * <AUTHOR>
 */
@Service
public class InteractClient {

    @GrpcClient("local-grpc-server")
    private InteracterGrpc.InteracterBlockingStub stub;

    public Object getDetailTemplate(ObjectId id) {
//        ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 9090)
//                .usePlaintext()
//                .build();

        String token2 = Context.getJwtAuthenticationTokenValue();

        BearerToken token = new BearerToken(token2);

//        stub = InteracterGrpc.newBlockingStub(channel).withCallCredentials(token);

        Reply response = stub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl("re/template/{id}")
                .setPathVariable(id.toString())
                .build());

        System.out.println(response.getMessage());

//        channel.shutdown();

        return GsonUtils.copyObject(response.getMessage());
    }

}