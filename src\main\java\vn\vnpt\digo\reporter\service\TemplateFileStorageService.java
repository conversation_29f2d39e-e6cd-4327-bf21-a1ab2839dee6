/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.reporter.document.Template;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.Minio.UploadResponseDto;
import vn.vnpt.digo.reporter.dto.MinioAccessDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.exception.TemplateFileStorageException;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.properties.TemplateFileStorageProperties;
import vn.vnpt.digo.reporter.repository.TemplateRepository;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
@Service
public class TemplateFileStorageService {

    private final Path templateFileStorageLocation;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private Translator translator;

    @Autowired
    private MinioService minioService;

    @Value("${upload-file-type}")
    private String uploadFileType;

    @Value("${vnpt.object-storage.moved-without-minio-access.recent}")
    private String movedWithoutMinioAccessRecent;
    SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    @Value("${vnpt.object-storage.download-file.access-key}")
    private String downloadFileByObjectStogareAccessKey;

    @Value("${vnpt.object-storage.download-file.secret-key}")
    private String downloadFileByObjectStogareSecretKey;

    @Value("${vnpt.object-storage.download-file.endpoint}")
    private String downloadFileByObjectStogareEndpoint;

    @Value("${vnpt.object-storage.download-file.bucket-name}")
    private String downloadFileByObjectStogareBucketName;

    @Autowired
    public TemplateFileStorageService(TemplateFileStorageProperties templateFileStorageProperties) {
        this.templateFileStorageLocation = Paths.get(templateFileStorageProperties.getUploadDir())
                .toAbsolutePath().normalize();

        try {
            Files.createDirectories(this.templateFileStorageLocation);
        } catch (Exception ex) {
            throw new TemplateFileStorageException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    public String getPath() {
        ObjectId deploymentId = Context.getDeploymentId();
        LocalDateTime now = LocalDateTime.now();
        String date = String.valueOf(now.getYear()) + "/" + String.valueOf(now.getMonthValue() + "/" + String.valueOf(now.getDayOfMonth()));
        String path = deploymentId.toString() + "/" + date;
        return path;
    }

    public TemplateFile storeFile(MultipartFile file) {
        // Normalize file name
        String fileName = StringUtils.cleanPath(file.getOriginalFilename());

        try {
            // Check if the file's name contains invalid characters
            if (fileName.contains("..")) {
                throw new TemplateFileStorageException("Sorry! Filename contains invalid path sequence " + fileName);
            }
            String fileExtension = "";
            try {
                fileExtension = fileName.substring(fileName.lastIndexOf("."));
            } catch (Exception e) {
                fileExtension = "";
            }

            if (fileExtension.toLowerCase().equals(".rptdesign") || fileExtension.toLowerCase().equals(".ftl") || fileExtension.toLowerCase().equals(".jrxml") || fileExtension.toLowerCase().equals(".docx")) {
                ObjectId name = new ObjectId();
                String nameWithExtension = "";
                if (fileExtension.toLowerCase().equals(".rptdesign")) {
                    nameWithExtension = name.toString() + ".rptdesign";
                } else if (fileExtension.toLowerCase().equals(".ftl")) {
                    nameWithExtension = name.toString() + ".ftl";
                } else if (fileExtension.toLowerCase().equals(".jrxml")) {
                    nameWithExtension = name.toString() + ".jrxml";
                } else if (fileExtension.toLowerCase().equals(".docx")) {
                    nameWithExtension = name.toString() + ".docx";
                }
                String path = getPath() + "/" + nameWithExtension;
                if("minio".equals(uploadFileType)){
                    // nếu uploadFileType là minio thì lưu cả minio và nfs
                    // lưu nfs
                    UploadResponseDto minioUploadFile= minioService.upload(file, path);
                    //lưu nfs
                    Files.createDirectories(this.templateFileStorageLocation.resolve(Paths.get(getPath())));
                    Path targetLocation = this.templateFileStorageLocation.resolve(Paths.get(getPath()).resolve(nameWithExtension));
                    Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
                    return new TemplateFile(fileName, file.getSize(), path, minioUploadFile.getMinioAccess());
                } else{
                    // Copy file to the target location (Replacing existing file with the same name)
                    Files.createDirectories(this.templateFileStorageLocation.resolve(Paths.get(getPath())));
                    Path targetLocation = this.templateFileStorageLocation.resolve(Paths.get(getPath()).resolve(nameWithExtension));

                    Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
                    return new TemplateFile(fileName, file.getSize(), path);
                }
            } else {
                throw new TemplateFileStorageException(fileName + " is wrong extesion");
            }
        } catch (IOException ex) {
            throw new TemplateFileStorageException("Could not store file " + fileName + ". Please try again!", ex);
        }
    }

    public Resource downloadTemplateFileById(ObjectId id) throws ParseException {
        try {
            ObjectId deploymentId = Context.getDeploymentId();
            if (deploymentId != null) {
                Template template = templateRepository.findOneById(id);
                if (template != null) {
                    TemplateFile file = template.getFile();
                    Resource resource;
                    String fileId = extractLastObjectIdBeforeDot(template.getFile().getPath());
                    if(Objects.nonNull(template.getFile().getMinioAccess())){
                        resource = minioService.downloadResource(template.getFile().getMinioAccess());
                    } else if( Objects.nonNull(fileId) && (dateFormat.parse(movedWithoutMinioAccessRecent).getTime()+ (24 * 60 * 60 * 1000 - 1))/1000 >= new ObjectId(fileId).getTimestamp()) {
                        try {
                            resource = downloadFileByObjectStogare(downloadFileByObjectStogareBucketName, file.getPath());
                            if (resource == null || !resource.exists()) {
                                System.out.println("download file Object Stogare fail");
                                Path filePath = this.templateFileStorageLocation.resolve(Paths.get(file.getPath())).normalize();
                                resource = new UrlResource(filePath.toUri());
                            } else {
                                System.out.println("download file Object Stogare success");
                            }
                        } catch (Exception e) {
                            System.out.println("download file Object Stogare fail");
                            Path filePath = this.templateFileStorageLocation.resolve(Paths.get(file.getPath())).normalize();
                            resource = new UrlResource(filePath.toUri());
                        }
                    }else {
                        Path filePath = this.templateFileStorageLocation.resolve(Paths.get(file.getPath())).normalize();
                        resource = new UrlResource(filePath.toUri());
                    }
                    if (resource.exists()) {
                        return resource;
                    } else {
                        throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.file")}, HttpServletResponse.SC_NOT_FOUND);
                    }
                }else {
                    throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_NOT_FOUND);
                }
            } else {
                throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (MalformedURLException ex) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Transactional
    public AffectedRowsDto deleteFile(ObjectId id) {
        try {
            Template template = templateRepository.findOneById(id);
            int affected = 0;
            if (template != null) {
                TemplateFile file = template.getFile();
                if(Objects.nonNull(file.getMinioAccess())){
                    minioService.delete(file.getMinioAccess());
                    affected = 1;
                }else {
                    Path filePath = this.templateFileStorageLocation.resolve(Paths.get(file.getPath())).normalize();
                    if (Files.exists(filePath)) {
                        Files.delete(filePath);
                        affected = 1;
                    }
                }
            return new AffectedRowsDto(affected);
            }
        } catch (Exception e) {
            System.out.println("Delete file failed! " + e.getMessage());
        }
        return new AffectedRowsDto(0);
    }

    public String deleteFileByPath(String path) {
        try {
            Path filePath = this.templateFileStorageLocation.resolve(Paths.get(path));
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                return "Deleted file";
            }
            throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.file")}, HttpServletResponse.SC_NOT_FOUND);
            
        } catch (IOException ex) {
            throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.file")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public static String joinPath(String... path) {
        String result = "";
        for (String temp : path) {
            result += "/" + temp.replaceAll("^/", "").replaceAll("/$", "");
        }
        return result.replaceAll("^/", "");
    }

    public String extractLastObjectIdBeforeDot(String filePath) {
        Pattern pattern = Pattern.compile("([0-9a-fA-F]{24})(?=\\.[^/]+$)");
        Matcher matcher = pattern.matcher(filePath);
        if (matcher.find()) {
            return matcher.group();
        } else {
            return null;
        }
    }

    public Resource downloadFileByObjectStogare(String bucketName, String filePath){
        try {

            //download file
            MinioAccessDto minioAccess = new MinioAccessDto();
            minioAccess.setAccessKey(downloadFileByObjectStogareAccessKey);
            minioAccess.setSecretKey(downloadFileByObjectStogareSecretKey);
            minioAccess.setServiceUrl(downloadFileByObjectStogareEndpoint);
            minioAccess.setBucketName(bucketName);
            minioAccess.setFilePath(filePath);
            Resource resource = minioService.downloadResource(minioAccess);
            return resource;
        } catch (Exception ex) {
            return null;
        }
    }
}
