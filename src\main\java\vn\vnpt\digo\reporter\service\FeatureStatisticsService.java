package vn.vnpt.digo.reporter.service;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.FeatureStatistics;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.featurestatistics.FeatureStatisticsDto;
import vn.vnpt.digo.reporter.dto.featurestatistics.FeatureStatisticsInputDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.repository.FeatureStatisticsRepository;
import vn.vnpt.digo.reporter.util.Context;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 * <AUTHOR>
 */

@Service
public class FeatureStatisticsService {

    private final FeatureStatisticsRepository featureStatisticsRepository;
    private final MongoTemplate mongoTemplate;

    private final Logger logger = LoggerFactory.getLogger(FeatureStatisticsService.class);

    public FeatureStatisticsService(
            FeatureStatisticsRepository featureStatisticsRepository,
            MongoTemplate mongoTemplate
    ) {
        this.featureStatisticsRepository = featureStatisticsRepository;
        this.mongoTemplate = mongoTemplate;
    }

    public List<PostResponseDto> create(List<FeatureStatisticsInputDto> input) {
        if (!input.isEmpty()) {
            List<PostResponseDto> result = new ArrayList<>();
            input.forEach(item -> {
                FeatureStatistics newFeatureStatistics = new FeatureStatistics(item);
                result.add(new PostResponseDto(featureStatisticsRepository.save(newFeatureStatistics).getId()));
            });
            return result;
        } else {
            throw new DigoHttpException(10005, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    public List<FeatureStatisticsDto> statistics(String appCode, Date startDate, Date endDate) {
        // Create new criteria
        Criteria criteria = new Criteria();
        // List query param
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("appCode").is(appCode));
        ObjectId deploymentId = Context.getDeploymentId();
        criteriaList.add(Criteria.where("deploymentId").is(deploymentId));

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            criteriaList.add(Criteria.where("createdDate").gte(startDate).lte(endDate));
        } else if (Objects.nonNull(startDate)) {
            criteriaList.add(Criteria.where("createdDate").gte(startDate));
        } else if (Objects.nonNull(endDate)) {
            criteriaList.add(Criteria.where("createdDate").lte(endDate));
        }

        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        Aggregation aggRaw = newAggregation(
                match(criteria),
                project("featureCode", "counter"),
                group("featureCode").sum("counter").as("counter"),
                sort(Sort.by(Sort.Order.desc("counter")))
        );
        AggregationResults<FeatureStatisticsDto> aggregationResults = mongoTemplate.aggregate(aggRaw, FeatureStatistics.class, FeatureStatisticsDto.class);

        return aggregationResults.getMappedResults();
    }

}
