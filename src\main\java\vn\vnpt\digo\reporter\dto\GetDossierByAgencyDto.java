/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.util.StringUtils;
import vn.vnpt.digo.reporter.pojo.AgencyDossierMonthData;
import vn.vnpt.digo.reporter.pojo.AgencyLevel;
import vn.vnpt.digo.reporter.pojo.AgencyName;
import vn.vnpt.digo.reporter.pojo.AgencyProcedure;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GetDossierByAgencyDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;

    @JsonProperty("name")
    private String agencyName;

    @JsonProperty("level")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyLevel;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    private Integer year;

    private String month = "";

    private Integer secondLevel = 0;

    private Integer thirdLevel = 0;

    private Integer fourthLevel = 0;

    private Integer received = 0;

    private Integer resolved = 0;

    private Integer early = 0;

    private Integer onTime = 0;

    private Integer overdue = 0;

    private Integer canceled = 0;

    private String earlyRate = "0%";

    private String onTimeRate = "0%";

    private String overdueRate = "0%";

    @JsonIgnore
    private AgencyLevel level;

    @JsonIgnore
    private List<AgencyDossierDto> dossier;

    @JsonIgnore
    private AgencyProcedure procedure;

    @JsonIgnore
    private List<AgencyName> name;

    @JsonIgnore
    private ObjectId originId;

    public void setData(Short localeId) {
        this.agencyId = this.originId;
        if (Objects.nonNull(this.level)) {
            this.agencyLevel = this.level.getId();
        }
        this.name.forEach(item -> {
            if (item.getLanguageId().equals(localeId)) {
                this.agencyName = item.getName();
            }
        });
        if (Objects.nonNull(this.procedure)) {
            this.secondLevel = this.procedure.getSecondLevelQuantity();
            this.thirdLevel = this.procedure.getThirdLevelQuantity();
            this.fourthLevel = this.procedure.getFourthLevelQuantity();
        }

    }

    public void setDossierDetail(Integer queryYear) {
        this.year = queryYear;
        if (Objects.nonNull(this.dossier)) {
            AgencyDossierDto yearValue = new AgencyDossierDto();
            for (AgencyDossierDto item : this.dossier) {
                if (Objects.equals(item.getYear(), queryYear)) {
                    yearValue = item;
                    break;
                }
            }
            if (Objects.nonNull(yearValue.getYear())) {
                for (int i = 1; i <= 12; i++) {
                    String queryMonth = "m" + i;
                    if (Objects.nonNull(queryMonth) && !StringUtils.isEmpty(queryMonth)) {
                        AgencyDossierMonthData data = yearValue.getMonth().get(queryMonth);
                        if (Objects.nonNull(data)) {
                            this.received = this.received + data.getReceived();
                            this.resolved = this.resolved + data.getResolved();
                            this.early = this.early + data.getEarly();
                            this.onTime = this.onTime + data.getOnTime();
                            this.overdue = this.overdue + data.getOverdue();
                            this.canceled = this.canceled + data.getCanceled();
                        }
                    }
                }
                this.earlyRate = String.format("%.02f", (float) (this.early * 100) / this.resolved) + "%";
                this.onTimeRate = String.format("%.02f", (float) (this.onTime * 100) / this.resolved) + "%";
                this.overdueRate = String.format("%.02f", (float) (this.overdue * 100) / this.resolved) + "%";
            }
        }
    }

    public void setDossierDetail(Integer queryYear, String queryMonth) {
        this.year = queryYear;
        this.month = queryMonth;
        if (Objects.nonNull(this.dossier)) {
            AgencyDossierDto yearValue = new AgencyDossierDto();
            for (AgencyDossierDto item : this.dossier) {
                if (Objects.equals(item.getYear(), queryYear)) {
                    yearValue = item;
                    break;
                }
            }
            if (Objects.nonNull(yearValue.getYear())) {
                if (Objects.nonNull(queryMonth) && !StringUtils.isEmpty(queryMonth)) {
                    AgencyDossierMonthData data = yearValue.getMonth().get(queryMonth);
                    if (Objects.nonNull(data)) {
                        this.received = data.getReceived();
                        this.resolved = data.getResolved();
                        this.early = data.getEarly();
                        this.onTime = data.getOnTime();
                        this.overdue = data.getOverdue();
                        this.canceled = data.getCanceled();
                        this.earlyRate = "0%";
                        this.onTimeRate = "0%";
                        this.overdueRate = "0%";
                        if (this.resolved != 0) {
                            if (this.early != 0) {
                                this.earlyRate = String.format("%.02f", (float) (this.early * 100) / this.resolved) + "%";
                            }
                            if (this.onTime != 0) {
                                this.onTimeRate = String.format("%.02f", (float) (this.onTime * 100) / this.resolved) + "%";
                            }
                            if (this.overdue != 0) {
                                this.overdueRate = String.format("%.02f", (float) (this.overdue * 100) / this.resolved) + "%";
                            }
                        }
                    }
                }

            }
        }
    }
}

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
class AgencyDossierDto implements Serializable{

    private Integer year;
    private HashMap<String, AgencyDossierMonthData> month;
}
