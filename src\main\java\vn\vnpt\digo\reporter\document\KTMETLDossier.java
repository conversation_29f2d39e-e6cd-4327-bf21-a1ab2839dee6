package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "ktmETLDossier")
public class KTMETLDossier {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;

    private Agency agency;

    private Sector sector;

    private Procedure procedure;

    private ProcedureLevel procedureLevel;

    private String code;

    private DossierStatus dossierStatus;

    private DossierTaskStatus dossierTaskStatus;

    private Applicant applicant;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date returnedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;

    private double processingTime;

    private ApplyMethod applyMethod;

    private Task currentTask;

    private List<Task> task;

    private List<Attachment> attachment;

    private List<AttachmentStorages> attachmentStorages;

    private List<ComponentsStorages> componentsStorages;

    private List<FormFile> listDossierFormFile;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date pauseDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date withdrawDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date cancelledDate; // ngay dung xu ly

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date financialObligationsDate; // ngay nghia vu tai chinh

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private List<Date> additionalDate; // ngay bo sung ho so

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Agency {
        private String id;

        private String name;

        private Parent parent;

        private List<Ancestors> ancestors;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Sector {
        private String id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Procedure {
        private String id;
        private String code;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierStatus {
        private int id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierTaskStatus {
        private String id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Applicant {

        @Id
        private String eformId; //this is eformId

        private String address;

        private String phoneNumber;

        private String fullname;

        private String organization;

    }



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplyMethod {
        private int id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Task {
        private Assignee assignee;

        private Agency agency;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date assignedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date dueDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date createdDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date updatedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date completedDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Assignee {
        private String id;
        private String fullname;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parent {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ancestors {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureLevel {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attachment {
        private String id;
        private String filename;
        private String group;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentStorages {
        private String code;
        private String filename;
        private String fileURL;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComponentsStorages {
        private String code;
        private String filename;
        private String fileURL;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormFile implements Serializable {
        @JsonProperty("file")
        List<File> file;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class File implements Serializable {
        @Id
        @JsonSerialize(using = ToStringSerializer.class)
        private String id;

        private String filename;

        private Integer size;
    }
}
