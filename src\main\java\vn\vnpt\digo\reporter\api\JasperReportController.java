package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.dto.JasperReportExportDto;
import vn.vnpt.digo.reporter.service.JasperReportService;

import java.io.FileNotFoundException;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;

@RestController
@RequestMapping("/jasper")
@IcodeAuthorize("vnpt.permission.jasper")
public class JasperReportController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateController.class);

    @Autowired
    private JasperReportService jasperReportService;

    @PostMapping("/--report")
    public <D> ByteArrayResource reportFreemarker(@RequestParam(value = "id") String id, @RequestBody JasperReportExportDto<D> param) throws FileNotFoundException {
        return jasperReportService.exportFilePdf(id, param);
    }

}
