/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.digo.reporter.document.AppStatistics;

/**
 *
 * <AUTHOR>
 */
public interface AppStatisticsRepository extends MongoRepository<AppStatistics, ObjectId> {
    @Query("{'deploymentId' : ?0}")
    AppStatistics findByDeploymentId(ObjectId deploymentId);
}
