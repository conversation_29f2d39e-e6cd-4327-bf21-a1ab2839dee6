/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NguoiXuLy implements Serializable {
    
    @JsonProperty("NguoiXuLy")
    private String NguoiXuLy;
    
    @JsonProperty("ChucDanh")
    private String ChucDanh;
    
    @JsonProperty("ThoiDiemXuLy")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date ThoiDiemXuLy;
    
    @JsonProperty("PhongBanXuLy")
    private String PhongBanXuLy;
    
    @JsonProperty("NoiDungXuLy")
    private String NoiDungXuLy;
    
    @JsonProperty("TrangThai")
    private String TrangThai;
    
    @JsonProperty("NgayBatDau")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date NgayBatDau;
    
    @JsonProperty("NgayKetThucTheoQuyDinh")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date NgayKetThucTheoQuyDinh;
}
