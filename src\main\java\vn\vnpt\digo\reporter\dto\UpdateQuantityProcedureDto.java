package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.ArrayList;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.pojo.AgencyByProcedure;
import vn.vnpt.digo.reporter.pojo.SectorByProcedure;
import vn.vnpt.digo.reporter.pojo.TagName;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateQuantityProcedureDto implements Serializable {

    @NotNull
    private ArrayList<AgencyByProcedure> listAgency;

    @NotNull
    private ArrayList<TagName> listProcedureAgencyLevel;

    @NotNull
    private SectorByProcedure sector;

    @NotNull
    private int status;  // 1 hoặc 0
}
