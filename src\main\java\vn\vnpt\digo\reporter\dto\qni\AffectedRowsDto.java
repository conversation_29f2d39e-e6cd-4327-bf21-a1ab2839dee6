/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto.qni;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AffectedRowsDto implements Serializable {

    private int affectedRows = 0;
    private String message;

    public AffectedRowsDto(int affectedRows, String message) {
        this.affectedRows = affectedRows;
        this.message = message;
    }
}
