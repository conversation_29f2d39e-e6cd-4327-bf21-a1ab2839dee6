/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.repository;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.TelecomCost;
import vn.vnpt.digo.reporter.dto.GetTelecomCostByYearDto;
import vn.vnpt.digo.reporter.dto.GetTelecomCostByMonthDto;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface TelecomCostRepository extends MongoRepository<TelecomCost, ObjectId> {

    @Query(value = "{'year': ?0, 'subscriptionType.id': {'$in' : ?1}}", fields = "{'month': 1, 'amount': 1}", sort = "{'month': 1}")
    List<GetTelecomCostByYearDto> getTelecomCostByYear(Integer year, List<ObjectId> subscriptionTypeId);

    @Query(value = "{'year': ?0}", fields = "{'month': 1, 'amount': 1}", sort = "{'month': 1}")
    List<GetTelecomCostByYearDto> getTelecomCostByYearSimple(Integer year);

    @Query(value = "{'subscriptionType.name.languageId': ?0, 'month': ?1, 'year': ?2, 'subscriptionType.id': {'$in' : ?3}}", fields = "{'subscriptionType': 1, 'subscriptionCode': 1, 'paymentCode': 1, 'amount': 1}")
    List<GetTelecomCostByMonthDto> getTelecomCostByMonth(Short languageId, Integer month, Integer year, List<ObjectId> subscriptionTypeId);

    @Query(value = "{'subscriptionType.name.languageId': ?0, 'month': ?1, 'year': ?2}", fields = "{'subscriptionType': 1, 'subscriptionCode': 1, 'paymentCode': 1, 'amount': 1}")
    List<GetTelecomCostByMonthDto> getTelecomCostByMonthSimple(Short languageId, Integer month, Integer year);

}
