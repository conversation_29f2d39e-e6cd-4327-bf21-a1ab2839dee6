package vn.vnpt.digo.reporter.config;

import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import vn.vnpt.digo.reporter.dto.DigoHttpExceptionDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.Permission;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Translator;

import java.util.Objects;

import javax.servlet.http.HttpServletResponse;

@RestControllerAdvice
public class BadRequestExceptionHandler extends ResponseEntityExceptionHandler {

    @Autowired
    private Translator translator;

    @Value("${vnpt.permission.debbuger.enable:true}")
    private boolean allowDebbuger;

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }

    @Override
    protected ResponseEntity<Object> handleServletRequestBindingException(
            ServletRequestBindingException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }

    @Override
    protected ResponseEntity<Object> handleTypeMismatch(
            TypeMismatchException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestPart(
            MissingServletRequestPartException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }

    @Override
    protected ResponseEntity<Object> handleBindException(
            BindException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        Permission isDebugger = Context.getPermission("debbuger");
        if (Objects.nonNull(isDebugger) || allowDebbuger) {
            return super.handleExceptionInternal(ex, ex.getCause().getCause().getCause().getStackTrace(), headers,
                    status, request);
        } else {
            DigoHttpException e = new DigoHttpException(Integer.parseInt("10" + HttpServletResponse.SC_BAD_REQUEST));
            DigoHttpExceptionDto errorResponse = e.getDigoHttpExceptionDto().bindMessageSource(translator);
            return super.handleExceptionInternal(ex, errorResponse, headers, status, request);
        }
    }
}
