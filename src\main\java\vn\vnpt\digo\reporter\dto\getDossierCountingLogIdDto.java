package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class getDossierCountingLogIdDto implements Serializable {

  @JsonProperty("code")
  private int code;

  @JsonProperty("listDossierId")
  private List<String> listDossierId;
}
