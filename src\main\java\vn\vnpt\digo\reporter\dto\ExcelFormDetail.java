package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.bson.types.ObjectId;
import java.io.Serializable;

@Data
public class ExcelFormDetail implements Serializable {
    private Long no;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId sectorId;
    private String sector;
    private Integer sum1;
    private Integer acceptedOnl;
    private Integer receivedDirect;
    private Integer acceptedDirect;
    private Integer pastDossier;
    private Integer sum2;
    private Integer resolvedEarly;
    private Integer returnedOnTime;
    private Integer resolvedOverdue;
    private Integer sum3;
    private Integer unresolvedNoTime;
    private Integer unresolvedOvertime;
}
