package vn.vnpt.digo.reporter.service.statistics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

@Service
public class StatisticsQNIService {
    
    Logger logger = LoggerFactory.getLogger(StatisticsQNIService.class);

    @Autowired
    private MongoTemplate mongoTemplate;
    
}
