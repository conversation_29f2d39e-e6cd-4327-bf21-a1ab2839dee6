package vn.vnpt.digo.reporter.api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.PrintToBirtViewerDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.service.PrintReportService;
import vn.vnpt.digo.reporter.service.ProcedureService;
import vn.vnpt.digo.reporter.service.ReportService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
@RestController
@RequestMapping("/print-report")
public class PrintReportController{

    @Autowired
    private PrintReportService printReportService;

    Logger logger = LoggerFactory.getLogger(PrintReportController.class);
    
    @Value(value = "${digo.microservice.gateway-url}")
    private String apiGatewayAllow;

    @PostMapping("/--output")
    public ResponseEntity<String> printToBirtViewer(
            HttpServletRequest request,
            @RequestBody PrintToBirtViewerDto body){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        UriComponentsBuilder uriApiGateway = UriComponentsBuilder.fromHttpUrl(body.getApiGateway());
        String domain = uriApiGateway.build().toUri().getScheme() + "://" + uriApiGateway.build().toUri().getHost();
        if(!domain.equals(apiGatewayAllow)){ 
            throw new DigoHttpException(11004, new String[]{"Domain apiGateway không hợp lệ"}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return printReportService.printToBirtViewer(body);
    }
}
