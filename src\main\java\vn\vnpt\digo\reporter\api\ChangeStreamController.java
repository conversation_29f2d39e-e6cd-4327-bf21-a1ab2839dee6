package vn.vnpt.digo.reporter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.ChangeStreamEventDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.service.ChangeStreamService;
import vn.vnpt.digo.reporter.util.Context;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 *
 * <AUTHOR>
 * 
 * Cung cấp bộ API theo dõi MongoDB Change Stream
 */

@RestController
@RequestMapping("/change-stream")
@IcodeAuthorize("vnpt.permission.manageDigo")
public class ChangeStreamController {

    Logger logger = LoggerFactory.getLogger(ChangeStreamController.class);
    
    @Autowired
    private ChangeStreamService changeStreamService;

    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;
    

    @GetMapping("/--events")
    public Page<ChangeStreamEventDto> getEvents (
            HttpServletRequest request,
            @RequestParam(value = "service", required = false) String service,
            @RequestParam(value = "collection", required = false) String collection,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "fromDate", required = false) String fromDate,
            @RequestParam(value = "toDate", required = false) String toDate,
            @RequestParam(value = "errorType", required = false) Integer errorType,
            @RequestParam(value = "itemId", required = false) ObjectId itemId,
            Pageable pageable
    ) {
        logger.info("DIGO-Info: " + request);
        
        List<String> rolesToCheck = List.of(adminRoles.split(","));
        if (!Context.getListPemission(rolesToCheck)) throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);

        return changeStreamService.getListChangeStreamEvent(service, collection, type, errorType, itemId, fromDate, toDate, pageable);
    }

    @GetMapping("/--retry-events")
    public AffectedRowsDto retryEvent (
            HttpServletRequest request,
            @RequestParam(value = "service", required = false) String service,
            @RequestParam(value = "collection", required = false) String collection,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "fromDate", required = false) String fromDate,
            @RequestParam(value = "toDate", required = false) String toDate,
            @RequestParam(value = "errorType", required = false) Integer errorType,
            @RequestParam(value = "itemId", required = false) ObjectId itemId
    ) {
        logger.info("DIGO-Info: " + request);
        
        List<String> rolesToCheck = List.of(adminRoles.split(","));
        if (!Context.getListPemission(rolesToCheck)) throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);

        return changeStreamService.retryEvent(service, collection, type, errorType, itemId, fromDate, toDate);
    }
    
}
