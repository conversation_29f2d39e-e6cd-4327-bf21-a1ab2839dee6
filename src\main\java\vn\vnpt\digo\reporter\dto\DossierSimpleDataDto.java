package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.Attachment;
import vn.vnpt.digo.reporter.pojo.DossierOutOfDate;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierSimpleDataDto implements Serializable {

  @Id
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId id; // dossierId
  private String sectorName;
  private String procedureName;
  private String dossierCode;
  private String dossierStatus;
  private String phone;
  private String fullName;
  private String organization;
  private Date acceptedDate;
  private String address;
  private String totalImplementDate;
  private String agency;
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId agencyId;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date appointmentDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date completedDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date returnedDate;

  private String applyMethod;
  private int applyMethodId;
  // 0 : Inprogress On Time
  // 1 : Inprogress Out Of Due
  // 2 : Completed  Early
  // 3 : Completed On Time
  // 4 : Completed Out Of Due
  private int  dossierDetailStatus;
  private String implementer;
  private int outOfDateCode; // 0: don't care, 1: OutOfDate at TaxAgency,2: OutOfDate at remaining Agency
  private List<DossierOutOfDate>  listDossierOutOfDate;
  private ArrayList<Attachment> attachment;

  @Override
  public boolean equals(Object obj) {
    if(obj instanceof DossierSimpleDataDto)
    {
      DossierSimpleDataDto temp = (DossierSimpleDataDto) obj;
      if(this.getId().equals(temp.getId()))
        return true;
    }
    return false;
  }


  @Override
  public int hashCode() {
    return (this.sectorName.hashCode() + this.procedureName.hashCode() + this.id.hashCode());
  }
}
