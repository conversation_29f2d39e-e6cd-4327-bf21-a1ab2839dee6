package vn.vnpt.digo.reporter.service;

import com.google.gson.Gson;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import vn.vnpt.digo.reporter.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.pojo.*;
import vn.vnpt.digo.reporter.util.Translator;
import vn.vnpt.digo.reporter.repository.AgencyRepository;
import vn.vnpt.digo.reporter.stream.AgencyProducerStream;
import vn.vnpt.digo.reporter.util.Context;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.ProcedureSectorAgency;
import vn.vnpt.digo.reporter.repository.ProcedureSectorAgencyRepository;
import vn.vnpt.digo.reporter.util.Microservice;

/**
 *
 * <AUTHOR>
 */
@Service
public class ProcedureSectorAgencyService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ProcedureSectorAgencyRepository agencyRepository;

    @Autowired
    private Translator translator;

    @Autowired
    private AgencyProducerStream agencyProducerStream;

    @Autowired
    private MongoTemplate mongoTemplate;

    Logger logger = org.slf4j.LoggerFactory.getLogger(ProcedureService.class);

    @Value("${digo.microservice.gateway-url}")
    private String gatewayURL;
    
//    @Value("${digo.microservice.province-procedure-agency-level-id}")
//    private String provinceProcedureAgencyLevel;
//    
//    @Value("${digo.microservice.district-procedure-agency-level-id}")
//    private String districtProcedureAgencyLevel;
//    
//    @Value("${digo.microservice.ward-procedure-agency-level-id}")
//    private String wardProcedureAgencyLevel;

    @Autowired
    private Microservice microservice;
    
    @CacheEvict(value = {"ProcedureSectorAgencyService::getListProcedureQuantityByTag"}, allEntries = true)
    @Transactional
    public AffectedRowsDto updateProcedureSector(ArrayList<ProcedureAgencySectorDto> procedureAgencySectorDto) {
        logger.info("start function updateProcedureSector");
        String token = Context.getJwtAuthenticationTokenValue();
        String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/";
        System.out.println(procedureAgencySectorDto);
        try {
            if(Objects.nonNull(procedureAgencySectorDto) && procedureAgencySectorDto.size() > 0){
                agencyRepository.deleteAll();
                for (ProcedureAgencySectorDto oneProcedureAgencySectorDto : procedureAgencySectorDto) {
                    ArrayList<ProcedureSectorAgency> listAgency = agencyRepository.getQuantityBySectorAgencyAndLevel(oneProcedureAgencySectorDto.getIdAgency(),oneProcedureAgencySectorDto.getProcedureAgencyLevel().getId());
                    if (listAgency.size() > 0) {
                        ProcedureSectorAgency item1 = listAgency.get(0);
                        System.out.println(item1);
                        item1.setSector(oneProcedureAgencySectorDto.getSector());
                        agencyRepository.save(item1);
                    } else {
                        try {
                            ProcedureSectorAgency item = new ProcedureSectorAgency();
                            item.setOriginId(oneProcedureAgencySectorDto.getIdAgency());
                            item.setSector(oneProcedureAgencySectorDto.getSector());
                            HttpHeaders headers = new HttpHeaders();
                            headers.setContentType(MediaType.APPLICATION_JSON);
                            headers.add("Authorization", "Bearer " + token);
                            HttpEntity<?> request = new HttpEntity<>(headers);
                            //Send request
                            AgencyDetailDto result = restTemplate.exchange(endpoint + oneProcedureAgencySectorDto.getIdAgency(), HttpMethod.GET, request, AgencyDetailDto.class).getBody();
                            if(result.getParent() != null){
                                item.setParentId(result.getParent());
                            }
                            if(result.getAddress() != null && result.getAddress().getPlace() != null){
                                item.setPlaceId(result.getAddress().getPlace().getId());
                            }
                            if(result.getTag() != null){
                                item.setTagAgency(result.getTag());
                            }
                            if(result.getName() != null){
                                item.setName(result.getName());
                            }
                            ArrayList<ObjectId> temp = new ArrayList<ObjectId>();
                            if(result.getAddress() != null && result.getAddress().getPlace() != null && result.getAddress().getPlace().getAncestor() != null){
                                for (IdPojo id : result.getAddress().getPlace().getAncestor()) {
                                    temp.add(id.getId());
                                }
                            }
                            item.setAncestorPlaceId(temp);
                            if(oneProcedureAgencySectorDto.getProcedureAgencyLevel() != null){
                                item.setProcedureAgencyLevel(oneProcedureAgencySectorDto.getProcedureAgencyLevel());
                            }
                            agencyRepository.save(item);
                        } catch (Exception e) {
                            System.out.println("Update failed! " + e.getMessage());
                        }
                    }
                }
                logger.info("end function updateProcedureSector success");
                return new AffectedRowsDto(1);
            }
        } catch (Exception e) {
            System.out.println("Update failed! " + e.getMessage());
        }
        logger.info("end function updateProcedureSector fail");
        return new AffectedRowsDto(0);
    }
    
    @CacheEvict(value = {"ProcedureSectorAgencyService::getListProcedureQuantityByTag"}, allEntries = true)
    @Transactional
    public AffectedRowsDto updateProcedureSectorByLevel(ArrayList<ProcedureAgencySectorDto> procedureAgencySectorDto, String agencyLevelId) {
        logger.info("start function updateProcedureSectorByLevel");
        String token = Context.getJwtAuthenticationTokenValue();
        String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/";
        System.out.println(procedureAgencySectorDto);
        try {
            if(Objects.nonNull(procedureAgencySectorDto) && procedureAgencySectorDto.size() > 0){
                Query query1 = new Query();
                if (Objects.nonNull(agencyLevelId)){
                    query1.addCriteria(Criteria.where("procedureAgencyLevel.id").is(new ObjectId(agencyLevelId)));
                }
                mongoTemplate.remove(query1, ProcedureSectorAgency.class);

                for (ProcedureAgencySectorDto oneProcedureAgencySectorDto : procedureAgencySectorDto) {
                    Query query2 = new Query();
                    query2.addCriteria(Criteria.where("originId").is(oneProcedureAgencySectorDto.getIdAgency()));
                    query2.addCriteria(Criteria.where("procedureAgencyLevel.id").is(oneProcedureAgencySectorDto.getProcedureAgencyLevel().getId()));
                    List<ProcedureSectorAgency> listAgency = mongoTemplate.find(query2, ProcedureSectorAgency.class);

                    if (listAgency.size() > 0) {
                        ProcedureSectorAgency item1 = listAgency.get(0);
                        System.out.println(item1);
                        item1.setSector(oneProcedureAgencySectorDto.getSector());
                        agencyRepository.save(item1);
                    } else {
                        try {
                            ProcedureSectorAgency item = new ProcedureSectorAgency();
                            item.setOriginId(oneProcedureAgencySectorDto.getIdAgency());
                            item.setSector(oneProcedureAgencySectorDto.getSector());
                            if(oneProcedureAgencySectorDto.getProcedureAgencyLevel() != null){
                                item.setProcedureAgencyLevel(oneProcedureAgencySectorDto.getProcedureAgencyLevel());
                            }

                            HttpHeaders headers = new HttpHeaders();
                            headers.setContentType(MediaType.APPLICATION_JSON);
                            headers.add("Authorization", "Bearer " + token);
                            HttpEntity<?> request = new HttpEntity<>(headers);
                            //Send request
                            AgencyDetailDto result = restTemplate.exchange(endpoint + oneProcedureAgencySectorDto.getIdAgency(), HttpMethod.GET, request, AgencyDetailDto.class).getBody();
                            if(result.getParent() != null){
                                item.setParentId(result.getParent());
                            }
                            if(result.getAddress() != null && result.getAddress().getPlace() != null){
                                item.setPlaceId(result.getAddress().getPlace().getId());
                            }
                            if(result.getTag() != null){
                                item.setTagAgency(result.getTag());
                            }
                            if(result.getName() != null){
                                item.setName(result.getName());
                            }
                            ArrayList<ObjectId> temp = new ArrayList<ObjectId>();
                            if(result.getAddress() != null && result.getAddress().getPlace() != null && result.getAddress().getPlace().getAncestor() != null){
                                for (IdPojo id : result.getAddress().getPlace().getAncestor()) {
                                    temp.add(id.getId());
                                }
                            }
                            item.setAncestorPlaceId(temp);

                            agencyRepository.save(item);
                        } catch (Exception e) {
                            System.out.println("Update failed! " + e.getMessage());
                        }
                    }
                }

                logger.info("end function updateProcedureSectorByLevel success");
                return new AffectedRowsDto(1);
            }
        } catch (Exception e) {
            System.out.println("Update failed! " + e.getMessage());
        }
        logger.info("end function updateProcedureSectorByLevel fail");
        return new AffectedRowsDto(0);
    }
    @CacheEvict(value = {"ProcedureSectorAgencyService::getListProcedureQuantityByTag"}, allEntries = true)
    @Transactional
    public AffectedRowsDto updateQuantitySectorProcedure(UpdateQuantityProcedureDto body) {
        logger.info("start function updateQuantitySectorProcedure");
        try {
            for (AgencyByProcedure agency : body.getListAgency()) {
                // set sector mới
                logger.info("Tao sector moi");
                AgencySector newSector = new AgencySector();
                newSector.setId(body.getSector().getId());
                newSector.setTransSector(body.getSector().getName());

                Short quantity = body.getStatus() == 1 ? (short)(1) : (short)(0);
                newSector.setProcedureQuantity(quantity);

                List<ObjectId> agencyUsed = new ArrayList<>();
                agencyUsed.add(agency.getId());
                newSector.setAgencyUsed(agencyUsed);
                // end set sector

                for (TagName procedureAgencyLevel : body.getListProcedureAgencyLevel()) {
                    if (procedureAgencyLevel.getId().equals(agency.getLevel().getId())){
                        Query query = new Query();
                        query.addCriteria(Criteria.where("originId").is(agency.getId()));
                        query.addCriteria(Criteria.where("procedureAgencyLevel.id").is(agency.getLevel().getId()));
                        List<ProcedureSectorAgency> listProcedureSectorAgency = mongoTemplate.find(query, ProcedureSectorAgency.class);
                        logger.info("listProcedureSectorAgency:  " + listProcedureSectorAgency.size());

                        if (!listProcedureSectorAgency.isEmpty()){
                            for (ProcedureSectorAgency procedureSectorAgency : listProcedureSectorAgency) {
                                boolean checkExistSector = false;

                                if (procedureSectorAgency.getSector() != null && !procedureSectorAgency.getSector().isEmpty()){
                                    // cập nhật khi có sẳn lĩnh vực
                                    logger.info("cap nhat khi co san linh vuc");
                                    for (AgencySector sector : procedureSectorAgency.getSector()) {
                                        if (sector.getId().equals(body.getSector().getId())){
                                            checkExistSector = true;
                                            Short num = sector.getProcedureQuantity();
                                            if (body.getStatus() == 1) {
                                                sector.setProcedureQuantity((short)(num + 1));
                                            } else {
                                                sector.setProcedureQuantity((short)(num - 1));
                                            }
                                        }
                                    }
                                }
                                logger.info("checkExistSector:  " + checkExistSector);

                                if (!checkExistSector){
                                    // thêm mới lĩnh vực, đã có cơ quan
                                    logger.info("them moi linh vuc, da co co quan");
                                    if (body.getStatus() == 1){
                                        if (procedureSectorAgency.getSector() != null){
                                            procedureSectorAgency.getSector().add(newSector);
                                        } else {
                                            procedureSectorAgency.setSector(new ArrayList<AgencySector>(){{add(newSector);}});
                                        }
                                    }
                                }

                                agencyRepository.save(procedureSectorAgency);
                            }
                        }
                        else {
                            // thêm mới khi chưa có lĩnh vực, chưa có cơ quan
                            logger.info("them moi khi chua co linh vuc, co quan");
                            if (body.getStatus() == 1){
                                // get agency detail
                                String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/" + agency.getId();
                                logger.info("get agency detail:  " + endpoint);
                                String token = Context.getJwtAuthenticationTokenValue();
                                HttpHeaders headers = new HttpHeaders();
                                headers.setContentType(MediaType.APPLICATION_JSON);
                                headers.add("Authorization", "Bearer " + token);
                                HttpEntity<?> request = new HttpEntity<>(headers);
                                AgencyDetailDto result = restTemplate.exchange(endpoint, HttpMethod.GET, request, AgencyDetailDto.class).getBody();
                                logger.info("result agency detail:  " + result.toString());

                                ProcedureSectorAgency item = new ProcedureSectorAgency();
                                item.setOriginId(agency.getId());
                                if(result.getParent() != null){
                                    item.setParentId(result.getParent());
                                }
                                if(result.getAddress() != null && result.getAddress().getPlace() != null){
                                    item.setPlaceId(result.getAddress().getPlace().getId());
                                }
                                if(result.getTag() != null){
                                    item.setTagAgency(result.getTag());
                                }
                                if(result.getName() != null){
                                    item.setName(result.getName());
                                }

                                ArrayList<ObjectId> ancestorPlaceId = new ArrayList<>();
                                if(result.getAddress() != null && result.getAddress().getPlace() != null && result.getAddress().getPlace().getAncestor() != null){
                                    for (IdPojo id : result.getAddress().getPlace().getAncestor()) {
                                        ancestorPlaceId.add(id.getId());
                                    }
                                }
                                item.setAncestorPlaceId(ancestorPlaceId);
                                item.setSector(new ArrayList<AgencySector>(){{add(newSector);}});
                                item.setProcedureAgencyLevel(procedureAgencyLevel);

                                agencyRepository.save(item);
                            }
                        }
                    }
                }
            }
            logger.info("end function updateQuantitySectorProcedure success");
            return new AffectedRowsDto(1);
        } catch (Exception e) {
            System.out.println("Update failed! " + e.getMessage());
        }
        logger.info("end function updateQuantitySectorProcedure fail");
        return new AffectedRowsDto(0);
    }
    
    @Cacheable(value = "ProcedureSectorAgencyService::getListProcedureQuantityByTag")
    public List<GetAgencyQuantityByProcedureSectorDto> getListProcedureQuantityByTag(ObjectId placeId, ObjectId tagId, ObjectId agencyId, ObjectId ancestorId, ArrayList<ObjectId> agencyLevelId, ArrayList<ObjectId> notAgencyLevelId) {
        
        Query query = new Query();

        if (placeId != null) {
            query.addCriteria(Criteria.where("placeId").is(placeId));
        }
        if (tagId != null) {
            query.addCriteria(Criteria.where("tagAgency").is(tagId));
        }

        if (agencyId != null) {
            query.addCriteria(Criteria.where("originId").is(agencyId));
        }

        if (agencyLevelId != null && agencyLevelId.size() > 0) {
            query.addCriteria(Criteria.where("procedureAgencyLevel.id").in(agencyLevelId));
        }
        
        if (notAgencyLevelId != null && notAgencyLevelId.size() > 0 && agencyLevelId == null) {
            query.addCriteria(Criteria.where("procedureAgencyLevel.id").nin(notAgencyLevelId));
        }
        
        List<GetAgencyQuantityByProcedureSectorDto> result = mongoTemplate.find(query, GetAgencyQuantityByProcedureSectorDto.class);
//        List<ProcedureQuantityBySectorDto> result = agencyRepository.getListProcedureQuantityByTag(placeId, tagId, agencyId, ancestorId);
//        ArrayList<GetAgencyQuantityByProcedureSectorDto> result = (ArrayList<GetAgencyQuantityByProcedureSectorDto>) accountAppDtos;
        result.forEach(procedureQuantityBySector -> {

            procedureQuantityBySector.setAgencyName(translator.getCurrentLocaleId());
            procedureQuantityBySector.getSector().forEach(transSector -> {
                transSector.setName(translator.getCurrentLocaleId());
            });
        });

        return result;
    }

    @CacheEvict(value = {"ProcedureSectorAgencyService::getListProcedureQuantityByTag"}, allEntries = true)
    @Transactional
    public AffectedRowsDto updateSectorByAgency(UpdateSectorByAgencyDto body, Boolean isReplace) {
        if (body.getSector() == null || body.getSector().isEmpty()) {
            return new AffectedRowsDto();
        }
        int affectedRows = 0;
        ArrayList<ProcedureSectorAgency> results = agencyRepository.getQuantityBySectorAgency(body.getAgencyId());
        if (!results.isEmpty()) {
            for (ProcedureSectorAgency item : results) {
                ArrayList<AgencySector> sectors = item.getSector();
                if (isReplace != null && isReplace) {
                    sectors = body.getSector();
                } else {
                    sectors.addAll(body.getSector());
                }
                item.setSector(sectors);
                agencyRepository.save(item);
                affectedRows++;
            }
            return new AffectedRowsDto(affectedRows);
        }
        return new AffectedRowsDto();
    }

}
