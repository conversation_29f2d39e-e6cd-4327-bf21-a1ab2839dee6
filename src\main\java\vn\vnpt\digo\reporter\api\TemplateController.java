/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.GetTemplateByIdDto;
import vn.vnpt.digo.reporter.dto.GetTemplateDto;
import vn.vnpt.digo.reporter.dto.TemplateFileSignDto;
import vn.vnpt.digo.reporter.dto.PostTemplateDto;
import vn.vnpt.digo.reporter.dto.PutTemplateDto;
import vn.vnpt.digo.reporter.pojo.IdResponse;
import vn.vnpt.digo.reporter.service.TemplateFileStorageService;
import vn.vnpt.digo.reporter.service.TemplateService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/template")
@IcodeAuthorize("vnpt.permission.template")
public class TemplateController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateController.class);

    @Autowired
    private TemplateService templateService;

    @Autowired
    private TemplateFileStorageService templateFileStorageService;

    @PostMapping("")
    public IdResponse createTemplate(HttpServletRequest request, @Valid @RequestBody PostTemplateDto templateDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return templateService.postTemplate(templateDto);
    }

    @GetMapping("")
    public Slice<GetTemplateDto> getListTemplate(HttpServletRequest request,
            @RequestParam(value = "keyword", defaultValue = "") String keyword,
            @RequestParam(value = "subsystem-id", required = false) String subsystemId,
            @RequestParam(value = "type-id", required = false) String typeId,
            @RequestParam(value = "spec", defaultValue = "slice") String spec,
            Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return templateService.getListTemplate(keyword, subsystemId, typeId, spec, pageable);
    }

    @GetMapping("/all")
    public List<GetTemplateDto> getListTemplate(HttpServletRequest request,
                                                @RequestParam(value = "keyword", defaultValue = "") String keyword,
                                                @RequestParam(value = "subsystem-id", required = false) String subsystemId,
                                                @RequestParam(value = "type-id", required = false) String typeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return templateService.getAllListTemplate(keyword, subsystemId, typeId);
    }

    @GetMapping("/{id}")
    public GetTemplateByIdDto getDetailTemplate(HttpServletRequest request, @PathVariable ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return templateService.getDetailTemplate(id);
    }

    @PutMapping("/{id}")
    public AffectedRowsDto updateTemplate(HttpServletRequest request, @PathVariable ObjectId id, @RequestBody PutTemplateDto templatePayload) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return templateService.putTemplate(id, templatePayload);
    }
    
    @PutMapping("/{id}/FileSign")
    public AffectedRowsDto updateTemplateFileSign(HttpServletRequest request, @PathVariable ObjectId id, @RequestBody TemplateFileSignDto templatePayload) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return templateService.putTemplateFileSignId(id, templatePayload);
    }

    @DeleteMapping("/{id}")
    public AffectedRowsDto deleteTemplate(HttpServletRequest request, @PathVariable ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        templateFileStorageService.deleteFile(id);
        return templateService.deleteTemplate(id);
    }

    @GetMapping("/{id}/--file")
    public ResponseEntity<Resource> downloadTemplateFileById(HttpServletRequest request, @PathVariable ObjectId id) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        
        // Load file as Resource
        Resource resource = templateFileStorageService.downloadTemplateFileById(id);

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            logger.info("Could not determine file type.");
        }

        // Fallback to the default content type if type could not be determined
        if (contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

}
