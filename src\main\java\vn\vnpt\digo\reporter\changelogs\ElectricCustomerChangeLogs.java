package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */

@ChangeLog(order = "2020-05-15-08-20")
public class ElectricCustomerChangeLogs {
	@ChangeSet(order = "2020-05-15-08-20", id = "ElectricCustomerChangeLogs::create", author = "haimn")
	public void create(DB db) {
		db.createCollection("electricCustomer", null);
	}

}
