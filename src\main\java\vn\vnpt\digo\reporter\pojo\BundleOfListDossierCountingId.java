package vn.vnpt.digo.reporter.pojo;
import java.util.ArrayList;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.document.DossierCountingLog;



@Data
@NoArgsConstructor
@AllArgsConstructor
public class BundleOfListDossierCountingId {
  private ListIdAndMaxDateDossierCountingLog listDossierId_3 = new ListIdAndMaxDateDossierCountingLog();
  private ListIdAndMaxDateDossierCountingLog listDossierId_2 = new ListIdAndMaxDateDossierCountingLog();
  private ListIdAndMaxDateDossierCountingLog listDossierId_4 = new ListIdAndMaxDateDossierCountingLog();
  private ListIdAndMaxDateDossierCountingLog listDossierId_5 = new ListIdAndMaxDateDossierCountingLog();
  private ListIdAndMaxDateDossierCountingLog listDossierId_6 = new ListIdAndMaxDateDossierCountingLog();

  public void parseListDossLog(List<DossierCountingLog> listDossCountingLog){
    List<String> listDossierId_temp_3 = new  ArrayList<>() ;
    List<String> listDossierId_temp_2 = new  ArrayList<>() ;
    List<String> listDossierId_temp_4 = new  ArrayList<>() ;
    List<String> listDossierId_temp_5 = new  ArrayList<>() ;
    List<String> listDossierId_temp_6 = new  ArrayList<>() ;
    if (listDossCountingLog!=null && !listDossCountingLog.isEmpty())
      for (DossierCountingLog item : listDossCountingLog){
        String temp = item.getDossierId().toString();
        if(item.getDossierDetailStatus() == 2){
          listDossierId_temp_2.add(temp);
          listDossierId_2.getMaxDate(item);
        }else if(item.getDossierDetailStatus() == 3){
          listDossierId_temp_3.add(temp);
          listDossierId_3.getMaxDate(item);
        }else if(item.getDossierDetailStatus() == 4){
          listDossierId_temp_4.add(temp);
          listDossierId_4.getMaxDate(item);
        }else if(item.getDossierDetailStatus() == 5){
          listDossierId_temp_5.add(temp);
          listDossierId_5.getMaxDate(item);
        }else if(item.getDossierDetailStatus() == 6){
          listDossierId_temp_6.add(temp);
          listDossierId_6.getMaxDate(item);
        }
      }
      listDossierId_2.setListDossierId(listDossierId_temp_2);
      listDossierId_3.setListDossierId(listDossierId_temp_3);
      listDossierId_4.setListDossierId(listDossierId_temp_4);
      listDossierId_5.setListDossierId(listDossierId_temp_5);
      listDossierId_6.setListDossierId(listDossierId_temp_6);

  }

} 
