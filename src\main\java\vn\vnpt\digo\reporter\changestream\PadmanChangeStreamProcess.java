package vn.vnpt.digo.reporter.changestream;

import com.google.gson.internal.LinkedTreeMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.changestream.document.Dossier;
import vn.vnpt.digo.reporter.changestream.pojo.TranslateName;
import vn.vnpt.digo.reporter.document.ChangeStreamEvent;
import vn.vnpt.digo.reporter.document.FlattenedDossier;
import vn.vnpt.digo.reporter.util.GsonUtils;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Component
public class PadmanChangeStreamProcess {

    @Autowired
    @Qualifier("taskExecutor")
    TaskExecutor taskExecutor;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${digo.thread.change-stream.enable}")
    private Boolean syncEnable;

    @Value("${digo.thread.change-stream.max-retry}")
    private int maxRetry;


    private static final Logger log = LoggerFactory.getLogger(PadmanChangeStreamProcess.class);

    @Async
    @Scheduled(initialDelay = 60 * 1000, fixedDelay = 200)
    @SchedulerLock(name = "processChangeStreamEvent", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void processChangeStreamEvent() {
        if (Boolean.FALSE.equals(syncEnable)) {
            // Tắt, không thực thi.
            return;
        }
        try {
            Query query = new Query(
                    Criteria.where("").andOperator(
                            Criteria.where("service").is("svc.padman"),
                            Criteria.where("errorType").ne(2),
                            Criteria.where("retryCount").lt(maxRetry)
                    )
            )
                    .with(Sort.by(Sort.Direction.ASC, "createdDate"))
                    .limit(1);

            ChangeStreamEvent event = mongoTemplate.findOne(query, ChangeStreamEvent.class);

            if (event != null) {
                boolean added = EventExecutor.changeStreamQueue.offer(event);
                if (added) {
                    mongoTemplate.remove(event);
                    log.info(" --- Event added to queue: {}", event.getItemId());
                }
            }
        } catch (Exception e) {
            log.error(" --- Error in change stream processor: {}", e.getMessage(), e);
        }
    }

    protected void handleEvent (ChangeStreamEvent event) {
        log.info(" --- Event collection: " + event.getCollection());
        int errorType = 0;
        String errorMsg = null;
        try {
            switch (event.getCollection()) {
                case "dossier":
                    Dossier dossier = GsonUtils.copyObject(event.getData(), Dossier.class);
                    if (dossier.getId() == null) {
                        dossier.setId(event.getItemId());
                    }
                    FlattenedDossier flattenedDossier = flatteningDossier(dossier);
                    if (flattenedDossier == null) {
                        errorMsg = "Không thể xử lý dữ liệu \"dossier\"!";
                        errorType = 2;
                    }
                    break;
                default:
                    errorMsg = "Collection \"" + event.getCollection() + "\" chưa được định nghĩa!";
                    errorType = 1;
                    break;
            }
        } catch (Exception e) {
            errorMsg = "Lỗi xử lý: " + e.getMessage();
            errorType = 2;
        }
        if (errorMsg != null && !errorMsg.isBlank()) {
            event.setCreatedDate(new Date());
            event.setErrorMsg(errorMsg);
            event.setErrorType(errorType);
            event.setRetryCount(Objects.nonNull(event.getRetryCount()) ? (event.getRetryCount() + 1) : 1);
            mongoTemplate.save(event);
        }
    }
    
    protected void handleFlatteningDossier (Dossier dossier) {
        try {
            flatteningDossier(dossier);
            dossier.setFlatteningCount(1);
            
        } catch (Exception e) {
            
        }
    }

    private FlattenedDossier flatteningDossier (Dossier dossier) {
        try {
            
            FlattenedDossier fd = new FlattenedDossier();
            fd.setId(dossier.getId());
            fd.setDossierCode(dossier.getCode());
            handleProcedureInfo(dossier, fd);
            handleApplicantInfo(dossier, fd);
            handleCurrentTaskInfo(dossier, fd);
            handleStatusInfo(dossier, fd);
            handleAgencyInfo(dossier, fd);
            handleOtherInfo(dossier, fd);

            mongoTemplate.save(fd);
            return fd;
        } catch (Exception e) {
            log.error("Flattening dossier {} error: {}", dossier.getId(), e.getMessage());
            return null;
        }
    }

    private void handleProcedureInfo(Dossier dossier, FlattenedDossier fd) {
        if (dossier.getProcedure() == null) return;

        var procedure = dossier.getProcedure();
        setIfNotNull(procedure::getId, fd::setProcedureId);
        setIfNotNull(procedure::getCode, fd::setProcedureCode);
        setIfNotNull(() -> getTranslateName(procedure.getTranslate()), fd::setProcedureName);

        if (procedure.getSector() != null) {
            setIfNotNull(procedure.getSector()::getId, fd::setSectorId);
            setIfNotNull(() -> getTranslateName(procedure.getSector().getName()), fd::setSectorName);
        }

        if (dossier.getAgencyLevel() != null) {
            setIfNotNull(dossier.getAgencyLevel()::getId, fd::setAgencyLevelId);
            setIfNotNull(() -> getTranslateName(dossier.getAgencyLevel().getName()), fd::setAgencyLevelName);
        }

        if (dossier.getProcedureLevel() != null) {
            var level = dossier.getProcedureLevel();
            setIfNotNull(level::getId, fd::setProcedureLevelId);
            setIfNotNull(() -> {
                if (level.getName() != null && !level.getName().isEmpty()) {
                    return level.getName().get(0).getName();
                }
                return null;
            }, fd::setProcedureLevelName);

            // Special flags based on ID
            if (level.getId() != null) {
                String idStr = level.getId().toHexString();
                switch (idStr) {
                    case "5f5b2c2b4e1bd312a6f3ae23":
                        fd.setIsProcedureLevel12(1);
                        break;
                    case "5f5b2c4b4e1bd312a6f3ae24":
                        fd.setIsProcedureLevel3(1);
                        break;
                    case "5f5b2c564e1bd312a6f3ae25":
                        fd.setIsProcedureLevel4(1);
                    break;
                }
            }
        }
    }

    private void handleApplicantInfo(Dossier dossier, FlattenedDossier fd) {
        if (dossier.getApplicant() == null || dossier.getApplicant().getData() == null) return;
        LinkedTreeMap obj = (LinkedTreeMap) dossier.getApplicant().getData();

        fd.setApplicantName(getFirstAvailableValue(obj, "fullname", "nycHoTen", "ownerFullname"));
        fd.setApplicantPhoneNumber(getFirstAvailableValue(obj, "phoneNumber", "nycPhoneNumber", "ownerPhoneNumber"));
    }

    private void handleCurrentTaskInfo(Dossier dossier, FlattenedDossier fd) {
        if (dossier.getAccepter() != null) {
            setIfNotNull(dossier.getAccepter()::getFullname, fd::setAccepterName);
        }

        if (dossier.getCurrentTask() != null && !dossier.getCurrentTask().isEmpty()) {
            var task = dossier.getCurrentTask().get(0);
            if (task.getAssignee() != null) {
                setIfNotNull(task.getAssignee()::getFullname, fd::setCurrentTaskAssigneeName);
                setIfNotNull(task.getAssignee()::getId, fd::setCurrentTaskAgencyId);
            }

            if (task.getCandidateGroup() != null && !task.getCandidateGroup().isEmpty()) {
                var group = task.getCandidateGroup().get(0);
                fd.setCurrentTaskAgencyId(group.getId());
                fd.setCurrentTaskAgencyName(getTranslateName(group.getName()));
                var ancestors = group.getAncestors();
                if (ancestors != null) {
                    if (ancestors.size() > 0) {
                        fd.setCurrentTaskAgencyAncestorsLevel1Id(ancestors.get(0).getId());
                        fd.setCurrentTaskAgencyAncestorsLevel1Name(getTranslateName(ancestors.get(0).getName()));
                    }
                    if (ancestors.size() > 1) {
                        fd.setCurrentTaskAgencyAncestorsLevel2Id(ancestors.get(1).getId());
                        fd.setCurrentTaskAgencyAncestorsLevel2Name(getTranslateName(ancestors.get(1).getName()));
                    }
                    if (ancestors.size() > 2) {
                        fd.setCurrentTaskAgencyAncestorsLevel3Id(ancestors.get(2).getId());
                        fd.setCurrentTaskAgencyAncestorsLevel3Name(getTranslateName(ancestors.get(2).getName()));
                    }
                    if (ancestors.size() > 3) {
                        fd.setCurrentTaskAgencyAncestorsLevel4Id(ancestors.get(3).getId());
                        fd.setCurrentTaskAgencyAncestorsLevel4Name(getTranslateName(ancestors.get(3).getName()));
                    }
                }
            }
        }
    }

    private void handleStatusInfo(Dossier dossier, FlattenedDossier fd) {
        if (dossier.getDossierStatus() != null) {
            var status = dossier.getDossierStatus();
            setIfNotNull(status::getId, fd::setDosserStatusId);
            setIfNotNull(() -> getTranslateName(status.getName()), fd::setDossierStatusName);

            int statusId = status.getId();
            fd = setValueDefault(fd);

            switch (statusId) {
                case 3:
                    fd.setPaused(1);
                    break;
                case 6:
                    fd.setSuspended(1);
                    break;
                case 12:
                    fd.setCancelled(1);
                    break;
                case 4:
                case 5:
                    fd.setResolved(1);
                    if (dossier.getCompletedDate() != null && dossier.getAppointmentDate() != null) {
                        if (dossier.getCompletedDate().before(dossier.getAppointmentDate()))
                            fd.setResolvedEarly(1);
                        else if (dossier.getCompletedDate().equals(dossier.getAppointmentDate()))
                            fd.setResolvedOnTime(1);
                        else
                            fd.setResolvedOverdue(1);
                    }
                    break;
                default:
                    if (statusId != 0) {
                        fd.setUnresolved(1);
                        if (dossier.getAppointmentDate() != null) {
                            if (dossier.getAppointmentDate().before(new Date()))
                                fd.setUnresolvedOverdue(1);
                            else
                                fd.setUnresolvedOnTime(1);
                        }
                    }
                    break;
            }
        }

        if (dossier.getDossierTaskStatus() != null) {
            fd.setDossierTaskStatusId(dossier.getDossierTaskStatus().getId());
            fd.setDossierTaskStatusName(getTranslateName(dossier.getDossierTaskStatus().getName()));
        }

        if (dossier.getDossierMenuTaskRemind() != null) {
            fd.setDossierMenuTaskRemindId(dossier.getDossierMenuTaskRemind().getId());
            fd.setDossierMenuTaskRemindName(getTranslateName(dossier.getDossierMenuTaskRemind().getName()));
        }

        if (dossier.getApplyMethod() != null) {
            fd.setApplyMethodId(dossier.getApplyMethod().getId());
            fd.setApplyMethodName(dossier.getApplyMethod().getName());
            if (dossier.getApplyMethod().getId() == 0) fd.setReceivedOnline(1);
            else fd.setReceivedDirect(1);
        }

        if (dossier.getDossierReceivingKind() != null) {
            fd.setDossierReceivingKindId(dossier.getDossierReceivingKind().getId());
            fd.setDossierReceivingKindName(getTranslateName(dossier.getDossierReceivingKind().getName()));
        }

        if (dossier.getPaymentMethod() != null) {
            fd.setPaymentMethodId(dossier.getPaymentMethod().getId());
            fd.setPaymentMethodName(dossier.getPaymentMethod().getName());
        }
    }

    private void handleAgencyInfo(Dossier dossier, FlattenedDossier fd) {
        if (dossier.getAgency() == null) return;

        var agency = dossier.getAgency();
        fd.setAgencyId(agency.getId());
        fd.setAgencyName(getTranslateName(agency.getName()));

        var ancestors = agency.getAncestors();
        if (ancestors == null) return;

        if (ancestors.size() > 0) {
            fd.setAgencyAncestorsLevel1Id(ancestors.get(0).getId());
            fd.setAgencyAncestorsLevel1Name(getTranslateName(ancestors.get(0).getName()));
        }
        if (ancestors.size() > 1) {
            fd.setAgencyAncestorsLevel2Id(ancestors.get(1).getId());
            fd.setAgencyAncestorsLevel2Name(getTranslateName(ancestors.get(1).getName()));
        }
        if (ancestors.size() > 2) {
            fd.setAgencyAncestorsLevel3Id(ancestors.get(2).getId());
            fd.setAgencyAncestorsLevel3Name(getTranslateName(ancestors.get(2).getName()));
        }
        if (ancestors.size() > 3) {
            fd.setAgencyAncestorsLevel4Id(ancestors.get(3).getId());
            fd.setAgencyAncestorsLevel4Name(getTranslateName(ancestors.get(3).getName()));
        }
    }

    private void handleOtherInfo(Dossier dossier, FlattenedDossier fd) {
        fd.setAppliedDate(dossier.getAppliedDate());
        fd.setCreateDate(dossier.getCreatedDate());
        fd.setAcceptedDate(dossier.getAcceptedDate());
        fd.setAppointmentDate(dossier.getAppointmentDate());
        fd.setCompletedDate(dossier.getCompletedDate());
        fd.setReturnedDate(dossier.getReturnedDate());
    }

    private String getFirstAvailableValue(LinkedTreeMap obj, String... keys) {
        for (String key : keys) {
            Object value = obj.get(key);
            if (value != null) return value.toString();
        }
        return null;
    }

    private <T> void setIfNotNull(Supplier<T> getter, Consumer<T> setter) {
        try {
            T value = getter.get();
            if (value != null) setter.accept(value);
        } catch (Exception ignored) {}
    }


    private String getTranslateName (List<TranslateName> translateNames) {
        if (translateNames == null || translateNames.isEmpty()) return "";
        for (TranslateName translateName: translateNames) {
            if (translateName.getLanguageId() == 228) return translateName.getName();
        }
        return translateNames.get(0).getName();
    }

    private static FlattenedDossier setValueDefault(FlattenedDossier flattenedDossier) {
        flattenedDossier.setResolved(0);
        flattenedDossier.setResolvedEarly(0);
        flattenedDossier.setReceivedOnline(0);
        flattenedDossier.setResolvedOverdue(0);
        flattenedDossier.setUnresolved(0);
        flattenedDossier.setUnresolvedOnTime(0);
        flattenedDossier.setUnresolvedOverdue(0);
        flattenedDossier.setPaused(0);
        flattenedDossier.setSuspended(0);
        flattenedDossier.setCancelled(0);
        return flattenedDossier;
    }
    
}

