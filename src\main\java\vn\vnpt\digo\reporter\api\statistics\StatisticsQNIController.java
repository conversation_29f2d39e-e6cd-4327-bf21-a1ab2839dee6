package vn.vnpt.digo.reporter.api.statistics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.service.statistics.StatisticsQNIService;

@RestController
@RequestMapping("/statistics-QNI")
public class StatisticsQNIController {

    Logger logger = LoggerFactory.getLogger(StatisticsQNIController.class);

    @Autowired
    private StatisticsQNIService statisticsQNIService;
    
}
