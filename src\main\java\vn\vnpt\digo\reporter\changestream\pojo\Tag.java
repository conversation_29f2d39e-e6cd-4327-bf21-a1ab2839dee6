package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
public class Tag implements Serializable{

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @Field("name")
    private ArrayList<TranslateName> name = new ArrayList<>();

    @Field("code")
    private String code;
    

    @JsonProperty("trans")
    private List<TranslateName> tagName;
    
}
