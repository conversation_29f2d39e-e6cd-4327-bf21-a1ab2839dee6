package vn.vnpt.digo.reporter.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.annotation.PostConstruct;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.annotation.FieldDescription;
import vn.vnpt.digo.reporter.dto.PostDataLogToKafkaDto;
import vn.vnpt.digo.reporter.dto.PostDataLogToKafkaDto.User;
import vn.vnpt.digo.reporter.stream.DataLoggerStream;

/**
 *
 * <AUTHOR>
 */
@Component
public class KafkaExchange {

    @Autowired
    private DataLoggerStream dataLoggerStream;

    private static DataLoggerStream stream;

    @PostConstruct
    private void initStatic() {
        stream = this.dataLoggerStream;
    }

    //param: origin, newObj instance of Document
    public static boolean pushLog(Integer groupId, Integer typeId, ObjectId itemId, Object origin, Object newObj) {
        Map<String, Object> tokenMap = Context.getJwtAuthenticationToken().getTokenAttributes();
        System.out.println(Context.getJwtAuthenticationToken().getToken().getTokenValue());
        Object userId = tokenMap.get("user_id");
        Object userName = tokenMap.get("name");
        Object deploymentId = tokenMap.get("deployment_id");
        System.out.println("Log...: user_id" + userId + " name:" + userName);
        if (Objects.nonNull(userId) && Objects.nonNull(userName)) {
            PostDataLogToKafkaDto dataLog = new PostDataLogToKafkaDto();
            dataLog.setGroupId(groupId);
            dataLog.setType(typeId);
            dataLog.setItemId(itemId);
            dataLog.setUser(new User(new ObjectId(userId.toString()), userName.toString()));
            if (deploymentId != null) {
                dataLog.setDeploymentId(new ObjectId(deploymentId.toString()));
            }
            System.out.println("makeLog");
            List<PostDataLogToKafkaDto.Action> action = makeChangedField(origin, newObj);
            if (Objects.nonNull(action)) {
                dataLog.setAction(action);
            }
            return stream.push(dataLog);
        }
        return false;
    }

    public static List<PostDataLogToKafkaDto.Action> makeChangedField(Object origin, Object newObj) {
        List<PostDataLogToKafkaDto.Action> resultList = new ArrayList<PostDataLogToKafkaDto.Action>();
        try {
            //addnew
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
            formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
            if (origin == null && newObj != null) {
                System.out.println("log NEW...");
                java.lang.reflect.Field[] fields = newObj.getClass().getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    try {
                        //System.out.println(field.getName() + ":old " + field.get(origin).toString() + " new " + field.get(newObj).toString());
                        field.setAccessible(true);
                        Object value = field.get(newObj);
                        if (value != null) {
                            PostDataLogToKafkaDto.Action resultObject = new PostDataLogToKafkaDto.Action();
                            FieldDescription logField = field.getAnnotation(FieldDescription.class);
                            if (logField != null) {
                                resultObject.setFieldNameRbk(logField.logKey());
                                resultObject.setOriginalValue("");
                                if (Date.class.equals(field.getType())) {
                                    String strDate = formatter.format(value);
                                    resultObject.setNewValue(strDate);
                                } else {
                                    resultObject.setNewValue(value.toString());
                                }
                                resultList.add(resultObject);
                            }
                        }
                    } catch (IllegalAccessException | IllegalArgumentException ex) {
                        System.out.println(ex.getMessage());
                    }
                }
            }//update
            else if (origin != null && newObj != null) {
                System.out.println("log PUT....");
                ArrayList<String> keys = new ArrayList<>();
                java.lang.reflect.Field[] fields = newObj.getClass().getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    try {
                        field.setAccessible(true);
                        keys.add(field.getName());
                        if (!Objects.equals(field.get(origin), field.get(newObj))) {
                            System.out.println(field.getName() + ":old " + (field.get(origin) == null ? "" : field.get(origin).toString()) + " new " + (field.get(newObj) == null ? "" : field.get(newObj).toString()));
                            PostDataLogToKafkaDto.Action resultObject = new PostDataLogToKafkaDto.Action();
                            FieldDescription logField = field.getAnnotation(FieldDescription.class);
                            if (logField != null) {
                                resultObject.setFieldNameRbk(logField.logKey());
                                if (Date.class.equals(field.getType())) {
                                    String oldDate = formatter.format(field.get(origin));
                                    String newDate = formatter.format(field.get(newObj));
                                    if (!oldDate.equals(newDate)) {
                                        resultObject.setOriginalValue(oldDate);
                                        resultObject.setNewValue(newDate);
                                        resultList.add(resultObject);
                                    }
                                } else {
                                    Object oldValue = field.get(origin);
                                    Object newValue = field.get(newObj);
                                    resultObject.setOriginalValue(oldValue == null ? "" : oldValue.toString());
                                    resultObject.setOriginalValue(newValue == null ? "" : newValue.toString());
                                    resultList.add(resultObject);
                                }
                            }
                        }
                    } catch (IllegalAccessException | IllegalArgumentException ex) {
                        System.out.println(ex.getMessage());
                    }
                }
                fields = origin.getClass().getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    try {
                        field.setAccessible(true);
                        if (!keys.contains(field.getName())) {
                            PostDataLogToKafkaDto.Action resultObject = new PostDataLogToKafkaDto.Action();
                            FieldDescription logField = field.getAnnotation(FieldDescription.class);
                            if (logField != null) {
                                resultObject.setFieldNameRbk(logField.logKey());
                                Object oldValue = field.get(origin);
                                resultObject.setOriginalValue(oldValue == null ? "" : oldValue.toString());
                                resultObject.setNewValue("");
                            }
                        }
                    } catch (IllegalAccessException | IllegalArgumentException ex) {
                        System.out.println(ex.getMessage());
                    }
                }
            }
        } catch (SecurityException ex) {
            System.out.println(ex.getMessage());
            Logger.getLogger(KafkaExchange.class.getName()).log(Level.SEVERE, null, ex);
        }
        return resultList;
    }

}
