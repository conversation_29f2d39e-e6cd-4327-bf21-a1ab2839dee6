package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.pojo.DossierCountingData;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DuplicateIdDto implements Serializable {

  @JsonProperty("agency")
  private DossierAgencyDto agency;

  @JsonProperty("sector")
  private DossierSectorDto sector;

  @JsonProperty("countingData")
  private DossierCountingData countingData;

}
