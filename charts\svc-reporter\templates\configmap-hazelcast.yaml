apiVersion: v1
kind: ConfigMap
metadata:
  name: svc-reporter.hazelcast
data:
  # hazelcast config
  hazelcast-config.xml: |
    <?xml version="1.0" encoding="UTF-8"?>
    <hazelcast
        xmlns="http://www.hazelcast.com/schema/config"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:schemaLocation="http://www.hazelcast.com/schema/config
               http://www.hazelcast.com/schema/config/hazelcast-config-3.12.xsd">
        <group>
            <name>svc-reporter</name>
        </group>
        <management-center enabled="false">http://localhost:8080/hazelcast-mancenter</management-center>
        <network>
            <port auto-increment="true" port-count="10">5701</port>
            <outbound-ports>
                <!--
                Allowed port range when connecting to other nodes.
                0 or * means use system provided port.
                -->
                <ports>0</ports>
            </outbound-ports>
            <join>
                <multicast enabled="false">
                    <multicast-group>*********</multicast-group>
                    <multicast-port>54327</multicast-port>
                </multicast>
                <tcp-ip enabled="true">
                    <member>127.0.0.1</member>
                </tcp-ip>
                <kubernetes enabled="false">
                    <namespace>test</namespace>
                    <service-name>svc-reporter</service-name>
                </kubernetes>
            </join>
        </network>
    </hazelcast>
    
  