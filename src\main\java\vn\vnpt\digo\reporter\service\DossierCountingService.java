package vn.vnpt.digo.reporter.service;

import com.google.common.base.Strings;
import com.mongodb.BasicDBObject;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.apache.kafka.common.protocol.types.Field;
import org.bson.BsonArray;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.exolab.castor.types.DateTime;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.DossierCounting;
import vn.vnpt.digo.reporter.document.DossierCountingLog;
import vn.vnpt.digo.reporter.document.DossierSynthesis;
import vn.vnpt.digo.reporter.document.KTMETLDossier;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.*;
import vn.vnpt.digo.reporter.pojo.ktm.AgencyIdName;
import vn.vnpt.digo.reporter.pojo.ktm.KTMDigitizationResultDTO;
import vn.vnpt.digo.reporter.pojo.ktm.SectorCase;
import vn.vnpt.digo.reporter.pojo.ktm.TotalAmountDataDTO;
import vn.vnpt.digo.reporter.properties.DossierCountingProperties;
import vn.vnpt.digo.reporter.repository.DossierCountingLogRepository;
import vn.vnpt.digo.reporter.repository.DossierCountingRepository;
import vn.vnpt.digo.reporter.util.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.FileHandler;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.base.Strings.isNullOrEmpty;
import org.springframework.cache.annotation.Cacheable;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.query.SerializationUtils.serializeToJsonSafely;

@Service
public class DossierCountingService {

  @Autowired
  private MongoTemplate mongoTemplate;
  @Autowired
  private Microservice microservice;
  @Autowired
  private RestTemplate restTemplate;
  @Autowired
  private DossierCountingRepository dossierCountingRepository;
  @Autowired
  private DossierCountingLogRepository dossierCountingLogRepository;

  @Autowired
  private DateConverter dateConverter;

  @Autowired
  private Translator translator;

  @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
  private String oidcPropertiesUrl;

  @Value(value = "${digo.petition-statistics.credentials-client-id}")
  private String clientId;

  @Value(value = "${digo.petition-statistics.credentials-client-secret}")
  private String clientSecret;

  @Value("${vnpt.permission.role.admin}")
  private String adminRoles;
  @Value("${digo.enable.hide.fullname}")
  private boolean enableHideName;

  @Value(value = Constant.LOCATION_DOSSIER_COUNTING_DETAIL)
  private Resource resourceTemplateDossierCountingDetail;

  @Value(value = Constant.LOCATION_DOSSIER_COUNTING_DETAIL_PADSVC)
  private Resource resourceTemplateDossierCountingDetailPadsvc;

  @Value(value = Constant.LOCATION_DOSSIER_COUNTING_DETAIL_WITH_LATE_AT_TAX_DEPT_OR_NATURAL_RESOURCES_AND_ENVIRONMENT)
  private Resource resourceTemplateDossierCountingDetailLateAtTaxDeptOrNaturalResourcesAndEnvironment;

  @Value(value = Constant.LOCATION_DOSSIER_LIST_DETAIL)
  private Resource resourceTemplateDossierListDetail;

  Logger logger = org.slf4j.LoggerFactory.getLogger(DossierCountingService.class);

  String token = null;

  ZoneId zoneId = ZoneId.systemDefault();
  DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

  private String getToken(){
    try {
      token = Context.getJwtAuthenticationTokenValue();
    } catch (Exception e) {
      token = MicroserviceExchange.getToken().getAccessToken();
    }
    return token;
  }

  public void getTokenClient() {
    // Header request
    URI uri = URI.create(oidcPropertiesUrl + "/protocol/openid-connect/token");
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    headers.set("Accept-Language", translator.getCurrentLocale().getLanguage());
    MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
    body.add("grant_type", "client_credentials");
    body.add("client_id", clientId);
    body.add("client_secret", clientSecret);

    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
    // Logging DigoRestTemplate calls
    logger.info("Call API -->" + uri.toString());
    logger.info("API send requests-->" + request);
    // Call DigoRestTemplate
    ResponseEntity<TokenDto> response = restTemplate.postForEntity(uri, request, TokenDto.class);
    // Print response
    logger.info("API send responses-->" + response);
    token = response.getBody().getAccess_token();
  }


  private DossierCounting getMinMaxDay(Boolean isMax, Integer year, Integer month, String agencyId) {
    Aggregation agg = null;
    Criteria aCriteria = new Criteria();
    if (isMax) {
      agg = (Aggregation) newAggregation(
              match(Criteria.where("").andOperator(
                      (year != null) ? Criteria.where("year").is(year) : aCriteria,
                      (month != null) ? Criteria.where("month").is(month) : aCriteria,
                      isNullOrEmpty(agencyId) ? aCriteria : Criteria.where("agency.id").is(new ObjectId(agencyId))
              )),
              group().max("countingDate").as("countingDate")
      );
    } else {
      agg = (Aggregation) newAggregation(
              match(Criteria.where("").andOperator(
                      (year != null) ? Criteria.where("year").is(year) : aCriteria,
                      (month != null) ? Criteria.where("month").is(month) : aCriteria,
                      isNullOrEmpty(agencyId) ? aCriteria : Criteria.where("agency.id").is(new ObjectId(agencyId))
              )),
              group().min("countingDate").as("countingDate")
      );
    }
    AggregationResults<DossierCounting> returnData = mongoTemplate.aggregate(agg, "dossierCounting", DossierCounting.class);
    if (returnData != null && !returnData.getMappedResults().isEmpty()) {
      DossierCounting result = returnData.getMappedResults().get(0);
//      Date date = result.getCountingDate();
//      Calendar cal = Calendar.getInstance();
//      cal.setTime(date);
//      cal.add(Calendar.HOUR, -7);
//      Date sevenHourBack = cal.getTime();
//      result.setCountingDate(sevenHourBack);
      return result;
    }
    return null;
  }

  private DossierCounting getMaxDateForRecusived( String agencyId,String sectorId) {
    Aggregation agg = null;
    Criteria aCriteria = new Criteria();
    agg = (Aggregation) newAggregation(
      match(Criteria.where("").andOperator(
              // (year != null) ? Criteria.where("year").is(year) : aCriteria,
              // (month != null) ? Criteria.where("month").is(month) : aCriteria,
              isNullOrEmpty(agencyId) ? aCriteria : Criteria.where("agency.id").is(new ObjectId(agencyId)),
              isNullOrEmpty(sectorId) ? aCriteria : Criteria.where("sector.id").is(new ObjectId(sectorId))
      )),
      group()
      .last("_id").as("id")
      .max("countingDate").as("countingDate"),
      // , aoc -> new Document("$unset","_id"),
      group("id").first("countingDate").as("countingDate")
    );
    AggregationResults<DossierCounting> returnData = mongoTemplate.aggregate(agg, "dossierCounting", DossierCounting.class);
    if (returnData != null && !returnData.getMappedResults().isEmpty()) {
      return returnData.getMappedResults().get(0);
    }
    return null;
  }

  @Cacheable("DossierCounting:getReportDossierByYear")
  public GetDossierCountingDto getReportDossierByYear(
          String strYear,
          String strListTagId
  ) throws ParseException {
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Criteria aCriteria = new Criteria();
    GetDossierCountingDto result = null;
    // get start date
    int lDay = 0, lYear = 0, lMonth = 0;
    int fDay = 0, fYear = 0, fMonth = 0;
    // find max day in db
    DossierCounting minDate = getMinMaxDay(false, Integer.parseInt(strYear), null, null);
    DossierCounting maxDate = getMinMaxDay(true, Integer.parseInt(strYear), null, null);
    //
    List<String> strlTagId =new ArrayList<>();
    List<ObjectId> lTagId = new ArrayList<>();
    if (!isNullOrEmpty(strListTagId)){
      strlTagId = List.of(strListTagId.split(","));
      for (String item: strlTagId){
        lTagId.add(new ObjectId(item));
      }
    }


    if (minDate == null || maxDate == null) {
      throw new DigoHttpException(11400, new String[]{});
    }
    if (maxDate != null) {
      Calendar cal = Calendar.getInstance();
      cal.setTime(maxDate.getCountingDate());
      lYear = cal.get(Calendar.YEAR);
      lMonth = cal.get(Calendar.MONTH) + 1;
      lDay = cal.get(Calendar.DAY_OF_MONTH);
    }
    if (minDate != null) {
      Calendar cal = Calendar.getInstance();
      cal.setTime(minDate.getCountingDate());
      fYear = cal.get(Calendar.YEAR);

      // check if year is min year in db => not subtract one day
      DossierCounting minDateOverYears = getMinMaxDay(false, null, null, null);
      Calendar calMinDateOverYears = Calendar.getInstance();
      calMinDateOverYears.setTime(minDateOverYears.getCountingDate());
      int minYear = calMinDateOverYears.get(Calendar.YEAR);
      if (minYear != fYear) {
        cal.add(Calendar.DATE, -1);
        fYear = cal.get(Calendar.YEAR);
      }

      fMonth = cal.get(Calendar.MONTH) + 1;
      fDay = cal.get(Calendar.DAY_OF_MONTH);
    }
    if (lYear < Integer.parseInt(strYear)) {
      throw new DigoHttpException(10004, new String[]{});
    }

    Aggregation aggTo = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    Criteria.where("year").is(lYear),
                    Criteria.where("day").is(lDay),
                    Criteria.where("month").is(lMonth),
                    (lTagId!=null && !lTagId.isEmpty())? Criteria.where("agency.tagId").in(lTagId):aCriteria
            )),
            group()
                    .first("day").as("day")
                    .first("month").as("month")
                    .first("year").as("year")
                    .sum("countingData.received").as("received")
                    .sum("countingData.inProgress").as("inProgress")
                    .sum("countingData.inProgressAndOnTime").as("inProgressAndOnTime")
                    .sum("countingData.inProgressAndOutOfDue").as("inProgressAndOutOfDue")
                    .sum("countingData.completed").as("completed")
                    .sum("countingData.completedOnTime").as("completedOnTime")
                    .sum("countingData.completedEarly").as("completedEarly")
                    .sum("countingData.completedOutOfDue").as("completedOutOfDue")
                    .sum("countingData.onlineReceived").as("onlineReceived")
                    .sum("countingData.directReceived").as("directReceived")
                    .sum("countingData.cancelled").as("cancelled")
                    .sum("countingData.suspended").as("suspended")
    );
    AggregationResults<DossierCountingSumDto> toResult = mongoTemplate.aggregate(aggTo, "dossierCounting", DossierCountingSumDto.class);
    Aggregation aggFrom = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    Criteria.where("year").is(fYear),
                    Criteria.where("month").is(fMonth),
                    Criteria.where("day").is(fDay),
                    (lTagId!=null && !lTagId.isEmpty())? Criteria.where("agency.tagId").in(lTagId):aCriteria
            )),
            group()
                    //                    .first("agency").as("agency")
                    .first("day").as("day")
                    .first("month").as("month")
                    .first("year").as("year")
                    .sum("countingData.received").as("received")
                    .sum("countingData.inProgress").as("inProgress")
                    .sum("countingData.inProgressAndOnTime").as("inProgressAndOnTime")
                    .sum("countingData.inProgressAndOutOfDue").as("inProgressAndOutOfDue")
                    .sum("countingData.completed").as("completed")
                    .sum("countingData.completedOnTime").as("completedOnTime")
                    .sum("countingData.completedEarly").as("completedEarly")
                    .sum("countingData.completedOutOfDue").as("completedOutOfDue")
                    .sum("countingData.onlineReceived").as("onlineReceived")
                    .sum("countingData.directReceived").as("directReceived")
                    .sum("countingData.cancelled").as("cancelled")
                    .sum("countingData.suspended").as("suspended")
    );
    AggregationResults<DossierCountingSumDto> fromResults = mongoTemplate.aggregate(aggFrom, "dossierCounting", DossierCountingSumDto.class);
    if (fromResults != null && !fromResults.getMappedResults().isEmpty()
            && toResult != null && !toResult.getMappedResults().isEmpty()) {
      DossierCountingSumDto fd = fromResults.getMappedResults().get(0);
      DossierCountingSumDto td = toResult.getMappedResults().get(0);
      result = calculateDossierCountingDto(fd, td);
    }
    if (Objects.isNull(result)) {
      throw new DigoHttpException(11400, new String[]{});
    }
    return result;
  }

  @Cacheable("DossierCounting:getReportDossierFromToBundle")
  public List<GetDossierCountingDto> getReportDossierFromToBundle(String strFromDate,
                                                                  String strToDate,
                                                                  String strParentId,
                                                                  String strTagAgencyId) throws JSONException, ParseException {
    List<GetDossierCountingDto> result = new ArrayList<GetDossierCountingDto>();
    int size = 50, page = 0;
    int agencyTotalPages = 0;
    do {
      if (!isNullOrEmpty(strTagAgencyId)) {
        ObjectId agencyTagId = new ObjectId(strTagAgencyId);
        if (!isNullOrEmpty(strParentId)) {
          // String getParentUrl = "http://10.58.35.22:8080/agency/--by-parent-id?tag-id=" + strTagAgencyId + "&parent-id="+strParentId;
         String parentURL = "agency/--by-parent-id?tag-id=" + strTagAgencyId + "&parent-id=" + strParentId;
         String getParentUrl = microservice.basedataUri(parentURL).toUriString();
          String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
          JSONArray parentJsonObject = new JSONArray(parentJson);
          for (int i = 0; i < parentJsonObject.length(); i++) {
            String tempParentId = parentJsonObject.getJSONObject(i).get("id").toString();
            GetDossierCountingDto ele = getReportDossierFromTo(strFromDate, strToDate, tempParentId, null, null, false);
            if (ele != null) {
              result.add(ele);
            } else {
              GetDossierCountingDto dcTemp = new GetDossierCountingDto();
              dcTemp.setAgency(new DossierAgencyDto(new ObjectId(parentJsonObject.getJSONObject(i).get("id").toString()),
                      parentJsonObject.getJSONObject(i).get("code").toString(),
                      parentJsonObject.getJSONObject(i).get("name").toString(),
                      agencyTagId));
              dcTemp.setCountingData(new DossierCountingData());
              dcTemp.setPreviousPeriod(0);
              result.add(dcTemp);
            }
          }
        } else {
          // String getAgencyUrl = "http://10.58.35.22:8080/agency/name+code?tag-id=" + agencyTagId.toString() + "&size=" + size + "&page=" + page;
         String agencyURL = "agency/name+code?tag-id=" + agencyTagId.toString() + "&size=" + size + "&page=" + page;
         String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
          String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
          JSONObject agencyJsonObject = new JSONObject(agencyJson);
          JSONArray agencyContentData = agencyJsonObject.getJSONArray("content");
          agencyTotalPages = agencyJsonObject.getInt("totalPages");
          for (int i = 0; i < agencyContentData.length(); i++) {
            String tempAgencyId = agencyContentData.getJSONObject(i).get("id").toString();
            GetDossierCountingDto ele = getReportDossierFromTo(strFromDate, strToDate, tempAgencyId, null, null, false);
            if (ele != null) {
              result.add(ele);
            } else {
              GetDossierCountingDto dcTemp = new GetDossierCountingDto();
              dcTemp.setAgency(new DossierAgencyDto(new ObjectId(agencyContentData.getJSONObject(i).get("id").toString()),
                      agencyContentData.getJSONObject(i).get("code").toString(),
                      agencyContentData.getJSONObject(i).get("name").toString(),
                      agencyTagId));
              dcTemp.setCountingData(new DossierCountingData());
              dcTemp.setPreviousPeriod(0);
              result.add(dcTemp);
            }
          }
        }

      }
      if (page <= agencyTotalPages - 1) {
        page++;
      }
    } while (page < agencyTotalPages);

    return result;
  }

  private boolean isDateExist(String dateCheck) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(dateCheck)) {
      Date byDay = df.parse(dateCheck);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }

    Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    Criteria.where("year").is(year),
                    Criteria.where("month").is(month),
                    Criteria.where("day").is(day))),
            count().as("count")
    );
    AggregationResults<DossierCountingDto> returnData = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingDto.class);
    if (returnData != null && !returnData.getMappedResults().isEmpty()) {
      return (returnData.getMappedResults().get(0).getCount() > 0) ? true : false;
    }
    return false;
  }

  private GetDossierCountingDto calculateDossierCountingDto(DossierCountingSumDto fd, DossierCountingSumDto td) {
    GetDossierCountingDto result = null;
    long received = td.getReceived() - fd.getReceived();
    long completed = td.getCompleted() - fd.getCompleted();
    long completedOnTime = td.getCompletedOnTime() - fd.getCompletedOnTime();
    long completedOutOfDue = td.getCompletedOutOfDue() - fd.getCompletedOutOfDue();
    long completedEarly = td.getCompletedEarly() - fd.getCompletedEarly();
    long inProgress = td.getInProgress();
    long inProgressAndOnTime = td.getInProgressAndOnTime();
    long inProgressAndOutOfDue = td.getInProgressAndOutOfDue();
    long previousPeriod = fd.getInProgress();
    long onlineReceived = td.getOnlineReceived() - fd.getOnlineReceived();
    long directReceived = td.getDirectReceived() - fd.getDirectReceived();
    long cancelled = td.getCancelled() - fd.getCancelled();
    long suspended = td.getSuspended() - fd.getSuspended();
    result = new GetDossierCountingDto();
    result.setCountingData(new DossierCountingData(received,
            inProgress,
            inProgressAndOnTime,
            inProgressAndOutOfDue,
            completed,
            completedOnTime,
            completedEarly,
            completedOutOfDue,
            onlineReceived,
            directReceived,
            cancelled,
            suspended));
    result.setPreviousPeriod(previousPeriod);
    result.setAgency(td.getAgency());
    return result;
  }

  private GetDossierCountingSectorDto calculateDossierCountingDto1(DossierCountingSectorSumDto fd, DossierCountingSectorSumDto td) {
    GetDossierCountingSectorDto result = null;
    long received = td.getReceived() - fd.getReceived();
    long completed = td.getCompleted() - fd.getCompleted();
    long completedOnTime = td.getCompletedOnTime() - fd.getCompletedOnTime();
    long completedOutOfDue = td.getCompletedOutOfDue() - fd.getCompletedOutOfDue();
    long inProgress = td.getInProgress();
    long inProgressAndOnTime = td.getInProgressAndOnTime();
    long inProgressAndOutOfDue = td.getInProgressAndOutOfDue();
    long previousPeriod = fd.getInProgress();
    long onlineReceived = td.getOnlineReceived() - fd.getOnlineReceived();
    long directReceived = td.getDirectReceived() - fd.getDirectReceived();
    long cancelled = td.getCancelled() - fd.getCancelled();
    long suspended = td.getSuspended() - fd.getSuspended();
    long completedEarly = td.getCompletedEarly() - fd.getCompletedEarly();
    result = new GetDossierCountingSectorDto();
    result.setCountingData(new DossierCountingData(received,
            inProgress,
            inProgressAndOnTime,
            inProgressAndOutOfDue,
            completed,
            completedOnTime,
            completedEarly,
            completedOutOfDue,
            onlineReceived,
            directReceived,
            cancelled,
            suspended));
    result.setPreviousPeriod(previousPeriod);
    result.setAgency(td.getAgency());
    result.setSector(td.getSector());
    return result;
  }

  @Cacheable("DossierCounting:getReportDossierFromTo")
  public GetDossierCountingDto getReportDossierFromTo(
          String strFromDate,
          String strToDate,
          String strAgencyId,
          String strTagAgencyId,
          String strSectorId,
          boolean isByTag) throws ParseException {
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.ENGLISH);

    Calendar calFTemp = Calendar.getInstance();
    Date byfDay = df.parse(strFromDate);
    calFTemp.setTime(byfDay);
    calFTemp.roll(Calendar.HOUR_OF_DAY, 7);
    int fTempYear = calFTemp.get(Calendar.YEAR);
    int fTempMonth = calFTemp.get(Calendar.MONTH) + 1;

    Calendar calTTemp = Calendar.getInstance();
    Date bytDay = df.parse(strToDate);
    calTTemp.setTime(bytDay);
    calFTemp.roll(Calendar.HOUR_OF_DAY, 7);
    int tTempYear = calTTemp.get(Calendar.YEAR);
    int tTempMonth = calTTemp.get(Calendar.MONTH) + 1;

    GetDossierCountingDto result = null;
    DossierCountingSumDto fd = null;
    DossierCountingSumDto td = null;

    if (isByTag) {
      fd = getDossierCountingByTagAgency(strFromDate, strTagAgencyId);
      td = getDossierCountingByTagAgency(strToDate, strTagAgencyId);
    } else {
      fd = getDossierCountingByAgency(strFromDate, strAgencyId, strSectorId);
      td = getDossierCountingByAgency(strToDate, strAgencyId, strSectorId);
    }

    if (Objects.isNull(fd) && Objects.isNull(td)) {  //1
      DossierCounting maxDate = getMinMaxDay(true, tTempYear, null, strAgencyId);
      DossierCounting minDate = getMinMaxDay(false, tTempYear, null, strAgencyId);
      if (maxDate == null) {
        return null;
      }
      if (maxDate.getCountingDate().after(bytDay)) { //input tDate fDate < maxDate ,below
        return null;
      } else if (maxDate.getCountingDate().before(byfDay)) { // maxDate<fd
        return null;
      } else {  // mindate<   , above
        fd = (fd == null) ? new DossierCountingSumDto() : fd;
        DossierCounting maxDate1 = getMinMaxDay(true, tTempYear, null, strAgencyId);
        if (maxDate1 == null) {
          return null;
        }
        if (isByTag) {
          td = getDossierCountingByTagAgency(df.format(maxDate1.getCountingDate()), strTagAgencyId);
        } else {
          td = getDossierCountingByAgency(df.format(maxDate1.getCountingDate()), strAgencyId, strSectorId);
        }
        result = calculateDossierCountingDto(fd, td);
      }
    } else if (!Objects.isNull(fd) && !Objects.isNull(td)) { //in range
      result = calculateDossierCountingDto(fd, td);
    } else if (!Objects.isNull(fd) && Objects.isNull(td)) { // fd in and td out of range
      fd = (fd == null) ? new DossierCountingSumDto() : fd;
      DossierCounting maxDate = getMinMaxDay(true, tTempYear, null, strAgencyId);
      if (maxDate == null) {
        return null;
      }
      if (isByTag) {
        td = getDossierCountingByTagAgency(df.format(maxDate.getCountingDate()), strTagAgencyId);
      } else {
        td = getDossierCountingByAgency(df.format(maxDate.getCountingDate()), strAgencyId, strSectorId);
      }
      result = calculateDossierCountingDto(fd, td);
    } else if (Objects.isNull(fd) && !Objects.isNull(td)) {
      // fd < mindate
      fd = (fd == null) ? new DossierCountingSumDto() : fd;
      if (isByTag) {
        td = getDossierCountingByTagAgency(strToDate, strTagAgencyId);
      } else {
        td = getDossierCountingByAgency(strToDate, strAgencyId, strSectorId);
      }
      result = calculateDossierCountingDto(fd, td);
    }

//    if(!Objects.isNull(td)){
//      fd = (fd==null)? new DossierCountingSumDto():fd;
//      result = calculateDossierCountingDto(fd,td);
//    } else if (!Objects.isNull(fd)){
//      if (td == null) {
//        DossierCounting maxDate = getMinMaxDay(true,tTempYear,tTempMonth,strAgencyId);
//        if(maxDate==null) return null;
//        if(isByTag){
//          td = getDossierCountingByTagAgency(df.format(maxDate.getCountingDate()),strTagAgencyId);
//        }else{
//          td = getDossierCountingByAgency(df.format(maxDate.getCountingDate()),strAgencyId,strSectorId);
//        }
//        result = calculateDossierCountingDto(fd,td);
//      }
//
//    }else{
//      if (td == null) {
//        fd = (fd==null)? new DossierCountingSumDto():fd;
//        DossierCounting maxDate = getMinMaxDay(true,tTempYear,null,strAgencyId);
//        if(maxDate==null) return null;
//        if(isByTag){
//          td = getDossierCountingByTagAgency(df.format(maxDate.getCountingDate()),strTagAgencyId);
//        }else{
//          td = getDossierCountingByAgency(df.format(maxDate.getCountingDate()),strAgencyId,strSectorId);
//        }
//        result = calculateDossierCountingDto(fd,td);
//      }
//    }
    return result;
  }

  //  public GetDossierCountingDto getReportDossierFromTo(
//            String strFromDate,
//            String strToDate,
//            String strAgencyId,
//            String strTagAgencyId,
//            String strSectorId,
//            boolean isByTag) throws ParseException {
//    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
//
//
//    /////
//    GetDossierCountingDto result = null;
//    DossierCountingSumDto fd = null;
//    DossierCountingSumDto td = null;
//    // check date time
//
//    if (isDateExist(strFromDate) && isDateExist(strToDate)){
//      if(isByTag){
//        fd = getDossierCountingByTagAgency(strFromDate,strTagAgencyId);
//        td = getDossierCountingByTagAgency(strToDate,strTagAgencyId);
//      }else{
//        fd = getDossierCountingByAgency(strFromDate,strAgencyId,strSectorId);
//        td = getDossierCountingByAgency(strToDate,strAgencyId,strSectorId);
//      }
//      if(!Objects.isNull(fd)  && !Objects.isNull(td)){
//        result = calculateDossierCountingDto(fd,td);
//      }
//    } else{
//      fd = (fd==null)? new DossierCountingSumDto():fd;
//      // get maxdate
//      Calendar calTTemp = Calendar.getInstance();
//      Date bytDay = df.parse(strToDate);
//      calTTemp.setTime(bytDay);
//      calTTemp.roll(Calendar.HOUR_OF_DAY, 7);
//      int tTempYear = calTTemp.get(Calendar.YEAR);
//      DossierCounting maxDate = getMinMaxDay(true,tTempYear,null,strAgencyId);
//      if(maxDate==null) return null;
//      if(isByTag){
//        td = getDossierCountingByTagAgency(df.format(maxDate.getCountingDate()),strTagAgencyId);
//      }else{
//        td = getDossierCountingByAgency(df.format(maxDate.getCountingDate()),strAgencyId,strSectorId);
//      }
//      result = calculateDossierCountingDto(fd,td);
//    }
//    if(Objects.isNull(result)){
//      throw new DigoHttpException(10004, new String[]{});
//    }
//    return result;
//  }
  public List<GetDossierCountingSectorDto> getReportDossierFromToByAgency(
          String strFromDate,
          String strToDate,
          String strAgencyId,
          String strTagAgencyId,
          boolean isByTag) throws ParseException {
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    /////
    List<GetDossierCountingSectorDto> result = new ArrayList<GetDossierCountingSectorDto>();
    List<DossierCountingSectorSumDto> fd = null;
    List<DossierCountingSectorSumDto> td = null;
    // check date time

    if (isDateExist(strFromDate) && isDateExist(strToDate)) {
      if (isByTag) {
        fd = getDossierCountingByTagAgency1(strFromDate, strTagAgencyId);
        td = getDossierCountingByTagAgency1(strToDate, strTagAgencyId);
      } else {
        fd = getDossierCountingByAgency1(strFromDate, strAgencyId, null);
        td = getDossierCountingByAgency1(strToDate, strAgencyId, null);
      }
      if (!Objects.isNull(fd) && !Objects.isNull(td)) {
        for (int i = 0; i < fd.size(); i++) {
          for (int j = 0; j < td.size(); j++) {
            String fdSector = fd.get(i).getSector().getId().toString();
            String tdSector = td.get(j).getSector().getId().toString();
            if (fdSector.equals(tdSector)) {
              GetDossierCountingSectorDto x = calculateDossierCountingDto1(fd.get(i), td.get(j));
              result.add(x);
            }
          }
        }
      }
    } else {
      fd = (fd == null) ? new ArrayList<DossierCountingSectorSumDto>() : fd;
      // get maxdate
      Calendar calTTemp = Calendar.getInstance();
      Date bytDay = df.parse(strToDate);
      calTTemp.setTime(bytDay);
      calTTemp.roll(Calendar.HOUR_OF_DAY, 7);
      int tTempYear = calTTemp.get(Calendar.YEAR);
      DossierCounting maxDate = getMinMaxDay(true, tTempYear, null, strAgencyId);
      if (maxDate == null) {
        return null;
      }
      if (isByTag) {
        td = getDossierCountingByTagAgency1(df.format(maxDate.getCountingDate()), strTagAgencyId);
      } else {
        td = getDossierCountingByAgency1(df.format(maxDate.getCountingDate()), strAgencyId, null);
      }
      for (int i = 0; i < fd.size(); i++) {
        for (int j = 0; j < td.size(); j++) {
          String fdSector = fd.get(i).getSector().getId().toString();
          String tdSector = td.get(j).getSector().getId().toString();
          if (fdSector.equals(tdSector)) {
            GetDossierCountingSectorDto x = calculateDossierCountingDto1(fd.get(i), td.get(j));
            result.add(x);
          }
        }
      }
    }
    if (Objects.isNull(result)) {
      throw new DigoHttpException(10004, new String[]{});
    }
    return result;
  }

  public AffectedRowsDto deleteReportDossierByDay(ObjectId id) throws ParseException {
    DossierCounting dc = dossierCountingRepository.getDossierCounting(id);
    AffectedRowsDto affectedRows = new AffectedRowsDto(0);
    if (dc != null) {
      dossierCountingRepository.deleteDossierCountingById(dc.getId());
      affectedRows.setAffectedRows(1);
    }
    return affectedRows;
  }

  public AffectedRowsDto deleteReportDossierByDay(String strByDay,
                                                  String strAgencyId,
                                                  String strSectorId,
                                                  String byTag
  ) throws ParseException {
    int count = 0;
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strByDay)) {
      Date byDay = df.parse(strByDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }

    Criteria aCriteria = new Criteria();
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    ObjectId sectorId = !isNullOrEmpty(strSectorId) ? new ObjectId(strSectorId) : null;
    ObjectId tagId = !isNullOrEmpty(byTag) ? new ObjectId(byTag) : null;
    Query query = new Query();
    query.addCriteria(Criteria.where("").andOperator(
            (agencyId != null) ? Criteria.where("agency.id").is(agencyId) : aCriteria,
            (sectorId != null) ? Criteria.where("sector.id").is(sectorId) : aCriteria,
            (tagId != null) ? Criteria.where("agency.tagId").is(tagId) : aCriteria,
            (year != null) ? Criteria.where("year").is(year) : aCriteria,
            (month != null) ? Criteria.where("month").is(month) : aCriteria,
            (day != null) ? Criteria.where("day").is(day) : aCriteria
    ));
    List<DossierCounting> result = mongoTemplate.find(query, DossierCounting.class);
    AffectedRowsDto affectedRows = new AffectedRowsDto(0);
    if (result != null) {
      for (DossierCounting item : result) {
        dossierCountingRepository.deleteDossierCountingById(item.getId());
        // update DossierCountingLog
        Query queryLog = new Query();
        List<DossierCountingLog> listDossCountingLog = new ArrayList<>();
        //get data
        queryLog.addCriteria(Criteria.where("").andOperator(
                Criteria.where("dossierCountingId").is(item.getId())
        ));
        listDossCountingLog = mongoTemplate.find(queryLog, DossierCountingLog.class);
        for (DossierCountingLog itemLog :listDossCountingLog){
          if (itemLog.getDossierCountingId().size()>1){
            //update
            List<ObjectId> listDossierCountingId = itemLog.getDossierCountingId();
            listDossierCountingId.remove(item.getId());
            dossierCountingLogRepository.save(itemLog);
          }else{
            //remove
            dossierCountingLogRepository.deleteDossierCountingById(itemLog.getId());
          }

        }
        count++;
      }

      affectedRows.setAffectedRows(count);
    }


    return affectedRows;
  }

  public DossierCounting getReportDossierByDay(ObjectId id) throws ParseException {
    DossierCounting dc = dossierCountingRepository.getDossierCounting(id);

    if (Objects.isNull(dc)) {
      throw new DigoHttpException(11000, HttpServletResponse.SC_NOT_FOUND);
    }

    return dc;
  }

  public Page<DossierCounting> getReportDossierByDay(String strByDay,
                                                     String strAgencyId,
                                                     String strAgencyTagId,
                                                     String strSectorId,
                                                     String spec,
                                                     Pageable pageable
  ) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strByDay)) {
      Date byDay = df.parse(strByDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    ObjectId agencyTagId = !isNullOrEmpty(strAgencyTagId) ? new ObjectId(strAgencyTagId) : null;
    ObjectId sectorId = !isNullOrEmpty(strSectorId) ? new ObjectId(strSectorId) : null;
    Query query = new Query();

    //get data
    query.addCriteria(Criteria.where("").andOperator(
            (agencyId != null) ? Criteria.where("agency.id").is(agencyId) : aCriteria,
            (agencyTagId != null) ? Criteria.where("agency.tagId").is(agencyTagId) : aCriteria,
            (sectorId != null) ? Criteria.where("sector.id").is(sectorId) : aCriteria,
            (year != null) ? Criteria.where("year").is(year) : aCriteria,
            (month != null) ? Criteria.where("month").is(month) : aCriteria,
            (day != null) ? Criteria.where("day").is(day) : aCriteria
    ));
    query.with(pageable);
    query.with(Sort.by(Sort.Order.desc("createdDate")));
    long offset = pageable.getPageNumber() * pageable.getPageSize();
    query.skip(offset);
    query.limit(pageable.getPageSize());

    List<DossierCounting> result = mongoTemplate.find(query, DossierCounting.class);
    Page<DossierCounting> page = PageableExecutionUtils.getPage(result, pageable,
            () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), DossierCounting.class));
    if (Objects.isNull(result)) {
      throw new DigoHttpException(11000, HttpServletResponse.SC_NOT_FOUND);
    }
    return page;
  }

  private List<DossierCounting> mapDossierCounting(List<DossierCounting> resultList) {
    List<DossierCounting> result = new ArrayList<>();
    for (DossierCounting item : resultList) {
      DossierCounting temp = new DossierCounting();
      temp.setCountingDate(item.getCountingDate());
      temp.setCountingData(item.getCountingData());
      temp.setYear(item.getYear());
      temp.setMonth(item.getMonth());
      temp.setUpdatedDate(item.getUpdatedDate());
      temp.setCreatedDate(item.getCreatedDate());
      temp.setAgency(item.getAgency());
      temp.setSector(item.getSector());
    }
    return result;
  }

  public DossierCountingSumDto getDossierCountingByAgency(String strbyDay,
                                                          String strAgencyId,
                                                          String strSectorId) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strbyDay)) {
      Date byDay = df.parse(strbyDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    ObjectId sectorId = !isNullOrEmpty(strSectorId) ? new ObjectId(strSectorId) : null;
    Query query = new Query();
    //get data
    Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    (agencyId != null) ? Criteria.where("agency.id").is(agencyId) : aCriteria,
                    (sectorId != null) ? Criteria.where("sector.id").is(sectorId) : aCriteria,
                    (year != null) ? Criteria.where("year").is(year) : aCriteria,
                    (month != null) ? Criteria.where("month").is(month) : aCriteria,
                    (day != null) ? Criteria.where("day").is(day) : aCriteria
            )),
            group("agency.id")
                    .first("agency").as("agency")
                    .sum("countingData.received").as("received")
                    .sum("countingData.inProgress").as("inProgress")
                    .sum("countingData.inProgressAndOnTime").as("inProgressAndOnTime")
                    .sum("countingData.inProgressAndOutOfDue").as("inProgressAndOutOfDue")
                    .sum("countingData.completed").as("completed")
                    .sum("countingData.completedEarly").as("completedEarly")
                    .sum("countingData.completedOnTime").as("completedOnTime")
                    .sum("countingData.completedOutOfDue").as("completedOutOfDue")
                    .sum("countingData.onlineReceived").as("onlineReceived")
                    .sum("countingData.directReceived").as("directReceived")
                    .sum("countingData.cancelled").as("cancelled")
                    .sum("countingData.suspended").as("suspended")
    );
    AggregationResults<DossierCountingSumDto> groupResults = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingSumDto.class);
    if (groupResults.getMappedResults().isEmpty()) {
      return null;
    }
    return groupResults.getMappedResults().get(0);
  }

  //phuongktm
  public List<DossierCountingSectorSumDto> getDossierCountingByAgency1(String strbyDay,
                                                                       String strAgencyId,
                                                                       String strSectorId) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strbyDay)) {
      Date byDay = df.parse(strbyDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    ObjectId sectorId = !isNullOrEmpty(strSectorId) ? new ObjectId(strSectorId) : null;
    Query query = new Query();
    //get data
    Aggregation agg = (Aggregation) newAggregation(
            project(
                    "sector",
                    "agency",
                    "countingData.received",
                    "countingData.inProgress",
                    "countingData.inProgressAndOnTime",
                    "countingData.inProgressAndOutOfDue",
                    "countingData.completed",
                    "countingData.completedEarly",
                    "countingData.completedOnTime",
                    "countingData.completedOutOfDue",
                    "countingData.onlineReceived",
                    "countingData.directReceived",
                    "countingData.cancelled",
                    "countingData.suspended",
                    "year",
                    "month",
                    "day"
            ),
            match(Criteria.where("").andOperator(
                    (agencyId != null) ? Criteria.where("agency.id").is(agencyId) : aCriteria,
                    (sectorId != null) ? Criteria.where("sector.id").is(sectorId) : aCriteria,
                    (year != null) ? Criteria.where("year").is(year) : aCriteria,
                    (month != null) ? Criteria.where("month").is(month) : aCriteria,
                    (day != null) ? Criteria.where("day").is(day) : aCriteria
            ))
            //            group()
            //                    .first("sector").as("sector")
            //                    .first("agency").as("agency")
            //                    .sum("countingData.received").as("received")
            //                    .sum("countingData.inProgress").as("inProgress")
            //                    .sum("countingData.inProgressAndOnTime").as("inProgressAndOnTime")
            //                    .sum("countingData.inProgressAndOutOfDue").as("inProgressAndOutOfDue")
            //                    .sum("countingData.completed").as("completed")
            //                    .sum("countingData.completedOnTime").as("completedOnTime")
            //                    .sum("countingData.completedOutOfDue").as("completedOutOfDue")
            //                    .sum("countingData.onlineReceived").as("onlineReceived")
            //                    .sum("countingData.directReceived").as("directReceived")
            //                    .sum("countingData.cancelled").as("cancelled")
            //                    .sum("countingData.suspended").as("suspended")
    );
    AggregationResults<DossierCountingSectorSumDto> groupResults = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingSectorSumDto.class);
    if (groupResults.getMappedResults().isEmpty()) {
      return null;
    }
    return groupResults.getMappedResults();
  }

  public DossierCountingSumDto getDossierCountingByTagAgency(String strbyDay,
                                                             String strAgencyTagId) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strbyDay)) {
      Date byDay = df.parse(strbyDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyTagId = !isNullOrEmpty(strAgencyTagId) ? new ObjectId(strAgencyTagId) : null;
    Query query = new Query();
    //get data
    Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    (agencyTagId != null) ? Criteria.where("agency.tagId").is(agencyTagId) : aCriteria,
                    (year != null) ? Criteria.where("year").is(year) : aCriteria,
                    (month != null) ? Criteria.where("month").is(month) : aCriteria,
                    (day != null) ? Criteria.where("day").is(day) : aCriteria
            )),
            group()
                    //                    .first("agency").as("agency")
                    .sum("countingData.received").as("received")
                    .sum("countingData.inProgress").as("inProgress")
                    .sum("countingData.inProgressAndOnTime").as("inProgressAndOnTime")
                    .sum("countingData.inProgressAndOutOfDue").as("inProgressAndOutOfDue")
                    .sum("countingData.completed").as("completed")
                    .sum("countingData.completedOnTime").as("completedOnTime")
                    .sum("countingData.completedEarly").as("completedEarly")
                    .sum("countingData.completedOutOfDue").as("completedOutOfDue")
                    .sum("countingData.onlineReceived").as("onlineReceived")
                    .sum("countingData.directReceived").as("directReceived")
                    .sum("countingData.cancelled").as("cancelled")
                    .sum("countingData.suspended").as("suspended")
    );
    AggregationResults<DossierCountingSumDto> groupResults = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingSumDto.class);
    if (groupResults.getMappedResults().isEmpty()) {
      return null;
    }
    return groupResults.getMappedResults().get(0);
  }

  //phuong ktm
  public List<DossierCountingSectorSumDto> getDossierCountingByTagAgency1(String strbyDay,
                                                                          String strAgencyTagId) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strbyDay)) {
      Date byDay = df.parse(strbyDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyTagId = !isNullOrEmpty(strAgencyTagId) ? new ObjectId(strAgencyTagId) : null;
    Query query = new Query();
    //get data
    Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    (agencyTagId != null) ? Criteria.where("agency.tagId").is(agencyTagId) : aCriteria,
                    (year != null) ? Criteria.where("year").is(year) : aCriteria,
                    (month != null) ? Criteria.where("month").is(month) : aCriteria,
                    (day != null) ? Criteria.where("day").is(day) : aCriteria
            )),
            group()
                    //                    .first("agency").as("agency")
                    .sum("countingData.received").as("received")
                    .sum("countingData.inProgress").as("inProgress")
                    .sum("countingData.inProgressAndOnTime").as("inProgressAndOnTime")
                    .sum("countingData.inProgressAndOutOfDue").as("inProgressAndOutOfDue")
                    .sum("countingData.completed").as("completed")
                    .sum("countingData.completedOnTime").as("completedOnTime")
                    .sum("countingData.completedOutOfDue").as("completedOutOfDue")
                    .sum("countingData.onlineReceived").as("onlineReceived")
                    .sum("countingData.directReceived").as("directReceived")
                    .sum("countingData.cancelled").as("cancelled")
                    .sum("countingData.suspended").as("suspended")
    );
    AggregationResults<DossierCountingSectorSumDto> groupResults = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingSectorSumDto.class);
    if (groupResults.getMappedResults().isEmpty()) {
      return null;
    }
    return groupResults.getMappedResults();
  }

  public AffectedRowsDto runReportDossier(
          String from,
          String to,
          String strAgencyTagId,
          String strListIsDept,
          String milestone
          ) throws ParseException, JSONException, IOException {
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Date fromDate = df.parse(from);
    Date toDate = df.parse(to);
    Calendar cStart = Calendar.getInstance();
    cStart.setTime(fromDate);
    Calendar cEnd = Calendar.getInstance();
    cEnd.setTime(toDate);
    int count = 0;
    AffectedRowsDto result = new AffectedRowsDto();
    while (cStart.before(cEnd)) {
      //add one day to date
      cStart.add(Calendar.DAY_OF_MONTH, 1);
//      cStart.add(Calendar.HOUR_OF_DAY, 7); // +7 hour
      count = result.getAffectedRows() + postReportDossierByDay(df.format(cStart.getTime()), strAgencyTagId,milestone, strListIsDept).getAffectedRows();
//      System.out.println(df.format(cStart.getTime()));
//      Date date = df.parse(df.format(cStart.getTime()));
      result.setAffectedRows(count);
    }
    return result;
  }

  public Date addDayToJavaUtilDate(Date date, int day) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, day);
    return calendar.getTime();
  }

  public AffectedRowsDto postReportDossierByDay(String strByDay,
                                                String strAgencyTagId,
                                                String strListIsDept,
                                                String milestone
  ) throws ParseException, IOException, JSONException {
    // your date
    int count = 0;
    int size = 50;
    int page = 0;
    Date currentDate = new Date();
    boolean isDept = false;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

//    Date date = addDayToJavaUtilDate(df.parse(strByDay),1);
    DossierCounting previousDossCounting =null;
    Calendar cal = Calendar.getInstance();
    cal.setTime(df.parse(strByDay));
    int year = cal.get(Calendar.YEAR);
    int month = cal.get(Calendar.MONTH) + 1;
    int day = cal.get(Calendar.DAY_OF_MONTH);
    cal.add(Calendar.DATE, -1);
    Date dateBefore1Days = cal.getTime();
    String byDay = (strByDay != null && strByDay != "") ? new StringBuilder(strByDay).toString() : null;
    //get list isDept
    List<Boolean> lisDept = new ArrayList<Boolean>();
    for (String item : strListIsDept.split(",")) {
      lisDept.add(Boolean.valueOf(item));
    }
    // check milestone
    milestone = isNullOrEmpty(milestone)?null:milestone;
    // get all agency
    if (Objects.nonNull(strAgencyTagId) && strAgencyTagId.length() != 0) {
      List<String> lAgencyTagId = List.of(strAgencyTagId.split(","));
      for (int a = 0; a < lAgencyTagId.size(); ++a) {
        ObjectId agencyTagId = new ObjectId(lAgencyTagId.get(a));
        int agencyTotalPages = 0;
        TAGID_BREAKLABEL:
        do {
          try{
            // String getAgencyUrl = new StringBuilder("http://10.58.35.22:8080/agency/name+code?tag-id=").append(agencyTagId.toString()).append("&size="+size +"&page="+page).toString();
            String agencyURL = "agency/name+code?tag-id=" + agencyTagId.toString() + "&size=" + size + "&page=" + page;
            String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
            String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
            JSONObject agencyJsonObject = new JSONObject(agencyJson);
            JSONArray agencyContentData = agencyJsonObject.getJSONArray("content");
            agencyTotalPages = agencyJsonObject.getInt("totalPages");
            if (agencyContentData != null) {
              for (int i = 0; i < agencyContentData.length(); i++) {
                // get list sector by agencyId
  //              logger.info("agencyContentData: " + agencyContentData.length());
                String tempAgencyId = agencyContentData.getJSONObject(i).get("id").toString();
                // String getSectorUrl = new StringBuilder("http://10.58.35.22:8083/sector/--all?agency-id=").append(tempAgencyId + "&onlyAgencyId=1").toString();
                String sectorURL = "sector/--all?agency-id=" + tempAgencyId + "&onlyAgencyId=1";
                String getSectorUrl = microservice.basepadUri(sectorURL).toUriString();
                String sectorJson = MicroserviceExchange.get(restTemplate, getSectorUrl, String.class);
                JSONArray sectorJsonObject = new JSONArray(sectorJson);
                for (int j = 0; j < sectorJsonObject.length(); ++j) {
                  String tempSectorId = sectorJsonObject.getJSONObject(j).get("id").toString();
                  List<DossierCounting> lDossTemp = getListDossierCounting(df.format(dateBefore1Days),
                        new ArrayList<String>(){{add(tempAgencyId);}},new ArrayList<String>(){{add(tempSectorId);}}
                  );
                  if (lDossTemp!=null && !lDossTemp.isEmpty()){
                    previousDossCounting = lDossTemp.get(0);
                  }
                  List<DossierCountingLog> listDossCountingLog = getAllDossierRecusiveByEnterDay( tempAgencyId, tempSectorId);
                  BundleOfListDossierCountingId bundleListDoss = new BundleOfListDossierCountingId();
                  bundleListDoss.parseListDossLog(listDossCountingLog);
                  ArrayList<DossierCountingDto> recordData = setDossierCountingData(byDay, tempAgencyId, tempSectorId,milestone, lisDept.get(a),false,bundleListDoss);
                  // CapData
                  String sectorName = new String();
                  if (sectorJsonObject.getJSONObject(j).getJSONArray("name").length() > 0) {
                    sectorName = sectorJsonObject.getJSONObject(j).getJSONArray("name").getJSONObject(0).get("name").toString();
                  }
                  DossierCounting dossCounting = new DossierCounting();
                  dossCounting.setDay(day);
                  dossCounting.setMonth(month);
                  dossCounting.setYear(year);
                  dossCounting.setCountingDate(df.parse(strByDay));
                  dossCounting.setCountingData(parserDataDossierCountingDto(recordData,bundleListDoss));
                  dossCounting.setAgency(new DossierAgencyDto(
                                  new ObjectId(agencyContentData.getJSONObject(i).get("id").toString()),
                                  agencyContentData.getJSONObject(i).get("code").toString(),
                                  agencyContentData.getJSONObject(i).get("name").toString(),
                                  agencyTagId
                          )
                  );
                  dossCounting.setSector(new DossierSectorDto(
                          new ObjectId(sectorJsonObject.getJSONObject(j).get("id").toString()),
                          sectorJsonObject.getJSONObject(j).get("code").toString(),
                          sectorName
                  ));
                  dossCounting.setCreatedDate(currentDate);
                  dossCounting.setUpdatedDate(currentDate);

                  if (previousDossCounting != null){
                    dossCounting.setPreviousDossierCountingId(previousDossCounting.getId());
                  }
                  dossierCountingRepository.save(dossCounting);
                  ObjectId dossId = dossCounting.getId();
                  saveDossLog2(recordData,dossId,listDossCountingLog);
                  count++;
                }
              }
            }
            if (page <= agencyTotalPages - 1) {
              page++;
            }
          }catch (Exception e) {
            logger.info("DIGO-Info: " + e.getMessage());
            break TAGID_BREAKLABEL;// break if Exception occur when running
          }

        } while (page < agencyTotalPages);

      }
    }
    AffectedRowsDto result = new AffectedRowsDto(count);

    return result;
  }

  public AffectedRowsDto postReportDossierByDayWithToken( String strByDay,
                                                          String strAgencyTagId,
                                                          String strListIsDept,
                                                          String milestone
  ) throws ParseException, IOException, JSONException {
    // your date
//    getToken();
    getTokenClient();

    int count = 0;
    int size = 50;
    int page = 0;
    Date currentDate = new Date();
    boolean isDept = false;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

//    Date date = addDayToJavaUtilDate(df.parse(strByDay),1);
    DossierCounting previousDossCounting =null;
    Calendar cal = Calendar.getInstance();
    cal.setTime(df.parse(strByDay));
    int year = cal.get(Calendar.YEAR);
    int month = cal.get(Calendar.MONTH) + 1;
    int day = cal.get(Calendar.DAY_OF_MONTH);
    cal.add(Calendar.DATE, -1);
    Date dateBefore1Days = cal.getTime();
    String byDay = (strByDay != null && strByDay != "") ? new StringBuilder(strByDay).toString() : null;
    //get list isDept
    List<Boolean> lisDept = new ArrayList<Boolean>();
    for (String item : strListIsDept.split(",")) {
      lisDept.add(Boolean.valueOf(item));
    }
    // check milestone
    milestone = isNullOrEmpty(milestone)?null:milestone;
    // get all agency
    if (Objects.nonNull(strAgencyTagId) && strAgencyTagId.length() != 0) {
      List<String> lAgencyTagId = List.of(strAgencyTagId.split(","));
      for (int a = 0; a < lAgencyTagId.size(); ++a) {
        ObjectId agencyTagId = new ObjectId(lAgencyTagId.get(a));
        int agencyTotalPages = 0;
        TAGID_BREAKLABEL:
        do {
          try{
            // String getAgencyUrl = new StringBuilder("http://10.58.35.22:8080/agency/name+code?tag-id=").append(agencyTagId.toString()).append("&size="+size +"&page="+page).toString();
            String agencyURL = "agency/name+code?tag-id=" + agencyTagId.toString() + "&size=" + size + "&page=" + page;
            String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
            String agencyJson = MicroserviceExchange.get(token,restTemplate, getAgencyUrl, String.class);
            JSONObject agencyJsonObject = new JSONObject(agencyJson);
            JSONArray agencyContentData = agencyJsonObject.getJSONArray("content");
            agencyTotalPages = agencyJsonObject.getInt("totalPages");
            if (agencyContentData != null) {
              for (int i = 0; i < agencyContentData.length(); i++) {
                // get list sector by agencyId
  //              logger.info("agencyContentData: " + agencyContentData.length());
                String tempAgencyId = agencyContentData.getJSONObject(i).get("id").toString();
                // String getSectorUrl = new StringBuilder("http://10.58.35.22:8083/sector/--all?agency-id=").append(tempAgencyId + "&onlyAgencyId=1").toString();
                String sectorURL = "sector/--all?agency-id=" + tempAgencyId + "&onlyAgencyId=1";
                String getSectorUrl = microservice.basepadUri(sectorURL).toUriString();
                String sectorJson = MicroserviceExchange.get(token,restTemplate, getSectorUrl, String.class);
                JSONArray sectorJsonObject = new JSONArray(sectorJson);
                for (int j = 0; j < sectorJsonObject.length(); ++j) {
                  String tempSectorId = sectorJsonObject.getJSONObject(j).get("id").toString();
                  List<DossierCounting> lDossTemp = getListDossierCounting(df.format(dateBefore1Days),
                        new ArrayList<String>(){{add(tempAgencyId);}},new ArrayList<String>(){{add(tempSectorId);}}
                  );
                  if (lDossTemp!=null && !lDossTemp.isEmpty()){
                    previousDossCounting = lDossTemp.get(0);
                  }
                  List<DossierCountingLog> listDossCountingLog = getAllDossierRecusiveByEnterDay( tempAgencyId, tempSectorId);
                  BundleOfListDossierCountingId bundleListDoss = new BundleOfListDossierCountingId();
                  bundleListDoss.parseListDossLog(listDossCountingLog);
                  ArrayList<DossierCountingDto> recordData = setDossierCountingData(byDay, tempAgencyId, tempSectorId,milestone, lisDept.get(a),true,bundleListDoss);
                  // CapData
                  String sectorName = new String();
                  if (sectorJsonObject.getJSONObject(j).getJSONArray("name").length() > 0) {
                    sectorName = sectorJsonObject.getJSONObject(j).getJSONArray("name").getJSONObject(0).get("name").toString();
                  }
                  DossierCounting dossCounting = new DossierCounting();
                  dossCounting.setDay(day);
                  dossCounting.setMonth(month);
                  dossCounting.setYear(year);
                  dossCounting.setCountingDate(df.parse(strByDay));
                  dossCounting.setCountingData(parserDataDossierCountingDto(recordData,bundleListDoss));
                  dossCounting.setAgency(new DossierAgencyDto(
                                  new ObjectId(agencyContentData.getJSONObject(i).get("id").toString()),
                                  agencyContentData.getJSONObject(i).get("code").toString(),
                                  agencyContentData.getJSONObject(i).get("name").toString(),
                                  agencyTagId
                          )
                  );
                  dossCounting.setSector(new DossierSectorDto(
                          new ObjectId(sectorJsonObject.getJSONObject(j).get("id").toString()),
                          sectorJsonObject.getJSONObject(j).get("code").toString(),
                          sectorName
                  ));
                  dossCounting.setCreatedDate(currentDate);
                  dossCounting.setUpdatedDate(currentDate);

                  if (previousDossCounting != null){
                    dossCounting.setPreviousDossierCountingId(previousDossCounting.getId());
                  }
                  dossierCountingRepository.save(dossCounting);
                  ObjectId dossId = dossCounting.getId();
                  saveDossLog2(recordData,dossId,listDossCountingLog);
                  count++;
                }
              }
            }
            if (page <= agencyTotalPages - 1) {
              page++;
            }
          }catch (Exception e) {
            logger.info("DIGO-Info: " + e.getMessage());
            break TAGID_BREAKLABEL;// break if Exception occur when running
          }

        } while (page < agencyTotalPages);

      }
    }
    AffectedRowsDto result = new AffectedRowsDto(count);

    return result;
  }


  public  List<DossierCounting> getListDossierCounting(
          String byDate,
          List<String> strListAgencyId,
          List<String> strListSectorId

  ) throws ParseException {
    if (strListAgencyId==null || strListAgencyId.isEmpty()  || isNullOrEmpty(byDate))
      return null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Calendar cal = Calendar.getInstance();
    cal.setTime(df.parse(byDate));
    int year = cal.get(Calendar.YEAR);
    int month = cal.get(Calendar.MONTH) + 1;
    int day = cal.get(Calendar.DAY_OF_MONTH);
    Criteria aCriteria = new Criteria();
    List<ObjectId> listSectorId = new ArrayList<>();
    if (strListSectorId!=null && !strListSectorId.isEmpty()){
      for(String item : strListSectorId){
        listSectorId.add( new ObjectId(item));
      }
    }
    //
    List<ObjectId> listAgencyId = new ArrayList<>();
    if (strListAgencyId!=null && !strListAgencyId.isEmpty()){
      for(String item : strListAgencyId){
        listAgencyId.add( new ObjectId(item));
      }
    }
    //get previous data
    Query query = new Query();
    query.addCriteria(Criteria.where("").andOperator(
            (listAgencyId != null && !listAgencyId.isEmpty()) ? Criteria.where("agency.id").in(listAgencyId) : aCriteria,
            (listSectorId != null && !listSectorId.isEmpty()) ? Criteria.where("sector.id").in(listSectorId) : aCriteria,
            Criteria.where("day").is(day),
            Criteria.where("month").is(month),
            Criteria.where("year").is(year)
    ));
    List<DossierCounting> doss = mongoTemplate.find(query, DossierCounting.class);
    return doss;
  }
  private  List<getDossierCountingLogIdDto> getListIdDossierCountingLogDto(String strByDay,String strAgencyId,String strSectorId) throws ParseException {
    // find id
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Calendar cal = Calendar.getInstance();
    cal.setTime(df.parse(strByDay));
    cal.add(Calendar.DATE, -1);
    Date dateBefore1Days = cal.getTime();
    cal.setTime(dateBefore1Days);
    String strDate = df.format(dateBefore1Days);
    List<DossierCounting> lDossTemp = getListDossierCounting(strDate,
        new ArrayList<String>(){{add(strAgencyId);}},new ArrayList<String>(){{add(strSectorId);}}
    ); //dossBeforeOneDate
    if (lDossTemp==null && lDossTemp.isEmpty())
      return null;
    DossierCounting dossBeforeOneDate = lDossTemp.get(0);
    ObjectId dossBeforeOneDateId = dossBeforeOneDate.getId();
    //Search Log by Id
    Criteria aCriteria = new Criteria();
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    ObjectId sectorId = !isNullOrEmpty(strSectorId) ? new ObjectId(strSectorId) : null;
    //get data
    Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    (agencyId != null) ? Criteria.where("dossierCountingId").is(dossBeforeOneDateId) : aCriteria
            )),
            group("dossierDetailStatus")
                    .first("dossierDetailStatus").as("code")
                    .push(new BasicDBObject("id", "$_id")).as("listDossierId")

    );
    AggregationResults<getDossierCountingLogIdDto> groupResults = mongoTemplate.aggregate(agg, "dossierCounting", getDossierCountingLogIdDto.class);
    if (groupResults.getMappedResults().isEmpty()) {
      return null;
    }
    return groupResults.getMappedResults();
  }
  public AffectedRowsDto postReportDossierByDayAgency(String strByDay,
                                                      String strAgencyId,
                                                      boolean isDept,
                                                      String strTagAgencyId,
                                                      String milestone
  ) throws ParseException, IOException, JSONException {
    // your date
    int count = 0;
    getToken();
    Date currentDate = new Date();
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
//    Date date = addDayToJavaUtilDate(df.parse(strByDay),1);
    DossierCounting previousDossCounting=null;
    Calendar cal = Calendar.getInstance();
    cal.setTime(df.parse(strByDay));
    int year = cal.get(Calendar.YEAR);
    int month = cal.get(Calendar.MONTH) + 1;
    int day = cal.get(Calendar.DAY_OF_MONTH);
    cal.add(Calendar.DATE, -1);
    Date dateBefore1Days = cal.getTime();
    String byDay = (strByDay != null && strByDay != "") ? new StringBuilder(strByDay).toString() : null;
    try{
      // String getSectorUrl = new StringBuilder("http://10.58.35.22:8083/sector/--all?agency-id=").append(strAgencyId).append("&onlyAgencyId=1").toString();
      String sectorURL = "sector/--all?agency-id=" + strAgencyId + "&onlyAgencyId=1";
      String getSectorUrl = microservice.basepadUri(sectorURL).toUriString();
      String sectorJson = MicroserviceExchange.get(restTemplate, getSectorUrl, String.class);
      JSONArray sectorJsonObject = new JSONArray(sectorJson);
      for (int j = 0; j < sectorJsonObject.length(); ++j) {
        String tempSectorId = sectorJsonObject.getJSONObject(j).get("id").toString();
        List<DossierCounting> lDossTemp = getListDossierCounting(df.format(dateBefore1Days),
                new ArrayList<String>(){{add(strAgencyId);}},new ArrayList<String>(){{add(tempSectorId);}}
        );

        if (lDossTemp!=null && !lDossTemp.isEmpty()){
          previousDossCounting = lDossTemp.get(0);
        }
        // get all dossier recusive
        List<DossierCountingLog> listDossCountingLog = getAllDossierRecusiveByEnterDay(strAgencyId, tempSectorId);
        BundleOfListDossierCountingId bundleListDoss = new BundleOfListDossierCountingId();
        bundleListDoss.parseListDossLog(listDossCountingLog);
        ArrayList<DossierCountingDto> recordData = setDossierCountingData(byDay, strAgencyId, tempSectorId,milestone,isDept,false,bundleListDoss);
        String sectorName = new String();
        if (sectorJsonObject.getJSONObject(j).getJSONArray("name").length() > 0) {
          sectorName = sectorJsonObject.getJSONObject(j).getJSONArray("name").getJSONObject(0).get("name").toString();
        }

        DossierCounting dossCounting = new DossierCounting();
        dossCounting.setDay(day);
        dossCounting.setMonth(month);
        dossCounting.setYear(year);
        dossCounting.setCountingDate(df.parse(strByDay));
        dossCounting.setCountingData(parserDataDossierCountingDto(recordData,bundleListDoss));
        //get agency detail
        // String getDetailAgencyUrl = new StringBuilder("http://10.58.35.22:8080/agency/").append(strAgencyId).toString();
        String detailAgencyURL = "agency/" + strAgencyId;
        String getDetailAgencyUrl = microservice.basedataUri(detailAgencyURL).toUriString();
        String detailAgencyJson = MicroserviceExchange.get(restTemplate, getDetailAgencyUrl, String.class);
        JSONObject detailAgencyJsonObject = new JSONObject(detailAgencyJson);
        JSONArray arrTag = detailAgencyJsonObject.getJSONArray("tag");
        Boolean isCorrectTagId = false;
        for (int i = 0; i < arrTag.length(); i++) {
          if(strTagAgencyId.equals(arrTag.get(i).toString())){
            isCorrectTagId = true;
            break;
          }
        }
        if(!isCorrectTagId || isNullOrEmpty(strTagAgencyId)){
          throw new DigoHttpException(11401, new String[]{});
        }
        JSONArray arrAgency = detailAgencyJsonObject.getJSONArray("name");
        String agencyName = new String();
        for (int i = 0; i < arrAgency.length(); i++) {
          JSONObject temp = (JSONObject) arrAgency.get(i);
          if(temp.get("languageId").toString().equals("228")){
            agencyName = temp.get("name").toString();
          }

        }
        dossCounting.setAgency(new DossierAgencyDto(
                        new ObjectId(strAgencyId),
                        detailAgencyJsonObject.get("code").toString(),
                        agencyName,
                        new ObjectId(strTagAgencyId)
                )
        );
        dossCounting.setSector(new DossierSectorDto(
                new ObjectId(sectorJsonObject.getJSONObject(j).get("id").toString()),
                sectorJsonObject.getJSONObject(j).get("code").toString(),
                sectorName
        ));
        dossCounting.setCreatedDate(currentDate);
        dossCounting.setUpdatedDate(currentDate);

        if (previousDossCounting != null){
          dossCounting.setPreviousDossierCountingId(previousDossCounting.getId());
        }
        dossierCountingRepository.save(dossCounting);
        ObjectId dossId = dossCounting.getId();
        if (!isRecordDataEmpty(recordData)){
          saveDossLog2(recordData,dossId,listDossCountingLog);
        }
        count++;
      }
    }catch (Exception e) {
      logger.info("DIGO-Info: " + e.getMessage());
    }

    AffectedRowsDto result = new AffectedRowsDto(count);

    return result;
  }
  private List<DossierCountingLog> getRecursiveDossierCountingLogData(List<String> strListAgencyId, List<String> strListSectorId,
                                                       String strFromDate, String strToDate,boolean isRecusiveFind,
                                                       List<Integer> lStatusCode,Integer isOutOfDateCode,boolean isTaxAndNREAgency,
                                                       List<ObjectId> listNREAgencyId
                                                       ) throws ParseException{
    Criteria aCriteria = new Criteria();
    AggregationResults<DossierCountingLog> listTemp =null ;
     if (strListAgencyId==null || strListAgencyId.isEmpty()  || isNullOrEmpty(strFromDate) || isNullOrEmpty(strToDate))
      return null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Calendar f_cal = Calendar.getInstance();
    f_cal.setTime(df.parse(strFromDate));
    // int f_year = f_cal.get(Calendar.YEAR);
    // int f_month = f_cal.get(Calendar.MONTH) + 1;
    // int f_day = f_cal.get(Calendar.DAY_OF_MONTH);
    Date  fd = (df.parse(strFromDate));
    Date  td = (df.parse(strToDate));
    Calendar t_cal = Calendar.getInstance();
    t_cal.setTime(df.parse(strToDate));
    int t_year = t_cal.get(Calendar.YEAR);
    int t_month = t_cal.get(Calendar.MONTH) + 1;
    int t_day = t_cal.get(Calendar.DAY_OF_MONTH);

    long diffInMillies = df.parse(strToDate).getTime() - df.parse(strFromDate).getTime();
    long dayDiff = TimeUnit.DAYS.convert(diffInMillies,TimeUnit.MILLISECONDS);
    long gapTime = 0;
    // counting data at 15/5 (input enter -> from  14/5 to 15/5 ) : didn't need recusive to counting data
    if (dayDiff -2  == -1){ // take 1 day
      isRecusiveFind = false;
    } else {
      // cause dossierCounting save previous dossierCountingId gapTime = dayDiff - 2
      // counting data from 15 -16/5  ( input enter -> from 14/5 to  16/5) gaptime  = 2 -2 =0
      // we have dossierCountingId and DossierCountingPreviousID in listChild
      // ex: dossierCountingId of day 16 and dossierCountingId day 15
      gapTime = dayDiff -2 ;
    }
    List<ObjectId> listSectorId = new ArrayList<>();
    if (strListSectorId!=null && !strListSectorId.isEmpty()){
      for(String item : strListSectorId){
        listSectorId.add( new ObjectId(item));
      }
    }
    //
    List<ObjectId> listAgencyId = new ArrayList<>();
    if (strListAgencyId!=null && !strListAgencyId.isEmpty()){
      for(String item : strListAgencyId){
        listAgencyId.add( new ObjectId(item));
      }
    }

    if(!isRecusiveFind){ // find by Only toDate
      Aggregation agg = (Aggregation) newAggregation(
        match(Criteria.where("").andOperator(
          (listAgencyId != null && !listAgencyId.isEmpty()) ?  Criteria.where("agency.id").in(listAgencyId): aCriteria,
          (listSectorId != null && !listSectorId.isEmpty()) ?  Criteria.where("sector.id").in(listSectorId): aCriteria,
          (!isNullOrEmpty(strToDate)) ? Criteria.where("day").in(t_day): aCriteria,
          (!isNullOrEmpty(strToDate)) ? Criteria.where("month").in(t_month): aCriteria,
          (!isNullOrEmpty(strToDate)) ? Criteria.where("year").in(t_year): aCriteria
        )),
        group().addToSet("$_id").as("listChild"),
        lookup("dossierCountingLog", "listChild", "dossierCountingId", "result"),
        unwind("result"),
        replaceRoot("result"),
        match(Criteria.where("").andOperator(
          (lStatusCode != null && !lStatusCode.isEmpty()) ? Criteria.where("dossierDetailStatus").in(lStatusCode) : aCriteria,
          (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria,
          (isTaxAndNREAgency && listNREAgencyId!=null && !listNREAgencyId.isEmpty()) ? Criteria.where("agencyId").in(listNREAgencyId): aCriteria
        ))
      );
      listTemp = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingLog.class);
    }else{ // find recusive from toDate
      // we using maxdept to recusive number data we want
      Aggregation agg = (Aggregation) newAggregation(
        match(Criteria.where("").andOperator(
              (listAgencyId != null && !listAgencyId.isEmpty()) ?  Criteria.where("agency.id").in(listAgencyId): aCriteria,
              (listSectorId != null && !listSectorId.isEmpty()) ?  Criteria.where("sector.id").in(listSectorId): aCriteria,
              (!isNullOrEmpty(strToDate)) ? Criteria.where("day").in(t_day): aCriteria,
              (!isNullOrEmpty(strToDate)) ? Criteria.where("month").in(t_month): aCriteria,
              (!isNullOrEmpty(strToDate)) ? Criteria.where("year").in(t_year): aCriteria
        )),
        graphLookup("dossierCounting").startWith("$previousDossierCountingId")
        .connectFrom("previousDossierCountingId").connectTo("_id").maxDepth(gapTime).as("listChild"),
        aoc -> new Document("$addFields",new Document("listChild",new Document("$reverseArray",
        new Document("$map",
          new Document("input","$listChild")
          .append("as", "t")
          .append("in","$$t._id")
          // .append("in",new Document("childId","$$t._id"))
        )))),
        project("listChild","agency.id"),
        aoc -> new Document("$addFields",new Document("listChild",new Document("$concatArrays",
          Arrays.asList("$listChild",Arrays.asList("$_id"))))),
        unwind("listChild"),
        group().addToSet("listChild").as("listChild"),
        lookup("dossierCountingLog", "listChild", "dossierCountingId", "result"),
        unwind("result"),
        replaceRoot("result"),
        match(Criteria.where("").andOperator(
          (lStatusCode != null && !lStatusCode.isEmpty()) ? Criteria.where("dossierDetailStatus").in(lStatusCode) : aCriteria,
          (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria,
          (isTaxAndNREAgency && listNREAgencyId!=null && !listNREAgencyId.isEmpty()) ? Criteria.where("agencyId").in(listNREAgencyId): aCriteria
        ))
        );
        listTemp = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingLog.class);
        // listTemp = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingListChildDto.class);
    }

    if(listTemp!=null && !listTemp.getMappedResults().isEmpty()){
      return listTemp.getMappedResults();
    }
    return null;
  }
  public List<DossierCountingLog> getAllDossierRecusiveByEnterDay( String strAgencyId,String strSectorId){
    // Query query = new Query();
    // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    // LocalDateTime  dateTime=  LocalDateTime.parse(byDate,formatter);
    // get lastes DossierCounting Data
    DossierCounting  temp = getMaxDateForRecusived( strAgencyId, strSectorId);
    if (temp!=null){
      //recusive all dossierCountingId
      List<DossierCountingLog> lisDossierCountingId = getRecursiveDossierCountingLogData(temp.getId(),strAgencyId,strSectorId);
      return lisDossierCountingId;
      // //find all dossierCountingLog by dossierCountingId
      // if (lisDossierCountingId != null) {
      //   query.addCriteria(Criteria.where("").andOperator(
      //           Criteria.where("dossierCountingId").in(lisDossierCountingId)
      //   ));
      //   List<DossierCountingLog> listDossCountingLog = mongoTemplate.find(query, DossierCountingLog.class);
      //   return listDossCountingLog;
      // }
    }
    return null;
  }
  private List<ObjectId> getRecursiveDossierCountingId(List<String> strListAgencyId, List<String> strListSectorId,
                                                       String strFromDate, String strToDate,boolean isRecusiveFind) throws ParseException{
    Criteria aCriteria = new Criteria();
    AggregationResults<DossierCountingListChildDto> listTemp =null ;
     if (strListAgencyId==null || strListAgencyId.isEmpty()  || isNullOrEmpty(strFromDate) || isNullOrEmpty(strToDate))
      return null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Calendar f_cal = Calendar.getInstance();
    f_cal.setTime(df.parse(strFromDate));
    // int f_year = f_cal.get(Calendar.YEAR);
    // int f_month = f_cal.get(Calendar.MONTH) + 1;
    // int f_day = f_cal.get(Calendar.DAY_OF_MONTH);
    Date  fd = (df.parse(strFromDate));
    Date  td = (df.parse(strToDate));
    Calendar t_cal = Calendar.getInstance();
    t_cal.setTime(df.parse(strToDate));
    int t_year = t_cal.get(Calendar.YEAR);
    int t_month = t_cal.get(Calendar.MONTH) + 1;
    int t_day = t_cal.get(Calendar.DAY_OF_MONTH);

    long diffInMillies = df.parse(strToDate).getTime() - df.parse(strFromDate).getTime();
    long dayDiff = TimeUnit.DAYS.convert(diffInMillies,TimeUnit.MILLISECONDS);
    long gapTime = 0;
    // counting data at 15/5 (input enter -> from  14/5 to 15/5 ) : didn't need recusive to counting data
    if (dayDiff -2  == -1){ // take 1 day
      isRecusiveFind = false;
    } else {
      // cause dossierCounting save previous dossierCountingId gapTime = dayDiff - 2
      // counting data from 15 -16/5  ( input enter -> from 14/5 to  16/5) gaptime  = 2 -2 =0
      // we have dossierCountingId and DossierCountingPreviousID in listChild
      // ex: dossierCountingId of day 16 and dossierCountingId day 15
      gapTime = dayDiff -2 ;
    }
    List<ObjectId> listSectorId = new ArrayList<>();
    if (strListSectorId!=null && !strListSectorId.isEmpty()){
      for(String item : strListSectorId){
        listSectorId.add( new ObjectId(item));
      }
    }
    //
    List<ObjectId> listAgencyId = new ArrayList<>();
    if (strListAgencyId!=null && !strListAgencyId.isEmpty()){
      for(String item : strListAgencyId){
        listAgencyId.add( new ObjectId(item));
      }
    }

    if(!isRecusiveFind){ // find by Only toDate
      Aggregation agg = (Aggregation) newAggregation(
        match(Criteria.where("").andOperator(
          (listAgencyId != null && !listAgencyId.isEmpty()) ?  Criteria.where("agency.id").in(listAgencyId): aCriteria,
          (listSectorId != null && !listSectorId.isEmpty()) ?  Criteria.where("sector.id").in(listSectorId): aCriteria,
          (!isNullOrEmpty(strToDate)) ? Criteria.where("day").in(t_day): aCriteria,
          (!isNullOrEmpty(strToDate)) ? Criteria.where("month").in(t_month): aCriteria,
          (!isNullOrEmpty(strToDate)) ? Criteria.where("year").in(t_year): aCriteria
        )),
        group().addToSet("$_id").as("listChild")
      );
      listTemp = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingListChildDto.class);
    }else{ // find recusive from toDate
      // we using maxdept to recusive number data we want
      Aggregation agg = (Aggregation) newAggregation(
        match(Criteria.where("").andOperator(
              (listAgencyId != null && !listAgencyId.isEmpty()) ?  Criteria.where("agency.id").in(listAgencyId): aCriteria,
              (listSectorId != null && !listSectorId.isEmpty()) ?  Criteria.where("sector.id").in(listSectorId): aCriteria,
              (!isNullOrEmpty(strToDate)) ? Criteria.where("day").in(t_day): aCriteria,
              (!isNullOrEmpty(strToDate)) ? Criteria.where("month").in(t_month): aCriteria,
              (!isNullOrEmpty(strToDate)) ? Criteria.where("year").in(t_year): aCriteria
        )),
        graphLookup("dossierCounting").startWith("$previousDossierCountingId")
        .connectFrom("previousDossierCountingId").connectTo("_id").maxDepth(gapTime).as("listChild"),
        aoc -> new Document("$addFields",new Document("listChild",new Document("$reverseArray",
        new Document("$map",
          new Document("input","$listChild")
          .append("as", "t")
          .append("in","$$t._id")
          // .append("in",new Document("childId","$$t._id"))
        )))),
        project("listChild","agency.id"),
        aoc -> new Document("$addFields",new Document("listChild",new Document("$concatArrays",
          Arrays.asList("$listChild",Arrays.asList("$_id"))))),
        unwind("listChild"),
        group().addToSet("listChild").as("listChild")
        );
        listTemp = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingListChildDto.class);
    }

    if(listTemp!=null && !listTemp.getMappedResults().isEmpty()){
      return listTemp.getMappedResults().get(0).getListChild();
    }
    return null;
  }


  private List<DossierCountingLog> getRecursiveDossierCountingLogData(ObjectId dossId,String strAgencyId,String strSectorId){
    Criteria aCriteria = new Criteria();
    //using graphLookup to recusive all DossierCountingLog by _id and insert to listChild
    AggregationResults<DossierCountingLog> listTemp =null ;
    if (dossId!=null){
      Aggregation agg = (Aggregation) newAggregation(
      match(Criteria.where("").andOperator(
              (!isNullOrEmpty(strAgencyId)) ? Criteria.where("agency.id").is(new ObjectId(strAgencyId)): aCriteria,
              (!isNullOrEmpty(strSectorId)) ? Criteria.where("sector.id").is(new ObjectId(strSectorId)): aCriteria
      )),
      match(Criteria.where("").andOperator(
              (dossId != null) ?  Criteria.where("_id").is(dossId): aCriteria
      )),
      graphLookup("dossierCounting").startWith("$previousDossierCountingId")
      .connectFrom("previousDossierCountingId").connectTo("_id").as("listChild"),
      aoc -> new Document("$addFields",new Document("listChild",new Document("$reverseArray",
      new Document("$map",
        new Document("input","$listChild")
        .append("as", "t")
        .append("in","$$t._id")
        // .append("in",new Document("childId","$$t._id"))
      )))),
      project("listChild"),
      aoc -> new Document("$addFields",new Document("listChild",new Document("$concatArrays",
      Arrays.asList("$listChild",Arrays.asList("$_id"))))),
      lookup("dossierCountingLog", "listChild", "dossierCountingId", "result"),
      unwind("result"),
      replaceRoot("result")
      // project("_id","dossierDetailStatus","dossierId","acceptedDate","dossierCountingId")
      ).withOptions(newAggregationOptions().allowDiskUse(true).build());
      listTemp = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingLog.class);
    }

    if(listTemp!=null && !listTemp.getMappedResults().isEmpty()){
      return listTemp.getMappedResults();
    }
    return null;
  }
  public boolean isRecordDataEmpty(List<DossierCountingDto> recordData){
    boolean result = true;
    for(var item : recordData){
      if (item.getCount()>0){
        result = false;
        return result;
      }
    }
    return result;
  }
  public void saveDossLog2( List<DossierCountingDto> recordData, ObjectId dossId,List<DossierCountingLog> listDossCountingLog ){
    // Query query = new Query();
    // List<ObjectId> lisDossierCountingId = new ArrayList<>();
    Set<DossierCountingLog> hSet = new HashSet<DossierCountingLog>();
    // List<DossierCountingLog> listDossCountingLog = new ArrayList<>();
    // get recursive
    // //get dossierCountingLog data by id
    // if (previousDossId != null) {
    //   lisDossierCountingId = getRecursiveDossierCountingId(dossId);
    // }
    // // find all dossierCountingLog by dossierCountingId
    // if (lisDossierCountingId != null) {
    //   query.addCriteria(Criteria.where("").andOperator(
    //           Criteria.where("dossierCountingId").in(lisDossierCountingId)
    //   ));
    //   listDossCountingLog = mongoTemplate.find(query, DossierCountingLog.class);
    // }
    // add elemnet to set if it exist we can't add into hSet
    if (listDossCountingLog!=null && !listDossCountingLog.isEmpty())
    for(var item : listDossCountingLog){
      hSet.add(item);
    }

    for (DossierCountingDto item: recordData) { // 6 elements
      // if (item.getCode() != 7 && item.getCode() != 8  && item.getLDossierSimpleData()!=null && !item.getLDossierSimpleData().isEmpty()){ // != apply method
        if (item.getCode() != 7 && item.getCode() != 8 ){
        for (DossierSimpleDataDto simpleItem: item.getLDossierSimpleData()) {
          DossierCountingLog dosTemp = InsertDossierCountingLog(simpleItem,dossId);
          if (hSet.add(dosTemp) == true){
            // if dossierCountingLog return from padman doesn't exist
            // append it into DB
            DossierCountingLog record = InsertDossierCountingLog(simpleItem,dossId);
            dossierCountingLogRepository.save(record);
          }else if(hSet.add(dosTemp) == false && (item.getCode() == 0||item.getCode() == 1)){
            // if it exist and  dossier in progress check detail status and update record
            DossierCountingLog dossLog = hSet.stream().filter(e->
            (e.equals(dosTemp) && e.getDossierDetailStatus() == dosTemp.getDossierDetailStatus())).findFirst().orElse(null);
            if(dossLog!=null){
              List<ObjectId> temp = dossLog.getDossierCountingId();
              temp.add(dossId);
              dossLog.setDossierCountingId(temp);
              dossierCountingLogRepository.save(dossLog);
            }
          }
        }
      }
    }
  }



  private DossierCountingLog InsertDossierCountingLog(DossierSimpleDataDto simpleItem,ObjectId dossId){
    DossierCountingLog dossLog = new DossierCountingLog();
    List<ObjectId> temp = new ArrayList<>();
    temp.add(dossId);
    dossLog.setDossierId(simpleItem.getId());
    dossLog.setDossierCountingId(temp);
    dossLog.setSectorName(simpleItem.getSectorName());
    dossLog.setProcedureName(simpleItem.getProcedureName());
    dossLog.setDossierCode(simpleItem.getDossierCode());
    dossLog.setDossierStatus(simpleItem.getDossierStatus());
    dossLog.setPhone(simpleItem.getPhone());
    dossLog.setFullName(simpleItem.getFullName());
    dossLog.setOrganization(simpleItem.getOrganization());
    dossLog.setAcceptedDate(simpleItem.getAcceptedDate());
    dossLog.setAddress(simpleItem.getAddress());
    dossLog.setTotalImplementDate(simpleItem.getTotalImplementDate());
    dossLog.setAgency(simpleItem.getAgency());
    dossLog.setAppointmentDate(simpleItem.getAppointmentDate());
    dossLog.setCompletedDate(simpleItem.getCompletedDate());
    dossLog.setReturnedDate(simpleItem.getReturnedDate());
    dossLog.setApplyMethod(simpleItem.getApplyMethod());
    dossLog.setApplyMethodId(simpleItem.getApplyMethodId());
    dossLog.setDossierDetailStatus(simpleItem.getDossierDetailStatus());
    dossLog.setImplementer(simpleItem.getImplementer());
    dossLog.setCreatedDate(new Date());
    dossLog.setUpdatedDate(new Date());
    dossLog.setOutOfDateCode(simpleItem.getOutOfDateCode());
    dossLog.setListDossierOutOfDate(simpleItem.getListDossierOutOfDate());
    dossLog.setAgencyId(simpleItem.getAgencyId());
    dossLog.setAttachment(simpleItem.getAttachment());
    return dossLog;
  }
  //1: exist, 2:doesn't exits,0 : don't care
  //1: exist, 2:doesn't exits,0 : don't care
  public ArrayList<DossierCountingDto> setDossierCountingData(
                                              String byDay,
                                              String strAgencyId,
                                              String strSectorId,
                                              String milestone,
                                              boolean isDept,
                                              boolean withToken,
                                              BundleOfListDossierCountingId bundleOfListDossLog
                                              ) throws JSONException, ParseException {
    String listStatus = new StringBuilder("1,2,3,4,5,6,8,9,10,11,12,13").toString();
    String listStatusIgnoreCancelAndSuspended = new StringBuilder("2,3,4,5,8,9,10,11").toString();
    String listStatusIgnoreCancelAndSuspendedInprogress = new StringBuilder("2,3,4,5,8,9,10,11").toString();
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    // received;  // da tiep nhan
    // isCompleted = 0 -> don't care
    // isCalculate = false -> don't calculated Ontime or Out Of Time
    ObjectId agencyId = null;
    ObjectId sectorId = null;
    if (strAgencyId != null && !strAgencyId.isEmpty()) {
      agencyId = new ObjectId(strAgencyId);
    }
    if (strSectorId != null && !strSectorId.isEmpty()) {
      sectorId = new ObjectId(strSectorId);
    }
    // inProgressAndOnTime; // hs chua den han va dang xu ly
    // completedDate doesn't exist (isCompletedDate = 2, isFocus = true), dossierStatus =2
    //  inputday < appointmentDate (isCalInprogressAndOutofDue = true)
    //appliedDate < input_day

    DossierCountingDto inProgressAndOnTime = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatusIgnoreCancelAndSuspendedInprogress,
            null,
            milestone,
            isDept,
            0,//3
            null,
            false,
            withToken
    );
    //CompletedOnTime; // hs hoàn thành som han
    //completedDate <= appointmentDate
    //completedDate <= input_day
    //dossierStatus = 4
    //

    // Date inputDateTime = new Date();
    // if (!isNullOrEmpty(milestone)){
    //   inputDateTime = df.parse(milestone);
    //   if (bundleOfListDossLog.getListDossierId_3().getMaxDate()!=null){
    //     milestone = inputDateTime.after(bundleOfListDossLog.getListDossierId_3().getMaxDate())
    //     ?milestone:df.format(bundleOfListDossLog.getListDossierId_3().getMaxDate());
    //   }

    // }

    DossierCountingDto completedOnTime = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatusIgnoreCancelAndSuspended,
            null,
            milestone,
            isDept,
            3,
            bundleOfListDossLog.getListDossierId_3().getListDossierId(),
            false,
            withToken
    );
    // if (!isNullOrEmpty(milestone)){
    //   milestone = df.format(inputDateTime);
    //   if (bundleOfListDossLog.getListDossierId_2().getMaxDate()!=null){
    //     milestone = inputDateTime.after(bundleOfListDossLog.getListDossierId_2().getMaxDate())
    //     ?milestone:df.format(bundleOfListDossLog.getListDossierId_2().getMaxDate());
    //   }
    // }

    DossierCountingDto completedEarly = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatusIgnoreCancelAndSuspended,
            null,
            milestone,
            isDept,
            2,
            bundleOfListDossLog.getListDossierId_2().getListDossierId(),
            false,
            withToken
    );
    //completedOutOfDue; // hs hoàn thành quá hạn
    //completedDate > appointmentDate
    //completedDate <= input_day
    //dossierStatus = 4

    // if (!isNullOrEmpty(milestone)){
    //   milestone = df.format(inputDateTime);
    //   if (bundleOfListDossLog.getListDossierId_4().getMaxDate()!=null){
    //     milestone = inputDateTime.after(bundleOfListDossLog.getListDossierId_4().getMaxDate())
    //     ?milestone:df.format(bundleOfListDossLog.getListDossierId_4().getMaxDate());

    //   }
    // }

    DossierCountingDto completedOutOfDue = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatusIgnoreCancelAndSuspended,
            null,
            milestone,
            isDept,
            4,
            bundleOfListDossLog.getListDossierId_4().getListDossierId(),
            false,
            withToken
    );
    //inProgressAndOutOfDue; // hs đang xử lý và quá hạn
//    inputday > appointmentDate
//    CompletedDate doesn't exist , dossierStatus =2,appliedDate < input_day
    DossierCountingDto inProgressAndOutOfDue = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatusIgnoreCancelAndSuspendedInprogress,
            null,
            milestone,
            isDept,
            1,
            null,
            false,
            withToken
    );
    // milestone = df.format(inputDateTime);
    //onlineReceived; //tiếp nhận trực tuyến
    // DossierCountingDto onlineReceived = new DossierCountingDto();
    DossierCountingDto onlineReceived = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatus,
            0,
            milestone,
            isDept,
            7,
            null
            ,true,
            withToken
    );
    // milestone = df.format(inputDateTime);
    //directReceived; // tiếp nhận trực tiếp
    // DossierCountingDto directReceived = new DossierCountingDto();
    DossierCountingDto directReceived = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            listStatus,
            1,
            milestone,
            isDept,
            7,
            null,
            true,
            withToken
    );
    //change reuturn type from 7 -> 8 (online-> direct)
    directReceived.setCode(8);
    //cancelled;  // DossierStatus = 6,12

    // if (!isNullOrEmpty(milestone)){
    //   milestone = df.format(inputDateTime);
    //   if (bundleOfListDossLog.getListDossierId_5().getMaxDate()!=null){
    //     milestone = inputDateTime.after(bundleOfListDossLog.getListDossierId_5().getMaxDate())
    //     ?milestone:df.format(bundleOfListDossLog.getListDossierId_5().getMaxDate());
    //   }

    // }

    DossierCountingDto cancelled = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            "6,12",
            null,
            milestone,
            isDept,
            5,
            bundleOfListDossLog.getListDossierId_5().getListDossierId(),
            false,
            withToken
    );
    //suspended; // DossierStatus = 13

    // if (!isNullOrEmpty(milestone)){
    //   milestone = df.format(inputDateTime);
    //   if (bundleOfListDossLog.getListDossierId_6().getMaxDate()!=null){
    //     milestone = inputDateTime.after(bundleOfListDossLog.getListDossierId_6().getMaxDate())
    //     ?milestone:df.format(bundleOfListDossLog.getListDossierId_6().getMaxDate());
    //   }

    // }

    DossierCountingDto suspended = countingDossier(
            byDay,
            agencyId,
            sectorId,
            null,
            "13",
            null,
            milestone,
            isDept,
            6,
            bundleOfListDossLog.getListDossierId_6().getListDossierId(),
            false,
            withToken
    );
    ArrayList<DossierCountingDto> result = new ArrayList<>();
    result.add(completedOnTime);
    result.add(completedEarly);
    result.add(completedOutOfDue);
    result.add(inProgressAndOnTime);
    result.add(inProgressAndOutOfDue);
    result.add(directReceived);
    result.add(onlineReceived);
    result.add(cancelled);
    result.add(suspended);
    return  result;
  }

  private DossierCountingDto countingDossier(
          String byDay,
          ObjectId agencyId,
          ObjectId sectorId,
          ObjectId procedureId,
          String lDossierStatus,
          Integer applyMethod,
          String milestone,
          boolean isDept,
          Integer code,
          List<String> lIgnoreDoss,
          Boolean isCountingbyApplyMethod,
          boolean withToken
  ) throws JSONException {
    String url = isCountingbyApplyMethod ?  "dossier/--counting-by-day?":"dossier/--counting-detail-by?";
    url += "is-dept=" + isDept;
    if (Objects.nonNull(agencyId)) {
      url += "&agency-id=" + agencyId;
    }
    if (Objects.nonNull(milestone)) {
      url += "&milestone=" + milestone;
    }
    if (Objects.nonNull(sectorId)) {
      url += "&sector-id=" + sectorId;
    }
    if (Objects.nonNull(procedureId)) {
      url += "&procedure-id=" + procedureId;
    }
    if (Objects.nonNull(lDossierStatus)) {
      url += "&list-dossier-status=" + lDossierStatus;
    }
    if (Objects.nonNull(byDay) && byDay.length() != 0) {
      url += "&by-day=" + byDay;
    }
    if (Objects.nonNull(applyMethod)) {
      url += "&apply-method=" + applyMethod;
    }
    if (Objects.nonNull(code)) {
      url += "&code=" + code;
    }
    String getUrl = microservice.padmanUri(url).toUriString();
  //  String getUrl = "http://10.58.35.22:8085/"+url;
    // System.out.println(getUrl);
    DossierCountingDto result = new DossierCountingDto();
    try {
      if (withToken){
        result = isCountingbyApplyMethod ?
        MicroserviceExchange.get(token,restTemplate, getUrl, DossierCountingDto.class)
        :MicroserviceExchange.post(token, getUrl,lIgnoreDoss, DossierCountingDto.class);
      }else{
        result = isCountingbyApplyMethod ?
        MicroserviceExchange.get(restTemplate, getUrl, DossierCountingDto.class)
        :MicroserviceExchange.post(getUrl,lIgnoreDoss, DossierCountingDto.class);
      }

    } catch (Exception e) {
      result = new DossierCountingDto();
    }
    return result;
  }

  public List<DuplicatesDataDto> getDuplicateInfo(String strDay,
                                                  String strAgencyId,
                                                  String strTagAgencyId,
                                                  String strSectorId
  ) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    Criteria aCriteria = new Criteria();
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strDay)) {
      Date byDay = df.parse(strDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Aggregation aggFrom = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    Criteria.where("year").is(year),
                    Criteria.where("month").is(month),
                    Criteria.where("day").is(day),
                    !isNullOrEmpty(strAgencyId)?  Criteria.where("agency.id").is(new ObjectId(strAgencyId)):aCriteria,
                    !isNullOrEmpty(strTagAgencyId)?  Criteria.where("agency.tagId").is(new ObjectId(strTagAgencyId)):aCriteria,
                    !isNullOrEmpty(strSectorId)?  Criteria.where("sector.id").is(new ObjectId(strSectorId)):aCriteria
            )),
            group(Fields.from(
                    Fields.field("agency"),
                    Fields.field("sector")
                    // ,Fields.field("countingData")
                    ))
                    .addToSet("$_id").as("dups")
                    .count().as("count"),
            match(Criteria.where("count").gt(1))
    );
//    System.out.println(aggFrom);
    AggregationResults<DuplicatesDataDto> results = mongoTemplate.aggregate(aggFrom, "dossierCounting", DuplicatesDataDto.class);
    return results.getMappedResults();
  }
  public AffectedRowsDto deleteDuplicate(String strDay,
                                         String strAgencyId,
                                         String strTagAgencyId,
                                         String strSectorId) throws ParseException {
    List<DuplicatesDataDto> listDup=  getDuplicateInfo(strDay, strAgencyId,strTagAgencyId,strSectorId);
    int count =0;
    AffectedRowsDto result = new AffectedRowsDto(0);
    for (int i=0; i< listDup.size();++i){
      for (int j=1; j< listDup.get(i).getDups().size();++j){
        dossierCountingRepository.deleteDossierCountingById(listDup.get(i).getDups().get(j));
        Query queryLog = new Query();
        List<DossierCountingLog> listDossCountingLog = new ArrayList<>();
        //get data
        queryLog.addCriteria(Criteria.where("").andOperator(
                Criteria.where("dossierCountingId").is(listDup.get(i).getDups().get(j))
        ));
        listDossCountingLog = mongoTemplate.find(queryLog, DossierCountingLog.class);
        for (DossierCountingLog itemLog :listDossCountingLog){
          if (itemLog.getDossierCountingId().size()>1){
            //update
            List<ObjectId> listDossierCountingId = itemLog.getDossierCountingId();
            listDossierCountingId.remove(listDup.get(i).getDups().get(j));
            dossierCountingLogRepository.save(itemLog);
          }else{
            //remove
            dossierCountingLogRepository.deleteDossierCountingById(itemLog.getId());
          }

        }
        count++;
      }
    }
    result.setAffectedRows(count);
    return result;
  }

  public DossierCountingMonitoringDto getMonitoring(String strbyDay,String strAgencyId,String strAgencyTagId,String sectorId) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strbyDay)) {
      Date byDay = df.parse(strbyDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyTagId = !isNullOrEmpty(strAgencyTagId) ? new ObjectId(strAgencyTagId) : null;
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    //get data
    Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                    (agencyId != null) ? Criteria.where("agency.id").is(agencyId) : aCriteria
            )),
            match(Criteria.where("").andOperator(
                    (agencyTagId != null) ? Criteria.where("agency.tagId").is(agencyTagId) : aCriteria,
                    (year != null) ? Criteria.where("year").is(year) : aCriteria,
                    (month != null) ? Criteria.where("month").is(month) : aCriteria,
                    (day != null) ? Criteria.where("day").is(day) : aCriteria
            )),
            group(Fields.from(
                    Fields.field("day"),
                    Fields.field("month"),
                    Fields.field("year")))
                    .first("day").as("day")
                    .first("month").as("month")
                    .first("year").as("year")
                    .count().as("count")
    );
    AggregationResults<DossierCountingMonitoringDto> groupResults = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingMonitoringDto.class);
    if (groupResults.getMappedResults().isEmpty()) {
      return null;
    }
    return groupResults.getMappedResults().get(0);
  }

  public DossierCountingIdDto getDossierIdByCode(String strbyDay,String strAgencyId,String strAgencyTagId,String strSectorId,int code) throws ParseException {
    Integer year = null;
    Integer month = null;
    Integer day = null;
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    if (!isNullOrEmpty(strbyDay)) {
      Date byDay = df.parse(strbyDay);
      Calendar cal = Calendar.getInstance();
      cal.setTime(byDay);
      year = cal.get(Calendar.YEAR);
      month = cal.get(Calendar.MONTH) + 1;
      day = cal.get(Calendar.DAY_OF_MONTH);
    }
    Criteria aCriteria = new Criteria();
    ObjectId agencyTagId = !isNullOrEmpty(strAgencyTagId) ? new ObjectId(strAgencyTagId) : null;
    ObjectId agencyId = !isNullOrEmpty(strAgencyId) ? new ObjectId(strAgencyId) : null;
    ObjectId sectorId = !isNullOrEmpty(strSectorId) ? new ObjectId(strSectorId) : null;
    // get minimum day
    DossierCounting minDateData = getMinMaxDay(false,null,null, strAgencyId);
    if (Objects.nonNull(minDateData)){
      Aggregation agg = (Aggregation) newAggregation(
              match(Criteria.where("").andOperator(
                      (agencyId != null) ? Criteria.where("agency.id").is(agencyId) : aCriteria
              )),
              match(Criteria.where("").andOperator(
                      (agencyId != null) ? Criteria.where("dossierDetail.code").is(code) : aCriteria
              )),
              match(Criteria.where("").andOperator(
                      (agencyTagId != null) ? Criteria.where("agency.tagId").is(agencyTagId) : aCriteria,
                      (sectorId != null) ? Criteria.where("sector.id").is(sectorId) : aCriteria,
                      Criteria.where("countingDate").gte(new DateTime(minDateData.getCountingDate())),
                      Criteria.where("countingDate").lt(new DateTime(strbyDay))

              )),
              group().push(new BasicDBObject("id", "id")).as("result"));
      AggregationResults<DossierCountingIdDto> groupResults
              = mongoTemplate.aggregate(agg, "dossierCounting", DossierCountingIdDto.class);
      if (groupResults.getMappedResults().isEmpty()) {
        return null;
      }
      return groupResults.getMappedResults().get(0);
    }
    return null;
  }
  public DossierCountingData parserDataDossierCountingDto(ArrayList<DossierCountingDto> dossierDetail,
    BundleOfListDossierCountingId bundleOfListDoss){
    long inProgressAndOnTime = 0;
    long inProgressAndOutOfDue = 0;
    long completedEarly = 0;
    long completedOnTime = 0;
    long completedOutOfDue = 0;
    long cancelled = 0;
    long suspended = 0;
    long onlineReceived = 0;
    long directReceived = 0;

    //
    long completedEarly_temp = 0;
    long completedOnTime_temp = 0;
    long completedOutOfDue_temp = 0;
    long suspended_temp = 0;
    long cancelled_temp = 0;

    if (bundleOfListDoss.getListDossierId_2()!=null && !bundleOfListDoss.getListDossierId_2().getListDossierId().isEmpty())
    completedEarly_temp = bundleOfListDoss.getListDossierId_2().getListDossierId().size();
    if (bundleOfListDoss.getListDossierId_3()!=null && !bundleOfListDoss.getListDossierId_3().getListDossierId().isEmpty())
    completedOnTime_temp = bundleOfListDoss.getListDossierId_3().getListDossierId().size();
    if (bundleOfListDoss.getListDossierId_4()!=null && !bundleOfListDoss.getListDossierId_4().getListDossierId().isEmpty())
    completedOutOfDue_temp = bundleOfListDoss.getListDossierId_4().getListDossierId().size();
    if (bundleOfListDoss.getListDossierId_5()!=null && !bundleOfListDoss.getListDossierId_5().getListDossierId().isEmpty())
    cancelled_temp = bundleOfListDoss.getListDossierId_5().getListDossierId().size();
    if (bundleOfListDoss.getListDossierId_6()!=null && !bundleOfListDoss.getListDossierId_6().getListDossierId().isEmpty())
    suspended_temp = bundleOfListDoss.getListDossierId_6().getListDossierId().size();

    for (DossierCountingDto item : dossierDetail){
      switch (item.getCode()){
        case 0:  //inProgressAndOnTime
          inProgressAndOnTime = item.getCount();
          break;
        case 1:  //inProgressAndOutOfDue
          inProgressAndOutOfDue = item.getCount();
          break;
        case 2: //completedEarly
          completedEarly = item.getCount()+completedEarly_temp;
          break;
        case 3: //completedOnTime
          completedOnTime = item.getCount()+completedOnTime_temp;
          break;
        case 4: //completedOutOfDue
          completedOutOfDue = item.getCount()+completedOutOfDue_temp;
          break;
        case 5: //Canceled
          cancelled = item.getCount()+cancelled_temp;
          break;
        case 6: //susppend
          suspended = item.getCount()+suspended_temp;
          break;
        case 7: //online
          onlineReceived = item.getCount();
          break;
        case 8: // direct
          directReceived = item.getCount();
          break;
      }
    }
    long inProgress = inProgressAndOnTime+ inProgressAndOutOfDue;
    long completed = completedEarly+ completedOnTime +completedOutOfDue;
    long received = inProgress + completed +suspended +cancelled;
    return new DossierCountingData(
            received,
            inProgress,
            inProgressAndOnTime,
            inProgressAndOutOfDue,
            completed,
            completedOnTime,
            completedEarly,
            completedOutOfDue,
            onlineReceived,
            directReceived,
            cancelled,
            suspended
    );
  }
  //isOutOfDateCode : using when determined late reason at Tax or Department
  public Slice<DossierCountingLog> getListDetailDossierCounting(
    String strFrom,
    String strTo,
    String strListAgencyId,
    String strNREAgencyId,
    String strListSectorId,
    String strProcedureId,
    String strListCode,
    Integer isOutOfDateCode, //0: don't care, 1: at tax, 2: another department
    boolean isTaxAndNREAgency,
    // boolean isDept,
    Pageable pageable
) throws ParseException {
  List<DossierCountingLog> result= new ArrayList<>();
  List<String> listSector = new ArrayList<>();
  List<String> listAgency = new ArrayList<>();
  List<String> listNREAgency = new ArrayList<>();
  List<Integer> listCode = new ArrayList<>();
  // get dossierCountingId fromDate and toDate
  if (!isNullOrEmpty(strListSectorId)){
    listSector = List.of(strListSectorId.split(","));
  }
  if (!isNullOrEmpty(strListAgencyId)){
    listAgency = List.of(strListAgencyId.split(","));
  }
  if (!isNullOrEmpty(strNREAgencyId)){
    listNREAgency = List.of(strNREAgencyId.split(","));
  }
  if (!isNullOrEmpty(strListCode)){
    for (String item : strListCode.split(",")){
      listCode.add(Integer.parseInt(item));
    }
  }
  if(listCode.size()==1 && listCode.get(0)==-1){
    listCode = Arrays.asList(0,1,2,3,4,5,6);
  }
  List<ObjectId> lisDossierCountingId = new ArrayList<>();


  //get previous data
  List<Integer> listInProgress = new ArrayList<>();
  List<Integer> listCompleted = new ArrayList<>();
  List<Integer> listCancelOrWithdraw = new ArrayList<>();
  for (var item :listCode ){

    if (item == 0 || item == 1){
      listInProgress.add(item);
    }else if (item ==4 || item ==2 || item ==3){
      listCompleted.add(item);
    }else if (item ==5 || item ==6){
      listCancelOrWithdraw.add(item);
    }

  }
  List<ObjectId> listNREAgencyId = new ArrayList<>();
  if (listNREAgency!=null && !listNREAgency.isEmpty()){
    for(String item : listNREAgency){
      listNREAgencyId.add( new ObjectId(item));
    }
  }
  // get recursive
  // cause compeleted status of dossier  is stable,
  // we count all dossier compelted day by day and save it into dossierCountingId and dossierCountingPrevious
  // using recursive to counting data form Date to Date
  // lisDossierCountingId = getRecursiveDossierCountingId(listAgency,listSector,strFrom,strTo,true);
  //completed
  if (listCompleted!=null && !listCompleted.isEmpty()){
    List<DossierCountingLog> ret =getRecursiveDossierCountingLogData(listAgency,listSector,strFrom,strTo,true,listCompleted,isOutOfDateCode,isTaxAndNREAgency,listNREAgencyId);
    if (ret!=null)
      result.addAll(ret);
    // // find all by DossierCountingId
    // if (lisDossierCountingId!=null && !lisDossierCountingId.isEmpty()){
    //   completeQuery.addCriteria(Criteria.where("").andOperator(
    //     (lisDossierCountingId != null && !lisDossierCountingId.isEmpty()) ? Criteria.where("dossierCountingId").in(lisDossierCountingId) : aCriteria,
    //     (listCompleted != null && !listCompleted.isEmpty()) ? Criteria.where("dossierDetailStatus").in(listCompleted) : aCriteria,
    //     (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria,
    //     (isTaxAndNREAgency && listNREAgencyId!=null && !listNREAgencyId.isEmpty()) ? Criteria.where("agencyId").in(listNREAgencyId): aCriteria
    //   ));
    //   retTemp = mongoTemplate.find(completeQuery, DossierCountingLog.class);
    //   result.addAll(retTemp);
    //   retTemp.clear();
    // }

  }

  // inProgress
  if (listInProgress!=null && !listInProgress.isEmpty()){
    // List<ObjectId> lisDossierCountingIdInProgress = getRecursiveDossierCountingId(listAgency,listSector,strFrom,strTo,false);
    List<DossierCountingLog> ret = getRecursiveDossierCountingLogData(listAgency,listSector,strFrom,strTo,false,listInProgress,isOutOfDateCode,isTaxAndNREAgency,listNREAgencyId);
    if (ret!=null)
      result.addAll(ret);
    // if(lisDossierCountingIdInProgress!=null && !lisDossierCountingIdInProgress.isEmpty()){
    //   inProgressQuery.addCriteria(Criteria.where("").andOperator(
    //     (lisDossierCountingIdInProgress != null && !lisDossierCountingIdInProgress.isEmpty()) ? Criteria.where("dossierCountingId").in(lisDossierCountingIdInProgress) : aCriteria,
    //     (listInProgress != null && !listInProgress.isEmpty()) ? Criteria.where("dossierDetailStatus").in(listInProgress) : aCriteria,
    //     (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria,
    //     (isTaxAndNREAgency && listNREAgencyId!=null && !listNREAgencyId.isEmpty()) ? Criteria.where("agencyId").in(listNREAgencyId): aCriteria
    //   ));
    //   retTemp = mongoTemplate.find(inProgressQuery, DossierCountingLog.class);
    //   result.addAll(retTemp);
    //   retTemp.clear();
    // }

  }

  //CancelOrWidraw
  if (listCancelOrWithdraw!=null && !listCancelOrWithdraw.isEmpty()){
    List<DossierCountingLog> ret = getRecursiveDossierCountingLogData(listAgency,listSector,strFrom,strTo,true,listCancelOrWithdraw,isOutOfDateCode,isTaxAndNREAgency,listNREAgencyId);
    if (ret!=null)
      result.addAll(ret);
    // if (lisDossierCountingId!=null && !lisDossierCountingId.isEmpty()){
    //   cancelOrWithdrawQuery.addCriteria(Criteria.where("").andOperator(
    //     (lisDossierCountingId != null && !lisDossierCountingId.isEmpty()) ? Criteria.where("dossierCountingId").in(lisDossierCountingId) : aCriteria,
    //     (listCancelOrWithdraw != null && !listCancelOrWithdraw.isEmpty()) ? Criteria.where("dossierDetailStatus").in(listCancelOrWithdraw) : aCriteria,
    //     (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria
    //   ));
    //   retTemp = mongoTemplate.find(cancelOrWithdrawQuery, DossierCountingLog.class);
    //   result.addAll(retTemp);
    //   retTemp.clear();
    // }

  }
    List<String> rolesToCheck = List.of(adminRoles.split(","));
    boolean  isAdmin  = Context.getListPemission(rolesToCheck);
    if(!isAdmin && enableHideName){
      result.forEach(i -> i.setHideSecurityInformation(i.getFullName()));
    }
  final int start = (int)pageable.getOffset();
  final int end = Math.min((start + pageable.getPageSize()), result.size());
  final Page<DossierCountingLog> page = new PageImpl<DossierCountingLog>(result.subList(start, end), pageable, result.size());
  return page;
}
  public Slice<DossierCountingLog> getListDetailDossierCountingByDayAndByAgencyFromTo(
          String strFrom,
          String strTo,
          String strListAgencyId,
          String strListSectorId,
          String strProcedureId,
          String strListCode,
          Integer isOutOfDateCode,
          boolean isTaxAndNREAgency,
          boolean isDept,
          Pageable pageable
  ) throws ParseException {
    List<DossierCountingLog> result= new ArrayList<>();
    List<String> listSector = new ArrayList<>();
    List<String> listAgency = new ArrayList<>();
    List<Integer> listCode = new ArrayList<>();
    // get dossierCountingId fromDate and toDate
    if (!isNullOrEmpty(strListSectorId)){
      listSector = List.of(strListSectorId.split(","));
    }
    if (!isNullOrEmpty(strListAgencyId)){
      listAgency = List.of(strListAgencyId.split(","));
    }
    if (!isNullOrEmpty(strListCode)){
      for (String item : strListCode.split(",")){
        listCode.add(Integer.parseInt(item));
      }
    }
    if(listCode.size()==1 && listCode.get(0)==-1){
      listCode = Arrays.asList(0,1,2,3,4,5,6);
    }
    // HUNGHS TEST
    //List<String> strListAgencyId, List<String> strListSectorId,
    // String strFromDate, String strToDate


    List<DossierCounting> fListDoss = getListDossierCounting(strFrom,listAgency,listSector);
    List<DossierCounting> tListDoss = getListDossierCounting(strTo,listAgency,listSector);
    if (fListDoss == null || fListDoss.isEmpty() ||tListDoss == null || tListDoss.isEmpty()){
      return null;
    }
//    if (fListDoss.size()!=tListDoss.size()){
//      throw new DigoHttpException(11402, new String[]{});
//    }
    for (int i = 0; i <fListDoss.size() ; i++) {
      for (int j = 0; j< tListDoss.size();j++){
        if (fListDoss.get(i).getSector().getId().equals(tListDoss.get(j).getSector().getId()) &&
        fListDoss.get(i).getAgency().getId().equals(tListDoss.get(j).getAgency().getId())
        ){
          List<DossierCountingLog> temp = getListDossierCountingLogbyCode
                  (fListDoss.get(i).getId(),tListDoss.get(j).getId(),listCode,isOutOfDateCode,fListDoss.get(i).getAgency().getId(),
                          isTaxAndNREAgency,null);
          tListDoss.remove(tListDoss.get(j));
          result.addAll(temp);
          break;
        }
      }
    }
    final int start = (int)pageable.getOffset();
    final int end = Math.min((start + pageable.getPageSize()), result.size());
    final Page<DossierCountingLog> page = new PageImpl<DossierCountingLog>(result.subList(start, end), pageable, result.size());
    return page;
  }

  public Slice<DossierCountingLog> getListDetailDossierCountingPrevPeriod(
          String strFrom,
          String strListAgencyId ,
          String strListSectorId,
          String strProcedureId,
          Integer isOutOfDateCode,
          // boolean isTaxAndNREAgency,
          Pageable pageable
  ) throws ParseException {
    Query query = new Query();
    // get dossierCountingId fromDate and toDate
    Criteria aCriteria = new Criteria();
    List<String> listSector = new ArrayList<>();
    List<String> listAgency = new ArrayList<>();
    List<DossierCountingLog> result = new ArrayList<>();
    // get dossierCountingId fromDate and toDate
    if (!isNullOrEmpty(strListSectorId)){
      listSector = List.of(strListSectorId.split(","));
    }
    if (!isNullOrEmpty(strListAgencyId)){
      listAgency = List.of(strListAgencyId.split(","));
    }
    List<DossierCounting> fListDoss = getListDossierCounting(strFrom,listAgency,listSector );
    if (fListDoss == null || fListDoss.isEmpty() ){
      return null;
    }
    // get dossierCountingLog Inprogress (0 and 1)
    List<ObjectId> lisDossierCountingPreviousId  = getRecursiveDossierCountingId(listAgency,listSector,strFrom,strFrom,false);
    query.addCriteria(Criteria.where("").andOperator(
      (lisDossierCountingPreviousId != null && !lisDossierCountingPreviousId.isEmpty())
      ? Criteria.where("dossierCountingId").in(lisDossierCountingPreviousId) : aCriteria,
      Criteria.where("dossierDetailStatus").in(new ArrayList<Integer>() {{add(0);add(1);}}),
      (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria

    ));
    result = mongoTemplate.find(query, DossierCountingLog.class);
    List<String> rolesToCheck = List.of(adminRoles.split(","));
    boolean  isAdmin  = Context.getListPemission(rolesToCheck);
    if(!isAdmin && enableHideName){
      result.forEach(i -> i.setHideSecurityInformation(i.getFullName()));
    }
    final int start = (int)pageable.getOffset();
    final int end = Math.min((start + pageable.getPageSize()), result.size());
    final Page<DossierCountingLog> page = new PageImpl<DossierCountingLog>(result.subList(start, end), pageable, result.size());
    return page;
  }
  public Slice<DossierCountingLog> getListDetailDossierCountingPrevPeriodByDayAndByAgencyFromTo(
          String strFrom,
          String strListAgencyId ,
          String strListSectorId,
          String strProcedureId,
          Integer isOutOfDateCode,
          boolean isTaxAndNREAgency,
          Pageable pageable
  ) throws ParseException {
    List<DossierCountingLog> result= new ArrayList<>();
    // get dossierCountingId fromDate and toDate
    List<String> listSector = new ArrayList<>();
    List<String> listAgency = new ArrayList<>();
    // get dossierCountingId fromDate and toDate
    if (!isNullOrEmpty(strListSectorId)){
      listSector = List.of(strListSectorId.split(","));
    }
    if (!isNullOrEmpty(strListAgencyId)){
      listAgency = List.of(strListAgencyId.split(","));
    }
    List<DossierCounting> fListDoss = getListDossierCounting(strFrom,listAgency,listSector );
    if (fListDoss == null || fListDoss.isEmpty() ){
      return null;
    }
//    if (fListDoss.size()!=tListDoss.size()){
//      throw new DigoHttpException(11402, new String[]{});
//    }
    for (int i = 0; i <fListDoss.size() ; i++) {
      List<DossierCountingLog> temp = getListDossierCountingLogbyCode(null,fListDoss.get(i).getId(), new ArrayList<Integer>() {{
        add(0);
        add(1);
      } },isOutOfDateCode,fListDoss.get(i).getAgency().getId(),isTaxAndNREAgency,null);
      result.addAll(temp);
    }
    final int start = (int)pageable.getOffset();
    final int end = Math.min((start + pageable.getPageSize()), result.size());
    final Page<DossierCountingLog> page = new PageImpl<DossierCountingLog>(result.subList(start, end), pageable, result.size());
    return page;
  }

  public Slice<DossierCountingLog> getListDetailDossierCountingForApplyMethodOrReceived(
          String strFrom,
          String strTo,
          String strListAgencyId ,
          String strListSectorId,
          String strProcedureId,
          String applyMethod,
          Integer isOutOfDateCode,
          Pageable pageable
  ) throws ParseException {
    Query query = new Query();
    AggregationResults<DossierCountingListChildDto> listTemp =null ;
    Criteria aCriteria = new Criteria();
    List<DossierCountingLog> result= new ArrayList<>();
    List<String> listSector = new ArrayList<>();
    List<Integer> listApplyMethod = new ArrayList<>();
    List<ObjectId> lisDossierCountingId = new ArrayList<>();
    List<String> listAgency = new ArrayList<>();
    Calendar cal = Calendar.getInstance();
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    cal.setTime(df.parse(strFrom));
    cal.add(Calendar.DATE, -1);
    Date dateBefore1Days = cal.getTime();
    if (!isNullOrEmpty(applyMethod)){
      for (String item : applyMethod.split(",")){
        listApplyMethod.add(Integer.parseInt(item));
      }
    }
    // get dossierCountingId fromDate and toDate
    if (!isNullOrEmpty(strListSectorId)){
      listSector = List.of(strListSectorId.split(","));
    }
    if (!isNullOrEmpty(strListAgencyId)){
      listAgency = List.of(strListAgencyId.split(","));
    }
    //get currentDossierCountingId at fromDate
    lisDossierCountingId = getRecursiveDossierCountingId(listAgency,listSector,strFrom,strFrom,false);
    //get current DossierCoungitnId from DateBefire1Day to toDate
    // we want get DossierCountingLog formt 15/5 to 18/5
    // 1st: we get data at 15/5
    // 2st: we get data at 14/5 - 18/5 cause : gapTime = (18-14)- 2 = 2
    // we has maxDept = 2, that mean we have Data 4 Date (18 : current Date, 17: previousDate and recusive 2 day is 16 and 15/5)
    // 3rd: caculate except Data between 1st and 2st
    List<ObjectId> lisDossierCountingPreviousId  = getRecursiveDossierCountingId(listAgency,listSector, df.format(dateBefore1Days),strTo,true);
    if (lisDossierCountingPreviousId!=null && !lisDossierCountingPreviousId.isEmpty()){
      Aggregation agg = (Aggregation) newAggregation(
      facet(
        match(Criteria.where("").andOperator(
          (lisDossierCountingId != null && !lisDossierCountingId.isEmpty()) ?  Criteria.where("dossierCountingId").in(lisDossierCountingId): aCriteria,
          (applyMethod != null) ? Criteria.where("applyMethodId").in(listApplyMethod) : aCriteria,
          (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria
        )),
        project("dossierCountingId","_id","dossierId")
      ).as("allDossier")
      .and(
        match(Criteria.where("").andOperator(
          (lisDossierCountingPreviousId != null && !lisDossierCountingPreviousId.isEmpty()) ?  Criteria.where("dossierCountingId").in(lisDossierCountingPreviousId): aCriteria,
          (applyMethod != null) ? Criteria.where("applyMethodId").in(listApplyMethod) : aCriteria,
          (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria
        )),
        project("dossierCountingId","_id","dossierId")
      ).as("allDossier2"),
      aoc -> new Document("$addFields",new Document("listChild",new Document("$concatArrays",
          Arrays.asList("$allDossier","$allDossier2")))),
      aoc -> new Document("$unwind","$listChild"),
      aoc -> new Document("$group",new Document("_id","$listChild.dossierId")
      .append("listChild", new Document("$addToSet","$listChild._id"))
      .append("count", new Document("$sum",1))
      ),
      aoc -> new Document("$group",new Document("_id",null)
      .append("listChild",
        new Document("$addToSet",
          new Document("$cond",
            new Document("if",
              new Document("$and",
                Arrays.asList(new Document("$gte",Arrays.asList(new Document("$size","$listChild"),"$count"))
                )
              ))
            .append("then",new Document("$arrayElemAt",Arrays.asList("$listChild",0)))
            .append("else","$$REMOVE")
            )
          )
        )
      )
    ).withOptions(newAggregationOptions().allowDiskUse(true).build());
    //list DossierCounting after caculate
    listTemp = mongoTemplate.aggregate(agg, "dossierCountingLog", DossierCountingListChildDto.class);
    if(listTemp != null && !listTemp.getMappedResults().isEmpty()){
      List<ObjectId> lisDossierId = listTemp.getMappedResults().get(0).getListChild();
      query.addCriteria(Criteria.where("").andOperator(
        (listTemp != null && !listTemp.getMappedResults().isEmpty()) ? Criteria.where("_id").in(lisDossierId) : aCriteria,
        (isOutOfDateCode!=null) ? Criteria.where("outOfDateCode").is(isOutOfDateCode): aCriteria
      ));
      result = mongoTemplate.find(query, DossierCountingLog.class);
    }
      List<String> rolesToCheck = List.of(adminRoles.split(","));
      boolean  isAdmin  = Context.getListPemission(rolesToCheck);
      if(!isAdmin && enableHideName){
        result.forEach(i -> i.setHideSecurityInformation(i.getFullName()));
      }
    final int start = (int)pageable.getOffset();
    final int end = Math.min((start + pageable.getPageSize()), result.size());
    final Page<DossierCountingLog> page = new PageImpl<DossierCountingLog>(result.subList(start, end), pageable, result.size());
    return page;
    }
    return null;
  }
  public Slice<DossierCountingLog> getListDetailDossierCountingTotalReceivedByDayAndByAgencyFromTo(
          String strFrom,
          String strTo,
          String strListAgencyId ,
          String strListSectorId,
          String strProcedureId,
          Integer applyMethod,
          Integer isOutOfDateCode,
          boolean isTaxAndNREAgency,
          Pageable pageable
  ) throws ParseException {
    List<DossierCountingLog> result= new ArrayList<>();
    List<DossierCountingLog> lAll= new ArrayList<>();
    List<DossierCountingLog> lPrevious= new ArrayList<>();
    List<String> listSector = new ArrayList<>();
    List<String> listAgency = new ArrayList<>();
    // get dossierCountingId fromDate and toDate
    if (!isNullOrEmpty(strListSectorId)){
      listSector = List.of(strListSectorId.split(","));
    }
    if (!isNullOrEmpty(strListAgencyId)){
      listAgency = List.of(strListAgencyId.split(","));
    }
    // get dossierCountingId fromDate and toDate
    List<DossierCounting> fListDoss = getListDossierCounting(strFrom,listAgency,listSector);
    List<DossierCounting> tListDoss = getListDossierCounting(strTo,listAgency,listSector);
    if (fListDoss == null || fListDoss.isEmpty() ||tListDoss == null || tListDoss.isEmpty() ){
      return null;
    }
//    if (fListDoss.size()!=tListDoss.size()){
//      throw new DigoHttpException(11402, new String[]{});
//    }
    for (int i = 0; i <fListDoss.size() ; i++) {
      for (int j = 0; j< tListDoss.size();j++){
        if (fListDoss.get(i).getSector().getId().equals(tListDoss.get(j).getSector().getId()) &&
                fListDoss.get(i).getAgency().getId().equals(tListDoss.get(j).getAgency().getId())){
          List<DossierCountingLog> tempListReceived = getListDossierCountingLogbyCode(fListDoss.get(i).getId(),tListDoss.get(j).getId(),new ArrayList<Integer>() {{
            add(0);add(1);add(2);add(3);add(4);add(5);add(6);
          } },isOutOfDateCode,fListDoss.get(i).getAgency().getId(),isTaxAndNREAgency,applyMethod);
          tListDoss.remove(tListDoss.get(j));
          result.addAll(tempListReceived);
          break;
        }
      }
    }
    for (int i = 0; i <fListDoss.size() ; i++) {
      List<DossierCountingLog> temp = getListDossierCountingLogbyCode(null,fListDoss.get(i).getId(),new ArrayList<Integer>() {{
        add(0);
        add(1);
      } },isOutOfDateCode,fListDoss.get(i).getAgency().getId(),isTaxAndNREAgency,applyMethod);
      lPrevious.addAll(temp);
    }
    result.removeAll(lPrevious);
    final int start = (int)pageable.getOffset();
    final int end = Math.min((start + pageable.getPageSize()), result.size());
    final Page<DossierCountingLog> page = new PageImpl<DossierCountingLog>(result.subList(start, end), pageable, result.size());
    return page;
  }

  private List<DossierCountingLog> getListDossierCountingLogbyCode(
          ObjectId fromDoss,
          ObjectId toDoss,
          List<Integer> lCode,
          Integer isOutOfDateCode, // 0: don't care, 1: at tax , 2: another agency except tax agency
          ObjectId agencyId,
          boolean isTaxAndNREAgency,
          Integer applyMethod
  ){
    Query query = new Query();
    Criteria aCriteria = new Criteria();
    query.addCriteria(Criteria.where("").andOperator(
            (fromDoss != null) ? Criteria.where("dossierCountingId").ne(fromDoss) : aCriteria,
            (toDoss != null) ? Criteria.where("dossierCountingId").is(toDoss) : aCriteria,
            (agencyId != null && isTaxAndNREAgency ==true) ? Criteria.where("agencyId").is(agencyId) : aCriteria,
            (!Objects.isNull(applyMethod)) ? Criteria.where("applyMethodId").is(applyMethod) : aCriteria,
            (!Objects.isNull(isOutOfDateCode))? Criteria.where("outOfDateCode").is(isOutOfDateCode) : aCriteria,
            (lCode != null && !lCode.isEmpty()) ? Criteria.where("dossierDetailStatus").in(lCode) : aCriteria

    ));
    List<DossierCountingLog> result = mongoTemplate.find(query, DossierCountingLog.class);
    return result;
  }
  public ResponseEntity<Object> statisticExport(Pageable pageable, String fromDate, String toDate,
                                                String listAgencyId,  Integer reportType,
                                                String listSectorId, String procedureId,String listCode,
                                                String applyMethod,
                                                Integer isOutOfDateCode,
                                                boolean isTaxAndNREReport,
                                                String listNREAgencyId,
                                                boolean isDept
                                                ) throws IOException, ParseException {
    byte[] resource = new byte[0];
    DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
    String timestamp = dateFormat.format(new Date());
    String filename = "";

    filename = isTaxAndNREReport?timestamp + "_baocaochitiet_Thue_TNMT.xlsx": timestamp + "_baocaochitiet.xlsx";
    if (null == reportType) {
      Slice<DossierCountingLog> listDossierCountingLog =
      getListDetailDossierCounting(fromDate, toDate, listAgencyId,listNREAgencyId,listSectorId,procedureId
                      ,listCode,isOutOfDateCode,isTaxAndNREReport,pageable);
              // getListDetailDossierCountingByDayAndByAgencyFromTo(fromDate, toDate, listAgencyId,listSectorId,procedureId
              //         ,listCode,isOutOfDateCode,isTaxAndNREAgencyReport,isDept,pageable);
      resource = exportExcelForm(pageable,fromDate, toDate,isTaxAndNREReport,listDossierCountingLog);
    }else {
     switch (reportType){
       case 0:  //--counting-detail
//         long start1 = System.nanoTime();
         Slice<DossierCountingLog> listDossierCountingLog =
                getListDetailDossierCounting(fromDate, toDate, listAgencyId,listNREAgencyId,listSectorId,procedureId
                      ,listCode,isOutOfDateCode,isTaxAndNREReport,pageable);
            // getListDetailDossierCountingByDayAndByAgencyFromTo(fromDate, toDate, listAgencyId,listSectorId,procedureId
            //          ,listCode,isOutOfDateCode,isTaxAndNREAgencyReport,isDept,pageable);
//         long end1 = System.nanoTime();
//         System.out.println("------> Thời gian get dữ liệu từ db: "+ (double)(end1-start1)/1000000000 + " seconds");
//         long start2 = System.nanoTime();
          resource = exportExcelForm(pageable,fromDate, toDate,isTaxAndNREReport,listDossierCountingLog);
          long end2 = System.nanoTime();
//         System.out.println("------> Thời gian tạo file excel: "+ (double)(end2-start2)/1000000000 + " seconds");
         break;
       case 1://--counting-detail-previous
         Slice<DossierCountingLog> listDossierCountingLogPrevious =
                getListDetailDossierCountingPrevPeriod(fromDate,listAgencyId,
                listSectorId,procedureId,isOutOfDateCode,pageable);
                //  getListDetailDossierCountingPrevPeriodByDayAndByAgencyFromTo(fromDate,listAgencyId,
                        //  listSectorId,procedureId,isOutOfDateCode,isTaxAndNREAgencyReport,pageable);
         resource = exportExcelForm(pageable,fromDate, toDate,isTaxAndNREReport,listDossierCountingLogPrevious);
         break;
       case 2://--counting-detail-received-or-applymethod
         Slice<DossierCountingLog> listDossierCountingLogReceived =
                getListDetailDossierCountingForApplyMethodOrReceived(fromDate,toDate,listAgencyId,
                listSectorId,procedureId,applyMethod,isOutOfDateCode, pageable);
                //  getListDetailDossierCountingTotalReceivedByDayAndByAgencyFromTo(fromDate,toDate,listAgencyId,
                //          listSectorId,procedureId,applyMethod,isOutOfDateCode,isTaxAndNREAgencyReport, pageable);
         resource = exportExcelForm(pageable,fromDate, toDate,isTaxAndNREReport,listDossierCountingLogReceived);
         break;
         case 4:  //--counting-detail padsvc
//         long start1 = System.nanoTime();
         Slice<DossierCountingLog> listDossierCountingLogPadsvc =
                getListDetailDossierCounting(fromDate, toDate, listAgencyId,listNREAgencyId,listSectorId,procedureId
                      ,listCode,isOutOfDateCode,isTaxAndNREReport,pageable);
          resource = exportExcelFormPadsvc(pageable,fromDate, toDate,isTaxAndNREReport,listDossierCountingLogPadsvc);
         break;
     }
    }
    return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
            .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
            .body(resource);
  }

  public byte[] exportExcelForm(Pageable pageable,
                                          String fromDate,
                                          String toDate,
                                          boolean isTaxAndNREReport,
                                          Slice<DossierCountingLog> listDossierCountingLog
                                          ) throws IOException, ParseException {
    try (InputStream is = isTaxAndNREReport? resourceTemplateDossierCountingDetailLateAtTaxDeptOrNaturalResourcesAndEnvironment.getInputStream():
            resourceTemplateDossierCountingDetail.getInputStream()) {
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      org.jxls.common.Context context = new org.jxls.common.Context();
      DateFormat df2 = new SimpleDateFormat("dd/MM/yyyy");
      DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      Date today = Calendar.getInstance().getTime();
      Date tDate = df.parse(toDate);

      Calendar cal = Calendar.getInstance();
      cal.setTime(df.parse(fromDate));
      cal.add(Calendar.DATE, 1);
      Date fDate = cal.getTime();
//      Slice<DossierCountingLog> listDossierCountingLog =
//              getListDetailDossierCountingByDayAndByAgencyFromTo(fromDate, toDate, listAgencyId,listSectorId,procedureId ,listCode,isDept,pageable);

      String nationalBrand=translator.toLocale("lang.word.ktm.national-brand");
      String subNational=translator.toLocale("lang.word.ktm.sub-national");
      String currentDate=translator.toLocale("lang.word.ktm.current-date",new String[]{df2.format(today)});
      String title=translator.toLocale("lang.word.ktm.title");
      String subTitle=translator.toLocale("lang.word.ktm.sub-title",new String[]{df2.format(fDate),df2.format(tDate)});
      String no=translator.toLocale("lang.word.ktm.no");
      String agency=translator.toLocale("lang.word.ktm.agency");
      String sector=translator.toLocale("lang.word.ktm.sector");
      String procedure=translator.toLocale("lang.word.ktm.procedure");
      String dossierCode=translator.toLocale("lang.word.ktm.dossier-code");
      String status=translator.toLocale("lang.word.ktm.status");
      String address=translator.toLocale("lang.word.ktm.address");
      String phone=translator.toLocale("lang.word.ktm.phone");
      String owner=translator.toLocale("lang.word.ktm.owner");
      String submitter=translator.toLocale("lang.word.ktm.submitter");
      String acceptedDate=translator.toLocale("lang.word.ktm.accepted-date");
      String appointmentDate=translator.toLocale("lang.word.ktm.appointment-date");
      String duration=translator.toLocale("lang.word.ktm.duration");
      String completedDate=translator.toLocale("lang.word.ktm.completed-date");
      String returnedDate=translator.toLocale("lang.word.ktm.returned-Date");
      String dossierType=translator.toLocale("lang.word.ktm.dossier-type");
      String dossierStatus=translator.toLocale("lang.word.ktm.dossier-status");
      String implementer=translator.toLocale("lang.word.ktm.implementer");
      String note=translator.toLocale("lang.word.ktm.note");
      String lateAt=translator.toLocale("lang.word.ktm.late-at");
      String creator=translator.toLocale("lang.word.ktm.creator");
      String reporter=translator.toLocale("lang.word.ktm.reporter");


      context.putVar("no", no);
      context.putVar("nationalBrand", nationalBrand);
      context.putVar("subNational", subNational);
      context.putVar("currentDate", currentDate);
      context.putVar("subTitle", subTitle);
      context.putVar("agency", agency);
      context.putVar("sector", sector);
      context.putVar("procedure", procedure);
      context.putVar("dossierCode", dossierCode);
      context.putVar("status", status);
      context.putVar("address", address);
      context.putVar("phone", phone);
      context.putVar("owner", owner);
      context.putVar("submitter", submitter);
      context.putVar("acceptedDate", acceptedDate);
      context.putVar("title", title);
      context.putVar("appointmentDate", appointmentDate);
      context.putVar("duration", duration);
      context.putVar("completedDate", completedDate);
      context.putVar("returnedDate", returnedDate);
      context.putVar("dossierType", dossierType);
      context.putVar("dossierStatus", dossierStatus);
      context.putVar("dossierType", dossierType);
      context.putVar("implementer", implementer);
      context.putVar("lateAt", lateAt);
      context.putVar("note", note);
      context.putVar("creator", creator);
      context.putVar("reporter", reporter);
      context.putVar("itemDtos", getFormDossierCountingDto(listDossierCountingLog,isTaxAndNREReport));
      XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
      JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
      return outputStream.toByteArray();
    }
  }

  public byte[] exportExcelFormPadsvc(Pageable pageable,
                                          String fromDate,
                                          String toDate,
                                          boolean isTaxAndNREReport,
                                          Slice<DossierCountingLog> listDossierCountingLog
                                          ) throws IOException, ParseException {
    try (InputStream is = resourceTemplateDossierCountingDetailPadsvc.getInputStream()) {
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      org.jxls.common.Context context = new org.jxls.common.Context();
      DateFormat df2 = new SimpleDateFormat("dd/MM/yyyy");
      DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      Date today = Calendar.getInstance().getTime();
      Date tDate = df.parse(toDate);

      Calendar cal = Calendar.getInstance();
      cal.setTime(df.parse(fromDate));
      cal.add(Calendar.DATE, 1);
      Date fDate = cal.getTime();

      String nationalBrand=translator.toLocale("lang.word.ktm.national-brand");
      String subNational=translator.toLocale("lang.word.ktm.sub-national");
      String currentDate=translator.toLocale("lang.word.ktm.current-date",new String[]{df2.format(today)});
      String title=translator.toLocale("lang.word.ktm.title");
      String subTitle=translator.toLocale("lang.word.ktm.sub-title",new String[]{df2.format(fDate),df2.format(tDate)});
      String no=translator.toLocale("lang.word.ktm.no");
      String agency=translator.toLocale("lang.word.ktm.agency");
      String sector=translator.toLocale("lang.word.ktm.sector");
      String procedure=translator.toLocale("lang.word.ktm.procedure");
      String dossierCode=translator.toLocale("lang.word.ktm.dossier-code");
      String status=translator.toLocale("lang.word.ktm.status");
      String address=translator.toLocale("lang.word.ktm.address");
      String phone=translator.toLocale("lang.word.ktm.phone");
      String owner=translator.toLocale("lang.word.ktm.owner");
      String submitter=translator.toLocale("lang.word.ktm.submitter");
      String acceptedDate=translator.toLocale("lang.word.ktm.accepted-date");
      String appointmentDate=translator.toLocale("lang.word.ktm.appointment-date");
      String duration=translator.toLocale("lang.word.ktm.duration");
      String completedDate=translator.toLocale("lang.word.ktm.completed-date");
      String returnedDate=translator.toLocale("lang.word.ktm.returned-Date");
      String dossierType=translator.toLocale("lang.word.ktm.dossier-type");
      String dossierStatus=translator.toLocale("lang.word.ktm.dossier-status");
      String implementer=translator.toLocale("lang.word.ktm.implementer");
      String note=translator.toLocale("lang.word.ktm.note");
      String lateAt=translator.toLocale("lang.word.ktm.late-at");
      String creator=translator.toLocale("lang.word.ktm.creator");
      String reporter=translator.toLocale("lang.word.ktm.reporter");


      context.putVar("no", no);
      context.putVar("nationalBrand", nationalBrand);
      context.putVar("subNational", subNational);
      context.putVar("currentDate", currentDate);
      context.putVar("subTitle", subTitle);
      context.putVar("agency", agency);
      context.putVar("sector", sector);
      context.putVar("procedure", procedure);
      context.putVar("dossierCode", dossierCode);
      context.putVar("status", status);
      context.putVar("address", address);
      // context.putVar("phone", phone);
      context.putVar("owner", owner);
      // context.putVar("submitter", submitter);
      context.putVar("acceptedDate", acceptedDate);
      context.putVar("title", title);
      context.putVar("appointmentDate", appointmentDate);
      // context.putVar("duration", duration);
      context.putVar("completedDate", completedDate);
      // context.putVar("returnedDate", returnedDate);
      // context.putVar("dossierType", dossierType);
      context.putVar("dossierStatus", dossierStatus);
      // context.putVar("dossierType", dossierType);
      // context.putVar("implementer", implementer);
      context.putVar("lateAt", lateAt);
      // context.putVar("note", note);
      context.putVar("creator", creator);
      // context.putVar("reporter", reporter);
      context.putVar("itemDtos", getFormDossierCountingDto(listDossierCountingLog,isTaxAndNREReport));
      XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
      JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
      return outputStream.toByteArray();
    }
  }


  private List<ExcelFormDossierCountingDetailDto> getFormDossierCountingDto(Slice<DossierCountingLog> listDossier,boolean isShowLateAt) {
    AtomicLong i = new AtomicLong(1L);
    return listDossier.getContent().stream()
            .map(item -> convertLogtoExcelDossierCountingDto(i.getAndIncrement(), item,isShowLateAt))
            .collect(Collectors.toList());
  }
  private static String formatDateToString(
            Date date, String format,
            String timeZone
    ) {
        // null check
        if (date == null) return null;
        // create SimpleDateFormat object with input format
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // default system timezone if passed null or empty
        if (timeZone == null || "".equalsIgnoreCase(timeZone.trim())) {
            timeZone = Calendar.getInstance().getTimeZone().getID();
        }
        // set timezone to SimpleDateFormat
        sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        // return Date in required format with timezone as String
        return sdf.format(date);
    }
  private ExcelFormDossierCountingDetailDto convertLogtoExcelDossierCountingDto(long no, DossierCountingLog entity,boolean isShowLateAt) {
    ExcelFormDossierCountingDetailDto dto = new ExcelFormDossierCountingDetailDto();
    // DateFormat df2 = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss"); "HH:mm:ss dd/MM/yyyy"
    String df2 = "dd/MM/yyyy HH:mm:ss";
    String timeZone= "Asia/Ho_Chi_Minh";
    dto.setNo(no);
    dto.setAgency(entity.getAgency());
    dto.setSector(entity.getSectorName());
    dto.setProcedure(entity.getProcedureName());
    dto.setDossierCode(entity.getDossierCode());
    dto.setStatus(entity.getDossierStatus());
    dto.setAddress(entity.getAddress());
    dto.setPhone(entity.getPhone());
    dto.setOwner(entity.getFullName());
    dto.setSubmitter(entity.getOrganization());
    if(entity.getAcceptedDate()!=null){
      String retDate = formatDateToString(entity.getAcceptedDate(), df2, timeZone);
      dto.setAcceptedDate(retDate);

    }
    if(entity.getAppointmentDate()!=null){
      String retDate = formatDateToString(entity.getAppointmentDate(), df2, timeZone);
      dto.setAppointmentDate(retDate);
    }
    if(entity.getTotalImplementDate()!= null){
      boolean vTemp = entity.getTotalImplementDate().contains("0.0") &&  (entity.getTotalImplementDate().length() == 3) ;
      dto.setDuration(vTemp?"Không xác định thời gian":entity.getTotalImplementDate());
    }

    if(entity.getCompletedDate()!=null ){
      String retDate = formatDateToString(entity.getCompletedDate(), df2, timeZone);
      dto.setCompletedDate(retDate);
    }
    if(entity.getReturnedDate()!=null ){
      String retDate = formatDateToString(entity.getReturnedDate(), df2, timeZone);
      dto.setReturnedDate(retDate);
    }

    dto.setDossierType(entity.getApplyMethod());
    switch (entity.getDossierDetailStatus()){
      case 0:
        dto.setDossierStatus("Đang giải quyết - Trong hạn");
        break;
      case 1:
        dto.setDossierStatus("Đang giải quyết - Quá hạn");
        break;
      case 2:
        dto.setDossierStatus("Đã giải quyết - Trước hạn");
        break;
      case 4:
        dto.setDossierStatus("Đã giải quyết - Quá hạn");
        break;
      case 3:
        dto.setDossierStatus("Đã giải quyết - Đúng hạn");
        break;
      case 5:
        dto.setDossierStatus("Dừng xử lý");
        break;
      case 6:
        dto.setDossierStatus("Rút");
        break;
    }
    String impTemp = entity.getImplementer();
    List<String> listImp=new ArrayList<>();
    String imp= "";
    if (!isNullOrEmpty(impTemp)){
      listImp = List.of(impTemp.split(","));
      imp= listImp.get(listImp.size()-1).equals("null")?"":listImp.get(listImp.size()-1);
    }
    dto.setImplementer(imp);
//    dto.setOutOfDateType(entity.getOutOfDateCode());
    String strTemp = "";
    //
    if (entity.getListDossierOutOfDate()!=null){
      for (int i=0; i<entity.getListDossierOutOfDate().size();++i){
        if (isShowLateAt){ // NRE reporter
          if ( entity.getOutOfDateCode() == 1){ // tax agency
            if (entity.getListDossierOutOfDate().get(i).getAccountName()!=null &&
                    entity.getListDossierOutOfDate().get(i).getAccountName().contains("thue")){
              strTemp+=entity.getListDossierOutOfDate().get(i).getUserName()+" \n";
            }
          }else if (entity.getOutOfDateCode() == 2){ // another agency
            if (entity.getListDossierOutOfDate().get(i).getAccountName()!=null){
              strTemp+=entity.getListDossierOutOfDate().get(i).getAgency() +" - "
                      +entity.getListDossierOutOfDate().get(i).getStep() +" - "
                      +entity.getListDossierOutOfDate().get(i).getUserName()+" \n";
            }
          }
        }else{
          if(entity.getOutOfDateCode()!=0){
            if (entity.getListDossierOutOfDate().get(i).getAccountName()!=null){
              strTemp+=entity.getListDossierOutOfDate().get(i).getAgency() +" - "
                      +entity.getListDossierOutOfDate().get(i).getStep() +" - "
                      +entity.getListDossierOutOfDate().get(i).getUserName()+" \n";
            }
          }
        }
      }
    }

    dto.setLateAt(strTemp);
    dto.setNote("");
    return dto;
  }
  @Scheduled(cron = "0 0 19 * * ?", zone = "GMT+7")
  protected void ktmReportRun() throws JSONException, ParseException, IOException {
    logger.info("---KTMReportRun---" );
    // get token
//    getToken();

    getTokenClient();

    if (DossierCountingProperties.DOSSIER_COUNTING_EVENT_AVAILABLE == true){
      logger.info("START DOSSIERCOUNTING JOB - "+ new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(new Date()));
      Date currentDate = new Date();
      String strCurrentDate = new SimpleDateFormat("yyyy-MM-dd").format(currentDate);
      strCurrentDate+="T23:59:59.165Z";
      logger.info("Date : "+strCurrentDate);
      postReportDossierByDayWithToken(strCurrentDate,DossierCountingProperties.DOSSIER_COUNTING_CNVP_ID,"false",DossierCountingProperties.DOSSIER_COUNTING_MILESTONE);
      postReportDossierByDayWithToken(strCurrentDate,DossierCountingProperties.DOSSIER_COUNTING_COMMUNE_ID,"false",DossierCountingProperties.DOSSIER_COUNTING_MILESTONE);
      postReportDossierByDayWithToken(strCurrentDate,DossierCountingProperties.DOSSIER_COUNTING_DISTRICT_ID,"false",DossierCountingProperties.DOSSIER_COUNTING_MILESTONE);
      postReportDossierByDayWithToken(strCurrentDate,DossierCountingProperties.DOSSIER_COUNTING_DEPT_ID,"true",DossierCountingProperties.DOSSIER_COUNTING_MILESTONE);
      logger.info("FINISH DOSSIERCOUNTING JOB - "+ new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(new Date()));
    }
  }


	public GetDigitizationDto getDigitizationInfo(String from,String to,String agencyId,boolean isDept,Pageable pageable) throws ParseException {
    GetDigitizationDto result = new GetDigitizationDto();
		Slice<DossierCountingLog> listDossierCountingLog = getListDetailDossierCountingForApplyMethodOrReceived(from,to,
						agencyId,null,null,null,null,pageable);

    // Slice<DossierCountingLog> listDossierCountingLog = getListDetailDossierCountingTotalReceivedByDayAndByAgencyFromTo(from,to,
						// agencyId,null,null,null,null,false,pageable);
    if (listDossierCountingLog!=null && !listDossierCountingLog.isEmpty()){
      List<String> listDossierId = listDossierCountingLog.stream()
              .map(DossierCountingLog::convertDossierIdToString)
              .collect(Collectors.toList());
      String url = "dossier-form-file/--digitization-counting/";
      // String getUrl = "http://10.58.35.22:8085/"+url;
      String getUrl = microservice.padmanUri(url).toUriString();
      result = MicroserviceExchange.post(getUrl,listDossierId,GetDigitizationDto.class);
    }


    //Set Data
    Slice<DossierCountingLog> listDossierCountingLogCompleted = getListDetailDossierCounting(from,to,
            agencyId,null,null,null,"2,3,4",null,false,pageable);
    // Slice<DossierCountingLog> listDossierCountingLogCompleted = getListDetailDossierCountingByDayAndByAgencyFromTo(from,to,
    //         agencyId,null,null,"2,3,4",null,false,isDept,pageable);
    if (listDossierCountingLogCompleted!=null && !listDossierCountingLogCompleted.isEmpty()){
      long totalDossierHabingDigitizationResult = listDossierCountingLogCompleted.stream().count();
      result.setTotalDossierCompleted(totalDossierHabingDigitizationResult);

      long numberDossierHaveResultDigitization = listDossierCountingLogCompleted.stream().filter(e->{
        boolean isHavingResult = false;
        if ( e.getAttachment()!=null) {
          for (var item : e.getAttachment()){
            if (item.getGroup()!=null && item.getGroup().toString().equals("5f9bd9692994dc687e68b5a6")){
              isHavingResult = true;
              break;
            }
          }
        }
        return isHavingResult;
      }).count();
      result.setNumberDossierHavingDigitizationResult(numberDossierHaveResultDigitization);
      result.setNumberDossierDoesNotHavingDigitizationResult(totalDossierHabingDigitizationResult - numberDossierHaveResultDigitization);

      // Previous PeriodgetListDetailDossierCountingPrevPeriod
      Slice<DossierCountingLog> listDossierCountingLogPrevPeriod =  getListDetailDossierCountingPrevPeriod(
              from,agencyId,null,null,null,pageable
      );
      // Slice<DossierCountingLog> listDossierCountingLogPrevPeriod =  getListDetailDossierCountingPrevPeriodByDayAndByAgencyFromTo(
      //         from,agencyId,null,null,null,false,pageable
      // );
      if (listDossierCountingLogPrevPeriod!=null && !listDossierCountingLogPrevPeriod.isEmpty()){
        Stream<DossierCountingLog> combinedPrevAndCompleted  = Stream.concat(
                listDossierCountingLogCompleted.stream(),
                listDossierCountingLogPrevPeriod.stream()
        );
        List<DossierCountingLog> listTotalTemp =  combinedPrevAndCompleted.distinct().collect(Collectors.toList());
        long numberDossierPrevPeriod = listDossierCountingLogPrevPeriod.stream().count() + numberDossierHaveResultDigitization - listTotalTemp.size();
        if ( numberDossierPrevPeriod <=0){
          result.setPreviousPeriod(0);
        }else{
          result.setPreviousPeriod(numberDossierPrevPeriod);
        }
      }
    }

    //get
    return result;
	}

    public void synthesizeGeneralReport(String fromDate, String toDate, String strAgencyId, String strSectorId, String strAgencyTagId, String milestone){
      LocalDateTime fromDateConvert = null;
      LocalDateTime toDateConvert = null;
      LocalDateTime milestoneConvert = null;
      List<String> tagAgencyIdList = new ArrayList<>(Arrays.asList("612c5c0f17a2264ee7f42fa4","5f7dade4b80e603d5300dcc4","5f6b177a4e1bd312a6f3ae4a","62a6f8e43a06bf0913558b0f"));
      try {
        fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        milestoneConvert = !isNullOrEmpty(milestone) ? df.parse(milestone).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      } catch (ParseException e) {
        throw new DigoHttpException(11003, new String[]{}, HttpServletResponse.SC_BAD_REQUEST);
      }

      int size = 50;
      int page = 0;

      // loop
      if (Objects.nonNull(strAgencyTagId) && strAgencyTagId.length() != 0){
        List<String> lAgencyTagId = List.of(strAgencyTagId.split(","));
        for (int a = 0; a < lAgencyTagId.size(); ++a) {
          ObjectId agencyTagId = new ObjectId(lAgencyTagId.get(a));
//          Boolean isDept = agencyTagId.toString().equals("612c5c0f17a2264ee7f42fa4") ? true : false;
          int agencyTotalPages = 0;
          page = 0;
          do {
            JSONArray agencyContentData = null;
            try {
//              String getAgencyUrl = new StringBuilder("http://localhost:8888/agency/name+code?tag-id=").append(agencyTagId.toString()).append("&size="+size +"&page="+page).toString();
              String agencyURL = "agency/name+code?tag-id=" + agencyTagId.toString() + "&size=" + size + "&page=" + page;
              String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
              String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
              JSONObject agencyJsonObject = new JSONObject(agencyJson);
              agencyContentData = agencyJsonObject.getJSONArray("content");
              agencyTotalPages = agencyJsonObject.getInt("totalPages");
              for (int i = 0; i < agencyContentData.length(); i++) {
                JSONObject tempAgencyContentData = agencyContentData.getJSONObject(i);
                String tempAgencyId = tempAgencyContentData.get("id").toString();
                String tempAgencyCode = tempAgencyContentData.get("code").toString();
                String tempAgencyName = tempAgencyContentData.get("name").toString();

                this.synthesizeDetailReport(fromDateConvert, toDateConvert, milestoneConvert, tempAgencyId, lAgencyTagId.get(a), tempAgencyCode, tempAgencyName);
              }
            } catch (Exception e) {
              logger.info("DIGO-Info: " + e.getMessage());
              throw new RuntimeException(e);
            }
            if (page <= agencyTotalPages - 1) {
              page++;
            }
          } while (page < agencyTotalPages);
        }
      } else {
        if(Objects.nonNull(strAgencyId) && strAgencyId.length() != 0){
          ObjectId agencyId = null;
          JSONObject agencyJsonObject = null;
          try {
            agencyId = new ObjectId(strAgencyId);
//               String getAgencyUrl = new StringBuilder("http://localhost:8888/agency/").append(strAgencyId).toString();
            String agencyURL = "agency/" + strAgencyId;
            String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
            String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
            agencyJsonObject = new JSONObject(agencyJson);
            String agencyTagId = "";
            String agencyCode = "";
            String agencyName = "";
            JSONArray dossierTagAgencyIDList = agencyJsonObject.getJSONArray("tag");
            for(int i = 0; i < dossierTagAgencyIDList.length(); i++){
              String tempID = dossierTagAgencyIDList.get(i).toString();
              if(tagAgencyIdList.contains(tempID)){
                agencyTagId = tempID;
                break;
              }
            }
            agencyCode = agencyJsonObject.getString("code");
            agencyName = agencyJsonObject.getJSONArray("name").getJSONObject(0).getString("name");
            this.synthesizeDetailReport(fromDateConvert, toDateConvert, milestoneConvert, agencyId.toString(), agencyTagId, agencyCode, agencyName);
          } catch (Exception e){
            throw new RuntimeException(e);
          }
        } else {
          throw new DigoHttpException(11401, HttpServletResponse.SC_BAD_REQUEST);
        }
      }
      // end loop
    }

    private void synthesizeDetailReport(LocalDateTime fromDateConvert,
                                        LocalDateTime toDateConvert,
                                        LocalDateTime milestoneConvert,
                                        String tempAgencyId,
                                        String agencyTagId,
                                        String agencyCode,
                                        String agencyName){
      Criteria aCriteria = new Criteria();
      LocalDateTime loopDate = fromDateConvert.withHour(23).withMinute(59).withSecond(59);
//                  String getSectorUrl = new StringBuilder("http://localhost:8060/sector/--all?agency-id=").append(tempAgencyId + "&onlyAgencyId=1").toString();
      String sectorURL = "sector/--all?agency-id=" + tempAgencyId + "&onlyAgencyId=1";
      String getSectorUrl = microservice.basepadUri(sectorURL).toUriString();
      SectorCase[] sectorJson = MicroserviceExchange.get(restTemplate, getSectorUrl, SectorCase[].class);

      while (!loopDate.isAfter(toDateConvert)) {
        LocalDateTime previousDate = loopDate.minusDays(1);
        Instant instantPreviousDate = previousDate.atZone(zoneId).toInstant();
        Date previousDateConvert = Date.from(instantPreviousDate);
        Aggregation agg = (Aggregation) newAggregation(
            match(Criteria.where("").andOperator(
                (milestoneConvert != null) ? Criteria.where("acceptedDate").gte(milestoneConvert) : aCriteria,
                (loopDate != null) ? Criteria.where("acceptedDate").lte(loopDate) : aCriteria,
//                            Criteria.where(isDept ? "agency.ancestors._id" : "agency.parent._id").is(new ObjectId(tempAgencyId)),
                Criteria.where("agency.parent._id").is(new ObjectId(tempAgencyId)),
                Criteria.where("").orOperator(
                    Criteria.where("completedDate").exists(false),
                    Criteria.where("completedDate").exists(true).gte(previousDate)
                )
            ))
        );
        AggregationResults<KTMETLDossier> groupResults = mongoTemplate.aggregate(agg, "ktmETLDossier", KTMETLDossier.class);
        for(KTMETLDossier dossier : groupResults.getMappedResults()){
          int caseNumber = this.analysisDossierCase(dossier, loopDate);
          var dossierSector = dossier.getSector();
          ObjectId dossierId = new ObjectId(dossier.getId());
          if(dossierSector != null){
            String sectorId = dossierSector.getId();
            for(var sector : sectorJson){
              if(sector.getId().equals(sectorId)){
                if(dossier.getAcceptedDate().after(previousDateConvert)){
                  if(dossier.getApplyMethod().getId() == 0){
                    sector.getOnlineReceivingList().add(dossierId);
                  } else {
                    sector.getDirectlyReceivingList().add(dossierId);
                  }
                }
                switch (caseNumber){
                  case 1: {
                    sector.getCompletedEarlyList().add(dossierId);
                    break;
                  }
                  case 2: {
                    sector.getCompletedOntimeList().add(dossierId);
                    break;
                  }
                  case 3: {
                    sector.getCompletedLatelyList().add(dossierId);
                    break;
                  }
                  case 4: {
                    sector.getInprogressEarlyList().add(dossierId);
                    break;
                  }
                  case 5: {
                    sector.getInprogressLatelyList().add(dossierId);
                    break;
                  }
                }
                break;
              }
            }

          }
        }

        // write data
        this.writeData(sectorJson, loopDate, agencyTagId, tempAgencyId, agencyCode, agencyName);

        // increase loop
        loopDate = loopDate.plusDays(1);
      }
    }

    private int analysisDossierCase(KTMETLDossier dossier, LocalDateTime loopDate){
      int result = -1;
//      result = 1 : Hồ sơ đã giải quyết sớm hạn
//      result = 2 : Hồ sơ đã giải quyết đúng hạn
//      result = 3 : Hồ sơ đã giải quyết trễ hạn
//      result = 4 : Hồ sơ đang giải quyết sớm hạn
//      result = 5 : Hồ sơ đang giải quyết quá hạn
      Date completedDate = dossier.getCompletedDate();
      Date appointmentDate = dossier.getAppointmentDate();
      Instant instant = loopDate.atZone(zoneId).toInstant();
      Date date = Date.from(instant);

      Instant previpusInstant = loopDate.minusDays(1).atZone(zoneId).toInstant();
      Date previousDate = Date.from(previpusInstant);

      var dossierTaskStatus = dossier.getDossierTaskStatus();
      // hồ sơ đặc biệt
      var processingTime = dossier.getProcessingTime();
      if(processingTime == 0){ // ngày xử lý = 0
        if(completedDate == null){
          var dossierTaskStatusId = dossierTaskStatus.getId();
          if(dossierTaskStatusId.equals("6147e4c4ebfba925f1e89bf0") // đã rút hồ sơ
              || dossierTaskStatusId.equals("6151c771ba2a04299f949875") // chờ rút hồ sơ
          ){
            completedDate = dossier.getWithdrawDate();
            if(completedDate.before(previousDate)){
              return 0;
            }
          }

          if(dossierTaskStatusId.equals("61ee30eada2d36b037e00005")) // Dừng xử lý
          {
            completedDate = dossier.getCancelledDate();
            if(completedDate.before(previousDate)){
              return 0;
            }
          }
        }

        if(completedDate != null && (completedDate.before(date) || completedDate.equals(date))
        ){
          return 1;
        } else {
          return 4;
        }
      }


      if(dossierTaskStatus != null){
        var dossierTaskStatusId = dossierTaskStatus.getId();

        // hồ sơ rút
        if(dossierTaskStatusId.equals("6147e4c4ebfba925f1e89bf0") // đã rút hồ sơ
            || dossierTaskStatusId.equals("6151c771ba2a04299f949875") // chờ rút hồ sơ
        ){
          var withdrawDate = dossier.getWithdrawDate();
          return this.analysisDossierCase4(withdrawDate, appointmentDate, loopDate);
        }

        // hồ sơ dừng xử lý
        if(dossierTaskStatusId.equals("61ee30eada2d36b037e00005")) // Dừng xử lý
        {
          var cancelledDate = dossier.getCancelledDate();
          return this.analysisDossierCase4(cancelledDate, appointmentDate, loopDate);
        }

        // hồ sơ thực hiện nghĩa vụ tài chính
        if(dossierTaskStatusId.equals("60f6364e09cbf91d41f88859")) // Thực hiện nghĩa vụ tài chính
        {
          var financialObligationsDate = dossier.getFinancialObligationsDate();
          return this.analysisDossierCase3(financialObligationsDate, appointmentDate);
        }

//        // hồ sơ yêu cầu bổ sung
        if(dossierTaskStatusId.equals("60ebf03109cbf91d41f87f8b")) // Yêu cầu bổ sung giấy tờ
        {
          return 4;
//          var additionalDate = dossier.getAdditionalDate();
//          if(additionalDate == null || additionalDate.size() != 1){
//            return 5;
//          }
//          return this.analysisDossierCase3(additionalDate.get(0), appointmentDate);
        }
      }

      // hồ sơ tạm dừng
      var dossierStatus = dossier.getDossierStatus();
      if(dossierStatus != null){
        var dossierStatusId = dossierStatus.getId();
        if(dossierStatusId == 3) // Đang tạm dừng
        {
          var pauseDate = dossier.getPauseDate();
          return this.analysisDossierCase3(pauseDate, appointmentDate);
        }
      }

      // hồ sơ yêu cầu bổ sung
//      var additionalDate = dossier.getAdditionalDate();
//      if(additionalDate != null && additionalDate.size() > 0){
//        this.analysisDossierCase5(completedDate, appointmentDate, date, additionalDate);
//      }

      // hs bình thường
      return this.analysisDossierCase2(completedDate, appointmentDate, date);
    }

    private int analysisDossierCase2(Date inputDate, Date appointmentDate, Date loopDate){
      if(inputDate != null
          && (inputDate.before(loopDate) || inputDate.equals(loopDate))
      ){
        if(inputDate.before(appointmentDate)){
          return 1;
        }
        if(inputDate.equals(appointmentDate)){
          return 2;
        }
        return 3;
      } else {
        if(loopDate.after(appointmentDate)){
          return 5;
        }
        return 4;
      }
    }

  private int analysisDossierCase3(Date inputDate, Date appointmentDate){
    if(inputDate == null
        || inputDate.after(appointmentDate)
    ){
      return 5;
    }
    return 4;
  }

  private int analysisDossierCase4(Date inputDate, Date appointmentDate, LocalDateTime loopDate){
    if(inputDate == null) return 5;
    Instant previpusInstant = loopDate.minusDays(1).atZone(zoneId).toInstant();
    Date previousDate = Date.from(previpusInstant);

    Instant instant = loopDate.atZone(zoneId).toInstant();
    Date date = Date.from(instant);
    if(inputDate.before(previousDate)){ // bo qua
      return 0;
    }
    if(inputDate.after(date)){ // dang xu ly
      if(date.after(appointmentDate)){
        return 5;
      } else {
        return 4;
      }
    }

    // hoan thanh
    if(inputDate.before(appointmentDate)){
      return 1;
    }

    if(inputDate.equals(appointmentDate)){
      return 2;
    }

    return 3;
  }

  private int analysisDossierCase5(Date inputDate, Date appointmentDate, Date loopDate, List<Date> additionalDate){
//    Date lastDate = Collections.max(additionalDate, Date::compareTo);
    if(inputDate != null
        && (inputDate.before(loopDate) || inputDate.equals(loopDate))
    ){
      if(additionalDate.size() != 1) return 3;
      if(inputDate.before(appointmentDate)){
        return 1;
      }
      if(inputDate.equals(appointmentDate)){
        return 2;
      }
      return 3;
    } else {
      if(additionalDate.size() != 1 || loopDate.after(appointmentDate)){
        return 5;
      }
      return 4;
    }
  }

  private void writeData(SectorCase[] sectorJson, LocalDateTime loopDate, String agencyTagId,
                         String agencyId, String agencyCode, String agencyName){
    for(var sector : sectorJson){
      DossierSynthesis record = new DossierSynthesis();
      record.setYear(loopDate.getYear());
      record.setMonth(loopDate.getMonthValue());
      record.setDay(loopDate.getDayOfMonth());
      Instant instant = loopDate.atZone(zoneId).toInstant();
      Date date = Date.from(instant);
      record.setSynthesisDate(date);
      record.setCreatedDate(date);
      record.setUpdatedDate(date);

      DossierAgencyDto agencyRecord = null;
      try {
        agencyRecord = new DossierAgencyDto(
            new ObjectId(agencyId),
            agencyCode,
            agencyName,
            new ObjectId(agencyTagId)
        );
      } catch (Exception e) {
//      throw new RuntimeException(e);
      }
      record.setAgency(agencyRecord);

      DossierSectorDto sectorRecord = new DossierSectorDto(
          new ObjectId(sector.getId()),
          sector.getCode(),
          sector.getName().get(0).getName()
      );
      record.setSector(sectorRecord);

      record.setCompletedEarlyList(sector.getCompletedEarlyList());
      record.setCompletedOntimeList(sector.getCompletedOntimeList());
      record.setCompletedLatelyList(sector.getCompletedLatelyList());
      record.setInprogressEarlyList(sector.getInprogressEarlyList());
      record.setInprogressLatelyList(sector.getInprogressLatelyList());
      record.setOnlineReceivingList(sector.getOnlineReceivingList());
      record.setDirectlyReceivingList(sector.getDirectlyReceivingList());

      DossierSynthesisData synthesisData = new DossierSynthesisData();
      int completedEarly = sector.getCompletedEarlyList().size();
      int completedOntime = sector.getCompletedOntimeList().size();
      int completedLately = sector.getCompletedLatelyList().size();
      int inprogressEarly = sector.getInprogressEarlyList().size();
      int inprogressLately = sector.getInprogressLatelyList().size();
      int onlineReceiving = sector.getOnlineReceivingList().size();
      int directlyReceiving = sector.getDirectlyReceivingList().size();

      synthesisData.setCompletedEarly(completedEarly);
      synthesisData.setCompletedOnTime(completedOntime);
      synthesisData.setCompletedLately(completedLately);
      synthesisData.setCompleted(completedEarly + completedOntime + completedLately);
      synthesisData.setInProgressEarly(inprogressEarly);
      synthesisData.setInProgressLately(inprogressLately);
      synthesisData.setInProgress(inprogressEarly + inprogressLately);
      synthesisData.setOnlineReceived(onlineReceiving);
      synthesisData.setDirectReceived(directlyReceiving);
      synthesisData.setReceived(onlineReceiving + directlyReceiving);
      record.setSynthesisData(synthesisData);

      // upsert
      Query query = new Query();
      query.addCriteria(
          Criteria.where("").andOperator(
              Criteria.where("day").is(record.getDay()),
              Criteria.where("month").is(record.getMonth()),
              Criteria.where("year").is(record.getYear()),
              Criteria.where("agency.id").is(record.getAgency().getId()),
              Criteria.where("sector.id").is(record.getSector().getId())
          ) );
      Update update = new Update()
          .set("day", record.getDay())
          .set("month", record.getMonth())
          .set("year", record.getYear())
          .set("agency", record.getAgency())
          .set("sector", record.getSector())
          .set("synthesisData", record.getSynthesisData())
          .set("synthesisDate", record.getSynthesisDate())
          .set("createdDate", record.getCreatedDate())
          .set("updatedDate", record.getUpdatedDate())
          .set("completedEarlyList", record.getCompletedEarlyList())
          .set("completedOntimeList", record.getCompletedOntimeList())
          .set("completedLatelyList", record.getCompletedLatelyList())
          .set("inprogressEarlyList", record.getInprogressEarlyList())
          .set("inprogressLatelyList", record.getInprogressLatelyList())
          .set("onlineReceivingList", record.getOnlineReceivingList())
          .set("directlyReceivingList", record.getDirectlyReceivingList());
      mongoTemplate.upsert(query, update, DossierSynthesis.class);

      // resetData
      sector.getCompletedEarlyList().clear();
      sector.getCompletedOntimeList().clear();
      sector.getCompletedLatelyList().clear();
      sector.getInprogressEarlyList().clear();
      sector.getInprogressLatelyList().clear();
      sector.getOnlineReceivingList().clear();
      sector.getDirectlyReceivingList().clear();
    }
  }

  public List<IdPojo> getETLDossierIdList(String fromDate, String toDate){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }
    Aggregation aggregation = Aggregation.newAggregation(
        match(
            Criteria.where("acceptedDate").gte(fromDateConvert).lte(toDateConvert)
        ),
        project(
            "_id"
        )
    );
    AggregationResults<IdPojo> groupResults = mongoTemplate.aggregate(aggregation, "ktmETLDossier", IdPojo.class);
    return groupResults.getMappedResults();
  }

  public KTMETLDossier getETLDossier(String id){
    ObjectId newId;
    try{
      newId = new ObjectId(id);
    } catch (Exception e){
      throw new RuntimeException(e);
    }
    Query  query = new Query().addCriteria(
        Criteria.where("_id").is(newId)
    );
    return mongoTemplate.findOne(query, KTMETLDossier.class);
  }

  public KTMETLDossier updateETLDossier(KTMETLDossier body){

    return mongoTemplate.save(body, "ktmETLDossier");
  }

  public Slice<KTMETLDossier> getETLDossierList(String fromDate, String toDate, Pageable pageable){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    Query query = new Query().addCriteria(
        Criteria.where("acceptedDate").gte(fromDateConvert).lte(toDateConvert)
    ).with(pageable);
    List<KTMETLDossier> resultList = mongoTemplate.find(query, KTMETLDossier.class, "ktmETLDossier");
    Slice<KTMETLDossier> slice = PageableExecutionUtils.getPage(resultList, pageable,
        () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), KTMETLDossier.class));
    return  slice;
  }

  public String calculateTotalDocumentByDay(String fromDate, String toDate, String tagAgencyId){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    ObjectId tagAgencyIdConverted = null;
    if(Strings.isNullOrEmpty(tagAgencyId)){

    } else {
      try{
        tagAgencyIdConverted = new ObjectId(tagAgencyId);
      } catch (Exception e){
        throw new RuntimeException(e);
      }
    }

    Criteria criteria = new Criteria();

    Query query = new Query();
    query.addCriteria(
        Criteria.where("").andOperator(
            Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert),
            (tagAgencyIdConverted != null) ? Criteria.where("agency.tagId").is(tagAgencyIdConverted) : criteria
        )
    );
    long count = mongoTemplate.count(query, "dossierSynthesis");
    return String.valueOf(count);
  }

  public Slice<DossierSynthesisDTO> getSynthesizeList(String fromDate, String toDate, String tagAgencyId, Pageable pageable){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    ObjectId tagAgencyIdConverted = null;
    try {
      tagAgencyIdConverted = new ObjectId(tagAgencyId);
    } catch (Exception e) {}
    Criteria criteria = new Criteria();

    Query query = new Query().addCriteria(
        Criteria.where("").andOperator(
            Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert),
            tagAgencyIdConverted != null ? Criteria.where("agency.tagId").is(tagAgencyIdConverted) : criteria
        )
    ).with(pageable);
    List<DossierSynthesisDTO> resultList = mongoTemplate.find(query, DossierSynthesisDTO.class, "dossierSynthesis");
    Slice<DossierSynthesisDTO> slice = PageableExecutionUtils.getPage(resultList, pageable,
        () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), DossierSynthesisDTO.class, "dossierSynthesis"));
    return slice;
  }

  public List<DossierSumEtlDto> getReportDossierAgency(String strFromDate,
                                                       String strToDate,
                                                       String strParentId,
                                                       String strTagAgencyId) throws JSONException, ParseException {
    List<DossierSumEtlDto> result = new ArrayList<DossierSumEtlDto>();

    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Date fromDate = df.parse(strFromDate);
    Date toDate = df.parse(strToDate);
    Date toDay = new Date();

    Integer year = null;
    Integer month = null;
    Integer day = null;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(fromDate);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    year = calendar.get(Calendar.YEAR) ;
    month = calendar.get(Calendar.MONTH) + 1;
    day = calendar.get(Calendar.DAY_OF_MONTH);
    int toD = 0 ;
    int toM = 0 ;
    int toY = 0 ;
    if(toDate.before(toDay)){
      toD = toDate.getDate();
      toM= toDate.getMonth()+1;
      toY = toDate.getYear()+1900;
    }if(toDate.after(toDay)){
      Calendar calendars = Calendar.getInstance();
      calendars.setTime(toDay);
      calendars.add(Calendar.DAY_OF_MONTH, -1);
      calendars.set(Calendar.HOUR,23);
      calendars.set(Calendar.MINUTE,59);
      calendars.set(Calendar.SECOND,59);
      toDate = calendars.getTime();

      toY = calendars.get(Calendar.YEAR) ;
      toM= calendars.get(Calendar.MONTH) +1;
      toD = calendars.get(Calendar.DAY_OF_MONTH);
    }
    // Lấy ngày tháng năm hôm trước
    Date previousDate = calendar.getTime();
    Criteria aCriteria = new Criteria();

    ObjectId tagAgencyId = null;
    List<ObjectId> tagAgencyIdList = new ArrayList<>();
    try{
      tagAgencyId = new ObjectId(strTagAgencyId);
      tagAgencyIdList.add(tagAgencyId);
      if(strTagAgencyId.equals("612c5c0f17a2264ee7f42fa4")){
        tagAgencyIdList.add(new ObjectId("62a6f8e43a06bf0913558b0f"));
      }
    } catch (Exception exception){}

    ObjectId parentId = null;
    try{
      parentId = new ObjectId(strParentId);
    } catch (Exception exception){}

    List<ObjectId> subAgencyIdList = new ArrayList<>();
    if(parentId != null){
//      String getParentUrl = "http://localhost:8888/agency/--by-parent-id?tag-id=" + strTagAgencyId + "&parent-id="+strParentId;
      String parentURL = "agency/--by-parent-id?tag-id=" + strTagAgencyId + "&parent-id=" + strParentId;
      String getParentUrl = microservice.basedataUri(parentURL).toUriString();
      String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
      JSONArray parentJsonObject = new JSONArray(parentJson);
      for(int i = 0; i < parentJsonObject.length(); i++){
        try{
          String tempParentId = parentJsonObject.getJSONObject(i).get("id").toString();
          subAgencyIdList.add(new ObjectId(tempParentId));
        } catch (Exception exception){}
      }
    }

    MatchOperation matchOperation = Aggregation.match(
            Criteria.where("").andOperator(
                    Criteria.where("synthesisDate").gte(fromDate).lte(toDate),
                    tagAgencyId != null ? Criteria.where("agency.tagId").in(tagAgencyIdList) : aCriteria,
                    subAgencyIdList.size() > 0 ? Criteria.where("agency.id").in(subAgencyIdList) : aCriteria
            )
    );

    GroupOperation groupByAgencyAndSector = Aggregation.group(
                    Fields.fields().and("agencyId", "$agency.id"))
            .sum("$synthesisData.received").as("received")
            .sum("$synthesisData.completed").as("completed")
            .sum("$synthesisData.completedOnTime").as("completedOnTime")
            .sum("$synthesisData.completedEarly").as("completedEarly")
            .sum("$synthesisData.completedLately").as("completedOutOfDue")
            .sum("$synthesisData.onlineReceived").as("onlineReceived")
            .sum("$synthesisData.directReceived").as("directReceived")
            .sum("$synthesisData.inProgress").as("inProgress")
            .sum("$synthesisData.inProgressEarly").as("inProgressAndOnTime")
            .sum("$synthesisData.inProgressLately").as("inProgressAndOutOfDue")
            .first("$agency").as("agency");
    ProjectionOperation projectCountingData = Aggregation.project()
            .and("received").as("countingData.received")
            .and("completed").as("countingData.completed")
            .and("completedOnTime").as("countingData.completedOnTime")
            .and("completedEarly").as("countingData.completedEarly")
            .and("completedOutOfDue").as("countingData.completedOutOfDue")
            .and("onlineReceived").as("countingData.onlineReceived")
            .and("directReceived").as("countingData.directReceived")
            .and("inProgress").as("countingData.inProgress")
            .and("inProgressAndOnTime").as("countingData.inProgressAndOnTime")
            .and("inProgressAndOutOfDue").as("countingData.inProgressAndOutOfDue")
            .and("$agency").as("agency");

    SortOperation sortByAgency = Aggregation.sort(Sort.Direction.ASC, "agency.name");

    Aggregation pipelineAggregation = Aggregation.newAggregation(
            matchOperation,
            groupByAgencyAndSector,
            projectCountingData,
            sortByAgency
    );


    AggregationResults<DossierSumEtlDto> resultFacet1 = mongoTemplate.aggregate(pipelineAggregation, "dossierSynthesis", DossierSumEtlDto.class);


    List<DossierSumEtlDto> facet1Results = resultFacet1.getMappedResults();



    Aggregation pipelineAggregation2 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
            groupByAgencyAndSector,
            projectCountingData
    );


    AggregationResults<DossierSumEtlDto> resultFacet2 = mongoTemplate.aggregate(pipelineAggregation2, "dossierSynthesis", DossierSumEtlDto.class);


    List<DossierSumEtlDto> facet2Results = resultFacet2.getMappedResults();

    Aggregation pipelineAggregation3 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
            groupByAgencyAndSector,
            projectCountingData
    );


    AggregationResults<DossierSumEtlDto> resultFacet3 = mongoTemplate.aggregate(pipelineAggregation3, "dossierSynthesis", DossierSumEtlDto.class);

    List<DossierSumEtlDto> facet3Results = resultFacet3.getMappedResults();

    for (DossierSumEtlDto facet1Result : facet1Results) {
      for (DossierSumEtlDto facet2Result : facet2Results) {
        if (facet1Result.getAgency().getId().equals(facet2Result.getAgency().getId()) ) {

          try{
            facet1Result.setPreviousPeriod(facet2Result.getCountingData().getInProgress());
          }catch (Exception e){

          }

          break;
        }
      }
    }
    for (DossierSumEtlDto facet1Result : facet1Results) {
      for (DossierSumEtlDto facet3Result : facet3Results) {
        if (facet1Result.getAgency().getId().equals(facet3Result.getAgency().getId()) ) {
          try{
            facet1Result.getCountingData().setInProgress(facet3Result.getCountingData().getInProgress());
            facet1Result.getCountingData().setInProgressAndOnTime(facet3Result.getCountingData().getInProgressAndOnTime());
            facet1Result.getCountingData().setInProgressAndOutOfDue(facet3Result.getCountingData().getInProgressAndOutOfDue());
          }catch (Exception e){

          }
          break;
        }
      }
    }

    //  replaceValueAtIndex()\
    List<DossierSumEtlDto> list1 = new ArrayList<>();
    List<DossierSumEtlDto> list2 = new ArrayList<>();
    List<DossierSumEtlDto> list3 = new ArrayList<>();
    List<DossierSumEtlDto> list4 = new ArrayList<>();
    List<DossierSumEtlDto> list5 = new ArrayList<>();
    List<DossierSumEtlDto> list6 = new ArrayList<>();
    List<DossierSumEtlDto> list7 = new ArrayList<>();
    for(int i = 0; i < facet1Results.size(); i++){
      if(facet1Results.get(i).getAgency().getTagId().equals(new ObjectId("612c5c0f17a2264ee7f42fa4")) &&
              !facet1Results.get(i).getAgency().getId().equals(new ObjectId("6184a96b2111cd3445e708e2"))
              && !facet1Results.get(i).getAgency().getId().equals(new ObjectId("6107428f2d79153a212b9223"))
      ){
        list1.add(facet1Results.get(i));
      }if(facet1Results.get(i).getAgency().getId().equals(new ObjectId("6184a96b2111cd3445e708e2"))){
        list2.add(facet1Results.get(i));
      }if(facet1Results.get(i).getAgency().getId().equals(new ObjectId("6184aa1e2111cd3445e70947"))){ // tt gioi thieu viec lam
        list3.add(facet1Results.get(i));
      }if(facet1Results.get(i).getAgency().getId().equals(new ObjectId("6107428f2d79153a212b9223"))){
        list4.add(facet1Results.get(i));
      }if(facet1Results.get(i).getAgency().getTagId().equals(new ObjectId("62a6f8e43a06bf0913558b0f"))
              && !facet1Results.get(i).getAgency().getId().equals(new ObjectId("6184aa1e2111cd3445e70947"))){
        list5.add(facet1Results.get(i));
      }if(facet1Results.get(i).getAgency().getTagId().equals(new ObjectId("5f7dade4b80e603d5300dcc4"))){
        list6.add(facet1Results.get(i));
      }if(facet1Results.get(i).getAgency().getTagId().equals(new ObjectId("5f6b177a4e1bd312a6f3ae4a"))){
        list7.add(facet1Results.get(i));
      }
    }
    List<DossierSumEtlDto> combinedList = new ArrayList<>();
    combinedList.addAll(list1);
    combinedList.addAll(list2);
    combinedList.addAll(list3);
    combinedList.addAll(list4);
    combinedList.addAll(list5);
    combinedList.addAll(list6);
    combinedList.addAll(list7);


    return combinedList;
  }

  public List<DossierCoutingEtlDto> getReportDossierAgencySector(String strFromDate,
                                                                 String strToDate,
                                                                 String agencyId) throws JSONException, ParseException {
    List<GetDossierCountingDto> result = new ArrayList<GetDossierCountingDto>();

    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Date fromDate = df.parse(strFromDate);
    Date toDate = df.parse(strToDate);

    Integer year = null;
    Integer month = null;
    Integer day = null;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(fromDate);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    year = calendar.get(Calendar.YEAR) ;
    month = calendar.get(Calendar.MONTH) + 1;
    day = calendar.get(Calendar.DAY_OF_MONTH);
    Date previousDate = calendar.getTime();
    int toD = toDate.getDate();
    int toM= toDate.getMonth()+1;
    int toY = toDate.getYear()+1900;
    Criteria aCriteria = new Criteria();
    AggregationOperation matchAgency = Aggregation.match(Criteria.where("agency.id").is(new ObjectId(agencyId)));

    AggregationOperation matchDate = Aggregation.match(Criteria.where("synthesisDate").gte(fromDate).lte(toDate));

    AggregationOperation groupByAgencyAndSector = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            .sum("$synthesisData.received").as("received")
            .sum("$synthesisData.completed").as("completed")
            .sum("$synthesisData.completedOnTime").as("completedOnTime")
            .sum("$synthesisData.completedEarly").as("completedEarly")
            .sum("$synthesisData.completedLately").as("completedOutOfDue")
            .sum("$synthesisData.onlineReceived").as("onlineReceived")
            .sum("$synthesisData.directReceived").as("directReceived")
            .sum("$synthesisData.inProgress").as("inProgress")
            .sum("$synthesisData.inProgressEarly").as("inProgressAndOnTime")
            .sum("$synthesisData.inProgressLately").as("inProgressAndOutOfDue")
            .first("$agency").as("agency")
            .first("$sector").as("sector");

    ProjectionOperation projectCountingData = Aggregation.project()
            .and("received").as("countingData.received")
            .and("completed").as("countingData.completed")
            .and("completedOnTime").as("countingData.completedOnTime")
            .and("completedEarly").as("countingData.completedEarly")
            .and("completedOutOfDue").as("countingData.completedOutOfDue")
            .and("onlineReceived").as("countingData.onlineReceived")
            .and("directReceived").as("countingData.directReceived")
            .and("inProgress").as("countingData.inProgress")
            .and("inProgressAndOnTime").as("countingData.inProgressAndOnTime")
            .and("inProgressAndOutOfDue").as("countingData.inProgressAndOutOfDue")
            .and("$agency").as("agency")
            .and("$sector").as("sector");

    SortOperation sortByAgency = Aggregation.sort(Sort.Direction.ASC, "sector.id");
    // Xây dựng pipeline aggregation
    Aggregation pipelineAggregation1 = Aggregation.newAggregation(
            matchAgency,
            matchDate,
            groupByAgencyAndSector,
            projectCountingData,
            sortByAgency
    );


    AggregationResults<DossierCoutingEtlDto> resultFacet1 = mongoTemplate.aggregate(pipelineAggregation1, "dossierSynthesis", DossierCoutingEtlDto.class);

    List<DossierCoutingEtlDto> facet1Results = resultFacet1.getMappedResults();


    Aggregation pipelineAggregation2 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
            matchAgency,
            groupByAgencyAndSector,
            projectCountingData
    );

    AggregationResults<DossierCoutingEtlDto> resultFacet2 = mongoTemplate.aggregate(pipelineAggregation2, "dossierSynthesis", DossierCoutingEtlDto.class);

    List<DossierCoutingEtlDto> facet2Results = resultFacet2.getMappedResults();

    Aggregation pipelineAggregation3 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
            matchAgency,
            groupByAgencyAndSector,
            projectCountingData
    );


    AggregationResults<DossierCoutingEtlDto> resultFacet3 = mongoTemplate.aggregate(pipelineAggregation3, "dossierSynthesis", DossierCoutingEtlDto.class);

    List<DossierCoutingEtlDto> facet3Results = resultFacet3.getMappedResults();


    for (DossierCoutingEtlDto facet1Result : facet1Results) {
      for (DossierCoutingEtlDto facet2Result : facet2Results) {
        if (facet1Result.getAgency().getId().equals(facet2Result.getAgency().getId()) &&
                facet1Result.getSector().getId().equals(facet2Result.getSector().getId())) {

          try{
            facet1Result.setPreviousPeriod(facet2Result.getCountingData().getInProgress());
          }catch (Exception e){

          }
          break;
        }
      }
    }
    for (DossierCoutingEtlDto facet1Result : facet1Results) {
      for (DossierCoutingEtlDto facet3Result : facet3Results) {
        if (facet1Result.getAgency().getId().equals(facet3Result.getAgency().getId()) &&
                facet1Result.getSector().getId().equals(facet3Result.getSector().getId())) {
          try{
            facet1Result.getCountingData().setInProgress(facet3Result.getCountingData().getInProgress());
            facet1Result.getCountingData().setInProgressAndOnTime(facet3Result.getCountingData().getInProgressAndOnTime());
            facet1Result.getCountingData().setInProgressAndOutOfDue(facet3Result.getCountingData().getInProgressAndOutOfDue());
          }catch (Exception e){

          }
          break;
        }
      }
    }
    return facet1Results;
  }

  public Slice getIdDossier(String strFromDate,
                            String strToDate,
                            int type,
                            List<String> agencyId,
                            List<String> sectorId,
                            Pageable pageable
  ) throws JSONException, ParseException {


    //TYPE
    //1 :received
    //2 :inProgress
    //3 :inProgressAndOnTime
    //4 :inProgressAndOutOfDue
    //5 :completed
    //6 :completedOnTime
    //7 :completedEarly
    //8 :completedOutOfDue
    //9 :onlineReceived
    //10:directReceived
    //11:previousPeriod
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Date fromDate = df.parse(strFromDate);
    Date toDate = df.parse(strToDate);

    Integer year = null;
    Integer month = null;
    Integer day = null;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(fromDate);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    year = calendar.get(Calendar.YEAR) ;
    month = calendar.get(Calendar.MONTH) + 1;
    day = calendar.get(Calendar.DAY_OF_MONTH);
    // Lấy ngày tháng năm hôm trước
    Date previousDate = calendar.getTime();
    int toD = toDate.getDate();
    int toM= toDate.getMonth()+1;
    int toY = toDate.getYear()+1900;

    List<ObjectId> agencyIds = new ArrayList<>();
    for (String idString : agencyId) {
      agencyIds.add(new ObjectId(idString));
    }

    Criteria matchAgencyId;
    if (agencyIds.isEmpty()) {
      matchAgencyId = new Criteria();
    } else {
      matchAgencyId = Criteria.where("agency.id").in(agencyIds);
    }

    List<ObjectId> sectorIds = new ArrayList<>();
    if(!(sectorId == null || sectorId.isEmpty())){
      for (String idString : sectorId) {
        sectorIds.add(new ObjectId(idString));
      }
    }
    Criteria matchSectorId;
    if (sectorIds.isEmpty()) {
      matchSectorId = new Criteria();
    } else {
      matchSectorId = Criteria.where("sector.id").in(sectorIds);
    }

    Criteria matchDateRange = Criteria.where("synthesisDate")
            .gte(fromDate)
            .lte(toDate);

    AggregationOperation matchAgencies = Aggregation.match(matchAgencyId);
    AggregationOperation matchSectors = Aggregation.match(matchSectorId);
    AggregationOperation matchDate = Aggregation.match(matchDateRange);

    AggregationOperation group = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            //  .count().as("count")

            .push("completedEarlyList").as("completedEarlyList")
            .push("completedLatelyList").as("completedLatelyList")
            .push("completedOntimeList").as("completedOntimeList")
            .push("onlineReceivingList").as("onlineReceivingList")
            .push("directlyReceivingList").as("directlyReceivingList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation groupNoSector = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id")))
            //  .count().as("count")

            .push("completedEarlyList").as("completedEarlyList")
            .push("completedLatelyList").as("completedLatelyList")
            .push("completedOntimeList").as("completedOntimeList")
            .push("onlineReceivingList").as("onlineReceivingList")
            .push("directlyReceivingList").as("directlyReceivingList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation project = project()
            .and("_id").as("_id")
            .and("agency").as("agency")
            .and("sector").as("sector")
            .and(ArrayOperators.Reduce.arrayOf("completedEarlyList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalcompletedEarlyListSum")
            .and(ArrayOperators.Reduce.arrayOf("completedLatelyList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalcompletedLatelyListSum")
            .and(ArrayOperators.Reduce.arrayOf("completedOntimeList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalcompletedOntimeListSum")
            .and(ArrayOperators.Reduce.arrayOf("onlineReceivingList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalonlineReceivingListSum")
            .and(ArrayOperators.Reduce.arrayOf("directlyReceivingList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totaldirectlyReceivingListSum");


    Aggregation aggregation1 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("fieldName").is("someValue"))
    );
    if(sectorId == null || sectorId.isEmpty()){
      aggregation1 =  Aggregation.newAggregation(matchAgencies,matchSectors, matchDate, groupNoSector, project).withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
    }else {
      aggregation1 =  Aggregation.newAggregation(matchAgencies,matchSectors, matchDate, group, project).withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
    }
    AggregationResults<DossierCoutingEtlDetailDto> result1 = mongoTemplate.aggregate(aggregation1, "dossierSynthesis", DossierCoutingEtlDetailDto.class);

    List<DossierCoutingEtlDetailDto> Results1 = result1.getMappedResults();


    AggregationOperation group2 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation groupNoSector2 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation project2 = project()
            .and("_id").as("_id")
            .and("agency").as("agency")
            .and("sector").as("sector")
            .and(ArrayOperators.Reduce.arrayOf("inprogressEarlyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressEarlyListSum")
            .and(ArrayOperators.Reduce.arrayOf("inprogressLatelyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressLatelyListSum");





    Aggregation aggregation2 =Aggregation.newAggregation(
            Aggregation.match(Criteria.where("fieldName").is("someValue"))
    );

    if(sectorId == null || sectorId.isEmpty()){
      aggregation2 =  Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
              groupNoSector2,
              project2
      );
    }else {
      aggregation2 =  Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
              group2,
              project2
      );
    }
    AggregationResults<DossierCoutingEtlDetailDto> result2 = mongoTemplate.aggregate(aggregation2, "dossierSynthesis", DossierCoutingEtlDetailDto.class);
    List<DossierCoutingEtlDetailDto> Results2 = result2.getMappedResults();

    if(sectorId == null || sectorId.isEmpty()){
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results2s : Results2) {
          if (Results1s.getAgency().getId().equals(Results2s.getAgency().getId())) {
            List<String> combinedList = new ArrayList<>();
            combinedList.addAll(Results2s.getTotalinprogressEarlyListSum());
            combinedList.addAll(Results2s.getTotalinprogressLatelyListSum());
            Results1s.setTotalpreviousPeriodListSum(combinedList);
            //
            break; //
          }
        }
      }
    }else{
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results2s : Results2) {
          if (Results1s.getAgency().getId().equals(Results2s.getAgency().getId()) &&
                  Results1s.getSector().getId().equals(Results2s.getSector().getId())) {

            List<String> combinedList = new ArrayList<>();
            combinedList.addAll(Results2s.getTotalinprogressEarlyListSum());
            combinedList.addAll(Results2s.getTotalinprogressLatelyListSum());
            Results1s.setTotalpreviousPeriodListSum(combinedList);

            //
            break; //
          }
        }
      }
    }

    AggregationOperation group3 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation groupNoSector3 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");


    AggregationOperation project3 = project()
            .and("_id").as("_id")
            .and("agency").as("agency")
            .and("sector").as("sector")
            .and(ArrayOperators.Reduce.arrayOf("inprogressEarlyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressEarlyListSum")
            .and(ArrayOperators.Reduce.arrayOf("inprogressLatelyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressLatelyListSum");

    Aggregation aggregation3 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("fieldName").is("someValue"))
    );

    if(sectorId == null || sectorId.isEmpty()){
      aggregation3 = Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
              groupNoSector3,
              project3
      );
    }else {
      aggregation3 = Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
              group3,
              project3
      );
    }


    AggregationResults<DossierCoutingEtlDetailDto> result3 = mongoTemplate.aggregate(aggregation3, "dossierSynthesis", DossierCoutingEtlDetailDto.class);
    List<DossierCoutingEtlDetailDto> Results3 = result3.getMappedResults();

    if(sectorId == null || sectorId.isEmpty()){
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results3s : Results3) {
          if (Results1s.getAgency().getId().equals(Results3s.getAgency().getId())) {
            Results1s.setTotalinprogressEarlyListSum(Results3s.getTotalinprogressEarlyListSum());
            Results1s.setTotalinprogressLatelyListSum(Results3s.getTotalinprogressLatelyListSum());
            //
            break; //
          }
        }
      }
    }else{
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results3s : Results3) {
          if (Results1s.getAgency().getId().equals(Results3s.getAgency().getId()) &&
                  Results1s.getSector().getId().equals(Results3s.getSector().getId())) {

            Results1s.setTotalinprogressEarlyListSum(Results3s.getTotalinprogressEarlyListSum());
            Results1s.setTotalinprogressLatelyListSum(Results3s.getTotalinprogressLatelyListSum());
            //
            break; //
          }
        }
      }
    }

    List<String> idDossierInProgressAndOnTime = new ArrayList<>();
    List<String> idDossierInProgressAndOutOfDue = new ArrayList<>();
    List<String> idDossierCompletedOnTime = new ArrayList<>();
    List<String> idDossierCompletedEarly = new ArrayList<>();
    List<String> idDossierCompletedOutOfDue = new ArrayList<>();


    List<DossierEtlDetailDto> result = new ArrayList<>();
    List<DossierEtlDetailDto> subResult = new ArrayList<>();

    for (int i = 0 ; i < Results1.size() ; i++){
      idDossierInProgressAndOnTime.addAll(Results1.get(i).getTotalinprogressEarlyListSum());//1
      idDossierInProgressAndOutOfDue.addAll(Results1.get(i).getTotalinprogressLatelyListSum());//2
      idDossierCompletedOnTime.addAll(Results1.get(i).getTotalcompletedOntimeListSum());//3
      idDossierCompletedEarly.addAll(Results1.get(i).getTotalcompletedEarlyListSum());//4
      idDossierCompletedOutOfDue.addAll(Results1.get(i).getTotalcompletedLatelyListSum());//5
    }
    if(type == 1){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
    }
    if(type == 2){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
    }
    if(type == 3){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
    }
    if(type == 4){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
    }
    if(type == 5){
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
    }
    if(type == 6){
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
    }
    if(type == 7){
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
    }
    if(type == 8){
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
    }
    if(type == 9){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
      for(int i = 0; i<result.size(); i++){
        if(Integer.valueOf(result.get(i).getApplyMethodId()).equals(0) && result.get(i).getAcceptedDate().after(fromDate)){
          subResult.add(result.get(i));
        }
      }
      result = subResult;
    }
    if(type == 10){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
      for(int i = 0; i<result.size(); i++){
        if(Integer.valueOf(result.get(i).getApplyMethodId()).equals(1) && result.get(i).getAcceptedDate().after(fromDate)){
          subResult.add(result.get(i));
        }
      }
      result = subResult;
    }
    if(type == 11){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
      for(int i = 0; i<result.size(); i++){
        if(result.get(i).getAcceptedDate().before(fromDate)){
          subResult.add(result.get(i));
        }
      }
      result = subResult;
    }

    int fromIndex = pageable.getPageNumber() * pageable.getPageSize();
    int toIndex = Math.min(fromIndex + pageable.getPageSize(), result.size());

    List<DossierEtlDetailDto> limitedResult = result.subList(fromIndex, toIndex);

    Page<DossierEtlDetailDto> page = new PageImpl<>(limitedResult, pageable, result.size());

    return page;

  }


  public List<DossierEtlDetailDto> getKTMETLDossiers(List<String> idList, int type) {


    Query query = new Query(Criteria.where("_id").in(idList));
   // query.with(pageable.getSort());
    List<KTMETLDossier> dossiers = mongoTemplate.find(query, KTMETLDossier.class);
    //  Page<KTMETLDossier> page = mongoTemplate.find(query, KTMETLDossier.class, "collectionName");
    List<DossierEtlDetailDto> result = new ArrayList<>();

    for (int i = 0 ; i < dossiers.size() ; i++){
      DossierEtlDetailDto item = new DossierEtlDetailDto();
      List<DossierEtlDetailDto.DossierOutOfDateDTO>  dossierOutOfDateList = new ArrayList<>();
      List<DossierEtlDetailDto.AttachmentDTO> attachmentList = new ArrayList<>();

      try{
        item.setId(dossiers.get(i).getId());
      }catch (Exception e){}
      try{
        item.setDossierId(dossiers.get(i).getId());
      }catch (Exception e){}
//      try{
//        item.setDossierCountingId(new ArrayList<>());
//      }catch (Exception e){}
      try{
        item.setSectorName(dossiers.get(i).getSector().getName());
      }catch (Exception e){}
      try{
        item.setProcedureLevel(dossiers.get(i).getProcedureLevel().getName());
      }catch (Exception e){}
      try{
        item.setProcedureName(dossiers.get(i).getProcedure().getName());
      }catch (Exception e){}
      try{
        item.setDossierCode(dossiers.get(i).getCode());
      }catch (Exception e){}
      try{
        item.setDossierStatus(dossiers.get(i).getDossierStatus().getName());
      }catch (Exception e){}
      try{
        item.setDossierTaskStatus(dossiers.get(i).getDossierTaskStatus().getName());
      }catch (Exception e){}
      try{
        item.setPhone(dossiers.get(i).getApplicant().getPhoneNumber());
      }catch (Exception e){}
      try{
        item.setFullName(dossiers.get(i).getApplicant().getFullname());
      }catch (Exception e){}
      try{
        item.setOrganization(dossiers.get(i).getApplicant().getOrganization());
      }catch (Exception e){}
      try{
        item.setAcceptedDate(dossiers.get(i).getAcceptedDate());
      }catch (Exception e){}
      try{
        item.setAddress(dossiers.get(i).getApplicant().getAddress());
      }catch (Exception e){}
      try{
        item.setTotalImplementDate(dossiers.get(i).getProcessingTime());
      }catch (Exception e){}
      try{
        item.setAgency(dossiers.get(i).getAgency().getParent().getName());
      }catch (Exception e){}
      try{
        item.setAgencyId(dossiers.get(i).getAgency().getParent().getId());
      }catch (Exception e){}
      try{
        item.setOutOfDateCode(0);
      }catch (Exception e){}

      if(type == 4 || type == 2){
        for (int j = 0 ; j < dossiers.get(i).getTask().size() ; j++ ){
          DossierEtlDetailDto.DossierOutOfDateDTO dossierOutOfDateDTO = new DossierEtlDetailDto.DossierOutOfDateDTO();
          try{
            dossierOutOfDateDTO.setUserName(dossiers.get(i).getTask().get(j).getAssignee().getFullname());
          }catch (Exception e){}
          try{
            dossierOutOfDateDTO.setStep("");//
          }catch (Exception e){}
          try{
            dossierOutOfDateDTO.setAccountName("");//
          }catch (Exception e){}
          try{
            dossierOutOfDateDTO.setAgency(dossiers.get(i).getTask().get(j).getAgency().getName());
          }catch (Exception e){}
          try{
            dossierOutOfDateDTO.setCompletedDate(dossiers.get(i).getTask().get(j).getCompletedDate());
          }catch (Exception e){}
          try{
            dossierOutOfDateDTO.setDueDate(dossiers.get(i).getTask().get(j).getDueDate().toString());
          }catch (Exception e){}
          try{
            dossierOutOfDateList.add(dossierOutOfDateDTO);
          }catch (Exception e){}

        }
      }else{
        DossierEtlDetailDto.DossierOutOfDateDTO dossierOutOfDateDTO = new DossierEtlDetailDto.DossierOutOfDateDTO();
      }

      try{
        item.setAppointmentDate(dossiers.get(i).getAppointmentDate());
      }catch (Exception e){}
      try{
        item.setCompletedDate(dossiers.get(i).getCompletedDate());
      }catch (Exception e){}
      try{
        item.setReturnedDate(dossiers.get(i).getReturnedDate());
      }catch (Exception e){}
      try{
        item.setWithdrawDate(dossiers.get(i).getWithdrawDate());
      }catch (Exception e){}
      try{
        item.setCancelledDate(dossiers.get(i).getCancelledDate());
      }catch (Exception e){}
      try{
        item.setApplyMethod(dossiers.get(i).getApplyMethod().getName());
      }catch (Exception e){}
      try{
        item.setApplyMethodId(dossiers.get(i).getApplyMethod().getId());
      }catch (Exception e){}
      try{
        if(Integer.valueOf(type).equals(1)){
          item.setDossierDetailStatus(1);
        }
        if(Integer.valueOf(type).equals(2)){
          item.setDossierDetailStatus(2);
        }
        if(Integer.valueOf(type).equals(3)){
          item.setDossierDetailStatus(3);
        }
        if(Integer.valueOf(type).equals(4)){
          item.setDossierDetailStatus(4);
        }
        if(Integer.valueOf(type).equals(5)){
          item.setDossierDetailStatus(5);
        }

      }catch (Exception e){}
      try{
        item.setImplementer(dossiers.get(i).getCurrentTask().getAssignee().getFullname());//
      }catch (Exception e){}
      try{
        item.setCreatedDate(new Date());//
      }catch (Exception e){}
      try{
        item.setUpdatedDate(new Date());//
      }catch (Exception e){}
      try{
        for (int k = 0 ; k < dossiers.get(i).getAttachment().size() ; k++ ){
          DossierEtlDetailDto.AttachmentDTO attachmentDTO = new DossierEtlDetailDto.AttachmentDTO();
          try {
            attachmentDTO.setId(dossiers.get(i).getAttachment().get(k).getId());
          }catch (Exception e){};
          try {
            attachmentDTO.setFilename(dossiers.get(i).getAttachment().get(k).getFilename());
          }catch (Exception e){};
          try {
            attachmentDTO.setSize(0);//
          }catch (Exception e){};
          try {
            attachmentDTO.setUuid("");//
          }catch (Exception e){};
          try {
            attachmentDTO.setGroup("");//
          }catch (Exception e){};
          try {
            attachmentList.add(attachmentDTO);
          }catch (Exception e){};

        }
      }catch (Exception e){};
      try {
        item.setListDossierOutOfDate(dossierOutOfDateList);
      }catch (Exception e){};
      try {
        item.setAttachment(attachmentList);
      }catch (Exception e){};
      result.add(item);
    }

    return result;

  }

  public List<GetDigitizationDto> getDigitizationInfoV2(String fromDate, String toDate, String agencyTagId, String agencyId, String procedureId, Pageable pageable) throws JSONException {
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    LocalDateTime previousDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      previousDateConvert = fromDateConvert.minusDays(1);
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    Boolean isAllAgency = false;
    ObjectId agencyTagIdConvert = null;
    try{
      agencyTagIdConvert = new ObjectId(agencyTagId);
      isAllAgency = true;
    } catch (Exception e){}

    List<String> procedureIdList = new ArrayList<>();
    List<ObjectId> procedureIdListConvert = new ArrayList<>();
    try{
      procedureIdList = List.of(procedureId.split(","));
      for(int i = 0; i < procedureIdList.size(); i++){
        procedureIdListConvert.add(new ObjectId(procedureIdList.get(i)));
      }
    } catch (Exception e){}

    List<GetDigitizationDto> resultList = new ArrayList<>();
    List<AgencyIdName> agencyList = new ArrayList<>();
    ObjectId agencyIdConvert;
    if(isAllAgency){
      int size = 50;
      int page = 0;
      int agencyTotalPages = 0;
      do {
//        String getAgencyUrl = new StringBuilder("http://localhost:8888/agency/name+code?tag-id=").append(agencyTagId.toString()).append("&size="+size +"&page="+page).toString();
        String agencyURL = "agency/name+code?tag-id=" + agencyTagIdConvert + "&size=" + size + "&page=" + page;
        String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
        String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
        JSONObject agencyJsonObject = new JSONObject(agencyJson);
        JSONArray agencyContentData = agencyJsonObject.getJSONArray("content");
        agencyTotalPages = agencyJsonObject.getInt("totalPages");

        if (agencyContentData != null) {
          for (int i = 0; i < agencyContentData.length(); i++) {
            JSONObject tempAgencyContentData = agencyContentData.getJSONObject(i);
            String tempAgencyId = tempAgencyContentData.get("id").toString();
            ObjectId tempAgencyIdConvert = null;
            String agencyName = "";
            try {
              tempAgencyIdConvert = new ObjectId(tempAgencyId);
              agencyName = tempAgencyContentData.get("name").toString();
              AgencyIdName agency = new AgencyIdName(tempAgencyIdConvert, agencyName);
              agencyList.add(agency);
            } catch (Exception e) {
              throw new RuntimeException(e);
            }
          }
        }

        if (page <= agencyTotalPages - 1) {
          page++;
        }
      } while (page < agencyTotalPages);

    } else {
      try {
        agencyIdConvert = new ObjectId(agencyId);
        AgencyIdName agency = new AgencyIdName(agencyIdConvert, "");
        agencyList.add(agency);
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }

    if(agencyList != null){
      List emptyList = new ArrayList<>();
      MatchOperation matchOperation2 = Aggregation.match(
          Criteria.where("").orOperator(
              Criteria.where("directlyReceivingList").gt(emptyList),
              Criteria.where("onlineReceivingList").gt(emptyList)
          )
      );

      MatchOperation matchOperation3 = Aggregation.match(
          Criteria.where("").orOperator(
              Criteria.where("completedEarlyList").gt(emptyList),
              Criteria.where("completedLatelyList").gt(emptyList),
              Criteria.where("completedOntimeList").gt(emptyList)
          )
      );

      GroupOperation groupOperation1 = Aggregation.group()
          .push("tempDossier")
          .as("dossier");

      GroupOperation groupOperation2 = Aggregation.group()
          .sum("synthesisData.inProgress")
          .as("totalAmount");

      ProjectionOperation projection1 = Aggregation.project()
          .and(
              "directlyReceivingList"
          )
          .concatArrays(
              "onlineReceivingList"
          )
          .as("tempDossier");

      ProjectionOperation projection2 = Aggregation.project()
          .and(
              "completedEarlyList"
          )
          .concatArrays(
              "completedLatelyList", "completedOntimeList"
          )
          .as("tempDossier");

      for(int i = 0; i < agencyList.size(); i++){
        MatchOperation matchOperation1 = Aggregation.match(
            Criteria.where("").andOperator(
                Criteria.where("agency.id").is(agencyList.get(i).getId()),
                Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert)
            )
        );

        Aggregation aggregation = Aggregation.newAggregation(
            matchOperation1,
            Aggregation.facet(
                    matchOperation2,
                    projection1,
                    unwind("tempDossier"),
                    groupOperation1
                ).as("received")
                .and(
                    matchOperation3,
                    projection2,
                    unwind("tempDossier"),
                    groupOperation1
                ).as("completed")
        );

        AggregationResults<KTMDigitizationResultDTO> aggResults = mongoTemplate.aggregate(aggregation, "dossierSynthesis", KTMDigitizationResultDTO.class);
        List<KTMDigitizationResultDTO> mappedResults = aggResults.getMappedResults();

        if(mappedResults != null && mappedResults.size() > 0){
          KTMDigitizationResultDTO resultDTO = mappedResults.get(0);
          if(resultDTO != null){
            GetDigitizationDto result = new GetDigitizationDto();
            result.setAgencyId(isAllAgency ? agencyList.get(i).getId().toString() : agencyId);
            result.setAgencyName(isAllAgency ? agencyList.get(i).getName() : "");
            // received
            List<KTMDigitizationResultDTO.DossierInfo> receivedlist = resultDTO.getReceived();
            if(receivedlist != null && receivedlist.size() > 0){
              MatchOperation matchOperation4 = Aggregation.match(
                  Criteria.where("").andOperator(
                      Criteria.where("_id").in(receivedlist.get(0).getDossier()),
                      (procedureIdListConvert != null && procedureIdListConvert.size() > 0) ? Criteria.where("procedure._id").nin(procedureIdListConvert) : new Criteria()
                  )
              );

              Aggregation aggregation1 = Aggregation.newAggregation(
                  matchOperation4
              );

              AggregationResults<KTMETLDossier> aggResults1 = mongoTemplate.aggregate(aggregation1, "ktmETLDossier", KTMETLDossier.class);
              List<KTMETLDossier> mappedResults1 = aggResults1.getMappedResults();

              int digiReceivedApart = 0;
              int digiReceivedAll = 0;
              int notDigiReceived = 0;

              for(int j = 0; j < mappedResults1.size(); j++){
                var formFile = mappedResults1.get(j).getListDossierFormFile();

                if(formFile != null && formFile.size() > 0){
                  int size = formFile.size();
                  int digiFile = 0;
                  for(int k = 0; k < size; k++){
                    var file = formFile.get(k);
                    if(file != null && file.getFile() != null && file.getFile().size() > 0){
                      digiFile++;
                    }
                  }
                  if(digiFile == 0){
                    notDigiReceived++;
                  } else {
                    digiReceivedApart++;
                    if(digiFile == size){
                      digiReceivedAll++;
                    }
                  }
                } else {
                  notDigiReceived++;
                }
              }

              result.setTotalDossierInProgress(mappedResults1.size());
              result.setNumberDossierHavingAPartDigitization(digiReceivedApart);
              result.setNumberDossierHavingFullDigitization(digiReceivedAll);
              result.setNumberDossierDoesNotHavingDigitization(notDigiReceived);
            }

            // completed
            List<KTMDigitizationResultDTO.DossierInfo> completedlist = resultDTO.getCompleted();

            if(completedlist != null && completedlist.size() > 0){
              MatchOperation matchOperation5 = Aggregation.match(
                      Criteria.where("").andOperator(
                              Criteria.where("_id").in(completedlist.get(0).getDossier()),
                              (procedureIdListConvert != null && procedureIdListConvert.size() > 0) ? Criteria.where("procedure._id").nin(procedureIdListConvert) : new Criteria()
                      )
              );

              Aggregation aggregation2 = Aggregation.newAggregation(
                      matchOperation5
              );

              AggregationResults<KTMETLDossier> aggResults2 = mongoTemplate.aggregate(aggregation2, "ktmETLDossier", KTMETLDossier.class);
              List<KTMETLDossier> mappedResults2 = aggResults2.getMappedResults();

              int digiCompleted = 0;
              int notDigiCompleted = 0;

              for(int j = 0; j < mappedResults2.size(); j++){
                var attachment = mappedResults2.get(j).getAttachment();

                if(attachment != null && attachment.size() > 0){
                  int size = attachment.size();
                  int digiFile = 0;
                  for(int k = 0; k < size; k++){
                    if(attachment.get(k).getGroup() != null && attachment.get(k).getGroup().equals("5f9bd9692994dc687e68b5a6")){
                      digiFile++;
                    }
                  }

                  if(digiFile == 0){
                    notDigiCompleted++;
                  } else {
                    digiCompleted++;
                  }
                } else {
                  notDigiCompleted++;
                }
              }
              result.setTotalDossierCompleted(mappedResults2.size());
              result.setNumberDossierHavingDigitizationResult(digiCompleted);
              result.setNumberDossierDoesNotHavingDigitizationResult(notDigiCompleted);
            }

            // previous
            MatchOperation matchOperation6 = Aggregation.match(
                    Criteria.where("").andOperator(
                            Criteria.where("day").is(previousDateConvert.getDayOfMonth()),
                            Criteria.where("month").is(previousDateConvert.getMonthValue()),
                            Criteria.where("year").is(previousDateConvert.getYear()),
                            Criteria.where("agency.id").is(agencyList.get(i).getId())
                    )
            );

            Aggregation aggregation3 = Aggregation.newAggregation(
                    matchOperation6,
                    groupOperation2
            );
            AggregationResults<TotalAmountDataDTO> aggResults3 = mongoTemplate.aggregate(aggregation3, "dossierSynthesis", TotalAmountDataDTO.class);
            List<TotalAmountDataDTO> mappedResults3 = aggResults3.getMappedResults();
            long previous = (mappedResults3 != null && mappedResults3.size() > 0) ? mappedResults3.get(0).getTotalAmount() : 0;
            result.setPreviousPeriod(previous);
            resultList.add(result);
          }
        }
      }
    }
    return resultList;
  }

  public List<GetDigitizationDto> getDigitizationInfoV2_1(String fromDate, String toDate, String agencyTagId, String agencyId, String procedureId, Pageable pageable) throws JSONException {
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    LocalDateTime previousDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      previousDateConvert = fromDateConvert.minusDays(1);
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    Boolean isAllAgency = false;
    ObjectId agencyTagIdConvert = null;
    try{
      agencyTagIdConvert = new ObjectId(agencyTagId);
      isAllAgency = true;
    } catch (Exception e){}

    List<String> procedureIdList = new ArrayList<>();
    List<ObjectId> procedureIdListConvert = new ArrayList<>();
    try{
      procedureIdList = List.of(procedureId.split(","));
      for(int i = 0; i < procedureIdList.size(); i++){
        procedureIdListConvert.add(new ObjectId(procedureIdList.get(i)));
      }
    } catch (Exception e){}

    List<GetDigitizationDto> resultList = new ArrayList<>();
    List<AgencyIdName> agencyList = new ArrayList<>();
    ObjectId agencyIdConvert;
    if(isAllAgency){
      int size = 50;
      int page = 0;
      int agencyTotalPages = 0;
      do {
//        String getAgencyUrl = new StringBuilder("http://localhost:8888/agency/name+code?tag-id=").append(agencyTagId.toString()).append("&size="+size +"&page="+page).toString();
        String agencyURL = "agency/name+code?tag-id=" + agencyTagIdConvert + "&size=" + size + "&page=" + page;
        String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
        String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
        JSONObject agencyJsonObject = new JSONObject(agencyJson);
        JSONArray agencyContentData = agencyJsonObject.getJSONArray("content");
        agencyTotalPages = agencyJsonObject.getInt("totalPages");

        if (agencyContentData != null) {
          for (int i = 0; i < agencyContentData.length(); i++) {
            JSONObject tempAgencyContentData = agencyContentData.getJSONObject(i);
            String tempAgencyId = tempAgencyContentData.get("id").toString();
            ObjectId tempAgencyIdConvert = null;
            String agencyName = "";
            try {
              tempAgencyIdConvert = new ObjectId(tempAgencyId);
              agencyName = tempAgencyContentData.get("name").toString();
              AgencyIdName agency = new AgencyIdName(tempAgencyIdConvert, agencyName);
              agencyList.add(agency);
            } catch (Exception e) {
              throw new RuntimeException(e);
            }
          }
        }

        if (page <= agencyTotalPages - 1) {
          page++;
        }
      } while (page < agencyTotalPages);

    } else {
      try {
        agencyIdConvert = new ObjectId(agencyId);
        AgencyIdName agency = new AgencyIdName(agencyIdConvert, "");
        agencyList.add(agency);
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }

    if(agencyList != null){
      List emptyList = new ArrayList<>();
      MatchOperation matchOperation2 = Aggregation.match(
              Criteria.where("").orOperator(
                      Criteria.where("directlyReceivingList").gt(emptyList),
                      Criteria.where("onlineReceivingList").gt(emptyList)
              )
      );

      MatchOperation matchOperation3 = Aggregation.match(
              Criteria.where("").orOperator(
                      Criteria.where("completedEarlyList").gt(emptyList),
                      Criteria.where("completedLatelyList").gt(emptyList),
                      Criteria.where("completedOntimeList").gt(emptyList)
              )
      );

      GroupOperation groupOperation1 = Aggregation.group()
              .push("tempDossier")
              .as("dossier");

      GroupOperation groupOperation2 = Aggregation.group()
              .sum("synthesisData.inProgress")
              .as("totalAmount");

      ProjectionOperation projection1 = Aggregation.project()
              .and(
                      "directlyReceivingList"
              )
              .concatArrays(
                      "onlineReceivingList"
              )
              .as("tempDossier");

      ProjectionOperation projection2 = Aggregation.project()
              .and(
                      "completedEarlyList"
              )
              .concatArrays(
                      "completedLatelyList", "completedOntimeList"
              )
              .as("tempDossier");

      for(int i = 0; i < agencyList.size(); i++){
        MatchOperation matchOperation1 = Aggregation.match(
                Criteria.where("").andOperator(
                        Criteria.where("agency.id").is(agencyList.get(i).getId()),
                        Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert)
                )
        );

        Aggregation aggregation = Aggregation.newAggregation(
                matchOperation1,
                Aggregation.facet(
                                matchOperation2,
                                projection1,
                                unwind("tempDossier"),
                                groupOperation1
                        ).as("received")
                        .and(
                                matchOperation3,
                                projection2,
                                unwind("tempDossier"),
                                groupOperation1
                        ).as("completed")
        );

        AggregationResults<KTMDigitizationResultDTO> aggResults = mongoTemplate.aggregate(aggregation, "dossierSynthesis", KTMDigitizationResultDTO.class);
        List<KTMDigitizationResultDTO> mappedResults = aggResults.getMappedResults();

        if(mappedResults != null && mappedResults.size() > 0){
          KTMDigitizationResultDTO resultDTO = mappedResults.get(0);
          if(resultDTO != null){
            GetDigitizationDto result = new GetDigitizationDto();
            result.setAgencyId(isAllAgency ? agencyList.get(i).getId().toString() : agencyId);
            result.setAgencyName(isAllAgency ? agencyList.get(i).getName() : "");
            // received
            List<KTMDigitizationResultDTO.DossierInfo> receivedlist = resultDTO.getReceived();
            if(receivedlist != null && receivedlist.size() > 0){
              MatchOperation matchOperation4 = Aggregation.match(
                      Criteria.where("").andOperator(
                              Criteria.where("_id").in(receivedlist.get(0).getDossier()),
                              (procedureIdListConvert != null && procedureIdListConvert.size() > 0) ? Criteria.where("procedure._id").nin(procedureIdListConvert) : new Criteria()
                      )
              );

              Aggregation aggregation1 = Aggregation.newAggregation(
                      matchOperation4
              );

              AggregationResults<KTMETLDossier> aggResults1 = mongoTemplate.aggregate(aggregation1, "ktmETLDossier", KTMETLDossier.class);
              List<KTMETLDossier> mappedResults1 = aggResults1.getMappedResults();

              int digiReceivedApart = 0;
              int digiReceivedAll = 0;
              int notDigiReceived = 0;

              for(int j = 0; j < mappedResults1.size(); j++){
                var formFileOri = mappedResults1.get(j).getListDossierFormFile();
                var formFile = mappedResults1.get(j).getComponentsStorages();
                if(formFile != null && formFile.size() > 0){
                  int size = formFileOri.size();
                  int digiFile = 0;
                  for(int k = 0; k < formFile.size(); k++){
                    var file = formFile.get(k);
                    if(file != null && file.getCode() != null ){
                      digiFile++;
                    }
                  }
                  if(digiFile == 0){
                    notDigiReceived++;
                  } else {
                    digiReceivedApart++;
                    if(digiFile == size){
                      digiReceivedAll++;
                    }
                  }
                } else {
                  notDigiReceived++;
                }
              }

              result.setTotalDossierInProgress(mappedResults1.size());
              result.setNumberDossierHavingAPartDigitization(digiReceivedApart);
              result.setNumberDossierHavingFullDigitization(digiReceivedAll);
              result.setNumberDossierDoesNotHavingDigitization(notDigiReceived);
            }

            // completed
            List<KTMDigitizationResultDTO.DossierInfo> completedlist = resultDTO.getCompleted();

            if(completedlist != null && completedlist.size() > 0){
              MatchOperation matchOperation5 = Aggregation.match(
                  Criteria.where("").andOperator(
                      Criteria.where("_id").in(completedlist.get(0).getDossier()),
                      (procedureIdListConvert != null && procedureIdListConvert.size() > 0) ? Criteria.where("procedure._id").nin(procedureIdListConvert) : new Criteria()
                  )
              );

              Aggregation aggregation2 = Aggregation.newAggregation(
                  matchOperation5
              );

              AggregationResults<KTMETLDossier> aggResults2 = mongoTemplate.aggregate(aggregation2, "ktmETLDossier", KTMETLDossier.class);
              List<KTMETLDossier> mappedResults2 = aggResults2.getMappedResults();

              int digiCompleted = 0;
              int notDigiCompleted = 0;

              for(int j = 0; j < mappedResults2.size(); j++){
                //   var attachmentOri = mappedResults2.get(j).getAttachment();
                var attachment = mappedResults2.get(j).getAttachmentStorages();
                if(attachment != null && attachment.size() > 0){
                  int size = attachment.size();
                  int digiFile = 0;
                  for(int k = 0; k < size; k++){
                    if(attachment.get(k).getCode() != null ){
                      digiFile++;
                    }
                  }

                  if(digiFile == 0){
                    notDigiCompleted++;
                  } else {
                    digiCompleted++;
                  }
                } else {
                  notDigiCompleted++;
                }
              }
              result.setTotalDossierCompleted(mappedResults2.size());
              result.setNumberDossierHavingDigitizationResult(digiCompleted);
              result.setNumberDossierDoesNotHavingDigitizationResult(notDigiCompleted);
            }

            // previous
            MatchOperation matchOperation6 = Aggregation.match(
                    Criteria.where("").andOperator(
                            Criteria.where("day").is(previousDateConvert.getDayOfMonth()),
                            Criteria.where("month").is(previousDateConvert.getMonthValue()),
                            Criteria.where("year").is(previousDateConvert.getYear()),
                            Criteria.where("agency.id").is(agencyList.get(i).getId())
                    )
            );

            Aggregation aggregation3 = Aggregation.newAggregation(
                    matchOperation6,
                    groupOperation2
            );
            AggregationResults<TotalAmountDataDTO> aggResults3 = mongoTemplate.aggregate(aggregation3, "dossierSynthesis", TotalAmountDataDTO.class);
            List<TotalAmountDataDTO> mappedResults3 = aggResults3.getMappedResults();
            long previous = (mappedResults3 != null && mappedResults3.size() > 0) ? mappedResults3.get(0).getTotalAmount() : 0;
            result.setPreviousPeriod(previous);
            resultList.add(result);
          }
        }
      }
    }
    return resultList;
  }

  public Slice getDigitizationInfoV2Details(String fromDate, String toDate, String agencyTagId, String agencyId, String procedureId, int type, Pageable pageable) throws JSONException {
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    LocalDateTime previousDateConvert = null;
    try {
      fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
      previousDateConvert = fromDateConvert.minusDays(1);
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }
    List<KTMETLDossier> mappedResultsRecived = new ArrayList<>();             //type 1
    List<KTMETLDossier> mappedResultsDigiReceivedApart = new ArrayList<>(); //type 2
    List<KTMETLDossier> mappedResultsDigiReceivedAll = new ArrayList<>();   //type 3
    List<KTMETLDossier> mappedResultsNotDigiReceived = new ArrayList<>();   //type 4
    List<KTMETLDossier> mappedResultsCompleted = new ArrayList<>();             //type 5
    List<KTMETLDossier> mappedResultsPrevious = new ArrayList<>();   //type 6
    List<KTMETLDossier> mappedResultsDigiCompleted = new ArrayList<>();             //type 7
    List<KTMETLDossier> mappedResultsNoDigiCompleted = new ArrayList<>();   //type 8


    Boolean isAllAgency = false;
    ObjectId agencyTagIdConvert = null;
    try{
      agencyTagIdConvert = new ObjectId(agencyTagId);
      isAllAgency = true;
    } catch (Exception e){}

    List<String> procedureIdList = new ArrayList<>();
    List<ObjectId> procedureIdListConvert = new ArrayList<>();
    try{
      procedureIdList = List.of(procedureId.split(","));
      for(int i = 0; i < procedureIdList.size(); i++){
        procedureIdListConvert.add(new ObjectId(procedureIdList.get(i)));
      }
    } catch (Exception e){}

    List<GetDigitizationDto> resultList = new ArrayList<>();
    List<AgencyIdName> agencyList = new ArrayList<>();
    ObjectId agencyIdConvert;
    if(isAllAgency){
      int size = 50;
      int page = 0;
      int agencyTotalPages = 0;
      do {
//        String getAgencyUrl = new StringBuilder("http://localhost:8888/agency/name+code?tag-id=").append(agencyTagId.toString()).append("&size="+size +"&page="+page).toString();
        String agencyURL = "agency/name+code?tag-id=" + agencyTagIdConvert + "&size=" + size + "&page=" + page;
        String getAgencyUrl = microservice.basedataUri(agencyURL).toUriString();
        String agencyJson = MicroserviceExchange.get(restTemplate, getAgencyUrl, String.class);
        JSONObject agencyJsonObject = new JSONObject(agencyJson);
        JSONArray agencyContentData = agencyJsonObject.getJSONArray("content");
        agencyTotalPages = agencyJsonObject.getInt("totalPages");

        if (agencyContentData != null) {
          for (int i = 0; i < agencyContentData.length(); i++) {
            JSONObject tempAgencyContentData = agencyContentData.getJSONObject(i);
            String tempAgencyId = tempAgencyContentData.get("id").toString();
            ObjectId tempAgencyIdConvert = null;
            String agencyName = "";
            try {
              tempAgencyIdConvert = new ObjectId(tempAgencyId);
              agencyName = tempAgencyContentData.get("name").toString();
              AgencyIdName agency = new AgencyIdName(tempAgencyIdConvert, agencyName);
              agencyList.add(agency);
            } catch (Exception e) {
              throw new RuntimeException(e);
            }
          }
        }

        if (page <= agencyTotalPages - 1) {
          page++;
        }
      } while (page < agencyTotalPages);

    } else {
      try {
        agencyIdConvert = new ObjectId(agencyId);
        AgencyIdName agency = new AgencyIdName(agencyIdConvert, "");
        agencyList.add(agency);
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }

    if(agencyList != null){
      List emptyList = new ArrayList<>();
      MatchOperation matchOperation2 = Aggregation.match(
              Criteria.where("").orOperator(
                      Criteria.where("directlyReceivingList").gt(emptyList),
                      Criteria.where("onlineReceivingList").gt(emptyList)
              )
      );

      MatchOperation matchOperation3 = Aggregation.match(
              Criteria.where("").orOperator(
                      Criteria.where("completedEarlyList").gt(emptyList),
                      Criteria.where("completedLatelyList").gt(emptyList),
                      Criteria.where("completedOntimeList").gt(emptyList)
              )
      );

      GroupOperation groupOperation1 = Aggregation.group()
              .push("tempDossier")
              .as("dossier");

      GroupOperation groupOperation2 = Aggregation.group()
              .sum("synthesisData.inProgress")
              .as("totalAmount");

      ProjectionOperation projection1 = Aggregation.project()
              .and(
                      "directlyReceivingList"
              )
              .concatArrays(
                      "onlineReceivingList"
              )
              .as("tempDossier");

      ProjectionOperation projection2 = Aggregation.project()
              .and(
                      "completedEarlyList"
              )
              .concatArrays(
                      "completedLatelyList", "completedOntimeList"
              )
              .as("tempDossier");

      for(int i = 0; i < agencyList.size(); i++){
        MatchOperation matchOperation1 = Aggregation.match(
                Criteria.where("").andOperator(
                        Criteria.where("agency.id").is(agencyList.get(i).getId()),
                        Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert)
                )
        );

        Aggregation aggregation = Aggregation.newAggregation(
                matchOperation1,
                Aggregation.facet(
                                matchOperation2,
                                projection1,
                                unwind("tempDossier"),
                                groupOperation1
                        ).as("received")
                        .and(
                                matchOperation3,
                                projection2,
                                unwind("tempDossier"),
                                groupOperation1
                        ).as("completed")
        );

        AggregationResults<KTMDigitizationResultDTO> aggResults = mongoTemplate.aggregate(aggregation, "dossierSynthesis", KTMDigitizationResultDTO.class);
        List<KTMDigitizationResultDTO> mappedResults = aggResults.getMappedResults();

        if(mappedResults != null && mappedResults.size() > 0){
          KTMDigitizationResultDTO resultDTO = mappedResults.get(0);
          if(resultDTO != null){
            GetDigitizationDto result = new GetDigitizationDto();
            result.setAgencyId(isAllAgency ? agencyList.get(i).getId().toString() : agencyId);
            result.setAgencyName(isAllAgency ? agencyList.get(i).getName() : "");
            // received
            if(type == 1 || type == 2 ||type == 3 ||type == 4 ){
              List<KTMDigitizationResultDTO.DossierInfo> receivedlist = resultDTO.getReceived();
              if(receivedlist != null && receivedlist.size() > 0){
                MatchOperation matchOperation4 = Aggregation.match(
                        Criteria.where("").andOperator(
                                Criteria.where("_id").in(receivedlist.get(0).getDossier()),
                                (procedureIdListConvert != null && procedureIdListConvert.size() > 0) ? Criteria.where("procedure._id").nin(procedureIdListConvert) : new Criteria()
                        )
                );

                Aggregation aggregation1 = Aggregation.newAggregation(
                        matchOperation4
                );


                AggregationResults<KTMETLDossier> aggResults1 = mongoTemplate.aggregate(aggregation1, "ktmETLDossier", KTMETLDossier.class);
                List<KTMETLDossier> mappedResults1 = aggResults1.getMappedResults();

                int digiReceivedApart = 0;
                int digiReceivedAll = 0;
                int notDigiReceived = 0;


                for(int j = 0; j < mappedResults1.size(); j++){
                  var formFileOri = mappedResults1.get(j).getListDossierFormFile();
                  var formFile = mappedResults1.get(j).getComponentsStorages();
                  if(formFile != null && formFile.size() > 0){
                    int size = formFile.size();
                    int digiFile = 0;
                    for(int k = 0; k < formFile.size(); k++){
                      var file = formFile.get(k);
                      if(file != null && file.getCode() != null ){
                        digiFile++;
                      }
                    }
                    if(digiFile == 0){
                      notDigiReceived++;
                      mappedResultsNotDigiReceived.add(mappedResults1.get(j));
                    } else {
                      digiReceivedApart++;
                      mappedResultsDigiReceivedApart.add(mappedResults1.get(j));
                      if(digiFile == size){
                        digiReceivedAll++;
                        mappedResultsDigiReceivedAll.add(mappedResults1.get(j));
                      }
                    }
                  } else {
                    mappedResultsNotDigiReceived.add(mappedResults1.get(j));
                    notDigiReceived++;
                  }
                }

                if(type == 1){
                  return arrayToSlice(mappedResults1,pageable);
                }
                if(type == 2){
                  return arrayToSlice(mappedResultsDigiReceivedApart, pageable);
                }
                if(type == 3){
                  return arrayToSlice(mappedResultsDigiReceivedAll, pageable);
                }
                if(type == 4){
                  return arrayToSlice(mappedResultsNotDigiReceived, pageable);
                }
                mappedResultsRecived = mappedResults1;
                //  mappedResultsDigiReceivedAll = listDigiReceivedAll;
                result.setTotalDossierInProgress(mappedResults1.size());
                result.setNumberDossierHavingAPartDigitization(digiReceivedApart);
                result.setNumberDossierHavingFullDigitization(digiReceivedAll);
                result.setNumberDossierDoesNotHavingDigitization(notDigiReceived);
              }
            }
           else{
              List<KTMDigitizationResultDTO.DossierInfo> completedlist = resultDTO.getCompleted();

              if(completedlist != null && completedlist.size() > 0){
                MatchOperation matchOperation5 = Aggregation.match(
                        Criteria.where("").andOperator(
                                Criteria.where("_id").in(completedlist.get(0).getDossier()),
                                (procedureIdListConvert != null && procedureIdListConvert.size() > 0) ? Criteria.where("procedure._id").nin(procedureIdListConvert) : new Criteria()
                        )
                );

                Aggregation aggregation2 = Aggregation.newAggregation(
                        matchOperation5
                );

                AggregationResults<KTMETLDossier> aggResults2 = mongoTemplate.aggregate(aggregation2, "ktmETLDossier", KTMETLDossier.class);
                List<KTMETLDossier> mappedResults2 = aggResults2.getMappedResults();

                int digiCompleted = 0;
                int notDigiCompleted = 0;

                for(int j = 0; j < mappedResults2.size(); j++){
                  //var attachment = mappedResults2.get(j).getAttachment();
                       var attachment = mappedResults2.get(j).getAttachmentStorages();
                  if(attachment != null && attachment.size() > 0){
                    int size = attachment.size();
                    int digiFile = 0;
                    for(int k = 0; k < size; k++){
                      if(attachment.get(k).getCode() != null ){
                        //                    if(attachment.get(k).getGroup() != null ){
                        digiFile++;
                      }
                    }

                    if(digiFile == 0){
                      notDigiCompleted++;
                      mappedResultsNoDigiCompleted.add(mappedResults2.get(j));
                    } else {
                      digiCompleted++;
                      mappedResultsDigiCompleted.add(mappedResults2.get(j));
                    }
                  } else {
                    notDigiCompleted++;
                    mappedResultsNoDigiCompleted.add(mappedResults2.get(j));
                  }
                }
                if(type == 5){
                  return arrayToSlice(mappedResults2, pageable);
                }
                if(type == 7){
                  return arrayToSlice(mappedResultsDigiCompleted, pageable);
                }
                if(type == 8){
                  return arrayToSlice(mappedResultsNoDigiCompleted, pageable);
                }
                result.setTotalDossierCompleted(mappedResults2.size());
                result.setNumberDossierHavingDigitizationResult(digiCompleted);
                result.setNumberDossierDoesNotHavingDigitizationResult(notDigiCompleted);
              }
            }

            // completed


            // previous
            MatchOperation matchOperation6 = Aggregation.match(
                    Criteria.where("").andOperator(
                            Criteria.where("day").is(previousDateConvert.getDayOfMonth()),
                            Criteria.where("month").is(previousDateConvert.getMonthValue()),
                            Criteria.where("year").is(previousDateConvert.getYear()),
                            Criteria.where("agency.id").is(agencyList.get(i).getId())
                    )
            );

            Aggregation aggregation3 = Aggregation.newAggregation(
                    matchOperation6,
                    groupOperation2
            );
            AggregationResults<TotalAmountDataDTO> aggResults3 = mongoTemplate.aggregate(aggregation3, "dossierSynthesis", TotalAmountDataDTO.class);
            List<TotalAmountDataDTO> mappedResults3 = aggResults3.getMappedResults();
          //  mappedResultsPrevious =
            long previous = (mappedResults3 != null && mappedResults3.size() > 0) ? mappedResults3.get(0).getTotalAmount() : 0;
            result.setPreviousPeriod(previous);
            resultList.add(result);
          }
        }
      }
    }

    return arrayToSlice(mappedResultsDigiReceivedAll,pageable);
  }

  public Slice arrayToSlice(List<KTMETLDossier> result, Pageable pageable){
    int fromIndex = pageable.getPageNumber() * pageable.getPageSize();
    int toIndex = Math.min(fromIndex + pageable.getPageSize(), result.size());

    List<KTMETLDossier> limitedResult = result.subList(fromIndex, toIndex);

    Page<KTMETLDossier> page = new PageImpl<>(limitedResult, pageable, result.size());
    return page;
  }

  public ResponseEntity<Object> exportListDossierDetail(String strFromDate,
                            String strToDate,
                            int type,
                            List<String> agencyId,
                            List<String> sectorId
  ) throws JSONException, ParseException, IOException {
    //TYPE
    //1 :received
    //2 :inProgress
    //3 :inProgressAndOnTime
    //4 :inProgressAndOutOfDue
    //5 :completed
    //6 :completedOnTime
    //7 :completedEarly
    //8 :completedOutOfDue
    //9 :onlineReceived
    //10:directReceived
    //11:previousPeriod
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Date fromDate = df.parse(strFromDate);
    Date toDate = df.parse(strToDate);

    Integer year = null;
    Integer month = null;
    Integer day = null;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(fromDate);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    year = calendar.get(Calendar.YEAR) ;
    month = calendar.get(Calendar.MONTH) + 1;
    day = calendar.get(Calendar.DAY_OF_MONTH);
    // Lấy ngày tháng năm hôm trước
    Date previousDate = calendar.getTime();
    int toD = toDate.getDate();
    int toM= toDate.getMonth()+1;
    int toY = toDate.getYear()+1900;

    List<ObjectId> agencyIds = new ArrayList<>();
    for (String idString : agencyId) {
      agencyIds.add(new ObjectId(idString));
    }

    Criteria matchAgencyId;
    if (agencyIds.isEmpty()) {
      matchAgencyId = new Criteria();
    } else {
      matchAgencyId = Criteria.where("agency.id").in(agencyIds);
    }

    List<ObjectId> sectorIds = new ArrayList<>();
    if(!(sectorId == null || sectorId.isEmpty())){
      for (String idString : sectorId) {
        sectorIds.add(new ObjectId(idString));
      }
    }
    Criteria matchSectorId;
    if (sectorIds.isEmpty()) {
      matchSectorId = new Criteria();
    } else {
      matchSectorId = Criteria.where("sector.id").in(sectorIds);
    }

    Criteria matchDateRange = Criteria.where("synthesisDate")
            .gte(fromDate)
            .lte(toDate);

    AggregationOperation matchAgencies = Aggregation.match(matchAgencyId);
    AggregationOperation matchSectors = Aggregation.match(matchSectorId);
    AggregationOperation matchDate = Aggregation.match(matchDateRange);

    AggregationOperation group = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            //  .count().as("count")

            .push("completedEarlyList").as("completedEarlyList")
            .push("completedLatelyList").as("completedLatelyList")
            .push("completedOntimeList").as("completedOntimeList")
            .push("onlineReceivingList").as("onlineReceivingList")
            .push("directlyReceivingList").as("directlyReceivingList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation groupNoSector = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id")))
            //  .count().as("count")

            .push("completedEarlyList").as("completedEarlyList")
            .push("completedLatelyList").as("completedLatelyList")
            .push("completedOntimeList").as("completedOntimeList")
            .push("onlineReceivingList").as("onlineReceivingList")
            .push("directlyReceivingList").as("directlyReceivingList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation project = project()
            .and("_id").as("_id")
            .and("agency").as("agency")
            .and("sector").as("sector")
            .and(ArrayOperators.Reduce.arrayOf("completedEarlyList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalcompletedEarlyListSum")
            .and(ArrayOperators.Reduce.arrayOf("completedLatelyList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalcompletedLatelyListSum")
            .and(ArrayOperators.Reduce.arrayOf("completedOntimeList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalcompletedOntimeListSum")
            .and(ArrayOperators.Reduce.arrayOf("onlineReceivingList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalonlineReceivingListSum")
            .and(ArrayOperators.Reduce.arrayOf("directlyReceivingList")
                    .withInitialValue(Collections.emptyList()) // Sử dụng Collections.emptyList()
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totaldirectlyReceivingListSum");


    Aggregation aggregation1 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("fieldName").is("someValue"))
    );
    if(sectorId == null || sectorId.isEmpty()){
      aggregation1 =  Aggregation.newAggregation(matchAgencies,matchSectors, matchDate, groupNoSector, project).withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
    }else {
      aggregation1 =  Aggregation.newAggregation(matchAgencies,matchSectors, matchDate, group, project).withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
    }
    AggregationResults<DossierCoutingEtlDetailDto> result1 = mongoTemplate.aggregate(aggregation1, "dossierSynthesis", DossierCoutingEtlDetailDto.class);

    List<DossierCoutingEtlDetailDto> Results1 = result1.getMappedResults();


    AggregationOperation group2 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation groupNoSector2 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation project2 = project()
            .and("_id").as("_id")
            .and("agency").as("agency")
            .and("sector").as("sector")
            .and(ArrayOperators.Reduce.arrayOf("inprogressEarlyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressEarlyListSum")
            .and(ArrayOperators.Reduce.arrayOf("inprogressLatelyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressLatelyListSum");


    Aggregation aggregation2 =Aggregation.newAggregation(
            Aggregation.match(Criteria.where("fieldName").is("someValue"))
    );
    if(sectorId == null || sectorId.isEmpty()){
      aggregation2 =  Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
              groupNoSector2,
              project2
      );
    }else {
      aggregation2 =  Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
              group2,
              project2
      );
    }
    AggregationResults<DossierCoutingEtlDetailDto> result2 = mongoTemplate.aggregate(aggregation2, "dossierSynthesis", DossierCoutingEtlDetailDto.class);
    List<DossierCoutingEtlDetailDto> Results2 = result2.getMappedResults();

    if(sectorId == null || sectorId.isEmpty()){
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results2s : Results2) {
          if (Results1s.getAgency().getId().equals(Results2s.getAgency().getId())) {
            List<String> combinedList = new ArrayList<>();
            combinedList.addAll(Results2s.getTotalinprogressEarlyListSum());
            combinedList.addAll(Results2s.getTotalinprogressLatelyListSum());
            Results1s.setTotalpreviousPeriodListSum(combinedList);
            //
            break; //
          }
        }
      }
    }else {
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results2s : Results2) {
          if (Results1s.getAgency().getId().equals(Results2s.getAgency().getId()) &&
                  Results1s.getSector().getId().equals(Results2s.getSector().getId())) {

            List<String> combinedList = new ArrayList<>();
            combinedList.addAll(Results2s.getTotalinprogressEarlyListSum());
            combinedList.addAll(Results2s.getTotalinprogressLatelyListSum());
            Results1s.setTotalpreviousPeriodListSum(combinedList);

            //
            break; //
          }
        }
      }
    }

    AggregationOperation group3 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id"), Fields.field("sectorId", "$sector.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");

    AggregationOperation groupNoSector3 = Aggregation.group(
                    Fields.from(Fields.field("agencyId", "$agency.id")))
            //  .count().as("count")
            .push("inprogressEarlyList").as("inprogressEarlyList")
            .push("inprogressLatelyList").as("inprogressLatelyList")
            // .push("completedEarlyList").as("completedEarlyList")
            .first("agency").as("agency")
            .first("sector").as("sector");


    AggregationOperation project3 = project()
            .and("_id").as("_id")
            .and("agency").as("agency")
            .and("sector").as("sector")
            .and(ArrayOperators.Reduce.arrayOf("inprogressEarlyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressEarlyListSum")
            .and(ArrayOperators.Reduce.arrayOf("inprogressLatelyList")
                    .withInitialValue(Collections.emptyList()) //
                    .reduce(ArrayOperators.ConcatArrays.arrayOf("$$value")
                            .concat("$$this"))).as("totalinprogressLatelyListSum");

    Aggregation aggregation3 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("fieldName").is("someValue"))
    );

    if(sectorId == null || sectorId.isEmpty()){
      aggregation3 = Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
              groupNoSector3,
              project3
      );
    }else {
      aggregation3 = Aggregation.newAggregation(
              matchAgencies,
              Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
              group3,
              project3
      );
    }
    AggregationResults<DossierCoutingEtlDetailDto> result3 = mongoTemplate.aggregate(aggregation3, "dossierSynthesis", DossierCoutingEtlDetailDto.class);
    List<DossierCoutingEtlDetailDto> Results3 = result3.getMappedResults();

    if(sectorId == null || sectorId.isEmpty()){
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results3s : Results3) {
          if (Results1s.getAgency().getId().equals(Results3s.getAgency().getId())) {
            Results1s.setTotalinprogressEarlyListSum(Results3s.getTotalinprogressEarlyListSum());
            Results1s.setTotalinprogressLatelyListSum(Results3s.getTotalinprogressLatelyListSum());
            //
            break; //
          }
        }
      }
    }else {
      for (DossierCoutingEtlDetailDto Results1s : Results1) {
        for (DossierCoutingEtlDetailDto Results3s : Results3) {
          if (Results1s.getAgency().getId().equals(Results3s.getAgency().getId()) &&
                  Results1s.getSector().getId().equals(Results3s.getSector().getId())) {

            Results1s.setTotalinprogressEarlyListSum(Results3s.getTotalinprogressEarlyListSum());
            Results1s.setTotalinprogressLatelyListSum(Results3s.getTotalinprogressLatelyListSum());
            //
            break; //
          }
        }
      }
    }
    List<String> idDossierInProgressAndOnTime = new ArrayList<>();
    List<String> idDossierInProgressAndOutOfDue = new ArrayList<>();
    List<String> idDossierCompletedOnTime = new ArrayList<>();
    List<String> idDossierCompletedEarly = new ArrayList<>();
    List<String> idDossierCompletedOutOfDue = new ArrayList<>();


    List<DossierEtlDetailDto> result = new ArrayList<>();
    List<DossierEtlDetailDto> subResult = new ArrayList<>();

    for (int i = 0 ; i < Results1.size() ; i++){
      idDossierInProgressAndOnTime.addAll(Results1.get(i).getTotalinprogressEarlyListSum());//1
      idDossierInProgressAndOutOfDue.addAll(Results1.get(i).getTotalinprogressLatelyListSum());//2
      idDossierCompletedOnTime.addAll(Results1.get(i).getTotalcompletedOntimeListSum());//3
      idDossierCompletedEarly.addAll(Results1.get(i).getTotalcompletedEarlyListSum());//4
      idDossierCompletedOutOfDue.addAll(Results1.get(i).getTotalcompletedLatelyListSum());//5
    }
    if(type == 1){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
    }
    if(type == 2){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
    }
    if(type == 3){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
    }
    if(type == 4){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
    }
    if(type == 5){
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
    }
    if(type == 6){
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
    }
    if(type == 7){
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
    }
    if(type == 8){
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
    }
    if(type == 9){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
      for(int i = 0; i<result.size(); i++){
        if(Integer.valueOf(result.get(i).getApplyMethodId()).equals(0) && result.get(i).getAcceptedDate().after(fromDate)){
          subResult.add(result.get(i));
        }
      }
      result = subResult;
    }
    if(type == 10){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
      for(int i = 0; i<result.size(); i++){
        if(Integer.valueOf(result.get(i).getApplyMethodId()).equals(1) && result.get(i).getAcceptedDate().after(fromDate)){
          subResult.add(result.get(i));
        }
      }
      result = subResult;
    }
    if(type == 11){
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOnTime,1));
      result.addAll(getKTMETLDossiers(idDossierInProgressAndOutOfDue,2));
      result.addAll(getKTMETLDossiers(idDossierCompletedOnTime,5));
      result.addAll(getKTMETLDossiers(idDossierCompletedEarly,3));
      result.addAll(getKTMETLDossiers(idDossierCompletedOutOfDue,4));
      for(int i = 0; i<result.size(); i++){
        if(result.get(i).getAcceptedDate().before(fromDate)){
          subResult.add(result.get(i));
        }
      }
      result = subResult;
    }

    byte[] resource = new byte[0];
    DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
    String timestamp = dateFormat.format(new Date());
    String filename = "";
    filename = timestamp + "BC_CHI_TIET.xlsx";
//    List<DossierEtlDetailDto> data = getKTMETLDossiersNoPagination(idDossier, type, toDate);
    resource = getExportListDossierDetail(result, strFromDate, strToDate);

    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
        .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
        .body(resource);
  }
  public byte[] getExportListDossierDetail(List<DossierEtlDetailDto> data, String fromDate, String toDate) throws IOException, ParseException {
    try (InputStream is = resourceTemplateDossierListDetail.getInputStream()) {
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      org.jxls.common.Context context = new org.jxls.common.Context();
      DateFormat df2 = new SimpleDateFormat("dd/MM/yyyy");
      DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
      Date today = Calendar.getInstance().getTime();
      Date tDate = df.parse(toDate);

      Calendar cal = Calendar.getInstance();
      cal.setTime(df.parse(fromDate));
//      cal.add(Calendar.DATE, 1);
      Date fDate = cal.getTime();

      String nationalBrand=translator.toLocale("lang.word.ktm.national-brand");
      String subNational=translator.toLocale("lang.word.ktm.sub-national");
      String currentDate=translator.toLocale("lang.word.ktm.current-date",new String[]{df2.format(today)});
      String title=translator.toLocale("lang.word.ktm.title");
      String subTitle=translator.toLocale("lang.word.ktm.sub-title",new String[]{df2.format(fDate),df2.format(tDate)});
      String no=translator.toLocale("lang.word.ktm.no");
      String agency=translator.toLocale("lang.word.ktm.agency");
      String sector=translator.toLocale("lang.word.ktm.sector");
      String procedure=translator.toLocale("lang.word.ktm.procedure");
      String procedureLevel= "Mức độ";
      String dossierCode=translator.toLocale("lang.word.ktm.dossier-code");
      String status=translator.toLocale("lang.word.ktm.status");
      String address=translator.toLocale("lang.word.ktm.address");
      String phone=translator.toLocale("lang.word.ktm.phone");
      String owner=translator.toLocale("lang.word.ktm.owner");
      String submitter=translator.toLocale("lang.word.ktm.submitter");
      String acceptedDate=translator.toLocale("lang.word.ktm.accepted-date");
      String appointmentDate=translator.toLocale("lang.word.ktm.appointment-date");
      String duration=translator.toLocale("lang.word.ktm.duration");
      String completedDate=translator.toLocale("lang.word.ktm.completed-date");
      String returnedDate=translator.toLocale("lang.word.ktm.returned-Date");
      String dossierType=translator.toLocale("lang.word.ktm.dossier-type");
      String dossierStatus=translator.toLocale("lang.word.ktm.dossier-status");
      String implementer=translator.toLocale("lang.word.ktm.implementer");
      String note=translator.toLocale("lang.word.ktm.note");
      String lateAt=translator.toLocale("lang.word.ktm.late-at");
      String creator=translator.toLocale("lang.word.ktm.creator");
      String reporter=translator.toLocale("lang.word.ktm.reporter");
      String dossierTaskStatus = "Trạng thái công việc";
      String cancelledDate = "Ngày dừng";
      String withdrawDate = "Ngày rút";

      context.putVar("no", no);
      context.putVar("nationalBrand", nationalBrand);
      context.putVar("subNational", subNational);
      context.putVar("currentDate", currentDate);
      context.putVar("subTitle", subTitle);
      context.putVar("agency", agency);
      context.putVar("sector", sector);
      context.putVar("procedure", procedure);
      context.putVar("dossierCode", dossierCode);
      context.putVar("status", status);
      context.putVar("address", address);
      context.putVar("phone", phone);
      context.putVar("owner", owner);
      context.putVar("submitter", submitter);
      context.putVar("acceptedDate", acceptedDate);
      context.putVar("title", title);
      context.putVar("appointmentDate", appointmentDate);
      context.putVar("duration", duration);
      context.putVar("completedDate", completedDate);
      context.putVar("returnedDate", returnedDate);
      context.putVar("dossierType", dossierType);
      context.putVar("dossierStatus", dossierStatus);
      context.putVar("dossierType", dossierType);
      context.putVar("implementer", implementer);
      context.putVar("lateAt", lateAt);
      context.putVar("note", note);
      context.putVar("creator", creator);
      context.putVar("reporter", reporter);
      context.putVar("procedureLevel", procedureLevel);
      context.putVar("dossierTaskStatus", dossierTaskStatus);
      context.putVar("cancelledDate", cancelledDate);
      context.putVar("withdrawDate", withdrawDate);
      context.putVar("itemDtos", convertToExcelForm(data));
      XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
      JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
      return outputStream.toByteArray();
    }
  }

  private List<ExcelFormDossierCountingDetailDto> convertToExcelForm(List<DossierEtlDetailDto> listDossier) {
    AtomicLong i = new AtomicLong(1L);
    return listDossier.stream()
        .map(item -> item.convertToExcelForm(i.getAndIncrement(), item))
        .collect(Collectors.toList());
  }
  public DossierSumEtlDto getReportDossierByYearV2(
          String strYear,
          String strListTagId
  ){
    DossierSumEtlDto getDossierCountingDto = new  DossierSumEtlDto();
    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.YEAR, Integer.parseInt(strYear));
    calendar.set(Calendar.MONTH, Calendar.JANUARY);
    calendar.set(Calendar.DAY_OF_MONTH, 1);
    calendar.set(Calendar.HOUR, 0);
    calendar.set(Calendar.MINUTE,0);
    calendar.set(Calendar.SECOND,1);
    Date startDate = calendar.getTime(); // Ngày bắt đầu
    Date endDate = new Date();
    Calendar calendars = Calendar.getInstance();
    int currentYear = Calendar.getInstance().get(Calendar.YEAR);
    int test =  endDate.getYear();
    int years = Integer.parseInt(strYear);
    if (years < currentYear) {
      // Nếu strYear nhỏ hơn năm hiện tại
      calendars.set(Calendar.YEAR, years);
      calendars.set(Calendar.MONTH, Calendar.DECEMBER);
      calendars.set(Calendar.DAY_OF_MONTH, 31);
      calendars.set(Calendar.HOUR_OF_DAY, 23);
      calendars.set(Calendar.MINUTE, 59);
      calendars.set(Calendar.SECOND, 59);
    } else {
      // Nếu strYear không nhỏ hơn năm hiện tại
      calendars.setTime(Calendar.getInstance().getTime());
      calendars.add(Calendar.DAY_OF_MONTH, -1);
      calendars.set(Calendar.HOUR_OF_DAY, 23);
      calendars.set(Calendar.MINUTE, 59);
      calendars.set(Calendar.SECOND, 59);
    }

    // endDate là ngày kết thúc
    Calendar endDates = calendars;
    int toD = endDates.get(Calendar.DAY_OF_MONTH) ;
    int toM = endDates.get(Calendar.MONTH) + 1;
    int toY = endDates.get(Calendar.YEAR);


    Integer year = null;
    Integer month = null;
    Integer day = null;
    calendar.setTime(startDate);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    year = calendar.get(Calendar.YEAR) ;
    month = calendar.get(Calendar.MONTH) + 1;
    day = calendar.get(Calendar.DAY_OF_MONTH);
    // Lấy ngày tháng năm hôm trước
    Date previousDate = calendar.getTime();

    Criteria aCriteria = new Criteria();

    GroupOperation groupByAgencyAndSector = Aggregation.group()
            .sum("$synthesisData.received").as("received")
            .sum("$synthesisData.completed").as("completed")
            .sum("$synthesisData.completedOnTime").as("completedOnTime")
            .sum("$synthesisData.completedEarly").as("completedEarly")
            .sum("$synthesisData.completedLately").as("completedOutOfDue")
            .sum("$synthesisData.onlineReceived").as("onlineReceived")
            .sum("$synthesisData.directReceived").as("directReceived")
            .sum("$synthesisData.inProgress").as("inProgress")
            .sum("$synthesisData.inProgressEarly").as("inProgressAndOnTime")
            .sum("$synthesisData.inProgressLately").as("inProgressAndOutOfDue")
            .first("$agency").as("agency");
    ProjectionOperation projectCountingData = Aggregation.project()
            .and("received").as("countingData.received")
            .and("completed").as("countingData.completed")
            .and("completedOnTime").as("countingData.completedOnTime")
            .and("completedEarly").as("countingData.completedEarly")
            .and("completedOutOfDue").as("countingData.completedOutOfDue")
            .and("onlineReceived").as("countingData.onlineReceived")
            .and("directReceived").as("countingData.directReceived")
            .and("inProgress").as("countingData.inProgress")
            .and("inProgressAndOnTime").as("countingData.inProgressAndOnTime")
            .and("inProgressAndOutOfDue").as("countingData.inProgressAndOutOfDue")
            .and("$agency").as("agency");

    SortOperation sortByAgency = Aggregation.sort(Sort.Direction.ASC, "agency.name");

    Aggregation pipelineAggregation = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("synthesisDate").gte(startDate).lte(endDate)),
            groupByAgencyAndSector,
            projectCountingData,
            sortByAgency
    );

    AggregationResults<DossierSumEtlDto> resultFacet1 = mongoTemplate.aggregate(pipelineAggregation, "dossierSynthesis", DossierSumEtlDto.class);


    List<DossierSumEtlDto> facet1Results = resultFacet1.getMappedResults();



    Aggregation pipelineAggregation2 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
            groupByAgencyAndSector,
            projectCountingData
    );


    AggregationResults<DossierSumEtlDto> resultFacet2 = mongoTemplate.aggregate(pipelineAggregation2, "dossierSynthesis", DossierSumEtlDto.class);


    List<DossierSumEtlDto> facet2Results = resultFacet2.getMappedResults();

    Aggregation pipelineAggregation3 = Aggregation.newAggregation(
            Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
            groupByAgencyAndSector,
            projectCountingData
    );


    AggregationResults<DossierSumEtlDto> resultFacet3 = mongoTemplate.aggregate(pipelineAggregation3, "dossierSynthesis", DossierSumEtlDto.class);

    List<DossierSumEtlDto> facet3Results = resultFacet3.getMappedResults();

    try{
      facet1Results.get(0).setPreviousPeriod(facet2Results.get(0).getCountingData().getInProgress());
    }catch (Exception e){

    }
    try{
      facet1Results.get(0).getCountingData().setInProgress(facet3Results.get(0).getCountingData().getInProgress());
      facet1Results.get(0).getCountingData().setInProgressAndOnTime(facet3Results.get(0).getCountingData().getInProgressAndOnTime());
      facet1Results.get(0).getCountingData().setInProgressAndOutOfDue(facet3Results.get(0).getCountingData().getInProgressAndOutOfDue());
    }catch (Exception e){

    }

    getDossierCountingDto = facet1Results.get(0);
    return getDossierCountingDto;
  }

  public DossierSumEtlDto getReportDossierFromToV2(
          String strFromDate,
          String strToDate,
          String agencyTagId,
          String agencyId
  ) throws ParseException {
    DossierSumEtlDto dossierCoutingEtlDto = new DossierSumEtlDto();
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    Date fromDate = df.parse(strFromDate);
    Date toDate = df.parse(strToDate);

    Integer year = null;
    Integer month = null;
    Integer day = null;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(fromDate);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    year = calendar.get(Calendar.YEAR) ;
    month = calendar.get(Calendar.MONTH) + 1;
    day = calendar.get(Calendar.DAY_OF_MONTH);
    // Lấy ngày tháng năm hôm trước
    Date previousDate = calendar.getTime();
    Calendar calendars = Calendar.getInstance();
    calendars.setTime(toDate);
    calendars.add(Calendar.DAY_OF_MONTH, -1);
    int toY = calendars.get(Calendar.YEAR) ;
    int toM= calendars.get(Calendar.MONTH) + 1;
    int toD = calendars.get(Calendar.DAY_OF_MONTH);
    Criteria aCriteria = new Criteria();
    // AggregationOperation matchAgency = Aggregation.match(Criteria.where("agency.tagId").is(new ObjectId(agencyId));


    // AggregationOperation matchTagAgency = Aggregation.match(Criteria.where("agency.tagId").is(new ObjectId(agencyTagId)));

    GroupOperation groupByTagAgency = Aggregation.group()
//                    Fields.fields().and("agencyId", "$agency.id"))
            .sum("$synthesisData.received").as("received")
            .sum("$synthesisData.completed").as("completed")
            .sum("$synthesisData.completedOnTime").as("completedOnTime")
            .sum("$synthesisData.completedEarly").as("completedEarly")
            .sum("$synthesisData.completedLately").as("completedOutOfDue")
            .sum("$synthesisData.onlineReceived").as("onlineReceived")
            .sum("$synthesisData.directReceived").as("directReceived")
            .sum("$synthesisData.inProgress").as("inProgress")
            .sum("$synthesisData.inProgressEarly").as("inProgressAndOnTime")
            .sum("$synthesisData.inProgressLately").as("inProgressAndOutOfDue")
            .first("$agency").as("agency");

    GroupOperation groupByAgency = Aggregation.group(
                    Fields.fields().and("agencyId", "$agency.id"))
            .sum("$synthesisData.received").as("received")
            .sum("$synthesisData.completed").as("completed")
            .sum("$synthesisData.completedOnTime").as("completedOnTime")
            .sum("$synthesisData.completedEarly").as("completedEarly")
            .sum("$synthesisData.completedLately").as("completedOutOfDue")
            .sum("$synthesisData.onlineReceived").as("onlineReceived")
            .sum("$synthesisData.directReceived").as("directReceived")
            .sum("$synthesisData.inProgress").as("inProgress")
            .sum("$synthesisData.inProgressEarly").as("inProgressAndOnTime")
            .sum("$synthesisData.inProgressLately").as("inProgressAndOutOfDue")
            .first("$agency").as("agency");
    ProjectionOperation projectCountingData = Aggregation.project()
            .and("received").as("countingData.received")
            .and("completed").as("countingData.completed")
            .and("completedOnTime").as("countingData.completedOnTime")
            .and("completedEarly").as("countingData.completedEarly")
            .and("completedOutOfDue").as("countingData.completedOutOfDue")
            .and("onlineReceived").as("countingData.onlineReceived")
            .and("directReceived").as("countingData.directReceived")
            .and("inProgress").as("countingData.inProgress")
            .and("inProgressAndOnTime").as("countingData.inProgressAndOnTime")
            .and("inProgressAndOutOfDue").as("countingData.inProgressAndOutOfDue")
            .and("$agency").as("agency");

    SortOperation sortByAgency = Aggregation.sort(Sort.Direction.ASC, "agency.name");

    Aggregation pipelineAggregation = null;
    if(agencyTagId != null ){
      pipelineAggregation= Aggregation.newAggregation(
              Aggregation.match(Criteria.where("agency.tagId").is(new ObjectId(agencyTagId))),
              Aggregation.match(Criteria.where("synthesisDate").gte(fromDate).lte(toDate)),
              groupByTagAgency,
              projectCountingData,
              sortByAgency
      );
    }else{
      pipelineAggregation= Aggregation.newAggregation(
              Aggregation.match(Criteria.where("agency.id").is(new ObjectId(agencyId))),
              Aggregation.match(Criteria.where("synthesisDate").gte(fromDate).lte(toDate)),
              groupByAgency,
              projectCountingData,
              sortByAgency
      );
    }

    AggregationResults<DossierSumEtlDto> resultFacet1 = mongoTemplate.aggregate(pipelineAggregation, "dossierSynthesis", DossierSumEtlDto.class);


    List<DossierSumEtlDto> facet1Results = resultFacet1.getMappedResults();


    Aggregation pipelineAggregation2 = null;
    if(agencyTagId != null ){
      pipelineAggregation2 = Aggregation.newAggregation(
              Aggregation.match(Criteria.where("agency.tagId").is(new ObjectId(agencyTagId))),
              Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
              groupByTagAgency,
              projectCountingData
      );
    }else{
      pipelineAggregation2 = Aggregation.newAggregation(
              Aggregation.match(Criteria.where("agency.id").is(new ObjectId(agencyId))),
              Aggregation.match(Criteria.where("day").is(day).and("month").is(month).and("year").is(year)),
              groupByAgency,
              projectCountingData
      );
    }


    AggregationResults<DossierSumEtlDto> resultFacet2 = mongoTemplate.aggregate(pipelineAggregation2, "dossierSynthesis", DossierSumEtlDto.class);


    List<DossierSumEtlDto> facet2Results = resultFacet2.getMappedResults();


    Aggregation pipelineAggregation3 = null;
    if(agencyTagId != null ){
      pipelineAggregation3 = Aggregation.newAggregation(
              Aggregation.match(Criteria.where("agency.tagId").is(new ObjectId(agencyTagId))),
              Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
              groupByTagAgency,
              projectCountingData
      );
    }else{
      pipelineAggregation3 = Aggregation.newAggregation(
              Aggregation.match(Criteria.where("agency.id").is(new ObjectId(agencyId))),
              Aggregation.match(Criteria.where("day").is(toD).and("month").is(toM).and("year").is(toY)),
              groupByAgency,
              projectCountingData
      );
    }


    AggregationResults<DossierSumEtlDto> resultFacet3 = mongoTemplate.aggregate(pipelineAggregation3, "dossierSynthesis", DossierSumEtlDto.class);

    List<DossierSumEtlDto> facet3Results = resultFacet3.getMappedResults();

    try{
      facet1Results.get(0).setPreviousPeriod(facet2Results.get(0).getCountingData().getInProgress());
    }catch (Exception e){

    }
    try{
      facet1Results.get(0).getCountingData().setInProgress(facet3Results.get(0).getCountingData().getInProgress());
      facet1Results.get(0).getCountingData().setInProgressAndOnTime(facet3Results.get(0).getCountingData().getInProgressAndOnTime());
      facet1Results.get(0).getCountingData().setInProgressAndOutOfDue(facet3Results.get(0).getCountingData().getInProgressAndOutOfDue());
    }catch (Exception e){

    }
    dossierCoutingEtlDto = facet1Results.get(0);
    return dossierCoutingEtlDto;
  }

  public long deleteETLDossierByRange(String strFromDate, String strToDate){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
      fromDateConvert = !isNullOrEmpty(strFromDate) ? df.parse(strFromDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(strToDate) ? df.parse(strToDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    AffectedRowsDto result = new AffectedRowsDto();
    Query query = new Query(
            Criteria.where("acceptedDate").gte(fromDateConvert).lte(toDateConvert)
    );
    DeleteResult deleteResult = mongoTemplate.remove(query, "ktmETLDossier");
    return deleteResult.getDeletedCount();
  }

  public long deleteSynthesisByRange(String strFromDate, String strToDate){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
      fromDateConvert = !isNullOrEmpty(strFromDate) ? df.parse(strFromDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(strToDate) ? df.parse(strToDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    AffectedRowsDto result = new AffectedRowsDto();
    Query query = new Query(
            Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert)
    );
    DeleteResult deleteResult = mongoTemplate.remove(query, "dossierSynthesis");
    return deleteResult.getDeletedCount();
  }

  public long deleteETLDossierByRangeAndAgency(String strFromDate, String strToDate, String agency ){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
      fromDateConvert = !isNullOrEmpty(strFromDate) ? df.parse(strFromDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(strToDate) ? df.parse(strToDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    AffectedRowsDto result = new AffectedRowsDto();
    Query query = new Query(
            Criteria.where("acceptedDate").gte(fromDateConvert).lte(toDateConvert)
                    .and("agency._id").is(new ObjectId(agency))
    );
    DeleteResult deleteResult = mongoTemplate.remove(query, "ktmETLDossier");
    return deleteResult.getDeletedCount();
  }

  public long deleteSynthesisByRangeAndAgency(String strFromDate, String strToDate , String agency){
    LocalDateTime fromDateConvert = null;
    LocalDateTime toDateConvert = null;
    try {
      TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
      fromDateConvert = !isNullOrEmpty(strFromDate) ? df.parse(strFromDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
      toDateConvert = !isNullOrEmpty(strToDate) ? df.parse(strToDate).toInstant().atZone(timezone.toZoneId()).toLocalDateTime() : null;
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    AffectedRowsDto result = new AffectedRowsDto();
    Query query = new Query(
            Criteria.where("synthesisDate").gte(fromDateConvert).lte(toDateConvert)
                    .and("agency.id").is(new ObjectId(agency))
    );
    DeleteResult deleteResult = mongoTemplate.remove(query, "dossierSynthesis");
    return deleteResult.getDeletedCount();
  }
}
