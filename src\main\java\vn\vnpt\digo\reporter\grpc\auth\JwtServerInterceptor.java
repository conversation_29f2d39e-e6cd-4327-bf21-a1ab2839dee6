package vn.vnpt.digo.reporter.grpc.auth;

import io.grpc.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class JwtServerInterceptor implements ServerInterceptor {

    private final JwtDecoder jwtDecoder;

    @Autowired
    public JwtServerInterceptor(JwtDecoder jwtDecoder) {
        this.jwtDecoder = jwtDecoder;
    }

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call,
            Metadata headers,
            ServerCallHandler<ReqT, RespT> next
    ) {
        String authorizationHeader = headers.get(Metadata.Key.of("Authorization", Metadata.ASCII_STRING_MARSHALLER));

        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            String token = authorizationHeader.substring(7);

            try {
                Jwt jwt = jwtDecoder.decode(token);
                JwtGrantedAuthoritiesConverter authoritiesConverter = new JwtGrantedAuthoritiesConverter();
                authoritiesConverter.setAuthorityPrefix("ROLE_");
                authoritiesConverter.setAuthoritiesClaimName("roles");

                JwtAuthenticationToken authenticationToken = new JwtAuthenticationToken(jwt, authoritiesConverter.convert(jwt));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            } catch (JwtException e) {
                // Handle JWT validation error
                throw new StatusRuntimeException(Status.UNAUTHENTICATED.withDescription("Invalid JWT token"));
            }
        } else {
            // Handle missing Authorization header
            throw new StatusRuntimeException(Status.UNAUTHENTICATED.withDescription("Authorization header is missing or invalid"));
        }

        return next.startCall(call, headers);
    }

}