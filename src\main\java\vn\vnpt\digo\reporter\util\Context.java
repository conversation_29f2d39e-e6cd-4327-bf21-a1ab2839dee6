/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.lang.reflect.Type;
import java.util.*;
import javax.servlet.http.HttpServletRequest;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.jwt.Jwt;
import org.springframework.security.jwt.JwtHelper;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import vn.vnpt.digo.reporter.pojo.Permissions;

/**
 *
 * <AUTHOR>
 */
public class Context {

    private static Logger logger = LoggerFactory.getLogger(Context.class);

    private static Gson gson = new Gson();

    public static SecurityContext getSecurityContext() {
        return SecurityContextHolder.getContext();
    }

    public static JwtAuthenticationToken getJwtAuthenticationToken() {
        return (JwtAuthenticationToken) getSecurityContext().getAuthentication();
    }

    public static WebAuthenticationDetails getWebAuthenticationDetails() {
        return (WebAuthenticationDetails) getJwtAuthenticationToken().getDetails();
    }

    @Deprecated
    public static String getOAuth2AccessTokenValue() {
        return getJwtAuthenticationTokenValue();
    }

    public static String getJwtAuthenticationTokenValue() {
        return getJwtAuthenticationToken().getToken().getTokenValue();
    }

    public static ServletRequestAttributes getServletRequestAttributes() {
        return (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
    }

    public static HttpServletRequest getHttpServletRequest() {
        return getServletRequestAttributes().getRequest();
    }

    public static String getRootURL() {
        String result = getHttpServletRequest().getScheme() + "://" + getHttpServletRequest().getServerName();
        if (getHttpServletRequest().getServerPort() != 80) {
            result += ":" + getHttpServletRequest().getServerPort();
        }
        return result;
    }

    public static ObjectId getDeploymentId() {
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        try{
            logger.info("DIGO-Info: deployment_id = " + tokenMap.get("deployment_id").toString());
            return new ObjectId(tokenMap.get("deployment_id").toString());
        }
        catch(NullPointerException nullex){
            logger.info("DIGO-Info: deployment_id = null");
            return null;
        }
    }

    public static String getJwtParameterValue(String parameter) {
        String value;
        // Get username from JWT token
        try {
            Jwt jwtToken = JwtHelper.decode(getJwtAuthenticationTokenValue());
            String claims = jwtToken.getClaims();
            HashMap claimsMap = new ObjectMapper().readValue(claims, HashMap.class);
            value = String.valueOf(claimsMap.get(parameter));
        } catch (JsonProcessingException e) {
            value = null;
        }
        return value;
    }

    public static vn.vnpt.digo.reporter.pojo.Permission getPermission(String permission){
        Permissions ret = new Permissions();
        try{
            Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
            String s = tokenMap.get("permissions").toString();
            ret.setPermissions(gson.fromJson(s,  vn.vnpt.digo.reporter.pojo.Permission[].class));
        } catch (Exception e){
            logger.info(e.getMessage());
            //Do nothing
        }
        return ret.getPermission(permission);
    }

    public static ObjectId getTokenObjectIdAttribute(String key) {
        String attr = getTokenAttribute(key);
        if (attr != null) {
            return new ObjectId(attr);
        }
        return null;
    }

    public static <T extends Object> T getTokenAttribute(String key) {
        try {
            if(Context.getJwtAuthenticationTokenValue()!= null){
                Jwt jwtToken = JwtHelper.decode(Context.getJwtAuthenticationTokenValue());
                String claims = jwtToken.getClaims();
                HashMap claimsMap;
                claimsMap = new ObjectMapper().readValue(claims, HashMap.class);
                return (T) claimsMap.get(key);
            }
        } catch (JsonProcessingException ex) {
            System.out.println("getToken attribute error!" + ex.getMessage());
        }
        return null;
    }
    public static List<String> formatListCodePermission(){
        List<String> userPermissions = new ArrayList<>();
        try{
            String permissionStr = Context.getJwtParameterValue("permissions");
            if (permissionStr == null || permissionStr.isBlank()) {
                userPermissions = null;
            }
            Type listType = new TypeToken<List<Permission>>() {
            }.getType();
            List<Permission> permissions = new Gson().fromJson(permissionStr, listType);
            for (var item : CollectionUtils.emptyIfNull(permissions)) {
                userPermissions.add(item.getPermission().getCode());
            }
        } catch (Exception e){
            logger.info(e.getMessage());
            //Do nothing
        }
        return userPermissions;
    }
    public static Boolean getListPemission(List<String> permissionsToCheck){
        Boolean check = false;
        try{
            List<String> listCodePermission = formatListCodePermission();
            if(listCodePermission!=null)
            {
            for (String permisssion : permissionsToCheck) {
                if (listCodePermission.contains(permisssion)) {
                    check = true;
                    break;
                }
            }
            }
        } catch (Exception e){
            logger.info(e.getMessage());
        }
        return check;
    }

    public static String getUserGroupString() {
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        List<String> ret = new ArrayList<>();
        try {
            logger.info("DIGO-Info: groups = " + tokenMap.get("groups").toString());
            ret = (List<String>) tokenMap.get("groups");
        } catch (NullPointerException e) {
            logger.info("DIGO-Info: groups = null");
        }
        return String.join(",", ret);
    }
    
    public static Map<String, Boolean> hasPermissions(List<String> perms){
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        String s = tokenMap.get("permissions") != null ? tokenMap.get("permissions").toString() : null;
        Permissions ret = new Permissions();
        if(s != null) {
            ret.setPermissions(gson.fromJson(s, vn.vnpt.digo.reporter.pojo.Permission[].class));
        }
        Map<String, Boolean> result = new HashMap<>();
        for(var perm: perms){
            if(Objects.nonNull(ret.getPermission(perm))){
                result.put(perm, true);
            }else{
                result.put(perm, false);
            }
        }
        return result;
    }
    
}
