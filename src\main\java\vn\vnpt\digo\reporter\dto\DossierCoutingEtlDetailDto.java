package vn.vnpt.digo.reporter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierCoutingEtlDetailDto {

    private List<String> totalcompletedEarlyListSum;
    private List<String> totalcompletedLatelyListSum;
    private List<String> totalcompletedOntimeListSum;
    private List<String> totalinprogressEarlyListSum;
    private List<String> totalinprogressLatelyListSum;
    private List<String> totalonlineReceivingListSum;
    private List<String> totaldirectlyReceivingListSum;
    private List<String> totalpreviousPeriodListSum;
    //    private long sumInProgressAndOnTime;
//    private long sumInProgressAndOutOfDue;
    private DossierAgencyDto agency;
    private DossierSectorDto sector;

}




