package vn.vnpt.digo.reporter.service;

import com.google.gson.Gson;
import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.SampleDataReportQni;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.qni.SampleDataReportDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.util.DigoRestTemplate;
import vn.vnpt.digo.reporter.util.Microservice;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SampleDataReportQNIService {
    Logger logger = org.slf4j.LoggerFactory.getLogger(SampleDataReportQNIService.class);
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Microservice microservice;
    @Autowired
    private DigoRestTemplate digoRestTemplate;

    @Value("${digo.qni.agency-level.department}")
    private String qniAgencyLevelDepartment;
    @Value("${digo.qni.agency-level.district}")
    private String qniAgencyLevelDistrict;
    @Value("${digo.qni.agency-level.commune}")
    private String qniAgencyLevelCommune;
    @Value("${digo.qni.agency-level.root}")
    private String qniAgencyLevelRoot;

    private final Gson gson = new Gson();

    public Map<String, String> getAgencyLevelId() {
        return Map.of(
                "digo.qni.agency-level.root", this.qniAgencyLevelRoot,
                "digo.qni.agency-level.department", this.qniAgencyLevelDepartment,
                "digo.qni.agency-level.district", this.qniAgencyLevelDistrict,
                "digo.qni.agency-level.commune", this.qniAgencyLevelCommune
        );
    }

    public AffectedRowsDto addSampleData(List<SampleDataReportDto> listSampleData, String agencyLevelId) {
        AffectedRowsDto result = new AffectedRowsDto();
        List<SampleDataReportQni> listResult = new ArrayList<>();
        for (SampleDataReportDto sampleData : listSampleData  ) {
            SampleDataReportQni  sampleDataReportQni = new SampleDataReportQni();
            sampleDataReportQni.setAgencyLevel(this.getAgencyLevel(agencyLevelId));
            sampleDataReportQni.setSampleData(sampleData);
            listResult.add(sampleDataReportQni);
        }
        mongoTemplate.insertAll(listResult);
        result.setAffectedRows(listResult.size());
        return result;
    }

    private SampleDataReportQni.AgencyLevel getAgencyLevel(String agencyLevelId) {
        SampleDataReportQni.AgencyLevel agencyLevel = new SampleDataReportQni.AgencyLevel();
        agencyLevel.setId(agencyLevelId);
        if (Objects.equals(agencyLevelId, this.qniAgencyLevelDepartment)) {
            agencyLevel.setName("Cấp sở");
        }
        if (Objects.equals(agencyLevelId, this.qniAgencyLevelDistrict)) {
            agencyLevel.setName("Cấp Huyện");
        }
        if (Objects.equals(agencyLevelId, this.qniAgencyLevelCommune)) {
            agencyLevel.setName("Cấp xã");
        }
        return agencyLevel;
    }

    public long deleteSampleData(String agencyLevelIds) {
        List<ObjectId> agencyLevelIdList = Arrays.stream(agencyLevelIds.split(","))
                .map(ObjectId::new)
                .collect(Collectors.toList());
        Query query = Query.query(
                Criteria.where("agencyLevel._id").in(agencyLevelIdList)
        );
        DeleteResult deleteResult = mongoTemplate.remove(query, SampleDataReportQni.class);
        return deleteResult.getDeletedCount();
    }

    public SampleDataReportDto getSampleDataReport(String agencyId){
        if (agencyId.equals(this.qniAgencyLevelRoot)){
            return this.getRootSampleDataReport();
        }
        return this.getAgencySampleDataReport(agencyId);
    }

    private SampleDataReportDto getAgencySampleDataReport(String agencyId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("sampleData.agency._id").is(new ObjectId(agencyId)));
        SampleDataReportQni sampleDataReportQni = mongoTemplate.findOne(query, SampleDataReportQni.class);
        if (sampleDataReportQni != null) {
            return sampleDataReportQni.getSampleData();
        }
        throw new DigoHttpException(10004, new String[]{}, HttpServletResponse.SC_NOT_FOUND);
    }

    private SampleDataReportDto getRootSampleDataReport() {
        Query query = new Query();
        query.addCriteria(Criteria.where("agencyLevel._id").is(new ObjectId(this.qniAgencyLevelDepartment)));
        List<SampleDataReportQni> listSampleDataReportQni = mongoTemplate.find(query, SampleDataReportQni.class);

        int direct = 0, procedureUsed = 0, receivedOld = 0, received = 0, receivedDirect = 0, receivedOnline = 0,
                receivedPostal = 0, receivedSmartphone = 0, receivedPublicPostal = 0, resolvedEarly = 0, resolved = 0,
                unresolved = 0, withdraw = 0, resolvedOnTime = 0, unresolvedOnTime = 0, resolvedOverdue = 0, unresolvedOverdue = 0;

        for (SampleDataReportQni reportQni : listSampleDataReportQni) {
            SampleDataReportDto sampleData = reportQni.getSampleData();
            if (sampleData != null) {
                direct += sampleData.getDirect();
                procedureUsed += sampleData.getProcedureUsed();
                receivedOld += sampleData.getReceivedOld();
                received += sampleData.getReceived();
                receivedDirect += sampleData.getReceivedDirect();
                receivedOnline += sampleData.getReceivedOnline();
                receivedPostal += sampleData.getReceivedPostal();
                receivedSmartphone += sampleData.getReceivedSmartphone();
                receivedPublicPostal += sampleData.getReceivedPublicPostal();
                resolvedEarly += sampleData.getResolvedEarly();
                resolved += sampleData.getResolved();
                unresolved += sampleData.getUnresolved();
                withdraw += sampleData.getWithdraw();
                resolvedOnTime += sampleData.getResolvedOnTime();
                unresolvedOnTime += sampleData.getUnresolvedOnTime();
                resolvedOverdue += sampleData.getResolvedOverdue();
                unresolvedOverdue += sampleData.getUnresolvedOverdue();
            }
        }

        SampleDataReportDto.Agency rootAgency = new SampleDataReportDto.Agency(this.qniAgencyLevelRoot, "UB tỉnh Quảng Ngãi");

        SampleDataReportDto rootAgencySampleData = new SampleDataReportDto();
        rootAgencySampleData.setDirect(direct);
        rootAgencySampleData.setProcedureUsed(procedureUsed);
        rootAgencySampleData.setReceivedOld(receivedOld);
        rootAgencySampleData.setReceived(received);
        rootAgencySampleData.setReceivedDirect(receivedDirect);
        rootAgencySampleData.setReceivedOnline(receivedOnline);
        rootAgencySampleData.setReceivedPostal(receivedPostal);
        rootAgencySampleData.setReceivedSmartphone(receivedSmartphone);
        rootAgencySampleData.setReceivedPublicPostal(receivedPublicPostal);
        rootAgencySampleData.setResolvedEarly(resolvedEarly);
        rootAgencySampleData.setResolved(resolved);
        rootAgencySampleData.setUnresolved(unresolved);
        rootAgencySampleData.setWithdraw(withdraw);
        rootAgencySampleData.setResolvedOnTime(resolvedOnTime);
        rootAgencySampleData.setUnresolvedOnTime(unresolvedOnTime);
        rootAgencySampleData.setResolvedOverdue(resolvedOverdue);
        rootAgencySampleData.setUnresolvedOverdue(unresolvedOverdue);
        rootAgencySampleData.setAgency(rootAgency);

        return rootAgencySampleData;
    }
}
