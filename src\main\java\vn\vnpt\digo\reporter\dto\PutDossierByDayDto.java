package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PutDossierByDayDto implements Serializable {
    
    private Integer year;
    
    private Integer month;
    
    private Integer day;
    
    private ArrayList<String> field;
    
    private Integer number;
    
    private AgencyDossierByDay agency;
    
    private SectorDossierByDay sector;
    
    private ProcedureDossierByDay procedure;
    
    private AgencyDossierByDay agencyLevel;
    
    private ProcedureLevelDossierByDay procedureLevel;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;
}
