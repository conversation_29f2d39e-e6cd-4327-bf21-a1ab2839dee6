package vn.vnpt.digo.reporter.api;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.dto.AgencyNameDto;
import vn.vnpt.digo.reporter.dto.GetTypeAgencyDto;
import vn.vnpt.digo.reporter.dto.GetDossierByAgencyDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.ProcedureQuantityBySectorDto;
import vn.vnpt.digo.reporter.dto.PutAgencyDossierDto;
import vn.vnpt.digo.reporter.dto.PutAgencyDto;
import vn.vnpt.digo.reporter.dto.SectorByAgencyReturnDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.service.AgencyService;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.ProcedureAgencySectorDto;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agency")
@IcodeAuthorize("vnpt.permission.agency")
public class AgencyController {

    @Autowired
    private AgencyService agencyService;

    @Value("${digo.ssrf.black-list}")
    private List<String> SSRFBlacklist;

    @Value("${digo.petition.callout.whitelist-url}")
    private List<String> SSRFWhitelist;

    @Value("${digo.ssrf.black-list-enable}")
    private Boolean SSRFBlacklistEnable;

    Logger logger = LoggerFactory.getLogger(AgencyController.class);

    //Digo 1216    
    @GetMapping("/--active-procedure")
    public List<AgencyNameDto> getListByActiveProcedure(HttpServletRequest request, @RequestParam(value = "place-id", required = true) ObjectId placeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<AgencyNameDto> listByActiveProcedure = agencyService.getListByActiveProcedure(placeId);
        logger.info(listByActiveProcedure.size() + "");
        return listByActiveProcedure;
    }

    //Digo 1206    
    @GetMapping("/--for-online")
    public List<AgencyNameDto> getListByOnlineProcedure(HttpServletRequest request, @RequestParam(value = "place-id", required = true) ObjectId placeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<AgencyNameDto> listByOnlineProcedure = agencyService.getListByOnlineProcedure(placeId);
        logger.info(listByOnlineProcedure.size() + "");
        return listByOnlineProcedure;
    }

    @GetMapping("/--sector")
    public List<SectorByAgencyReturnDto> getListFieldBySector(HttpServletRequest request, @RequestParam(value = "agency-id", required = true) ObjectId agencyId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<SectorByAgencyReturnDto> listSector = agencyService.getListSectorBySector(agencyId);
        logger.info(listSector.size() + "");
        return listSector;
    }

    @GetMapping(value = "/--procedure-quantity-by-sector")
    public List<ProcedureQuantityBySectorDto> getProcedureQuantityBySector(HttpServletRequest request, @RequestParam(value = "place-id") ObjectId placeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<ProcedureQuantityBySectorDto> listSector = agencyService.getListProcedureQuantityBySector(placeId);

        logger.info("DIGO-Info: " + listSector.size());
        return listSector;
    }

    @GetMapping("/--dossier")
    @Validated
    public Slice<GetDossierByAgencyDto> getDossier(HttpServletRequest request,
            @RequestParam(value = "agency-id", required = false) ObjectId agencyId,
            @RequestParam(value = "parent-id", required = false) ObjectId parentId,
            @RequestParam(value = "agency-type", required = false) ObjectId agencyType,
            @RequestParam(value = "year", required = true) Integer year,
            @RequestParam(value = "month", required = false) @Min(1) @Max(12) Integer month,
            Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<GetDossierByAgencyDto> ret = agencyService.getAgencyDossier(agencyId, agencyType, year, month,parentId, pageable);

        logger.info("DIGO-Info: " + ret.getContent().size());
        return ret;
    }
    
    @GetMapping(value = "/--count-agency")
    public Page<GetTypeAgencyDto> getCountAgency(HttpServletRequest request, Pageable pageable, 
                                                @RequestParam(value = "deployment-id", required = false) ObjectId deploymentId,
                                                @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        Page<GetTypeAgencyDto> countAgency = agencyService.getCountAgency(pageable, deploymentId, spec);
        return countAgency;
    }
    
    @PutMapping("{id}/")
    public AffectedRowsDto updateAgency(HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id,
            @RequestBody PutAgencyDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRows = agencyService.updateAgency(id, body);
        return affectedRows;
    }
    
    @PostMapping("")
    public PostResponseDto addNewAgency(HttpServletRequest request,
            @RequestBody PutAgencyDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        PostResponseDto newCate = agencyService.addNewAgency(body);
        logger.info(newCate.getId().toString());
        return newCate;
    }
    
    @PutMapping("{id}/--dossier")
    public AffectedRowsDto updateAgencyDossier(HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id,
            @RequestParam(value = "year", required = true) Integer year,
            @RequestParam(value = "month", required = true) @Min(1) @Max(12) Integer month,
            @RequestBody PutAgencyDossierDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRows = agencyService.updateAgencyDossier(id, year, month, body);
        return affectedRows;
    }
    
    @PutMapping("{id}/--procedure")
    public AffectedRowsDto updateAgencyProcedure(HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id,
            @RequestBody PutAgencyDossierDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRows = agencyService.updateAgencyProcedure(id, body);
        return affectedRows;
    }

    @GetMapping("/test-kafka")
    public boolean testKafka() {
        return agencyService.test();
    }
    
    @PutMapping("/--update-procedure-sector")
    public AffectedRowsDto updateProcedureSector(HttpServletRequest request, 
            @RequestBody ArrayList<ProcedureAgencySectorDto> procedureAgencySectorDto,
            @RequestParam(value = "site-url", required = true) String url) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Info: " + requestPath);
        StringBuilder requestData = new StringBuilder();
        requestData.append(url);
        checkWhiteUrlSsrf(requestData);

        AffectedRowsDto listByActiveProcedure = agencyService.updateBySector(procedureAgencySectorDto, url);
        return listByActiveProcedure;
    }


    private void checkWhiteUrlSsrf(StringBuilder requestData){
        if(SSRFBlacklistEnable){
            for (String blacklistItem : SSRFBlacklist) {
                if (requestData.indexOf(blacklistItem) != -1) {
                    throw new DigoHttpException(10400, new String[]{"input URL"}, HttpServletResponse.SC_BAD_REQUEST);
                }
            }

            boolean isWhitelisted = false;
            for (String whitelistItem : SSRFWhitelist) {
                if (Pattern.matches(whitelistItem, requestData.toString())) {
                    isWhitelisted = true;
                    break;
                }
            }
            if(!isWhitelisted){
                throw new DigoHttpException(10400, new String[]{"input URL"}, HttpServletResponse.SC_BAD_REQUEST);
            }
        }
    }
    
    @GetMapping(value = "/--procedure-quantity-by-tag")
    public List<ProcedureQuantityBySectorDto> getProcedureQuantityByTag(HttpServletRequest request, 
            @RequestParam(value = "tag-id", required = false) ObjectId tagId,
            @RequestParam(value = "agency-id", required = false) ObjectId agencyId,
            @RequestParam(value = "ancestor-id", required = false) ObjectId ancestorId,
            @RequestParam(value = "place-id", required = false) ObjectId placeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<ProcedureQuantityBySectorDto> listSector = agencyService.getListProcedureQuantityByTag(placeId, tagId, agencyId,ancestorId);

        logger.info("DIGO-Info: " + listSector.size());
        return listSector;
    }

}
