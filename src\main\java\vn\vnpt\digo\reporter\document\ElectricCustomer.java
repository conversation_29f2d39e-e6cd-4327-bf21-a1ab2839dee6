package vn.vnpt.digo.reporter.document;

import java.util.Date;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Document(collection = "electricCustomer")
@CompoundIndex(name = "unique_electricCustomer", def = "{'userId': 1, 'customerCode': 1}", unique = true)
public class ElectricCustomer {

    @Id
    private ObjectId id;
    private ObjectId userId;
    private String customerCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    public ElectricCustomer(ObjectId userId, String customerCode) {

        this.userId = userId;
        this.customerCode = customerCode;
        this.updatedDate = new Date();
    }

    public void setUpdateDate() {
        this.updatedDate = new Date();
    }
}
