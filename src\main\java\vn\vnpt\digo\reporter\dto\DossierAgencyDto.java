package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierAgencyDto implements Serializable {
  @Field("id")
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId id;
  private String code;
  private String name;
  @Field("tagId")
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId tagId;
}
