package vn.vnpt.digo.reporter.service;

import java.util.NoSuchElementException;
import java.util.Optional;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.WaterBill;
import vn.vnpt.digo.reporter.dto.GetWaterBillByIdDto;
import vn.vnpt.digo.reporter.dto.GetWaterBillDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.WaterBillInputDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.repository.WaterBillRepository;
import vn.vnpt.digo.reporter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class WaterBillService {

    @Autowired
    private WaterBillRepository waterBillRepository;

    @Autowired
    private Translator translator;

    public PostResponseDto addNewBillForCustomer(WaterBillInputDto waterBillInputDto) {

        WaterBill bill = new WaterBill(waterBillInputDto);
        bill = waterBillRepository.save(bill);

        return new PostResponseDto(bill.getId());
    }

    // Digo 2464
    public GetWaterBillByIdDto getWaterBillById(ObjectId id) {
        GetWaterBillByIdDto waterBillDto = new GetWaterBillByIdDto();

        try {
            Optional<WaterBill> waterBill = waterBillRepository.findById(id);

            waterBillDto.setId(waterBill.get().getId());
            waterBillDto.setBillCode(waterBill.get().getBillCode());
            waterBillDto.setMonth(waterBill.get().getMonth());
            waterBillDto.setYear(waterBill.get().getYear());
            waterBillDto.setStartDate(waterBill.get().getStartDate());
            waterBillDto.setEndDate(waterBill.get().getEndDate());
            waterBillDto.setPaid(waterBill.get().isPaid());
            waterBillDto.setOldIndex(waterBill.get().getOldIndex());
            waterBillDto.setNewIndex(waterBill.get().getNewIndex());
            waterBillDto.setCustomerCode(waterBill.get().getCustomerCode());
            waterBillDto.setPaymentAmount(waterBill.get().getPaymentAmount());
            waterBillDto.setConsumedAmount(waterBill.get().getConsumedAmount());
            waterBillDto.setMeterNumber(waterBill.get().getMeterNumber());
        } catch (NullPointerException | NoSuchElementException e) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.bill")},
                    HttpServletResponse.SC_NOT_FOUND);
        }

        return waterBillDto;
    }

    public Slice<GetWaterBillDto> getWaterBillByMonth(String customerCode, Integer month, Integer year,
            Pageable pageable) {
        Slice<GetWaterBillDto> waterBillDto;

        if (month != null && year != null) {
            waterBillDto = waterBillRepository.getWaterBillByMonth(customerCode, month, year, pageable);
        } else {
            waterBillDto = waterBillRepository.getWaterBillByMonthAndYearDesc(customerCode, pageable);
        }

        return waterBillDto;
    }

}
