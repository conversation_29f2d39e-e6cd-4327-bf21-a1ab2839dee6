package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.KTMETLDossier;

import java.beans.PropertyDescriptor;
import java.util.List;


@Service
public class KTMETLDossierService {

    private final MongoTemplate mongoTemplate;

    @Autowired
    public KTMETLDossierService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public boolean addKTMETLDossierData(List<KTMETLDossier> listKTMETLDossier) {
        if(listKTMETLDossier.size()>0){
            for(int i = 0; i <listKTMETLDossier.size(); i++ ){
                mongoTemplate.save(listKTMETLDossier.get(i), "ktmETLDossier");
            }
        }

        return true;
    }
}
