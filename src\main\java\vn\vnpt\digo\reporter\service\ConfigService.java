package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.dto.Minio.ParamDto;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConfigService {

    @Autowired
    private Microservice microservice;

    @Autowired
    private RestTemplate restTemplate;

    Logger logger = LoggerFactory.getLogger(ConfigService.class);

    private final ObjectId subsystemId = new ObjectId("5f7c16069abb62f511880007");
    public List<ParamDto> getParams(ObjectId serviceId) throws JsonProcessingException {
        String URL = microservice.adapterUri("integrated-configuration/--params").toUriString();
        URL += "?subsystem-id=" + this.subsystemId;
        URL += "&service-id=" + serviceId;
        try{
            logger.info("getParams:" + URL);
            String json = MicroserviceExchange.getNoAuth(this.restTemplate, URL, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<ParamDto> params = mapper.readValue(json, new TypeReference<List<ParamDto>>(){});
            return params;
        }catch (Exception ex){
            String token = MicroserviceExchange.getTokenMinio();
            logger.info("getParams:" + URL);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            String json = MicroserviceExchange.getMinio(this.restTemplate, URL, headers, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<ParamDto> params = mapper.readValue(json, new TypeReference<List<ParamDto>>(){});
            return params;
        }
    }

    public String getParam(List<ParamDto> params, String key){
        try{
            List<ParamDto> _params = params.stream().filter(i->i.getKey().equals(key)).collect(Collectors.toList());
            return _params.get(0).getValue();
        }catch (Exception ex){
            return null;
        }
    }

}
