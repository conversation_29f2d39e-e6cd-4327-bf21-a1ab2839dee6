package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.util.HideSecurityInformationHelper;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailGeneralReportDto {

    @JsonProperty("pagedResults")
    private List<PageResult> pagedResults;
    @JsonProperty("totalCount")
    private List<TotalCountResult> totalCount;

    public static class PageResult {
        @JsonProperty("no")
        private Integer no;
        @JsonProperty("id")
        private String id;
        @JsonProperty("dossierCode")
        private String dossierCode;
        @JsonProperty("acceptedDate")
        private String acceptedDate;
        @JsonProperty("appointmentDate")
        private String appointmentDate;
        @JsonProperty("completedDate")
        private String completedDate;
        @JsonProperty("applicantOwnerFullName")
        private String applicantOwnerFullName;
        @JsonProperty("applicantPhoneNumber")
        private String applicantPhoneNumber;
        @JsonProperty("noiDungYeuCauGiaiQuyet")
        private String noiDungYeuCauGiaiQuyet;
        @JsonProperty("assigneeFullname")
        private String assigneeFullname;
        @JsonProperty("procedureName")
        private String procedureName;
        @JsonProperty("sectorName")
        private String sectorName;
        @JsonProperty("curTaskAgencyName")
        private String curTaskAgencyName;
        @JsonProperty("dossierStatusName")
        private String dossierStatusName;
        @JsonProperty("applyMethod")
        private String applyMethod;

        public String getDossierStatusName() {
            return dossierStatusName;
        }

        public void setDossierStatusName(String value) {
            this.dossierStatusName = value;
        }

        public String getCurTaskAgencyName() {
            return curTaskAgencyName;
        }

        public void setCurTaskAgencyName(String value) {
            this.curTaskAgencyName = value;
        }

        public String getSectorName() {
            return sectorName;
        }

        public void setSectorName(String value) {
            this.sectorName = value;
        }

        public String getProcedureName() {
            return procedureName;
        }

        public void setProcedureName(String value) {
            this.procedureName = value;
        }

        public String getApplicantOwnerFullName() {
            return applicantOwnerFullName;
        }

        public void setApplicantOwnerFullName(String value) {
            this.applicantOwnerFullName = value;
        }

        public String getApplicantPhoneNumber() {
            return applicantPhoneNumber;
        }

        public void setApplicantPhoneNumber(String value) {
            this.applicantPhoneNumber = value;
        }

        public String getNoiDungYeuCauGiaiQuyet() {
            return noiDungYeuCauGiaiQuyet;
        }

        public void setNoiDungYeuCauGiaiQuyet(String value) {
            this.noiDungYeuCauGiaiQuyet = value;
        }

        public Integer getNo() {
            return no;
        }

        public void setNo(Integer value) {
            this.no = value;
        }

        public String getId() {
            return id;
        }

        public void setId(String value) {
            this.id = value;
        }

        public String getDossierCode() {
            return dossierCode;
        }

        public void setDossierCode(String value) {
            this.dossierCode = value;
        }

        public String getAcceptedDate() {
            return acceptedDate;
        }

        public void setAcceptedDate(String value) {
            this.acceptedDate = value;
        }

        public String getAppointmentDate() {
            return appointmentDate;
        }

        public void setAppointmentDate(String value) {
            this.appointmentDate = value;
        }

        public String getCompletedDate() {
            return completedDate;
        }

        public void setCompletedDate(String value) {
            this.completedDate = value;
        }

        public String getAssigneeFullname() {
            return assigneeFullname;
        }

        public void setAssigneeFullname(String assigneeFullname) {
            this.assigneeFullname = assigneeFullname;
        }

        public String getApplyMethod() {
            return applyMethod;
        }

        public void setApplyMethod(String applyMethod) {
            this.applyMethod = applyMethod;
        }

        public void setHideSecurityInformation(String applicantOwnerFullName, String assigneeFullname) {
          this.applicantOwnerFullName =  HideSecurityInformationHelper.setFullnameSecurity(applicantOwnerFullName);
          this.assigneeFullname = HideSecurityInformationHelper.setFullnameSecurity(assigneeFullname);
        }
    }

    public class TotalCountResult {
        @JsonProperty("totalCount")
        private Integer totalCount;

        public Integer getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Integer value) {
            this.totalCount = value;
        }
    }

}

