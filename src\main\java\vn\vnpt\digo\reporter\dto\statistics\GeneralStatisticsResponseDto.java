package vn.vnpt.digo.reporter.dto.statistics;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GeneralStatisticsResponseDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private String deploymentId;

    private String tenKhachHang;

    private String phienBan;

    private String urlTrangChu;

    private List<ThongKe> arrayThongKe;

}
