/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;
import com.mongodb.DBObject;
import com.mongodb.InsertOptions;
import com.mongodb.util.JSON;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.io.FileUtils;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-10-25-08-24")
public class NotifyTemplateChangeLogs {

    Logger logger = LoggerFactory.getLogger(NotifyTemplateChangeLogs.class);

    @ChangeSet(order = "2021-10-25-08-25", id = "NotifyTemplateChangeLogs::notifyIGateProcessing", author = "mongtt")
    public void notifyIGateProcessing(DB db) throws IOException {

        InputStream inputStream = this.getClass().getResourceAsStream("/db/migration/mongo/V1.0.0__template.json");
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        List<DBObject> objectList = new ArrayList<>();
        String currentLine;
        int line = 0;
        logger.info("insert01 - Inserting...");
        while ((currentLine = reader.readLine()) != null) {
            try {
                line++;
                DBObject object = (DBObject) JSON.parse(currentLine);
                objectList.add(object);
            } catch (Exception e) {
                logger.info("Line " + String.valueOf(line) + ": " + currentLine);
                logger.info("Parse error: " + e.getMessage());
            }
        }
        try {
            db.getCollection("template").insert(objectList, new InsertOptions().continueOnError(true));
            File srcDir = new File("/db/migration/file/20211024");
            File destDir = new File("/upload/5ee091507d567c9fe29f82fa/2021/10/24");
            try {
                FileUtils.copyDirectory(srcDir, destDir);
            } catch (IOException e) {
                logger.info("Copy ftl error: " + e.getMessage());
            }
        } catch (Exception e) {
            logger.info("Insert error: " + e.getMessage());
        }
        logger.info("insert01 - Complete insert data");
    }

    @ChangeSet(order = "2021-11-08-08-25", id = "NotifyTemplateChangeLogs::notifyIGateProcedure", author = "mongtt")
    public void notifyIGateProcedure(DB db) throws IOException {

        InputStream inputStream = this.getClass().getResourceAsStream("/db/migration/mongo/V1.0.1__template.json");
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        List<DBObject> objectList = new ArrayList<>();
        String currentLine;
        int line = 0;
        logger.info("insert01 - Inserting...");
        while ((currentLine = reader.readLine()) != null) {
            try {
                line++;
                DBObject object = (DBObject) JSON.parse(currentLine);
                objectList.add(object);
            } catch (Exception e) {
                logger.info("Line " + String.valueOf(line) + ": " + currentLine);
                logger.info("Parse error: " + e.getMessage());
            }
        }
        try {
            db.getCollection("template").insert(objectList, new InsertOptions().continueOnError(true));
            File srcDir = new File("/db/migration/file/20211108");
            File destDir = new File("/upload/5ee091507d567c9fe29f82fa/2021/11/08");
            try {
                FileUtils.copyDirectory(srcDir, destDir);
            } catch (IOException e) {
                logger.info("Copy ftl error: " + e.getMessage());
            }
        } catch (Exception e) {
            logger.info("Insert error: " + e.getMessage());
        }
        logger.info("insert01 - Complete insert data");
    }
    
    @ChangeSet(order = "2022-06-02-11-50", id = "NotifyTemplateChangeLogs::NotifyIGateProcedured", author = "thuannm")
    public void NotifyIGateProcedured(DB db) throws IOException {

        InputStream inputStream = this.getClass().getResourceAsStream("/db/migration/mongo/V1.0.2__template.json");
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        List<DBObject> objectList = new ArrayList<>();
        String currentLine;
        int line = 0;
        logger.info("insert01 - Inserting...");
        while ((currentLine = reader.readLine()) != null) {
            try {
                line++;
                DBObject object = (DBObject) JSON.parse(currentLine);
                objectList.add(object);
            } catch (Exception e) {
                logger.info("Line " + String.valueOf(line) + ": " + currentLine);
                logger.info("Parse error: " + e.getMessage());
            }
        }
        try {
            db.getCollection("template").insert(objectList, new InsertOptions().continueOnError(true));
            File srcDir = new File("/db/migration/file/20220602");
            File destDir = new File("/upload/5ee091507d567c9fe29f82fa/2022/06/02");
            try {
                FileUtils.copyDirectory(srcDir, destDir);
            } catch (IOException e) {
                logger.info("Copy ftl error: " + e.getMessage());
            }
        } catch (Exception e) {
            logger.info("Insert error: " + e.getMessage());
        }
        logger.info("insert01 - Complete insert data");
    }
    
    
    
}