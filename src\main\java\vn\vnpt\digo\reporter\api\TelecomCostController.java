/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.InputTelecomCostDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.TelecomCostAndAmountByMonthDto;
import vn.vnpt.digo.reporter.dto.TelecomCostAndAmountByYearDto;
import vn.vnpt.digo.reporter.service.TelecomCostService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/telecom-cost")
@IcodeAuthorize("vnpt.permission.telecomcost")
public class TelecomCostController {

    @Autowired
    private TelecomCostService telecomCostService;

    Logger logger = LoggerFactory.getLogger(WaterCustomerController.class);

    @PostMapping("")
    public PostResponseDto postTelecomCost(HttpServletRequest request, @RequestBody @ModelAttribute InputTelecomCostDto postTelecomCostData) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PostResponseDto result = telecomCostService.postTelecomCost(postTelecomCostData);
        logger.info("DIGO-Info: " + result.getId());

        return result;
    }
    // Digo 2552
    @GetMapping("/--month")
    public TelecomCostAndAmountByMonthDto getTelecomCostByMonth(HttpServletRequest request,
            @RequestParam(value = "month") Integer month,
            @RequestParam(value = "year") Integer year,
            @RequestParam(value = "subscription-type-id", required = false) List<ObjectId> subscriptionTypeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        TelecomCostAndAmountByMonthDto telecomCost = telecomCostService.getTelecomCostByMonth(month, year, subscriptionTypeId);
        logger.info(telecomCost.getTelecomCost().size() + "");

        return telecomCost;
    }
    // Digo 2553
    @GetMapping("/--year")
    public TelecomCostAndAmountByYearDto getTelecomCostByYear(HttpServletRequest request,
            @RequestParam(value = "year") Integer year,
            @RequestParam(value = "subscription-type-id", required = false) List<ObjectId> subscriptionTypeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        TelecomCostAndAmountByYearDto telecomCost = telecomCostService.getTelecomCostByYear(year, subscriptionTypeId);
        logger.info(telecomCost.getTelecomCost().size() + "");

        return telecomCost;
    }
}
