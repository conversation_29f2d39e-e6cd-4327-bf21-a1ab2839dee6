/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.pojo.SubscriptionTypeName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "subscriptionType")
@CompoundIndex(name = "unique_subscription_type", def = "{'name': 1}", unique = true)
public class SubscriptionType {

    @Id
    @JsonSerialize
    private ObjectId id;

    @NotNull
    private String idMapping;

    @NotNull
    private List<SubscriptionTypeName> name;

    public SubscriptionType(String idMapping, List<SubscriptionTypeName> name) {
        this.idMapping = idMapping;
        this.name = name;
    }
}
