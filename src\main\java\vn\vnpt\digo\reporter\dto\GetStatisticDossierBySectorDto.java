/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
public class GetStatisticDossierBySectorDto implements Serializable {
    
    private AgencyDossierByDay agency;
    
    private SectorDossierByDay sector;
    
    private ProcedureLevelDossierByDay procedureLevel;
    
    private ProcedureDossierByDay procedure;
    
    private Integer appliedOnline;
    
    private Integer received;
    
    private Integer receivedOnline;
    
    private Integer receivedDirect;
    
    private Integer resolved;
    
    private Integer resolvedEarly;
    
    private Integer resolvedOverdue;
    
    private Integer unresolvedOverdue;
    
    private Integer cancelled;

    private Integer deleted;
    
    private Integer suspended;
    
    private Integer returnOnTime;
    
    private Integer returnOverdue;
    
    private Integer unresolved;
}
