package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.AgencySector;
import vn.vnpt.digo.reporter.pojo.SectorProcedureReporter;
import vn.vnpt.digo.reporter.pojo.Tag;
import vn.vnpt.digo.reporter.pojo.TranslateName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdPojo implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
}
