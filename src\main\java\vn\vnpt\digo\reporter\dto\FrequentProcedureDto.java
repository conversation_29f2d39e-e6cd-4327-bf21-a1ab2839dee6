package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FrequentProcedureDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;

    private List<ProcedureDto> citizen;

    private List<ProcedureDto> enterprise;
}
