/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.SubscriptionType;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GetTelecomCostByMonthDto implements Serializable {

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subscriptionTypeId;

    @NotNull
    private String subscriptionTypeName;

    @NotNull
    @JsonIgnore
    private SubscriptionType subscriptionType;

    private String paymentCode;

    @NotNull
    private String subscriptionCode;

    @NotNull
    private Double amount;

    public void setSubscriptionTypeId() {
        subscriptionTypeId = subscriptionType.getId();
    }

    public void setSubscriptionTypeName(Short localeId) {
        subscriptionType.getName().forEach(item -> {
            if (item.getLanguageId().equals(localeId)) {
                this.subscriptionTypeName = item.getName();
            }
        });

    }

}
