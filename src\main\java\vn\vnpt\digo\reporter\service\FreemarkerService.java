/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import freemarker.template.Configuration;
import freemarker.template.Template;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import vn.vnpt.digo.reporter.config.FreemarkerConfig;
import vn.vnpt.digo.reporter.dto.FreemarkerReportDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.DynamicObject;
import vn.vnpt.digo.reporter.repository.TemplateRepository;
import vn.vnpt.digo.reporter.util.Translator;
import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.google.gson.reflect.TypeToken;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;

/**
 *
 * <AUTHOR>
 */
@Service
public class FreemarkerService {

    @Autowired
    private FreemarkerConfig freemarkerConfig;

    @Autowired
    private TemplateRepository templateRepository;

    @Autowired
    private Translator translator;

    @Autowired
    private ObjectMapper objectMapper;

    public String report(String _id, FreemarkerReportDto freemarkerReportDto) {
        ObjectId id = (_id != "" && _id != null) ? new ObjectId(_id) : null;

        try {
            vn.vnpt.digo.reporter.document.Template templateReporter = templateRepository.findOneById(id);

            Map model = new HashMap();

            //Get key & value in object
            freemarkerReportDto.getParameters().forEach((key, value) -> {
                if (key == "applicantFullname"){
                    model.put(key, value);
                }else
                //Check value is array or not
                if (value.toString().contains("[") && value.toString().contains("]")) {
                    List<DynamicObject> listDynamicObjects = new ArrayList<>();
                   
                    //Convert string object array to object array
                    Gson gson = new Gson();
                    List<Object> objectsFromValue = gson.fromJson(value.toString(), new TypeToken<List<Object>>() {
                    }.getType());

                    for (int i = 0; i < objectsFromValue.size(); i++) {
                        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                        try {
                            //Convert object to json
                            String objectJson = ow.writeValueAsString(objectsFromValue.get(i));

                            DynamicObject dynamicObject = objectMapper.readValue(objectJson, DynamicObject.class);

                            listDynamicObjects.add(dynamicObject);
                        } catch (JsonProcessingException ex) {
                            Logger.getLogger(FreemarkerService.class.getName()).log(Level.SEVERE, null, ex);
                        }
                    }

                    model.put(key, listDynamicObjects);
                } else {
                    model.put(key, value);
                }
            });

            freemarkerReportDto.setModel(model);

            Configuration cfg = freemarkerConfig.getFreeMarkerConfiguration();
            Template template = cfg.getTemplate(templateReporter.getFile().getPath());
            String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, freemarkerReportDto.getModel());

            return html;
        } catch (Exception e) {
            if (e.getMessage().contains("Template not found for name")) {
                throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_NOT_FOUND);
            }
        }
        throw new DigoHttpException(10004, new String[]{}, HttpServletResponse.SC_BAD_REQUEST);
    }

}
