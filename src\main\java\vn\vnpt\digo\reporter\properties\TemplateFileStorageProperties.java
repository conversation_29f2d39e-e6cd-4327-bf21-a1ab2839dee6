/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "file")
public class TemplateFileStorageProperties {
    
    private String uploadDir;

    public String getUploadDir() {
        return uploadDir;
    }

    public void setUploadDir(String uploadDir) {
        this.uploadDir = uploadDir;
    }
    
}
