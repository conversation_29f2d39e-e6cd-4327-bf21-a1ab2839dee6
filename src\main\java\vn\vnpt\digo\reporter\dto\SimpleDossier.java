package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleDossier implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private String _id;
    
    @JsonProperty("sector")
    private SectorDossierByDay sector;

    private String sectorName;

    private String agencyName;
    
    @JsonProperty("agency")
    private AgencyDossierByDay agency;
    
    @JsonProperty("agencyLevel")
    private AgencyDossierByDay agencyLevel;
    
    @JsonProperty("appliedOnline")
    private Integer appliedOnline;
    
    @JsonProperty("received")
    private Integer received;
    
    @JsonProperty("receivedOnline")
    private Integer receivedOnline;
    
    @JsonProperty("receivedDirect")
    private Integer receivedDirect;
    
    @JsonProperty("resolved")
    private Integer resolved;
    
    @JsonProperty("resolvedEarly")
    private Integer resolvedEarly;
    
    @JsonProperty("resolvedOverdue")
    private Integer resolvedOverdue;
    
    @JsonProperty("unresolvedOverdue")
    private Integer unresolvedOverdue;
    
    @JsonProperty("cancelled")
    private Integer cancelled;
    
    @JsonProperty("deleted")
    private Integer deleted;
    
    @JsonProperty("suspended")
    private Integer suspended;
    
    @JsonProperty("returnOnTime")
    private Integer returnOnTime;
    
    @JsonProperty("returnOverdue")
    private Integer returnOverdue;   
    
    @JsonProperty("unresolved")
    private Integer unresolved;
    
    @JsonSerialize(using = ToStringSerializer.class)
    @Field("deploymentId")
    private String deploymentId;

    public void setSectorName(Short localeId) {
        sector.getName().forEach(item -> {
            if (item.getLanguageId().equals(localeId)) {
                this.sectorName = item.getName();
            }
        });

    }

    public void setName(short langId) {
        agency.getName().forEach(trans -> {
            if (trans.getLanguageId().equals(langId)) {
                this.agencyName = trans.getName();
            }
        });
    }
    
}
