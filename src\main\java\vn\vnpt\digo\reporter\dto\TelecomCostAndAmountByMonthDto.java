/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TelecomCostAndAmountByMonthDto implements Serializable {

    @NotNull
    private double total = 0;

    @NotNull
    private List<GetTelecomCostByMonthDto> telecomCost;

    public void setTotal() {
        telecomCost.forEach(item -> {
            total += item.getAmount();
        });
    }
}
