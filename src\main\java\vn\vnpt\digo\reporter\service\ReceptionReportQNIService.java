package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.mongodb.MongoClient;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.util.*;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
public class ReceptionReportQNIService {
    private static final RestTemplate restTemplate = new RestTemplate();
    private final Gson gson = new Gson();
    Logger logger = org.slf4j.LoggerFactory.getLogger(ReceptionReportQNIService.class);
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    TimeZone timezone = TimeZone.getTimeZone("GMT");
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MongoConverter mongoConverter;
    @Autowired
    private Microservice microservice;
    @Autowired
    private Translator translator;
    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_RECEPTION__QNI)
    private Resource resourceTemplateDossierStatisticAssigneeQNI;
    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_RECEPTION__QNI_V2)
    private Resource resourceTemplateDossierStatisticAssigneeQNIV2;
    @Autowired
    private MongoClient client;
    @Value("${digo.dossier.off-time}")
    private String offTime;
    @Value("${digo.qni.timesheet-id}")
    private String qniTimeSheetId;
    @Value("${digo.qni.max-sleep-time}")
    private Integer qniMaxSleepTime;
    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;
    @Value("${digo.enable.hide.fullname}")
    private boolean enableHideName;
    @Autowired
    private AgencyFilterReportQniService agencyFilterReportQniService;
    @Autowired
    private QniRestTemplate qniRestTemplate;

    private Set<ObjectId> uniqueObjectIdAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<ObjectId> uniqueObjectIds = new HashSet<>();
        Set<String> uniqueStringIds = new HashSet<>();
        for (String objectIdString : objectIdStrings) {
            try {
                ObjectId objectId = new ObjectId(objectIdString);
                uniqueObjectIds.add(objectId);
                uniqueStringIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    private List<String> getRootAgencies(List<String> agencyIds) throws JSONException {
        String getObjUrl;
        String jsonString;
        JSONArray jsonArr;

        List<String> rootAgencyIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(agencyIds)) {
            getObjUrl = "qni-agency/--find-root?id=" + String.join(",", agencyIds);
            getObjUrl = microservice.basedataUri(getObjUrl).toUriString();
            try {
                jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
                jsonArr = new JSONArray(jsonString);
                for (int i = 0; i < jsonArr.length(); i++) {
                    rootAgencyIds.add(jsonArr.getJSONObject(i).get("id").toString());
                }
            } catch (Exception ex) {
                logger.error("Can not find root agencies ", ex);
            }
        }
        return rootAgencyIds;
    }

    private Set<String> uniqueStringAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<String> uniqueObjectIds = new HashSet<>();

        for (String objectIdString : objectIdStrings) {
            try {
                uniqueObjectIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    public List<ReceptionReportDto> findDossiersWithConditions(List<String> agencyIds, String fromDateString, String toDateString) {
        Query query = new Query();
        logger.info("Begin findDossiersWithConditions");

        try {
            // format Date
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);

            // get Agency
            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            // Điều kiện dựa trên agency và khoảng thời gian
            query.addCriteria(Criteria.where("agency._id").in(agencyObjectIds));
            query.addCriteria(Criteria.where("appliedDate").gte(fromDate).lte(toDate));

            // Điều kiện cho financialObligationOnline
            query.addCriteria(new Criteria().andOperator(
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));

            var dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            // Tính toán cộng lại theo agency cơ quan cha được cấu hình
            List<DigitizingReportDto.Agency> agencyTransList = new ArrayList<>();
            var rootAgencys = getRootAgencies(agencyIds);
            var getObjUrl = microservice.basedataUri("agency/--by-parent-agency?arr-id=" + String.join(",", rootAgencys)).toUriString();
            var jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
            var jsonArr = new JSONArray(jsonString);

            if (Objects.nonNull(jsonArr)) {
                for (int i = 0; i < jsonArr.length(); i++) {
                    String agencyIdTemp = jsonArr.getJSONObject(i).get("id").toString();
                    var target = new DigitizingReportDto.Agency();
                    target.setIdAgency(agencyIdTemp);
                    target.setName(jsonArr.getJSONObject(i).get("name").toString());

                    agencyTransList.add(target);
                }
            }

            var resultDB = aggregateResults(dossiers);
            var aggregateResultsFinal = aggregateResultsFinal(resultDB, agencyTransList);

            logger.info("End getGeneralReportDetailDto");
            return aggregateResultsFinal;
        } catch (Exception e) {
            logger.error("Error findDossiersWithConditions: " + e.getMessage());
        }

        return null;
    }

    public List<ReceptionReportDto> aggregateResultsFinal(List<ReceptionReportDto> paymentReports, List<DigitizingReportDto.Agency> agencyTransList) {
        // Bản đồ agencyId -> agencyName
        Map<String, String> agencyMap = new HashMap<>();
        for (DigitizingReportDto.Agency agency : agencyTransList) {
            agencyMap.put(agency.getId(), agency.getName());
        }

        // Danh sách lưu trữ kết quả cuối cùng
        List<ReceptionReportDto> finalResult = new ArrayList<>();

        // Lặp qua từng agency trong agencyMap
        for (Map.Entry<String, String> entry : agencyMap.entrySet()) {
            String agencyId = entry.getKey();
            String agencyName = entry.getValue();

            // Lấy danh sách agencyTemp cho agency hiện tại (list Id)
            AgencyFilterReportQniResponse agencyTemp = null;
            List<String> agencyTemps = null;
            try {
                agencyTemp = agencyFilterReportQniService.getDocumentsByAgencyId(agencyId);
                if (agencyTemp != null) {
                    agencyTemps = new ArrayList<>(uniqueStringAgency(agencyTemp.getIdFilter()));
                }
            } catch (Exception e) {
                continue;
            }

            // Lọc danh sách ReceptionReportDto hiện tại theo agencyTemp
            List<String> finalAgencyTemps = agencyTemps;
            List<ReceptionReportDto> filteredReports = paymentReports.stream()
                    .filter(report -> finalAgencyTemps.contains(report.getAgencyIdAgency()))
                    .collect(Collectors.toList());

            // Bản đồ gộp theo procedureId
            Map<String, ReceptionReportDto.ProcedureReceptionDto> aggregatedProcedurePayments = new HashMap<>();

            ObjectMapper objectMapper = new ObjectMapper(); // Sử dụng ObjectMapper để chuyển đổi dữ liệu
            Set<String> countedProcedureNames = new HashSet<>(); // Để kiểm tra procedureId đã đếm chưa
            int procedureUsingPaymentOnline = 0; // Đếm số procedure với paymentOnline > 0

            // Lặp qua danh sách filteredReports để gộp các procedurePayment
            for (ReceptionReportDto report : filteredReports) {
                for (Object procedurePayment : report.getProcedureReception()) {
                    ReceptionReportDto.ProcedureReceptionDto procedureReceptionDto;

                    // Kiểm tra xem procedurePayment có phải là HashMap không
                    if (procedurePayment instanceof HashMap) {
                        // Chuyển đổi từ HashMap sang ProcedurePaymentDto
                        procedureReceptionDto = objectMapper.convertValue(procedurePayment, ReceptionReportDto.ProcedureReceptionDto.class);
                    } else {
                        // Nếu không phải HashMap, thì casting sang ProcedurePaymentDto
                        procedureReceptionDto = (ReceptionReportDto.ProcedureReceptionDto) procedurePayment;
                    }

                    String procedureName = procedureReceptionDto.getProcedureName();

                    // Nếu đã có procedureId trong bản đồ, gộp kết quả
                    if (aggregatedProcedurePayments.containsKey(procedureName)) {
                        ReceptionReportDto.ProcedureReceptionDto existingProcedureReception = aggregatedProcedurePayments.get(procedureName);
                        existingProcedureReception.setSlowReception(
                                existingProcedureReception.getSlowReception() + procedureReceptionDto.getSlowReception());
                        existingProcedureReception.setOnTimeReception(
                                existingProcedureReception.getOnTimeReception() + procedureReceptionDto.getOnTimeReception());
                        existingProcedureReception.setToTal(
                                existingProcedureReception.getToTal() + procedureReceptionDto.getToTal());
                    } else {
                        // Nếu chưa có, thêm mới procedurePayment
                        aggregatedProcedurePayments.put(procedureName, procedureReceptionDto);
                    }
                }
            }

            // Tạo ReceptionReportDto cho agency hiện tại
            ReceptionReportDto aggregatedReport = new ReceptionReportDto();
            aggregatedReport.setAgencyIdAgency(agencyId);
            aggregatedReport.setAgencyName(agencyName);
            aggregatedReport.setProcedureReception(new ArrayList<>(aggregatedProcedurePayments.values()));

            // Thêm kết quả vào finalResult
            finalResult.add(aggregatedReport);
        }

        return finalResult;
    }

    public List<ReceptionReportDto> aggregateResults(List<QNIETLDossier> dossiers) {
        try {
            // Bản đồ lưu trữ kết quả gộp theo agencyId và procedureId
            Map<String, Map<String, Object>> aggregatedResults = new HashMap<>();

            // Lặp qua danh sách Dossier để gộp kết quả
            for (QNIETLDossier item : dossiers) {
                String agencyId = item.getAgency().getId();  // Lấy agencyId
                String procedureId = item.getProcedure().getId();  // Lấy procedureId
                String procedureName = item.getProcedure().getName();

                // Tạo key cho nhóm theo agencyId
                String agencyKey = agencyId + "_" + item.getAgency().getName();

                // Kiểm tra nếu key chưa tồn tại trong aggregatedResults thì khởi tạo
                if (!aggregatedResults.containsKey(agencyKey)) {
                    Map<String, Object> agencyData = new HashMap<>();
                    agencyData.put("agencyIdAgency", agencyId);
                    agencyData.put("agencyName", item.getAgency().getName());
                    agencyData.put("procedureReception", new ArrayList<Map<String, Object>>());
                    aggregatedResults.put(agencyKey, agencyData);
                }

                // Lấy dữ liệu gộp cho agency
                Map<String, Object> agencyData = aggregatedResults.get(agencyKey);
                List<Map<String, Object>> procedureReceptions = (List<Map<String, Object>>) agencyData.get("procedureReception");

                // Tìm kiếm nếu procedure đã tồn tại trong danh sách procedureReception
                Map<String, Object> existingProcedure = null;
                for (Map<String, Object> reception : procedureReceptions) {
                    if (reception.get("procedureName").equals(procedureName)) {
                        existingProcedure = reception;
                        break;
                    }
                }

                if (existingProcedure != null) {
                    // Nếu procedure đã tồn tại, cộng dồn giá trị slowReception và onTimeReception
                    existingProcedure.put("slowReception", (Integer) existingProcedure.get("slowReception") + (item.getSlowReceptionCondition() ? 1 : 0));
                    existingProcedure.put("onTimeReception", (Integer) existingProcedure.get("onTimeReception") + (item.getOnTimeReceptionCondition() ? 1 : 0));

                    // Cập nhật lại toTal
                    int newTotal = (Integer) existingProcedure.get("slowReception") + (Integer) existingProcedure.get("onTimeReception");
                    existingProcedure.put("toTal", newTotal);
                } else {
                    // Nếu procedureId không tồn tại, thêm mới vào danh sách
                    Map<String, Object> newReception = new HashMap<>();
                    int slowReceptionCount = item.getSlowReceptionCondition() ? 1 : 0;
                    int onTimeReceptionCount = item.getOnTimeReceptionCondition() ? 1 : 0;
                    newReception.put("slowReception", slowReceptionCount);
                    newReception.put("onTimeReception", onTimeReceptionCount);
                    newReception.put("agencyBabyIdAgency", agencyId);
                    newReception.put("agencyBabyName", item.getAgency().getName());
                    newReception.put("procedureName", procedureName);
                    newReception.put("procedureId", procedureId);

                    // Tính tổng ban đầu cho toTal
                    int total = slowReceptionCount + onTimeReceptionCount;
                    newReception.put("toTal", total);

                    // Thêm vào danh sách procedureReception
                    procedureReceptions.add(newReception);
                }
            }

            // Chuyển đổi kết quả gộp thành danh sách ReceptionReportDto
            List<ReceptionReportDto> finalResult = new ArrayList<>();
            for (Map<String, Object> item : aggregatedResults.values()) {
                ReceptionReportDto reportDto = new ReceptionReportDto();
                reportDto.setAgencyIdAgency((String) item.get("agencyIdAgency"));
                reportDto.setAgencyName((String) item.get("agencyName"));
                reportDto.setProcedureReception((List<ReceptionReportDto.ProcedureReceptionDto>) item.get("procedureReception"));
                finalResult.add(reportDto);
            }

            return finalResult;
        } catch (Exception e) {
            logger.error("error aggregateResults: ", e);
        }

        return null;
    }

    public ImportResponseDto updateDealineAcceptedDate(String fromDateString, String toDateString, Boolean ignoreExist, Boolean isTimeSheetV2) {
        Query query = new Query();
        logger.info("Begin updateDealineAcceptedDate");
        var results = new ImportResponseDto();

        try {
            // Format Date
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);

            query.addCriteria(Criteria.where("appliedDate").gte(fromDate).lte(toDate));

            if (!ignoreExist) {
                query.addCriteria(Criteria.where("deadlineAcceptedDate").exists(false));
            }

            query.addCriteria(new Criteria().andOperator(
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));

            // Find the dossiers matching the query
            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            // Prepare payload for the API request
            List<TimeSheetGenPayloadDto> payload = new ArrayList<>();
            String path = "timesheet-gen/--by-dossier-id";
            if (isTimeSheetV2){
                path = "v2/timesheet-gen/--by-dossier-id";
            }
            String url = microservice.basecatUri(path).build().toUriString();

            // Prepare payload data for API
            for (QNIETLDossier qnietlDossier : dossiers) {
                TimeSheetGenPayloadDto timeSheetGenPayloadDto = new TimeSheetGenPayloadDto();
                timeSheetGenPayloadDto.setData(new ObjectId(qnietlDossier.getId()),
                        new ObjectId(this.qniTimeSheetId),
                        0,
                        qnietlDossier.getAppliedDate(),
                        null);
                timeSheetGenPayloadDto.setCheckOffDay(true);
                timeSheetGenPayloadDto.setProcessingTimeUnit("d");
                timeSheetGenPayloadDto.setOffTime(offTime);
                timeSheetGenPayloadDto.setDuration(1.0);

                payload.add(timeSheetGenPayloadDto);
            }

            // Call external API for timesheet data
            TimesheetGenResponseDto[] responseParent = MicroserviceExchange.postJson(restTemplate, url, payload, TimesheetGenResponseDto[].class);
            List<String> errorMessages = new ArrayList<>();
            List<Pair<Query, Update>> bulkOperations = new ArrayList<>();

            // Iterate over the responses and prepare bulk updates
            for (TimesheetGenResponseDto response : responseParent) {
                if (response.getDossier() instanceof Map) {
                    Map<String, Object> dossierMap = (Map<String, Object>) response.getDossier();
                    if (dossierMap.containsKey("id")) {
                        String id = (String) dossierMap.get("id");

                        // Directly get the corresponding dossier to update
                        QNIETLDossier dossierToUpdate = dossiers.stream()
                                .filter(d -> id.equals(d.getId()))
                                .findFirst()
                                .orElse(null);

                        // If dossier found, prepare the update
                        if (dossierToUpdate != null) {
                            // Create filter and update using Query and Update objects
                            Query filter = new Query(Criteria.where("id").is(id));
                            Update update = new Update().set("deadlineAcceptedDate", response.getDue());

                            // Add the Pair to the bulk operations list
                            bulkOperations.add(Pair.of(filter, update));
                        } else {
                            errorMessages.add(String.format("Update error: Dossier with ID %s not found, deadlineAcceptedDate: %s", id, response.getDue()));
                        }
                    }
                }
            }

            // Ensure that there are operations to execute
            if (!bulkOperations.isEmpty()) {
                // Use BulkOperations to execute the batch update
                mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, QNIETLDossier.class)
                        .updateOne(bulkOperations)  // Perform bulk update
                        .execute();  // Execute the operation
            }

            // Set the results
            results.setInsertedRows(String.valueOf(bulkOperations.size()));
            results.setErrorMessages(errorMessages);

            logger.info("End updateDealineAcceptedDate");

        } catch (Exception e) {
            logger.error("Error in updateDealineAcceptedDate: " + e.getMessage(), e);
        }

        return results;
    }

    public ImportResponseDto updateDeadlineAcceptedDateV2(String fromDateString, String toDateString, Boolean ignoreExist, Boolean isTimeSheetV2) {
        Query query = new Query();
        logger.info("Begin updateDealineAcceptedDate");
        var results = new ImportResponseDto();

        try {
            // Format Date
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);

            query.addCriteria(Criteria.where("appliedDate").gte(fromDate).lte(toDate));

            if (!ignoreExist) {
                query.addCriteria(Criteria.where("deadlineAcceptedDate").exists(false));
            }

            query.addCriteria(new Criteria().andOperator(
                    new Criteria().orOperator(
                            Criteria.where("dossierStatus._id").ne(6),
                            new Criteria().andOperator(
                                    Criteria.where("dossierStatus._id").is(6),
                                    Criteria.where("withdrawDate").ne(null)
                            )
                    ),
                    Criteria.where("appointmentDate").ne(null)
            ));

            // Find the dossiers matching the query
            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            // Prepare payload for the API request
            List<TimeSheetGenPayloadDto> payload = new ArrayList<>();
            String path = "timesheet-gen/--by-dossier-id";
            if (isTimeSheetV2){
                path = "v2/timesheet-gen/--by-dossier-id";
            }
            String url = microservice.basecatUri(path).build().toUriString();

            // Prepare payload data for API
            for (QNIETLDossier qnietlDossier : dossiers) {
                TimeSheetGenPayloadDto timeSheetGenPayloadDto = new TimeSheetGenPayloadDto();
                timeSheetGenPayloadDto.setData(new ObjectId(qnietlDossier.getId()),
                        new ObjectId(qniTimeSheetId),
                        0,
                        this.getTimeSheetStartDate(qnietlDossier),
                        null);
                timeSheetGenPayloadDto.setCheckOffDay(true);
                timeSheetGenPayloadDto.setProcessingTimeUnit("d");
                timeSheetGenPayloadDto.setOffTime(offTime);
                timeSheetGenPayloadDto.setDuration(1.0);

                payload.add(timeSheetGenPayloadDto);
            }

            // Call external API for timesheet data
            TimesheetGenResponseDto[] responseParent = MicroserviceExchange.postJson(restTemplate, url, payload, TimesheetGenResponseDto[].class);
            List<String> errorMessages = new ArrayList<>();
            List<Pair<Query, Update>> bulkOperations = new ArrayList<>();

            // Iterate over the responses and prepare bulk updates
            for (TimesheetGenResponseDto response : responseParent) {
                if (response.getDossier() instanceof Map) {
                    Map<String, Object> dossierMap = (Map<String, Object>) response.getDossier();
                    if (dossierMap.containsKey("id")) {
                        String id = (String) dossierMap.get("id");

                        // Directly get the corresponding dossier to update
                        QNIETLDossier dossierToUpdate = dossiers.stream()
                                .filter(d -> id.equals(d.getId()))
                                .findFirst()
                                .orElse(null);

                        // If dossier found, prepare the update
                        if (dossierToUpdate != null) {
                            // Create filter and update using Query and Update objects
                            Query filter = new Query(Criteria.where("id").is(id));
                            Update update = new Update().set("deadlineAcceptedDate", response.getDue());

                            // Add the Pair to the bulk operations list
                            bulkOperations.add(Pair.of(filter, update));
                        } else {
                            errorMessages.add(String.format("Update error: Dossier with ID %s not found, deadlineAcceptedDate: %s", id, response.getDue()));
                        }
                    }
                }
            }

            // Ensure that there are operations to execute
            if (!bulkOperations.isEmpty()) {
                // Use BulkOperations to execute the batch update
                mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, QNIETLDossier.class)
                        .updateOne(bulkOperations)  // Perform bulk update
                        .execute();  // Execute the operation
            }

            // Set the results
            results.setInsertedRows(String.valueOf(bulkOperations.size()));
            results.setErrorMessages(errorMessages);

            logger.info("End updateDealineAcceptedDate");

        } catch (Exception e) {
            logger.error("Error in updateDealineAcceptedDate: " + e.getMessage(), e);
        }

        return results;
    }

    private Date getTimeSheetStartDate(QNIETLDossier dossier){
        try{
            String url = microservice.messengerUri("/qni-comment/--list-comment/" + dossier.getId()).toUriString();
            String rawResponse = MicroserviceExchange.get(restTemplate, url, String.class);
            JsonArray comments = gson.fromJson(rawResponse, JsonArray.class);
            for (int i = comments.size() - 1; i >= 0; i--) {
                JsonObject comment = comments.get(i).getAsJsonObject();
                if (comment.get("content").getAsString().toLowerCase().contains("công dân cập nhập hồ sơ: hồ sơ đã được bổ sung")){
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
                    ZonedDateTime zonedDateTime = ZonedDateTime.parse(comment.get("createdDate").getAsString(), formatter);
                    return Date.from(zonedDateTime.toInstant());
                }
            }
        } catch (Exception e) {
            logger.error("Error in getComment: " + e.getMessage(), e);
            return dossier.getAppliedDate();
        }
        return dossier.getAppliedDate();
    }

    public ImportResponseDto updateDeadlineListDossier(UpdateDeadlineDossierDto dto, Boolean isSleep, Integer sleepTime, Boolean isTimeSheetV2) {
        logger.info("Begin updateDeadlineListDossier");
        ImportResponseDto response = new ImportResponseDto();
        int successRow = 0;
        List<String> error = new ArrayList<>();

        try{
            if (isSleep){
                sleepTime = sleepTime > this.qniMaxSleepTime ? this.qniMaxSleepTime : sleepTime;
                TimeUnit.SECONDS.sleep(sleepTime); // Delay kafka receive message
            }
            JsonArray timesheetGenResponse = this.checkTimeSheetDossier(dto, isTimeSheetV2);
            if (timesheetGenResponse == null){
                error.add("Error at post timesheet");
                response.setErrorMessages(error);
                return response;
            }
            if (timesheetGenResponse.size() > 0){
                try{
                    JsonObject element = timesheetGenResponse.get(0).getAsJsonObject();
                    QNIETLDossier qniEtlDossier = mongoTemplate.findById(new ObjectId(dto.getId()), QNIETLDossier.class);
                    if (qniEtlDossier != null && qniEtlDossier.getAcceptedDate() == null) {
                        qniEtlDossier.setDeadlineAcceptedDate(parseStrToDate(element.get("due").getAsString()));
                        mongoTemplate.save(qniEtlDossier);
                        successRow++;
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage());
                    error.add(String.format("Error %s at: %s", e.getMessage(), dto.getId()));
                }
            }
        } catch (Exception e) {
           logger.error("Error in updateDeadlineListDossier: " + e.getMessage(), e);
           error.add("Error in updateDeadlineListDossier: " + e.getMessage());
        }

        response.setSuccessRows(successRow);
        response.setErrorMessages(error);
        logger.info("end updateDeadlineListDossier");
        return response;
    }

    private Date parseStrToDate(String strDate){
        if (strDate == null){
            return null;
        }
        try{
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(strDate, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
            ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(java.time.ZoneId.of("UTC"));
            return Date.from(utcDateTime.toInstant());
        } catch (Exception e) {
            logger.error("parseStrToDate" + e.getMessage());
            return null;
        }
    }

    private JsonArray checkTimeSheetDossier (UpdateDeadlineDossierDto dto, Boolean isTimeSheetV2) {
        try{
            List<TimeSheetGenPayloadDto> payload = new ArrayList<>();
            String path = "timesheet-gen/--by-dossier-id";
            if(isTimeSheetV2){
                path = "v2/timesheet-gen/--by-dossier-id";
            }
            String url = microservice.basecatUri(path).build().toUriString();

            // Prepare payload data for API

            TimeSheetGenPayloadDto timeSheetGenPayloadDto = new TimeSheetGenPayloadDto();
            timeSheetGenPayloadDto.setData(new ObjectId(dto.getId()),
                    new ObjectId(qniTimeSheetId),
                    0,
                    dto.getDeadlineAcceptedDate(),
                    null);
            timeSheetGenPayloadDto.setCheckOffDay(true);
            timeSheetGenPayloadDto.setProcessingTimeUnit("d");
            timeSheetGenPayloadDto.setOffTime(offTime);
            timeSheetGenPayloadDto.setDuration(1.0);

            payload.add(timeSheetGenPayloadDto);

            // Call external API for timesheet data
            String rawResponse = MicroserviceExchange.postJson(qniRestTemplate.getRestTemplate(), url, payload, String.class);
            return gson.fromJson(rawResponse, JsonArray.class);
        }catch (Exception e) {
            logger.error("Error in checkTimeSheetDossier: " + e.getMessage(), e);
            return null;
        }
    }

    public PageImpl<DetailReceptionDossierDto> getSlowReceptionDetail(List<String> agencyIds, String fromDateString, String toDateString, String procedureName, int type, Pageable pageable) {
        logger.info("Begin getSlowReceptionDetail");
        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);
            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());
            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);
            Criteria criteria;
            switch (type) {
                case 4: // tổng số hồ sơ tiếp nhận
                    criteria = this.buildGetTotalCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 5: // Số hồ sơ tiếp nhận đúng hạn
                    criteria = this.buildOntimeReceptionCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 6: // Số hồ sơ tiếp nhận trễ hạn
                    criteria = this.buildSlowReceptionCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                default:
                    criteria = this.buildGetTotalCriteria(fromDate, toDate, agencyObjectIds);
            }

            if (procedureName != null){
                criteria.and("procedure.name").is(procedureName);
            }

            Query query = new Query().addCriteria(criteria).with(pageable);
            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            List<DetailReceptionDossierDto> qniEtlDossiers = IntStream.range(0, dossiers.size())
                    .mapToObj(i -> this.mapToDossierResponse(dossiers.get(i), i))
                    .collect(Collectors.toList());

            long count = mongoTemplate.count(query.skip(-1).limit(-1), QNIETLDossier.class);

            return new PageImpl<>(qniEtlDossiers, pageable, count);

        } catch (Exception e) {
            logger.error("Error getSlowReceptionDetail: " + e.getMessage());
        }

        logger.info("End getSlowReceptionDetail");
        return null;
    }

    private DetailReceptionDossierDto mapToDossierResponse(QNIETLDossier qnietlDossier, int index){
        try{
            TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            df.setTimeZone(timezone);
            DetailReceptionDossierDto result = new DetailReceptionDossierDto();

            result.setId(qnietlDossier.getId());

            result.setNo(index + 1);

            result.setDossierCode(qnietlDossier.getCode());

            result.setProcedureName(qnietlDossier.getProcedure().getName());

            result.setSectorName(qnietlDossier.getSector().getName());

            result.setNoiDungYeuCauGiaiQuyet(qnietlDossier.getApplicant().getNoiDungYeuCauGiaiQuyet());

            result.setAppliedDate(df.format(qnietlDossier.getAppliedDate()));

            if (qnietlDossier.getAcceptedDate() != null) {
                result.setAcceptedDate(df.format(qnietlDossier.getAcceptedDate()));
            }

            if (qnietlDossier.getAppointmentDate() != null) {
                result.setAppointmentDate(df.format(qnietlDossier.getAppointmentDate()));
            }

            if(qnietlDossier.getCompletedDate() != null){
                result.setCompletedDate(df.format(qnietlDossier.getCompletedDate()));
            }

            result.setApplicantOwnerFullName(qnietlDossier.getApplicant().getOwnerFullName());

            result.setApplicantPhoneNumber(qnietlDossier.getApplicant().getPhoneNumber());

            result.setAgencyName(qnietlDossier.getAgency().getParent().getName());

            result.setApplyMethod(qnietlDossier.getApplyMethod().getName());

            if (qnietlDossier.getAcceptedDate() == null){
                result.setAppliedDue(calculateDaysBetween(qnietlDossier.getAppliedDate(), new Date()));
            }else {
                result.setAppliedDue(calculateDaysBetween(qnietlDossier.getAppliedDate(), qnietlDossier.getAcceptedDate()));
            }
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                result.setHideSecurityInformation(result.getApplicantOwnerFullName());
            }
                return result;
        }catch (Exception e){
            logger.error("Error with code: {}", qnietlDossier.getCode());
            return null;
        }
    }

    private Criteria buildSlowReceptionCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        Instant utcNow = Instant.now();
        Date currentUtcDate = Date.from(utcNow);
        return new Criteria().andOperator(
                Criteria.where("appliedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null),
                Criteria.where("deadlineAcceptedDate").ne(null),
                new Criteria().orOperator(
                        new Criteria().andOperator(
                                Criteria.where("acceptedDate").exists(true),
                                Criteria.where("$expr")
                                        .is(new Document("$gt", Arrays.asList("$acceptedDate", "$deadlineAcceptedDate")))
                        ),
                        new Criteria().andOperator(
                                Criteria.where("acceptedDate").exists(false),
                                Criteria.where("deadlineAcceptedDate").lte(currentUtcDate)
                        )
                )
        );
    }

    private Criteria buildOntimeReceptionCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        Instant utcNow = Instant.now();
        Date currentUtcDate = Date.from(utcNow);
        return new Criteria().andOperator(
                Criteria.where("appliedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null),
                new Criteria().orOperator(
                        new Criteria().andOperator(
                                Criteria.where("acceptedDate").exists(true),
                                Criteria.where("$expr")
                                        .is(new Document("$lt", Arrays.asList("$acceptedDate", "$deadlineAcceptedDate")))
                        ),
                        new Criteria().andOperator(
                                Criteria.where("acceptedDate").exists(false),
                                Criteria.where("deadlineAcceptedDate").gte(currentUtcDate)
                        ),
                        Criteria.where("deadlineAcceptedDate").exists(false),
                        Criteria.where("deadlineAcceptedDate").is(null)
                )
        );
    }

    private Criteria buildGetTotalCriteria(Date fromDate, Date toDate, List<ObjectId> agencyObjectIds) {
        return new Criteria().andOperator(
                Criteria.where("appliedDate").gte(fromDate).lte(toDate),
                Criteria.where("agency._id").in(agencyObjectIds),
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null)
        );
    }

    public ResponseEntity<Object> exportDossierReceptionDetail(String fromDate, String toDate, List<String> agencyIds, int type, String procedureName) {
        logger.info("Begin exportDossierReceptionDetail");
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfTimestamp = new SimpleDateFormat("yyyyMMddHHmm");
        DateFormat dfCurrentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dfTimestamp.setTimeZone(timezone);
        dfCurrentDate.setTimeZone(timezone);
        String timestamp = dfTimestamp.format(date);
        String currentDate = dfCurrentDate.format(date);
        String fromDateReport = fromDate.substring(8, 10) + "/" + fromDate.substring(5, 7) + "/" + fromDate.substring(0, 4);
        String toDateReport = toDate.substring(8, 10) + "/" + toDate.substring(5, 7) + "/" + toDate.substring(0, 4);
        String filename = timestamp + "-danh-sach-ho-so.xlsx";
        byte[] resource = new byte[0];
        try {
            InputStream is = resourceTemplateDossierStatisticAssigneeQNIV2.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            var itemDtos = getDataExcelDto(fromDate, toDate, agencyIds, type, procedureName);

            context.putVar("textBanner", translator.toLocale("lang.word.gov"));
            context.putVar("subtextBanner", translator.toLocale("lang.word.ifh"));

            context.putVar("title", translator.toLocale("lang.word.dossier-statistic-title"));
            context.putVar("subTitle", translator.toLocale("lang.word.dossier-statistic-fromdate-todate", new String[]{fromDateReport, toDateReport}));
            context.putVar("currentDate", translator.toLocale("lang.word.dossier-statistic-current-date", new String[]{currentDate}));
            context.putVar("no", translator.toLocale("lang.word.no"));
            context.putVar("dossierCode", translator.toLocale("lang.word.dossier-statistic-dossier-code"));
            context.putVar("procedureName", translator.toLocale("lang.word.dossier-statistic-procedure-name"));
            context.putVar("sectorName", translator.toLocale("lang.word.dossier-statistic-sector-name"));
            context.putVar("noiDungYeuCauGiaiQuyet", translator.toLocale("lang.word.dossier-statistic-noidungyeucaugiaiquyet"));
            context.putVar("appliedDate", translator.toLocale("lang.word.dossier-statistic-applied-date"));
            context.putVar("acceptedDate", translator.toLocale("lang.word.dossier-statistic-accepted-date"));
            context.putVar("appointmentDate", translator.toLocale("lang.word.dossier-statistic-appointment-date"));
            context.putVar("completedDate", translator.toLocale("lang.word.dossier-statistic-completed-date"));
            context.putVar("applicantOwnerFullName", translator.toLocale("lang.word.dossier-statistic-applicant-ownerfullname"));
            context.putVar("applicantPhoneNumber", translator.toLocale("lang.word.dossier-statistic-applicant-phonenumber"));
            context.putVar("agencyName", translator.toLocale("lang.word.dossier-statistic-agency-name"));
            context.putVar("appliedDue", translator.toLocale("lang.word.dossier-statistic-applied-due"));
            context.putVar("applyMethod", translator.toLocale("lang.word.dossier-statistic-applied-method"));
            context.putVar("writer", translator.toLocale("lang.word.dossier-statistic-writer"));
            context.putVar("reporter", translator.toLocale("lang.word.dossier-statistic-reporter"));
            context.putVar("itemDtos", itemDtos);

            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            resource = outputStream.toByteArray();
        } catch (Exception ex) {
            logger.info("exportDossierStatistic012020Detail error:" + ex.getMessage());
        }
        logger.info("End exportDossierReceptionDetail");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(resource);
    }

    public List<DetailReceptionDossierDto> getDataExcelDto(String fromDateString,
                                                                   String toDateString,
                                                                   List<String> agencyIds,
                                                                   Integer type,
                                                                   String procedureName) {
        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);
            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);
            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }
            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());
            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);
            Criteria criteria;
            switch (type) {
                case 4: // tổng số tiếp nhận
                    criteria = this.buildGetTotalCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 5: // tếp nhận đúng hạn
                    criteria = this.buildOntimeReceptionCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                case 6: // tiếp nhận chậm
                    criteria = this.buildSlowReceptionCriteria(fromDate, toDate, agencyObjectIds);
                    break;
                default: // tổng số tiếp nhận
                    criteria = this.buildGetTotalCriteria(fromDate, toDate, agencyObjectIds);
            }

            if (procedureName != null){
                criteria.and("procedure.name").is(procedureName);
            }

            Query query = new Query().addCriteria(criteria);

            List<QNIETLDossier> dossiers = mongoTemplate.find(query, QNIETLDossier.class);

            return IntStream.range(0, dossiers.size())
                    .mapToObj(i -> this.mapToDossierResponse(dossiers.get(i), i))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error export getPaymentReportDetail: " + e.getMessage());
        }
        return null;
    }

    public String calculateDaysBetween(Date date1, Date date2) {
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        String subText = translator.toLocale("lang.word.dossier-statistic-due-date");
        long days = ChronoUnit.DAYS.between(localDate1, localDate2);
        return String.format("%d %s", days, subText);
    }
}
