package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.File;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostReportDto  implements Serializable {

    private File file;

    private String keyword;

    private String reportType;

    private String reportName;

    private String userAgencyId;

    private Object nation;

    private Object province;

    @JsonProperty("district")
    private Object district;

    private Object ward;

    private Object sector;

    private Object procedure;

    private Object receivingService;

    private Object applyMethod;

    private Object sort;

    private String headerArray;

    private Object agencyName;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date fromDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date toDate;
}
