/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import java.util.Date;
import java.util.Objects;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.AppStatistics;
import vn.vnpt.digo.reporter.dto.AppStatisticsOutputDto;
import vn.vnpt.digo.reporter.repository.AppStatisticsRepository;
import vn.vnpt.digo.reporter.util.Context;


/**
 *
 * <AUTHOR>
 */
@Service
public class AppStatisticsService {
    
    @Autowired
    private AppStatisticsRepository appStatisticsRepository;
    
    public boolean insertOrUpdateAppStatistics(String type) {
        try {
            ObjectId deploymentId = Context.getDeploymentId();
            if (deploymentId != null) {
                AppStatistics appStatistics = new AppStatistics();
                appStatistics = appStatisticsRepository.findByDeploymentId(deploymentId);
                if (Objects.equals(appStatistics, null)) {
                    appStatistics = new AppStatistics();
                    appStatistics.setAndroidDownloadAmount(0);
                    appStatistics.setIosDownloadAmount(0);
                    appStatistics.setUpdatedDate(new Date());
                    appStatistics.setDeploymentId(deploymentId);
                    appStatisticsRepository.save(appStatistics);
                } else {
                    if (type.equals("0")) {
                        appStatistics.increaseAndroidDownload();
                    } else if (type.equals("1")) {
                        appStatistics.increaseIosDownload();
                    } else {
                        return false;
                    }
                    appStatisticsRepository.save(appStatistics);
                }
            } 
        } catch (Exception e) {
            return false;
        }
        return true;
    }
    
    @Cacheable("appStatisticsAmount")
    public AppStatisticsOutputDto getAmount() {
        ObjectId deploymentId = Context.getDeploymentId();
        AppStatisticsOutputDto appStatisticsOutputDto = new AppStatisticsOutputDto(0, 0);
        if (deploymentId != null) {
            AppStatistics appStatistics = appStatisticsRepository.findByDeploymentId(deploymentId);
            if (!Objects.equals(appStatistics, null)) {
                appStatisticsOutputDto.setAndroidDownloadAmount(appStatistics.getAndroidDownloadAmount());
                appStatisticsOutputDto.setIosDownloadAmount(appStatistics.getIosDownloadAmount());
            }
        }

        return appStatisticsOutputDto;
    }
}
