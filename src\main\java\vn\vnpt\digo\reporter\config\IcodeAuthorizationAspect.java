/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.config;

import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.google.gson.Gson;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;

/**
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class IcodeAuthorizationAspect {

    @Autowired
    IcodeAuthorizationImpl authBean;
    
    @Autowired
    private Environment env;

    @Autowired
    Microservice microservice;
    
    @Value("${vnpt.permission.api.allow}")
    private List<String> allowApis;
    
    @Value("${vnpt.permission.credentials.allow}")
    private List<String> credentialAllowApis;


    @Value("${vnpt.permission.interceptor.enable:false}")
    private boolean enableInterceptor;

    @Value("${vnpt.permission.rbac.enable:false}")
    private boolean enableRbac;

    @Value("${digo.permission.rbac.secret:VnptRbac@2022}")
    private String rbacSecret;

    @Before("pointcutForIcodeAnnotation(icodeAuthorize)")
    public void methodJoinPoint(JoinPoint joinPoint, IcodeAuthorize icodeAuthorize){
        if (enableInterceptor == true) {
            return;
        }
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        IcodeAuthorize annotation = method.getAnnotation(IcodeAuthorize.class);

        if(Objects.nonNull(annotation)){
            // Check authentication base on grant-type
            String preferredUsername = Context.getJwtParameterValue("preferred_username");
            if(Objects.nonNull(preferredUsername) && preferredUsername.startsWith("service-account")){
                // token was created with credential grant-type
                if(!this.apiIsAllow(methodSignature,2)){
                    this.checkAnnotation(annotation);
                }
            } else if(!this.apiIsAllow(methodSignature,0)){
                this.checkAnnotation(annotation);
            } else if(!this.checkRbac(methodSignature)){
                this.checkAnnotation(annotation);
            } 
        }
    } 

    @Pointcut("@annotation(icodeAuthorize) || @within(icodeAuthorize)")
    public void pointcutForIcodeAnnotation(IcodeAuthorize icodeAuthorize) {
        // Do nothing.
    }

    @Around("pointcutForIcodeAnnotation(icodeAuthorize)")
    public Object classJoinPoint(ProceedingJoinPoint pjp, IcodeAuthorize icodeAuthorize) throws Throwable {
        if (enableInterceptor == true) {
            return pjp.proceed();
        }

        if (Objects.isNull(icodeAuthorize)) {
            return pjp.proceed();
        }
        
        // Check authentication base on grant-type
        String preferredUsername = Context.getJwtParameterValue("preferred_username");
        if(Objects.nonNull(preferredUsername) && preferredUsername.startsWith("service-account")){
            if(this.apiIsAllow((MethodSignature)pjp.getSignature(),2) ){
                return pjp.proceed();
            }
        } else if(this.apiIsAllow((MethodSignature)pjp.getSignature(),0)){
            return pjp.proceed();
        } else if(this.checkRbac((MethodSignature)pjp.getSignature())){
            return pjp.proceed();
        } 

        this.checkAnnotation(icodeAuthorize);
        return pjp.proceed();
    }
    
    boolean apiIsAllow(MethodSignature signature, int strategy) {
        // strategy
        // 0: check normal allowed APIs
        // 1: check credential allowed APIs
        // 2: check either 0 or 1 is true
        // 3: check both 0 and 1 are true
        try {
            String path = "";
            //Get collection of api
            Class reqController = signature.getDeclaringType();
            RequestMapping collection = (RequestMapping) reqController.getAnnotation(RequestMapping.class);

            //Get path of api
            Method method = signature.getMethod();
            GetMapping getPath = method.getAnnotation(GetMapping.class);
            if (Objects.nonNull(getPath)) {
                // get get path
                path = "GET:";
                for (String i : collection.value()) {
                    path += i;
                }
                for (String i : getPath.value()) {
                    path += i;
                }
            } else {
                PostMapping postPath = method.getAnnotation(PostMapping.class);
                if (Objects.nonNull(postPath)) {
                    //get post path
                    path = "POST:";
                    for (String i : collection.value()) {
                        path += i;
                    }
                    for (String i : postPath.value()) {
                        path += i;
                    }
                } else {
                    PutMapping putPath = method.getAnnotation(PutMapping.class);
                    if (Objects.nonNull(putPath)) {
                        //get put path
                        path = "PUT:";
                        for (String i : collection.value()) {
                            path += i;
                        }
                        for (String i : putPath.value()) {
                            path += i;
                        }
                    } else {
                        DeleteMapping deletePath = method.getAnnotation(DeleteMapping.class);
                        if (Objects.nonNull(deletePath)) {
                            //get delete path
                            path = "DELETE:";
                            for (String i : collection.value()) {
                                path += i;
                            }
                            for (String i : deletePath.value()) {
                                path += i;
                            }
                        }
                    }
                }
            }
            switch(strategy){
                case (1):{
                    return (credentialAllowApis.contains(path));
                }
                case (2):{

                    return (credentialAllowApis.contains(path) || allowApis.contains(path));
                }
//                case (3):{
//                    return (credentialAllowApis.contains(path) && allowApis.contains(path));
//                }
                default:{
                    return allowApis.contains(path);
                }
            }
        } catch (Exception e) {
            return false;
        }
    }

    private void checkAnnotation(IcodeAuthorize annotation){
        if (annotation.attribute().isBlank()) {
            if (!authBean.hasPrivilege(annotation.value(), annotation.option())) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        } else {
            if (!authBean.hasPrivilege(annotation.value(), annotation.attribute(), annotation.option())) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
    }
    
    boolean checkRbac(MethodSignature signature) {
        //check tham số enable thi check ko thì return false
        if( enableRbac == false ){
            return false;
        }
        try {
            // List<String> role = new ArrayList<>();
            boolean hasRole = false;
            String path = "";
            //Get collection of api
            Class reqController = signature.getDeclaringType();
            RequestMapping collection = (RequestMapping) reqController.getAnnotation(RequestMapping.class);

            //Get path of api
            Method method = signature.getMethod();
            GetMapping getPath = method.getAnnotation(GetMapping.class);
            if (Objects.nonNull(getPath)) {
                // get get path
                // path = "GET:";
                for (String i : collection.value()) {
                    path += i;
                }
                for (String i : getPath.value()) {
                    path += i;
                }
                hasRole = this.getRoleByApi(HttpMethod.GET, path);
            } else {
                PostMapping postPath = method.getAnnotation(PostMapping.class);
                if (Objects.nonNull(postPath)) {
                    //get post path
                    // path = "POST:";
                    for (String i : collection.value()) {
                        path += i;
                    }
                    for (String i : postPath.value()) {
                        path += i;
                    }
                    hasRole = this.getRoleByApi(HttpMethod.POST, path);
                } else {
                    PutMapping putPath = method.getAnnotation(PutMapping.class);
                    if (Objects.nonNull(putPath)) {
                        //get put path
                        // path = "PUT:";
                        for (String i : collection.value()) {
                            path += i;
                        }
                        for (String i : putPath.value()) {
                            path += i;
                        }
                        hasRole = this.getRoleByApi(HttpMethod.PUT, path);
                    } else {
                        DeleteMapping deletePath = method.getAnnotation(DeleteMapping.class);
                        if (Objects.nonNull(deletePath)) {
                            //get delete path
                            // path = "DELETE:";
                            for (String i : collection.value()) {
                                path += i;
                            }
                            for (String i : deletePath.value()) {
                                path += i;
                            }
                            hasRole = this.getRoleByApi(HttpMethod.DELETE, path);
                        }
                    }
                }
            }
            return hasRole;
        } catch(DigoHttpException digoe){
            throw digoe;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean getRoleByApi(HttpMethod method, String path){
        try{
            String rolebaseApiUrl = microservice.sysmanUri("/rolebase-api/--by-api").toUriString();
            InputCheckApiIsAllowsDto body = new InputCheckApiIsAllowsDto(rbacSecret, "reporter", method, path);
            String[] role = MicroserviceExchange.post(rolebaseApiUrl, body, String[].class);
            if(role.length > 0){
                try{
                    String realmAccess = Context.getJwtParameterValue("realm_access");
                    Gson gson = new Gson();
                    RealmRolesDto realmRoles = gson.fromJson(realmAccess, RealmRolesDto.class);
                    List<String> lstRole = Arrays.asList(role);
                    boolean roleChecked = lstRole.stream().anyMatch(element -> realmRoles.getRoles().contains(element));
                    if (roleChecked) {
                        return roleChecked;
                    } else {
                        throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                    }
                } catch (Exception e){
                    throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                }
            } else {
                return false;
            }
        } catch(DigoHttpException digoe){
            throw digoe;
        } catch(Exception e){
            return false;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InputCheckApiIsAllowsDto implements Serializable{
        
        private String secret;

        private String microservice;

        private HttpMethod method;

        private String path;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RealmRolesDto implements Serializable{
        private List<String> roles;
    }
}