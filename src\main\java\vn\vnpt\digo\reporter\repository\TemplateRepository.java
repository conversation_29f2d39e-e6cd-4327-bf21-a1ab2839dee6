/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.digo.reporter.document.Template;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface TemplateRepository extends MongoRepository<Template, ObjectId> {

    @Query(value = "{$and: ["
            + "{$or: [{'name': {$regex : :#{#keyword}, $options: 'i'}},{'type.name': {$regex : :#{#keyword}, $options: 'i'}} ]},"
            + "?#{ [1] == null ? { $where : 'true'} : { 'subsystem.id' : [1] }},"
            + "?#{ [2] == null ? { $where : 'true'} : { 'type.id' : [2] }},"
            + "{'deploymentId' : ?3 }]}")
    Page<Template> searchPage(
            @Param("keyword") String keyword, 
            @Param("subsystem-id") ObjectId subsystemId, 
            @Param("type-id") ObjectId typeId, 
            ObjectId deploymentId, 
            Pageable pageable);

    @Query(value = "{$and: ["
            + "{$or: [{'name': {$regex : :#{#keyword}, $options: 'i'}},{'type.name': {$regex : :#{#keyword}, $options: 'i'}} ]},"
            + "?#{ [1] == null ? { $where : 'true'} : { 'subsystem.id' : [1] }},"
            + "?#{ [2] == null ? { $where : 'true'} : { 'type.id' : [2] }},"
            + "{'deploymentId' : ?3 }]}")
    Slice<Template> searchSlice(
            @Param("keyword") String keyword, 
            @Param("subsystem-id") ObjectId subsystemId, 
            @Param("type-id") ObjectId typeId, 
            ObjectId deploymentId, 
            Pageable pageable);

    @Query(value = "{$and: ["
            + "{$or: [{'name': {$regex : :#{#keyword}, $options: 'i'}},{'type.name': {$regex : :#{#keyword}, $options: 'i'}} ]},"
            + "?#{ [1] == null ? { $where : 'true'} : { 'subsystem.id' : [1] }},"
            + "?#{ [2] == null ? { $where : 'true'} : { 'type.id' : [2] }},"
            + "{'deploymentId' : ?3 }]}")
    List<Template> searchAll(
            @Param("keyword") String keyword,
            @Param("subsystem-id") ObjectId subsystemId,
            @Param("type-id") ObjectId typeId,
            ObjectId deploymentId);

    @Query(value = "{'_id': ?0}")
    Template findOneById(@Param("id") ObjectId id);

    int deleteTemplateById(@Param("id") ObjectId id);

}
