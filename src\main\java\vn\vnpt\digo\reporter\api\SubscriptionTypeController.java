/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.SubscriptionTypeDto;
import vn.vnpt.digo.reporter.service.SubscriptionTypeService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subscription-type")
@IcodeAuthorize("vnpt.permission.subscriptiontype")
public class SubscriptionTypeController {

    @Autowired
    private SubscriptionTypeService subscriptionTypeService;

    Logger logger = LoggerFactory.getLogger(AgencyController.class);
    
    //Digo 2554   
    @GetMapping("")
    public List<SubscriptionTypeDto> getListSubscriptionType(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<SubscriptionTypeDto> listSubscriptionType = subscriptionTypeService.getListSubscriptionType();
        logger.info(listSubscriptionType.size() + "");
        return listSubscriptionType;
    }
}
