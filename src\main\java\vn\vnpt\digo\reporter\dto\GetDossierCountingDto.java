package vn.vnpt.digo.reporter.dto;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.pojo.DossierCountingData;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetDossierCountingDto implements Serializable {

  private DossierCountingData countingData;

  @JsonProperty("agency")
  private DossierAgencyDto agency;

  @JsonProperty("previousPeriod")
  private long previousPeriod;
}
