package vn.vnpt.digo.reporter.pojo;


import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor

public class DossierCountingData  implements Serializable {


  @JsonProperty("received")
  private long received;  // da tiep nhan

  @JsonProperty("inProgress")
  private long inProgress; // dang xu ly

  @JsonProperty("inProgressAndOnTime")
  private long inProgressAndOnTime; // hs chua den han, dang xu ly

  @JsonProperty("inProgressAndOutOfDue")
  private long inProgressAndOutOfDue; // hs đang xử lý và quá hạn

  @JsonProperty("completed")
  private long completed;  // da hoan thanh (cả trể và ko trể)

  @JsonProperty("completedOnTime")
  private long completedOnTime; // hs hoàn thành dung han

  @JsonProperty("completedEarly")
  private long completedEarly; // hs hoàn thành som han

  @JsonProperty("completedOutOfDue")
  private long completedOutOfDue; // hs hoàn thành quá hạn

  @JsonProperty("onlineReceived")
  private long onlineReceived; // tiếp nhận trực tuyến

  @JsonProperty("directReceived")
  private long directReceived; // tiếp nhận trực tiếp

  @JsonProperty("cancelled")
  private long cancelled;

  @JsonProperty("suspended")
  private long suspended;

}
