package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostDataLogToKafkaDto implements Serializable {

    private Integer groupId;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId itemId;

    private User user;

    private Integer type;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    private List<Action> action;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class User {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Action {

        private String fieldNameRbk;
        private String originalValue;
        private String newValue;
    }
}
