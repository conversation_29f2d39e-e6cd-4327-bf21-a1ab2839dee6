package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.qni.SampleDataReportDto;
import vn.vnpt.digo.reporter.service.SampleDataReportQNIService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/sample-report-qni")
@IcodeAuthorize("vnpt.permission.generalreportqni")
public class SampleDataReportQNIController {
    Logger logger = LoggerFactory.getLogger(SampleDataReportQNIController.class);
    @Autowired
    private SampleDataReportQNIService sampleDataReportQNIService;

    @GetMapping()
    public SampleDataReportDto getSampleDataReport(
            HttpServletRequest request,
            @RequestParam(value = "agency-id", required = true) String agencyId
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return sampleDataReportQNIService.getSampleDataReport(agencyId);
    }

    @PostMapping()
    public AffectedRowsDto addSampleData(
            HttpServletRequest request,
            @RequestParam(value = "agency-level", required = true) String agencyLevelId,
            @RequestBody List<SampleDataReportDto> listSampleData) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return sampleDataReportQNIService.addSampleData(listSampleData, agencyLevelId);
    }

    @DeleteMapping()
    public long deleteSampleData(
            HttpServletRequest request,
            @RequestParam(value = "agency-level", required = true) String agencyLevelId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return sampleDataReportQNIService.deleteSampleData(agencyLevelId);
    }

    @GetMapping("/--agency-id")
    public Map<String, String> getAgencyLevelId(
            HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return sampleDataReportQNIService.getAgencyLevelId();
    }
}
