package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.DossierSynthesisData;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierSynthesisDTO {
    private String id;

    private Integer year;

    private Integer month;

    private Integer day;

    @JsonProperty("agency")
    private DossierAgencyDto agency;
    @JsonProperty("sector")
    private DossierSectorDto sector;

    private DossierSynthesisData synthesisData;

//  private ArrayList<DossierCountingDto> dossierDetail;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date synthesisDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private List<String> completedEarlyList = new ArrayList<>();
    private List<String> completedOntimeList = new ArrayList<>();
    private List<String> completedLatelyList = new ArrayList<>();
    private List<String> inprogressEarlyList = new ArrayList<>();
    private List<String> inprogressLatelyList = new ArrayList<>();
    private List<String> onlineReceivingList = new ArrayList<>();
    private List<String> directlyReceivingList = new ArrayList<>();
}
