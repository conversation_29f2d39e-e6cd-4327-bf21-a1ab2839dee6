package vn.vnpt.digo.reporter.stream;



import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.reflect.TypeToken;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.KTMETLDossier;
import vn.vnpt.digo.reporter.service.KTMETLDossierService;
import vn.vnpt.digo.reporter.stream.messaging.KTMReceiveETLDossierRequest;

import java.lang.reflect.Type;
import java.util.List;

@Component
@EnableBinding(KTMReceiveETLDossierRequest.class)
public class KTMReceiveETLDossierStream {
    @Autowired
    private KTMETLDossierService ktmetlDossierService;
    @StreamListener(value = KTMReceiveETLDossierRequest.INPUT)
    public void KtmDossierDataConsumerStream(Message<List<KTMETLDossier>> dossier) {
        List<KTMETLDossier> dossierMessage = dossier.getPayload();

        ObjectMapper mapper = new ObjectMapper();

        Gson gson = new Gson();
        String json = gson.toJson(dossierMessage);

        TypeToken<List<KTMETLDossier>> typeToken = new TypeToken<List<KTMETLDossier>>() {};
        List<KTMETLDossier> dossierList = gson.fromJson(json, new TypeToken<List<KTMETLDossier>>() {}.getType());
        ktmetlDossierService.addKTMETLDossierData(dossierList);
    }

}