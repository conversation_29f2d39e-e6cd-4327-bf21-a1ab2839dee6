package vn.vnpt.digo.reporter.dto.featurestatistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder({"appCode", "counter"})
public class FeatureStatisticsDto implements Comparable<FeatureStatisticsDto> {

    @JsonProperty("appCode")
    private String id;
    private Long counter;

    @Override
    public int compareTo(FeatureStatisticsDto featureStatistics) {
        return this.getId().compareTo(featureStatistics.getId());
    }

}
