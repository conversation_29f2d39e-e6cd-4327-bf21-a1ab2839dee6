/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CandidatePosition implements Serializable{

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @Field("name")
    private ArrayList<TranslateName> name;

    @JsonIgnore
    private String candidateName;

    public void setCandidateName(short langId) {
        if(Objects.nonNull(name)){
            name.forEach(trans -> {
                if (trans.getLanguageId().equals(langId)) {
                    this.candidateName = trans.getName();
                }
            });
        }
    }


}
