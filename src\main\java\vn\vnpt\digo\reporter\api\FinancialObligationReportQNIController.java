package vn.vnpt.digo.reporter.api;

import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.qni.FinancialObligationDto;
import vn.vnpt.digo.reporter.service.FinancialObligationReportQNIService;
import vn.vnpt.digo.reporter.util.AutoRowHeightCommand;
import vn.vnpt.digo.reporter.util.Constant;
import vn.vnpt.digo.reporter.util.Translator;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/financial-obligation-report-qni")
@IcodeAuthorize("vnpt.permission.financialobligationreport")
public class FinancialObligationReportQNIController {

    private static final Logger logger = LoggerFactory.getLogger(FinancialObligationReportQNIController.class);

    @Autowired
    private FinancialObligationReportQNIService financialObligationReportQNIService;

    @Autowired
    private Translator translator;

    @Value(value = Constant.LOCATION_FINANCIAL_OBLIGATION_REPORT_QNI)
    private Resource resourceTemplateFinancialObligationReportQNI;

    // Định dạng thời gian và tạo metadata cho export
    private String[] prepareExportMetadata(String fromDate, String toDate) throws Exception {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfTimestamp = new SimpleDateFormat("yyyyMMddHHmm");
        DateFormat dfCurrentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dfTimestamp.setTimeZone(timezone);
        dfCurrentDate.setTimeZone(timezone);
        String timestamp = dfTimestamp.format(date);
        String currentDate = dfCurrentDate.format(date);

        // Parse fromDate và toDate để tạo subTitle
        DateFormat dfParse = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dfParse.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date fromDateParsed = dfParse.parse(fromDate);
        Date toDateParsed = dfParse.parse(toDate);

        SimpleDateFormat dfReportDate = new SimpleDateFormat("dd/MM/yyyy");
        String fromDateReport = dfReportDate.format(fromDateParsed);
        String toDateReport = dfReportDate.format(toDateParsed);

        String filename = timestamp + "-danh-sach-ho-so-finance.xlsx";
        String subTitle = translator.toLocale("lang.word.dossier-statistic-fromdate-todate", new String[]{fromDateReport, toDateReport});
        String currentDateStr = translator.toLocale("lang.word.dossier-statistic-current-date", new String[]{currentDate});

        return new String[]{filename, subTitle, currentDateStr};
    }

    // Ghi log request
    private void logRequest(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() +
                (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: {}", requestPath);
    }

    // API phân trang lấy danh sách hồ sơ
    @GetMapping
    public Page<FinancialObligationDto> getDossiersWithAgency(
            HttpServletRequest request,
            @RequestParam String fromDate,
            @RequestParam String toDate,
            @RequestParam List<String> agencyIds,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        logRequest(request);

        Page<FinancialObligationDto> reportResult = financialObligationReportQNIService
                .getDossiersWithAgency(fromDate, toDate, page, size, agencyIds);
        logger.info("DIGO-Info: Total elements retrieved: {}", reportResult.getTotalElements());
        return reportResult;
    }

    // API export Excel (lấy toàn bộ dữ liệu, không phân trang)
    @GetMapping("/export")
    public ResponseEntity<Object> exportDossiersToExcel(
            HttpServletRequest request,
            @RequestParam String fromDate,
            @RequestParam String toDate,
            @RequestParam List<String> agencyIds) {
        logRequest(request);

        try {
            logger.info("Starting exportDossiersToExcel with fromDate: {}, toDate: {}, agencyIds: {}",
                    fromDate, toDate, agencyIds);

            // Lấy dữ liệu từ service
            List<FinancialObligationDto> itemDtos = financialObligationReportQNIService
                    .getAllDossiersForExport(fromDate, toDate, agencyIds);
            logger.info("Retrieved {} items for export", itemDtos.size());

            // Chuẩn bị metadata cho export
            String[] metadata = prepareExportMetadata(fromDate, toDate);
            String filename = metadata[0];
            String subTitle = metadata[1];
            String currentDateStr = metadata[2];

            // Chuẩn bị template và context cho JXLS
            byte[] resourceBytes;
            try (InputStream is = resourceTemplateFinancialObligationReportQNI.getInputStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                Context context = new Context();
                context.putVar("textBanner", translator.toLocale("lang.word.gov"));
                context.putVar("subtextBanner", translator.toLocale("lang.word.ifh"));
                context.putVar("title", "BÁO CÁO HỒ SƠ NGHĨA VỤ TÀI CHÍNH");
                context.putVar("subTitle", subTitle);
                context.putVar("currentDate", currentDateStr);

                // Tiêu đề cột (theo tiếng Việt)
                context.putVar("stt", "STT");
                context.putVar("code", "Mã hồ sơ");
                context.putVar("procedure", "Tên thủ tục hành chính");
                context.putVar("noiDungYeuCauGiaiQuyet", "Nội dung yêu cầu giải quyết");
                context.putVar("acceptedDate", "Ngày tiếp nhận");
                context.putVar("appointmentDate", "Ngày hẹn trả");
                context.putVar("completedDate", "Ngày kết thúc xử lý");
                context.putVar("dossierStatus", "Trạng thái hồ sơ");
                context.putVar("awaitingFinancialObligations", "Ngày chuyển CQT");
                context.putVar("awaitingFinancialCompareAcceptedDate", "Thời gian so với ngày tiếp nhận");
                context.putVar("financialObligationsDate", "Ngày có kết quả thuế");
                context.putVar("financialCompareAcceptedDate", "Thời gian so với ngày tiếp nhận");

                context.putVar("writer", translator.toLocale("lang.word.dossier-statistic-writer"));
                context.putVar("reporter", translator.toLocale("lang.word.dossier-statistic-reporter"));
                context.putVar("itemDtos", itemDtos);

                XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
                JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");

                resourceBytes = outputStream.toByteArray();
            } catch (Exception e) {
                logger.error("Error generating Excel file", e);
                throw new RuntimeException("Failed to generate Excel file", e);
            }

            logger.info("Successfully generated Excel file with {} records", itemDtos.size());
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                    .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                    .body(resourceBytes);

        } catch (Exception e) {
            logger.error("Error in exportDossiersToExcel with fromDate: {}, toDate: {}, agencyIds: {}",
                    fromDate, toDate, agencyIds, e);
            return ResponseEntity.status(500).body("Failed to export dossiers to Excel: " + e.getMessage());
        }
    }
}
