package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.DossierCountingLog;

import java.util.List;

@Repository
public interface DossierCountingLogRepository extends MongoRepository<DossierCountingLog, ObjectId> {
  @Query(value = "{'$and':[{'dossierId': :#{#id} },{'dossierDetailStatus': :#{#code} }]}")
  List<DossierCountingLog> getDossierCountingLog(@Param("id") ObjectId id, @Param("code") int code);

  int deleteDossierCountingById(@Param("id") ObjectId id);


}
