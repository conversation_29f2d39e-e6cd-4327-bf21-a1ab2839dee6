/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
public class GetStatisticDossierByLevelDto implements Serializable {
    
    private AgencyDossierByDay agency;
    
    private SectorDossierByDay sector;
    
    private ProcedureLevelDossierByDay procedureLevel;
    
    private ProcedureDossierByDay procedure;
    
    private Number totalReceive;
}
