package vn.vnpt.digo.reporter.api;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.document.PetitionStatistics;
import vn.vnpt.digo.reporter.service.PetitionStatisticsService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/petition-statistics")
@IcodeAuthorize("vnpt.permission.petition-statistics")
public class PetitionStatisticsController {

    @Autowired
    public PetitionStatisticsService service;

    Logger logger = LoggerFactory.getLogger(PetitionStatisticsController.class);
    
    @GetMapping("/--today")
    public PetitionStatistics getPetitionStatistics(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PetitionStatistics response = this.service.getTodayPetitionStatistics();
        return response;
    }
    
     @GetMapping("/--by-day")
    public PetitionStatistics getTodayPetitionStatisticsByDay(HttpServletRequest request,
            @RequestParam(name = "start-date", required = false) String wday){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PetitionStatistics response = this.service.getTodayPetitionStatisticsByDay(wday);
        return response;
    }

}
