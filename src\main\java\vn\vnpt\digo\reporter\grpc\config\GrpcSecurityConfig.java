package vn.vnpt.digo.reporter.grpc.config;

import io.grpc.ServerInterceptor;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import vn.vnpt.digo.reporter.grpc.auth.JwtServerInterceptor;

/**
 * <AUTHOR>
 */
@Configuration
public class GrpcSecurityConfig {

    private final JwtDecoder jwtDecoder;

    // Inject the auto-configured JwtDecoder
    public GrpcSecurityConfig(JwtDecoder jwtDecoder) {
        this.jwtDecoder = jwtDecoder;
    }

    @Bean
    @GrpcGlobalServerInterceptor
    public ServerInterceptor customJwtServerInterceptor() {
        return new JwtServerInterceptor(jwtDecoder);
    }

}