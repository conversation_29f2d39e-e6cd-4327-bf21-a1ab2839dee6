package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.statistics.GeneralStatisticsRequestDto;
import vn.vnpt.digo.reporter.dto.statistics.GeneralStatisticsResponseDto;
import vn.vnpt.digo.reporter.service.CommonStatisticsService;

import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/common-statistics/")
//@IcodeAuthorize("vnpt.permission.statistics")
public class CommonStatisticsController {

    @Autowired
    private CommonStatisticsService commonStatisticsService;

    Logger logger = LoggerFactory.getLogger(CommonStatisticsController.class);

    @PostMapping("general")
    public GeneralStatisticsResponseDto generalStatistics(
            HttpServletRequest request,
            @RequestBody GeneralStatisticsRequestDto body
            ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return commonStatisticsService.generalStatistics(body);
    }
    
}
