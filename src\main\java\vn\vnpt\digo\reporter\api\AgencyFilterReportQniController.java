package vn.vnpt.digo.reporter.api;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.reporter.document.AgencyFilterReportQni;
import vn.vnpt.digo.reporter.dto.qni.AgencyFilterReportQniResponse;
import vn.vnpt.digo.reporter.dto.qni.ImportResponseDto;
import vn.vnpt.digo.reporter.service.AgencyFilterReportQniService;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agency-filter-report-qni")
public class AgencyFilterReportQniController {
    @Autowired
    private AgencyFilterReportQniService documentService;

    @GetMapping
    public List<AgencyFilterReportQniResponse> getAllDocuments() {
        return documentService.getAllDocuments();
    }

    @GetMapping("/{id}")
    public AgencyFilterReportQniResponse getDocumentById(@PathVariable String id) {
        return documentService.getDocumentById(id);
    }

    @GetMapping("/agency-id/{id}")
    public AgencyFilterReportQniResponse getDocumentByAgencyId(@PathVariable String id) {
        return documentService.getDocumentsByAgencyId(id);
    }

    @PostMapping
    public AgencyFilterReportQni addDocument(@RequestBody AgencyFilterReportQni document) {
        return documentService.addDocument(document);
    }

    @PutMapping("/{id}")
    public AgencyFilterReportQni updateDocument(@PathVariable String id, @RequestBody AgencyFilterReportQni updatedDocument) {
        return documentService.updateDocument(id, updatedDocument);
    }

    @DeleteMapping("/{id}")
    public long deleteDocument(@PathVariable String id) {
        return documentService.deleteDocument(id);
    }

    @GetMapping("/--check-agency-not-filter")
    public ResponseEntity checkAgencyNotFilter(
            @RequestParam(value = "agency-ids", required = true) List<String> agencyIds,
            HttpServletRequest request
    ) {
        return ResponseEntity.ok(documentService.checkAgencyNotFilter(agencyIds));
    }

    @PostMapping("/import-excel")
    public ResponseEntity<ImportResponseDto> importFromExcel(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                ImportResponseDto errorResponse = new ImportResponseDto();
                errorResponse.getErrorMessages().add("File is empty");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            List<AgencyFilterReportQni> documents = parseExcelFile(file);
            ImportResponseDto result = documentService.importFromExcel(documents);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            ImportResponseDto errorResponse = new ImportResponseDto();
            errorResponse.getErrorMessages().add("Error processing file: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    private List<AgencyFilterReportQni> parseExcelFile(MultipartFile file) throws IOException {
        List<AgencyFilterReportQni> documents = new ArrayList<>();
        
        Workbook workbook;
        String fileName = file.getOriginalFilename();
        
        // Determine file type and create appropriate workbook
        if (fileName != null && fileName.endsWith(".xlsx")) {
            workbook = new XSSFWorkbook(file.getInputStream());
        } else if (fileName != null && fileName.endsWith(".xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            throw new IllegalArgumentException("File must be Excel format (.xls or .xlsx)");
        }

        Sheet sheet = workbook.getSheetAt(0); // Get first sheet
        
        // Skip header row (row 0) and start from row 1
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            
            // Create document for every row to maintain order, even if some fields are empty
            AgencyFilterReportQni document = new AgencyFilterReportQni();
            
            // Column 0: agencyName - handle null row or null cell
            String agencyName = "";
            if (row != null) {
                agencyName = getCellValueAsString(row.getCell(0));
            }
            document.setAgencyName(agencyName);
            
            // Column 1: idAgency - handle null row or null cell
            String idAgency = "";
            if (row != null) {
                idAgency = getCellValueAsString(row.getCell(1));
            }
            document.setIdAgency(idAgency);
            
            // Column 2: idFilter - handle null row or null cell
            String idFilter = "";
            if (row != null) {
                idFilter = getCellValueAsString(row.getCell(2));
            }
            document.setIdFilter(idFilter);

            documents.add(document);
        }
        
        workbook.close();
        return documents;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // Format as integer if it's a whole number, otherwise as decimal
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}
