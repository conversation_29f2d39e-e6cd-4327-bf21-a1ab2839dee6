package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import vn.vnpt.digo.reporter.document.PetitionStatistics;
import vn.vnpt.digo.reporter.pojo.PetitionStatisticsMilestone;
import vn.vnpt.digo.reporter.pojo.StatisticPetition;
import vn.vnpt.digo.reporter.pojo.StatusPetitionStatistics;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.DigoRestTemplate;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Base64;
import java.util.Objects;
import org.springframework.cache.CacheManager;
import vn.vnpt.digo.reporter.pojo.FieldPetitionStatistics;
import vn.vnpt.digo.reporter.pojo.ReceptionMethodPetitionStatistics;
import vn.vnpt.digo.reporter.pojo.StatusStatisticsByTag;
import vn.vnpt.digo.reporter.pojo.StatusStatisticsByTagTotalDetail;
import vn.vnpt.digo.reporter.pojo.StatusTotalDetail;
import vn.vnpt.digo.reporter.util.GsonHelper;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.repository.PetitionStatisticsRepository;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import vn.vnpt.digo.reporter.pojo.AccessToken;
import vn.vnpt.digo.reporter.pojo.DecodedAccessToken;
import vn.vnpt.digo.reporter.pojo.IdTrans;
import org.springframework.data.domain.Sort;

/**
 *
 * <AUTHOR>
 */
@Service
public class PetitionStatisticsCronJobService {

    @Autowired
    private MongoTemplate mongoTemplate;
        
    @Autowired
    private Microservice microservice;
    
    @Autowired
    private DigoRestTemplate digoRestTemplate;
    
    @Value("${digo.microservice.gateway-url}")
    private String gatewayURL;
    
    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;
    
    @Value(value = "${digo.petition-statistics.credentials-client-id}")
    private String clientId;
    
    @Value(value = "${digo.petition-statistics.credentials-client-secret}")
    private String clientSecret;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private PetitionStatisticsRepository repository;
    
    @Autowired
    CacheManager cacheManager;
        
    String clientToken;
    
    ObjectId clientDeploymentId;
    
    Logger logger = LoggerFactory.getLogger(PetitionStatisticsCronJobService.class);

    public boolean getClientTokenAndDeploymentId() {
        String endpoint = this.tokenUrl + "/protocol/openid-connect/token";
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        MultiValueMap<String, String> requestBody= new LinkedMultiValueMap<String, String>();
        requestBody.add("grant_type", "client_credentials");
        requestBody.add("client_id", this.clientId);
        requestBody.add("client_secret", this.clientSecret);
        
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(requestBody, headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(endpoint, request , String.class );

            Gson g = new Gson();
            AccessToken tokenObj = g.fromJson(response.getBody(), AccessToken.class);
            String token = tokenObj.getAccess_token();
            this.clientToken = token;

            String[] chunks = token.split("\\.");
            Base64.Decoder decoder = Base64.getDecoder();
            String payload = new String(decoder.decode(chunks[1]));

            DecodedAccessToken decodedToken = g.fromJson(payload, DecodedAccessToken.class);
            
            this.clientDeploymentId = decodedToken.getDeploymentId();

            return true;
        } catch (HttpClientErrorException clientErr) {
            logger.info("Client credentials token getting client error - " + clientErr.getStatusCode() + " - " + clientErr.getMessage());
        } catch (HttpServerErrorException serverErr) {
            logger.info("Client credentials token getting server error - " + serverErr.getStatusCode() + " - " + serverErr.getMessage());
        } catch (UnknownHttpStatusCodeException unknownErr) {
            logger.info("Client credentials token getting unknown error - " + unknownErr.getStatusText() + " - " + unknownErr.getMessage());
        }
        return false;
    }

//    @Scheduled(cron = "0 47 0 * * *")
    @Scheduled(cron = "0 0 5,11,17,23 * * ?", zone = "Etc/GMT+7")
    protected void updateDailyPetitionStatistics() {
        logger.info("PETITION STATISTICS CRON JOB RUNS!");
        
        this.cacheManager.getCache("getPetitionStatistics").clear();
        
        if (this.getClientTokenAndDeploymentId()) {
            PetitionStatistics petitionStatistics = new PetitionStatistics();
            petitionStatistics.setDeploymentId(this.clientDeploymentId);
            petitionStatistics.setCreatedDate(new Date());
            petitionStatistics.setUpdatedDate(new Date());

            Date today = new Date();
            today.setHours(0);
            today.setMinutes(0);
            today.setSeconds(0);

            // get today's petition statistics
            StatisticPetition todaysStatistics = this.getStatusStatisticsByReceptionMethod(today);

            // get this month's petition statistics
            Date thisMonthsFirstDate = GsonHelper.copyObject(today);
            thisMonthsFirstDate.setDate(1);
            StatisticPetition thisMonthsStatistics = this.getStatusStatisticsByReceptionMethod(thisMonthsFirstDate);

            // get total system petition statistics:
            StatisticPetition totalStatistics = this.getStatusStatisticsByReceptionMethod(null);

            // set status statistics
            List<StatusPetitionStatistics> statusStatisticsList = new ArrayList<StatusPetitionStatistics>();
            statusStatisticsList = this.getStatusPetitionStatistics(todaysStatistics, thisMonthsStatistics, totalStatistics);
            petitionStatistics.setStatusStatistics(statusStatisticsList);

            // set reception method statistics
            List<ReceptionMethodPetitionStatistics> receptionMethodStatisticsList = new ArrayList<ReceptionMethodPetitionStatistics>();
            receptionMethodStatisticsList = this.getReceptionMethodPetitionStatistics(todaysStatistics, thisMonthsStatistics, totalStatistics);
            petitionStatistics.setReceptionMethodStatistics(receptionMethodStatisticsList);

            // set field statistics
            List<FieldPetitionStatistics> fieldStatisticsList = new ArrayList<FieldPetitionStatistics>();
            fieldStatisticsList = this.getTagPetitionStatistics(today, thisMonthsFirstDate);
            petitionStatistics.setFieldStatistics(fieldStatisticsList);

            Query query = new Query();
            query.with(Sort.by(Sort.Direction.DESC, "createdDate"));
            query.addCriteria(Criteria.where("deploymentId").is(this.clientDeploymentId));
            query.addCriteria(Criteria.where("createdDate").gte(today));

//            PetitionStatistics todayStats = mongoTemplate.findOne(query, PetitionStatistics.class);
            List<PetitionStatistics> todayStatsList = mongoTemplate.find(query, PetitionStatistics.class);
            logger.info(GsonHelper.getJson(todayStatsList));
//            if (Objects.nonNull(todayStats)) {
//                todayStats.setStatusStatistics(petitionStatistics.getStatusStatistics());
//                todayStats.setReceptionMethodStatistics(petitionStatistics.getReceptionMethodStatistics());
//                todayStats.setFieldStatistics(petitionStatistics.getFieldStatistics());
//                todayStats.setUpdatedDate(new Date());
//                try {
//                    repository.save(todayStats);
//                    logger.info("Petition statistics updated successfully!");
//                } catch (Exception e) {
//                    logger.info("Petition statistics failed to update T.T ");
//                }
            if (Objects.nonNull(todayStatsList) && !todayStatsList.isEmpty()) {
                for (int i = 0; i < todayStatsList.size(); i++) {
                    if (i == 0) {
                        todayStatsList.get(i).setStatusStatistics(petitionStatistics.getStatusStatistics());
                        todayStatsList.get(i).setReceptionMethodStatistics(petitionStatistics.getReceptionMethodStatistics());
                        todayStatsList.get(i).setFieldStatistics(petitionStatistics.getFieldStatistics());
                        todayStatsList.get(i).setUpdatedDate(new Date());
                        try {
                            repository.save(todayStatsList.get(i));
                            logger.info("Petition statistics updated successfully!");
                        } catch (Exception e) {
                            logger.info("Petition statistics failed to update T.T ");
                        }
                    } else {
                        if (repository.deleteObjectById(todayStatsList.get(i).getId()) != 0) {
                            logger.info("Deleted redundant petition statistics.");
                        } else {
                            logger.info("Could not delete redundant petition statistics!??");
                        }
                    }
                }
            } else {
                try {
                    repository.save(petitionStatistics);
                    logger.info("Petition statistics created successfully!");
                } catch (Exception e) {
                    logger.info("Petition statistics failed to create T.T ");
                }
            }
        }
    }
    
    private List<StatusPetitionStatistics> getStatusPetitionStatistics(
        StatisticPetition todaysStatistics,
        StatisticPetition thisMonthsStatistics,
        StatisticPetition totalStatistics
    ) {
        List<StatusPetitionStatistics> resultList = new ArrayList<StatusPetitionStatistics>();
        
        // pending statistics:
        PetitionStatisticsMilestone pendingMileStones = new PetitionStatisticsMilestone();
        pendingMileStones.setToday(todaysStatistics.getStatus().getWaitingReception());
        pendingMileStones.setThisMonth(thisMonthsStatistics.getStatus().getWaitingReception());
        pendingMileStones.setSystemTotal(totalStatistics.getStatus().getWaitingReception());
        resultList.add(new StatusPetitionStatistics(1, pendingMileStones));
        
        // inProgress statistics:
        PetitionStatisticsMilestone inProgressMileStones = new PetitionStatisticsMilestone();
        inProgressMileStones.setToday(todaysStatistics.getStatus().getInprogress());
        inProgressMileStones.setThisMonth(thisMonthsStatistics.getStatus().getInprogress());
        inProgressMileStones.setSystemTotal(totalStatistics.getStatus().getInprogress());
        resultList.add(new StatusPetitionStatistics(2, inProgressMileStones));
        
        // completed statistics:
        PetitionStatisticsMilestone completedMileStones = new PetitionStatisticsMilestone();
        completedMileStones.setToday(todaysStatistics.getStatus().getCompleted());
        completedMileStones.setThisMonth(thisMonthsStatistics.getStatus().getCompleted());
        completedMileStones.setSystemTotal(totalStatistics.getStatus().getCompleted());
        resultList.add(new StatusPetitionStatistics(3, completedMileStones));
        
        // total statistics:
        PetitionStatisticsMilestone totalMileStones = new PetitionStatisticsMilestone();
        totalMileStones.setToday(
            todaysStatistics.getStatus().getWaitingReception() +
            todaysStatistics.getStatus().getInprogress() +
            todaysStatistics.getStatus().getCompleted()
        );
        totalMileStones.setThisMonth(
            thisMonthsStatistics.getStatus().getWaitingReception() +
            thisMonthsStatistics.getStatus().getInprogress() +
            thisMonthsStatistics.getStatus().getCompleted()
        );
        totalMileStones.setSystemTotal(
            totalStatistics.getStatus().getWaitingReception() +
            totalStatistics.getStatus().getInprogress() +
            totalStatistics.getStatus().getCompleted()
        );
        resultList.add(new StatusPetitionStatistics(null, totalMileStones));
        
        return resultList;
    }
    
    private List<ReceptionMethodPetitionStatistics> getReceptionMethodPetitionStatistics(
        StatisticPetition todaysStatistics,
        StatisticPetition thisMonthsStatistics,
        StatisticPetition totalStatistics
    ) {
        List<ReceptionMethodPetitionStatistics> resultList = new ArrayList<ReceptionMethodPetitionStatistics>();
        
        // total
        for (StatusTotalDetail totalStats : totalStatistics.getDetail()) {
            if (Objects.nonNull(totalStats.getReceptionMethod().getId())) {
                IdTrans receptionMethod = new IdTrans();
                receptionMethod.setId(totalStats.getReceptionMethod().getId());
                receptionMethod.setTrans(totalStats.getReceptionMethod().getTrans());
                PetitionStatisticsMilestone milestones = new PetitionStatisticsMilestone();
                milestones.setSystemTotal(totalStats.getAmount());
                ReceptionMethodPetitionStatistics rMStats = new ReceptionMethodPetitionStatistics(receptionMethod, milestones);
                resultList.add(rMStats);
            }
        }
        
        for (ReceptionMethodPetitionStatistics rMPStats : resultList) {
            // today
            for (StatusTotalDetail todaysStats : todaysStatistics.getDetail()) {
                if (Objects.equals(todaysStats.getReceptionMethod().getId(), rMPStats.getReceptionMethod().getId())) {
                    rMPStats.getStatistics().setToday(todaysStats.getAmount());
                    break;
                }
            }
            // this month
            for (StatusTotalDetail thisMonthsStats : thisMonthsStatistics.getDetail()) {
                if (Objects.equals(thisMonthsStats.getReceptionMethod().getId(), rMPStats.getReceptionMethod().getId())) {
                    rMPStats.getStatistics().setThisMonth(thisMonthsStats.getAmount());
                    break;
                }
            }
        }
        
        return resultList;
    }
    
    private List<FieldPetitionStatistics> getTagPetitionStatistics(Date today, Date thisMonthsFirstDate) {
        List<FieldPetitionStatistics> resultList = new ArrayList<FieldPetitionStatistics>();
        
        // get today's petition statistics
        StatusStatisticsByTag todaysStatistics = this.getStatusStatisticsByTag(today);
        
        // get this month's petition statistics
        StatusStatisticsByTag thisMonthsStatistics = this.getStatusStatisticsByTag(thisMonthsFirstDate);
        
        // get total petition statistics:
        StatusStatisticsByTag totalStatistics = this.getStatusStatisticsByTag(null);
        
        for (StatusStatisticsByTagTotalDetail totalStats : totalStatistics.getDetail()) {
            if (Objects.nonNull(totalStats.getTag().getParent())) {
                int index = -1;
                for (int i = 0; i < resultList.size(); i++) {
                    if (Objects.equals(resultList.get(i).getField().getId(), totalStats.getTag().getParent().getId())) {
                        index = i;
                        break;
                    }
                }
                if (index == -1) {
                    IdTrans parent = new IdTrans();
                    parent.setId(totalStats.getTag().getParent().getId());
                    parent.setTrans(totalStats.getTag().getParent().getTrans());
                    PetitionStatisticsMilestone milestones = new PetitionStatisticsMilestone();
                    milestones.setSystemTotal(totalStats.getTotal());
                    FieldPetitionStatistics fieldStats = new FieldPetitionStatistics(parent, milestones);
                    resultList.add(fieldStats);
                } else {
                    long currentTotal = resultList.get(index).getStatistics().getSystemTotal();
                    resultList.get(index).getStatistics().setSystemTotal(currentTotal + totalStats.getTotal());
                }
            }
        }
        
        for (FieldPetitionStatistics tPStats : resultList) {
            // today
            for (StatusStatisticsByTagTotalDetail todaysStats : todaysStatistics.getDetail()) {
                if (Objects.nonNull(todaysStats.getTag().getParent())) {
                    if (Objects.equals(todaysStats.getTag().getParent().getId(), tPStats.getField().getId())) {
                        tPStats.getStatistics().setToday(
                            tPStats.getStatistics().getToday() + todaysStats.getTotal()
                        );
                        tPStats.getStatistics().getToday();
                    }
                }
                
            }
            // this month
            for (StatusStatisticsByTagTotalDetail thisMonthsStats : thisMonthsStatistics.getDetail()) {
                if (Objects.nonNull(thisMonthsStats.getTag().getParent())) {
                    if (Objects.equals(thisMonthsStats.getTag().getParent().getId(), tPStats.getField().getId())) {
                        tPStats.getStatistics().setThisMonth(
                            tPStats.getStatistics().getThisMonth() + thisMonthsStats.getTotal()
                        );
                    }
                }
            }
        }
        
        return resultList;
    }
    
    private StatisticPetition getStatusStatisticsByReceptionMethod(Date startDate) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        String endpoint = this.gatewayURL + "/" + microservice.getPathCode("petition") + "/petition/--statistics-by-tag?";
        
        if (startDate != null) {
            String strStartDate = dateFormat.format(startDate);
            strStartDate = strStartDate.replace("+", "%2B");
            endpoint += "&start-date=" + strStartDate;
        }
                
        if (endpoint.contains("&")) {
            int index = endpoint.indexOf("&");
            endpoint = endpoint.substring(0, index) + endpoint.substring(index + 1);
        } else {
            endpoint = endpoint.replace("?", "");
        }
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        ObjectMapper mapper = new ObjectMapper();
        StatisticPetition data = new StatisticPetition();
        try {
            URI endpointUri = new URI(endpoint);
            String response = digoRestTemplate.exchangeWithResponse(this.clientToken, endpointUri, body, HttpMethod.GET);
            data = mapper.readValue(response, StatisticPetition.class);
            return data;
        } catch (URISyntaxException err) {
            //Do nothing
            logger.info("URISyntaxException: " + err.toString());
        } catch (HttpClientErrorException err) {
            //Do nothing
            logger.info("HttpClientErrorException: " + err.toString());
        } catch (JsonProcessingException err) {
            logger.info("JsonProcessingException: " + err.toString());
        }
        
        return null;
    }
    
    private StatusStatisticsByTag getStatusStatisticsByTag(Date startDate) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        String endpoint = this.gatewayURL + "/" + microservice.getPathCode("petition") + "/petition/--status-statistics-by-tag?";
        
        if (startDate != null) {
            String strStartDate = dateFormat.format(startDate);
            strStartDate = strStartDate.replace("+", "%2B");
            endpoint += "&start-date=" + strStartDate;
        }
                
        if (endpoint.contains("&")) {
            int index = endpoint.indexOf("&");
            endpoint = endpoint.substring(0, index) + endpoint.substring(index + 1);
        } else {
            endpoint = endpoint.replace("?", "");
        }
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        ObjectMapper mapper = new ObjectMapper();
        StatusStatisticsByTag data = new StatusStatisticsByTag();
        try {
            URI endpointUri = new URI(endpoint);
            logger.info("endpointUri: " + endpointUri);
            String response = digoRestTemplate.exchangeWithResponse(this.clientToken, endpointUri, body, HttpMethod.GET);
            data = mapper.readValue(response, StatusStatisticsByTag.class);
            return data;
        } catch (URISyntaxException err) {
            //Do nothing
            logger.info("URISyntaxException: " + err.toString());
        } catch (HttpClientErrorException err) {
            //Do nothing
            logger.info("HttpClientErrorException: " + err.toString());
        } catch (JsonProcessingException err) {
            logger.info("JsonProcessingException: " + err.toString());
        }
        
        return null;
    }
    @Async
    @Scheduled(cron = "${digo.analytics.sync.clear.statistic}")
    public void evictCache() {
        // xoa cache bieu do thong ke analytics
        org.springframework.cache.Cache cache = cacheManager.getCache("StatisticBTTTTService.analyticsReport");
        if (cache != null) {
            cache.clear();
        }
    }
}
