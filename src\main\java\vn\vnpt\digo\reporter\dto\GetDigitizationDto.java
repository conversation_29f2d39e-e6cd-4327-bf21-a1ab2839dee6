package vn.vnpt.digo.reporter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetDigitizationDto {
		private long totalDossierInProgress; //1
		private long numberDossierHavingAPartDigitization;//2
		private long numberDossierHavingFullDigitization;//3
		private long numberDossierDoesNotHavingDigitization;//5

		private long totalDossierCompleted;//6
		private long previousPeriod; //7
		private long numberDossierHavingDigitizationResult;//8
		private long numberDossierDoesNotHavingDigitizationResult;//10

		private String agencyName;
		private String agencyId;
}
