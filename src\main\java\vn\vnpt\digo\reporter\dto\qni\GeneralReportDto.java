package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class GeneralReportDto {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Agency agency;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Sector sector;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Procedure procedure;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Agency agencyBaby;
    private int receivedOnline;
    private int receivedDirect;
    private int received;
    private int receivedOld;
    private int unresolvedOnTime;
    private int unresolvedOverdue;
    private int resolvedEarly;
    private int resolvedOnTime;
    private int resolvedOverdue;
    private int withdraw;
    private int resolved;
    private int unresolved;
    private int direct;
    private int receivedPostal;
    private int receivedPublicPostal;
    private int receivedSmartphone;
    private int procedureUsed;

    public GeneralReportDto(Agency agency, Sector sector, Procedure procedure, Agency agencyBaby, int receivedOnline, int receivedDirect, int received, int receivedOld, int unresolvedOnTime,
                            int unresolvedOverdue, int resolvedEarly, int resolvedOnTime, int resolvedOverdue, int withdraw, int resolved,
                            int unresolved, int direct, int receivedPostal, int receivedPublicPostal, int receivedSmartphone, int procedureUsed) {
        this.agency = agency;
        this.sector = sector;
        this.procedure = procedure;
        this.agencyBaby = agencyBaby;
        this.receivedOnline = receivedOnline;
        this.receivedDirect = receivedDirect;
        this.received = received;
        this.receivedOld = receivedOld;
        this.unresolvedOnTime = unresolvedOnTime;
        this.unresolvedOverdue = unresolvedOverdue;
        this.resolvedEarly = resolvedEarly;
        this.resolvedOnTime = resolvedOnTime;
        this.resolvedOverdue = resolvedOverdue;
        this.withdraw = withdraw;
        this.resolved = resolved;
        this.unresolved = unresolved;
        this.direct = direct;
        this.receivedPostal = receivedPostal;
        this.receivedPublicPostal = receivedPublicPostal;
        this.receivedSmartphone = receivedSmartphone;
        this.procedureUsed = procedureUsed;
    }

    public CustomSummary toCustomObject() {
        var temp = new CustomSummary(
                getProcedure(),
                getSector(),
                getAgency(),
                getReceivedOnline(),
                getReceivedDirect(),
                getReceived(),
                getReceivedOld(),
                getUnresolvedOnTime(),
                getUnresolvedOverdue(),
                getResolvedEarly(),
                getResolvedOnTime(),
                getResolvedOverdue(),
                getWithdraw(),
                getResolved(),
                getUnresolved(),
                getDirect(),
                getReceivedPostal(),
                getReceivedPublicPostal(),
                getReceivedSmartphone(),
                getProcedureUsed());
        return temp;
    }

    // Getter and Setter methods

    public static class Agency {
        @JsonProperty("id")
        private String idAgency;
        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getId() {
            return idAgency;
        }

        public void setIdAgency(String idAgency) {
            this.idAgency = idAgency;
        }
    }

    public static class Sector {

        public Sector(String idSector, String name, String code) {
            this.idSector = idSector;
            this.name = name;
            this.code = code;
        }

        @JsonProperty("id")
        private String idSector;
        private String name;
        private String code;

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

        public String getIdSector() {
            return idSector;
        }

        public void setIdSector(String idSector) {
            this.idSector = idSector;
        }
    }

    public static class Procedure {
        @JsonProperty("id")
        private String idProcedure;
        private String name;
        private String code;

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

        public String getIdProcedure() {
            return idProcedure;
        }

        public Procedure(String idProcedure, String name, String code) {
            this.idProcedure = idProcedure;
            this.name = name;
            this.code = code;
        }

        public void setIdProcedure(String idProcedure) {
            this.idProcedure = idProcedure;
        }
    }

    public static class CustomSummary {
        private int receivedOnline;
        private int receivedDirect;
        private int received;
        private int receivedOld;
        private int unresolvedOnTime;
        private int unresolvedOverdue;
        private int resolvedEarly;
        private int resolvedOnTime;
        private int resolvedOverdue;
        private int withdraw;
        private int resolved;
        private int unresolved;
        private int direct;
        private int receivedPostal;
        private int receivedPublicPostal;
        private int receivedSmartphone;
        private int procedureUsed;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Agency agency;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Sector sector;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Procedure procedure;

        public CustomSummary(Procedure procedure, Sector sector, Agency agency, int receivedOnline, int receivedDirect,
                             int received, int receivedOld, int unresolvedOnTime,
                             int unresolvedOverdue, int resolvedEarly, int resolvedOnTime,
                             int resolvedOverdue, int withdraw, int resolved, int unresolved,
                             int direct, int receivedPostal, int receivedPublicPostal, int receivedSmartphone, int procedureUsed) {
            this.receivedOnline = receivedOnline;
            this.receivedDirect = receivedDirect;
            this.received = received;
            this.receivedOld = receivedOld;
            this.unresolvedOnTime = unresolvedOnTime;
            this.unresolvedOverdue = unresolvedOverdue;
            this.resolvedEarly = resolvedEarly;
            this.resolvedOnTime = resolvedOnTime;
            this.resolvedOverdue = resolvedOverdue;
            this.withdraw = withdraw;
            this.resolved = resolved;
            this.unresolved = unresolved;
            this.direct = direct;
            this.receivedPostal = receivedPostal;
            this.receivedPublicPostal = receivedPublicPostal;
            this.receivedSmartphone = receivedSmartphone;
            this.agency = agency;
            this.procedure = procedure;
            this.sector = sector;
            this.procedureUsed = procedureUsed;
        }

        public Sector getSector() {
            return sector;
        }

        public void setSector(Sector sector) {
            this.sector = sector;
        }

        public Procedure getProcedure() {
            return procedure;
        }

        public void setProcedure(Procedure procedure) {
            this.procedure = procedure;
        }

        public Agency getAgency() {
            return agency;
        }

        public void setAgency(Agency agency) {
            this.agency = agency;
        }

        public int getReceivedOnline() {
            return receivedOnline;
        }

        public void setReceivedOnline(int receivedOnline) {
            this.receivedOnline = receivedOnline;
        }

        public int getReceivedDirect() {
            return receivedDirect;
        }

        public void setReceivedDirect(int receivedDirect) {
            this.receivedDirect = receivedDirect;
        }

        public int getReceived() {
            return received;
        }

        public void setReceived(int received) {
            this.received = received;
        }

        public int getReceivedOld() {
            return receivedOld;
        }

        public void setReceivedOld(int receivedOld) {
            this.receivedOld = receivedOld;
        }

        public int getUnresolvedOnTime() {
            return unresolvedOnTime;
        }

        public void setUnresolvedOnTime(int unresolvedOnTime) {
            this.unresolvedOnTime = unresolvedOnTime;
        }

        public int getUnresolvedOverdue() {
            return unresolvedOverdue;
        }

        public void setUnresolvedOverdue(int unresolvedOverdue) {
            this.unresolvedOverdue = unresolvedOverdue;
        }

        public int getResolvedEarly() {
            return resolvedEarly;
        }

        public void setResolvedEarly(int resolvedEarly) {
            this.resolvedEarly = resolvedEarly;
        }

        public int getResolvedOnTime() {
            return resolvedOnTime;
        }

        public void setResolvedOnTime(int resolvedOnTime) {
            this.resolvedOnTime = resolvedOnTime;
        }

        public int getResolvedOverdue() {
            return resolvedOverdue;
        }

        public void setResolvedOverdue(int resolvedOverdue) {
            this.resolvedOverdue = resolvedOverdue;
        }

        public int getWithdraw() {
            return withdraw;
        }

        public void setWithdraw(int withdraw) {
            this.withdraw = withdraw;
        }

        public int getResolved() {
            return resolved;
        }

        public void setResolved(int resolved) {
            this.resolved = resolved;
        }

        public int getUnresolved() {
            return unresolved;
        }

        public void setUnresolved(int unresolved) {
            this.unresolved = unresolved;
        }

        public int getDirect() {
            return direct;
        }

        public void setDirect(int direct) {
            this.direct = direct;
        }

        public int getReceivedPostal() {
            return receivedPostal;
        }

        public void setReceivedPostal(int receivedPostal) {
            this.receivedPostal = receivedPostal;
        }

        public int getReceivedPublicPostal() {
            return receivedPublicPostal;
        }

        public void setReceivedPublicPostal(int receivedPublicPostal) {
            this.receivedPublicPostal = receivedPublicPostal;
        }

        public int getReceivedSmartphone() {
            return receivedSmartphone;
        }

        public void setReceivedSmartphone(int receivedSmartphone) {
            this.receivedSmartphone = receivedSmartphone;
        }

        public int getProcedureUsed() {
            return procedureUsed;
        }

        public void setProcedureUsed(int procedureUsed) {
            this.procedureUsed = procedureUsed;
        }
    }

}

