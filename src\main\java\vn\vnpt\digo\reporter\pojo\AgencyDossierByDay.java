package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AgencyDossierByDay implements Serializable {

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String code;

    private String agencyName;

    private ArrayList<NameDossierByDay> name;

    private AgencyDossierByDay parent;

    private ArrayList<AgencyDossierByDay> ancestors;

    private ArrayList<Tag> tag;

    public void setNameAgencyData(short langId) {
        if (this.name != null && !this.name.isEmpty()) {
            this.name.forEach(trans -> {
                if (trans.getLanguageId().equals(langId)) {
                    this.agencyName = trans.getName();
                }
            });
        }

    }

}
