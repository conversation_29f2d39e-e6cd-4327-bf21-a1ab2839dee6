package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetDossierByDay implements Serializable {

    @JsonProperty("year")
    private Integer year;
    
    @JsonProperty("month")
    private Integer month;
    
    @JsonProperty("day")
    private Integer day;
    
    @JsonProperty("agency")
    private AgencyDossierByDay agency;
    
    @JsonProperty("agencyLevel")
    private AgencyDossierByDay agencyLevel;
    
    @JsonProperty("sector")
    private SectorDossierByDay sector;
    
    @JsonProperty("procedureLevel")
    private ProcedureLevelDossierByDay procedureLevel;
    
    @JsonProperty("procedure")
    private ProcedureDossierByDay procedure;
    
    @JsonProperty("deploymentId")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;
    
    @JsonProperty("appliedOnline")
    private Integer appliedOnline;
    
    @JsonProperty("received")
    private Integer received;
    
    @JsonProperty("receivedOnline")
    private Integer receivedOnline;
    
    @JsonProperty("receivedDirect")
    private Integer receivedDirect;
    
    @JsonProperty("resolved")
    private Integer resolved;
    
    @JsonProperty("resolvedEarly")
    private Integer resolvedEarly;
    
    @JsonProperty("resolvedOverdue")
    private Integer resolvedOverdue;
    
    @JsonProperty("unresolvedOverdue")
    private Integer unresolvedOverdue;
    
    @JsonProperty("cancelled")
    private Integer cancelled;
    
    @JsonProperty("deleted")
    private Integer deleted;
    
    @JsonProperty("suspended")
    private Integer suspended;
    
    @JsonProperty("returnOnTime")
    private Integer returnOnTime;
    
    @JsonProperty("returnOverdue")
    private Integer returnOverdue;
    
    @JsonProperty("unresolved")
    private Integer unresolved;
      
}
