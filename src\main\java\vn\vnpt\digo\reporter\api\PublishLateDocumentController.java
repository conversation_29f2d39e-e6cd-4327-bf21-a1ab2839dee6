package vn.vnpt.digo.reporter.api;


import com.hazelcast.internal.json.ParseException;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.PublishLateDocumentsDto;
import vn.vnpt.digo.reporter.pojo.EvaluationResultDto;
import vn.vnpt.digo.reporter.service.EvaluationResultFormService;
import vn.vnpt.digo.reporter.service.PublishLateDocumentsService;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@RequestMapping("/publish-late-documents")
@IcodeAuthorize("vnpt.permission.publish-late-documents")
public class PublishLateDocumentController {

    @Autowired
    private PublishLateDocumentsService publishLateDocumentsService;

    Logger logger = LoggerFactory.getLogger(DossierByDayController.class);
    

    @PostMapping("")
    public ResponseEntity<PostResponseDto> postPublishLateDocuments(HttpServletRequest request,
                                                                    @RequestBody PublishLateDocumentsDto input
    ) throws ParseException, IOException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PostResponseDto result = publishLateDocumentsService.postPublishLateDocuments(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--list")
    public ResponseEntity<Slice<PublishLateDocumentsDto>> getListPublishLateDocuments(HttpServletRequest request,
                                                                                  @RequestParam(value = "keyword",required = false) String keyword,
                                                                                  Pageable pageable
    ) throws ParseException, IOException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<PublishLateDocumentsDto> result = publishLateDocumentsService.getListPublishLateDocuments( keyword, pageable);
        return ResponseEntity.ok(result);
    }


    @GetMapping("/{id}")
    public ResponseEntity<PublishLateDocumentsDto> getPublishLateDocumentsById(HttpServletRequest request,
                                                                           @PathVariable(value = "id", required = true) ObjectId id
    ) throws ParseException, IOException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PublishLateDocumentsDto result = publishLateDocumentsService.getPublishLateDocumentsById(id);
        return ResponseEntity.ok(result);
    }

    @PutMapping("/{id}")
    public ResponseEntity<AffectedRowsDto> updatePublishLateDocumentsById(HttpServletRequest request,
                                                                          @PathVariable(value = "id", required = true) ObjectId id,
                                                                          @RequestBody PublishLateDocumentsDto input
    ) throws ParseException, IOException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        AffectedRowsDto result = publishLateDocumentsService.updatePublishLateDocumentsById(id,input);
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<AffectedRowsDto> deletePublishLateDocumentsById(HttpServletRequest request,
                                                                          @PathVariable(value = "id", required = true) ObjectId id
    ) throws ParseException, IOException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        AffectedRowsDto result = publishLateDocumentsService.deletePublishLateDocumentsById(id);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--activation")
    public ResponseEntity<PublishLateDocumentsDto> getPublishLateDocumentsActive(HttpServletRequest request) throws ParseException, IOException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PublishLateDocumentsDto result = publishLateDocumentsService.getPublishLateDocumentsActive();
        return ResponseEntity.ok(result);
    }
}
