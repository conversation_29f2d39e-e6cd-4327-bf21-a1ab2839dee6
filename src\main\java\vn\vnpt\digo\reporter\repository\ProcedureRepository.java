package vn.vnpt.digo.reporter.repository;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.Procedure;
import vn.vnpt.digo.reporter.dto.ProcedureDto;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface ProcedureRepository extends MongoRepository<Procedure, ObjectId> {

    @Query(value = "{ 'agency.id': ?0 }", fields = "{'id': 1, 'agency': 1, 'translate': 1, 'dossierQuantity': 1}")
    List<ProcedureDto> getListByOnlineProcedure(ObjectId agencyId);

    @Query(value = "{'originId': :#{#originId} }")
    Procedure getProcedure(@Param("originId") ObjectId originId);
    
    @Query(value = "{'agency.id' : { :#{#agencyId != null ? '$eq' : '$ne'} : :#{#agencyId != null ? #agencyId : '0'} }, "
            + "'implementer.id': :#{#implementerId}, 'deploymentId': :#{#deploymentId} }", sort = "{'dossierQuantity': -1}")
    List<ProcedureDto> getProcedureFrequent(@Param("agencyId") ObjectId agencyId, @Param("implementerId") ObjectId implementerId, @Param("deploymentId") ObjectId deploymentId, Pageable pageable);

    @Query(value = "{'agency.id': :#{#agencyId}, 'deploymentId': :#{#deploymentId} }", count = true)
    Long countAgencyById(@Param("agencyId") ObjectId agencyId, @Param("deploymentId") ObjectId deploymentId);

    int deleteProcedureFrequentById(@Param("id") ObjectId id);

    @Query(value = "{ 'deploymentId': ?0, 'dossierQuantity': { $gt: 0 } }", sort = "{ 'dossierQuantity': -1 }")
    List<ProcedureDto> findAllSortDescending(ObjectId deploymentId, Pageable pageable);
}
