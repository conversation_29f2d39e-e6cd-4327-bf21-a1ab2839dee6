package vn.vnpt.digo.reporter.changestream;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.ChangeStreamEvent;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

@Component
public class EventExecutor {

    @Autowired
    @Qualifier("taskExecutor")
    TaskExecutor taskExecutor;

    @Autowired
    PadmanChangeStreamProcess padmanChangeStreamProcess;

    @Value("${thread.sleeptime}")
    private Integer sleeptime;

    @Value("${digo.thread.change-stream.enable}")
    private Boolean syncEnable;

    public static BlockingQueue<ChangeStreamEvent> changeStreamQueue = new LinkedBlockingQueue<>();

    private final Logger log = LoggerFactory.getLogger(EventExecutor.class);

    @Async
    @Scheduled(initialDelay = 60 * 1000, fixedDelay = 200)
    @SchedulerLock(name = "processChangeStreamEvent", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void processChangeStreamEvent() {
        if (Boolean.FALSE.equals(syncEnable)) {
            return;
        }

        try {
            if (changeStreamQueue.isEmpty()) {
                Thread.sleep(sleeptime);
                return;
            }

            ChangeStreamEvent event = changeStreamQueue.poll(5, TimeUnit.SECONDS);
            if (event != null) {
                log.info("[Scheduled]: Process item: {}", event.getId());
                taskExecutor.execute(() -> handleEvent(event));
            }

        } catch (Exception e) {
            log.error("[Scheduled]: ERROR", e);
        }
    }

    private void handleEvent(ChangeStreamEvent eventData) {
        try {
            log.info("[Process]: Thread [{}] processing data id: {}", Thread.currentThread().getName(), eventData.getId());

            if (eventData != null) {
                log.info(" --- Event received: Service: {} - id: {}", eventData.getService(), eventData.getId());
                switch (eventData.getService()) {
                    case "svc.padman":
                        padmanChangeStreamProcess.handleEvent(eventData);
                        break;
                    default:
                        log.warn(" --- No matching service for {}!", eventData.getService());
                }
            } else {
                log.warn("Event is null!");
            }

        } catch (Exception e) {
            log.error("[Process]: ERROR", e);
        }
    }
}
