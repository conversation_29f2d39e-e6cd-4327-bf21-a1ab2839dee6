package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import org.bson.types.ObjectId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaterBillInputDto implements Serializable {

    private String billCode;

    private ObjectId userId;

    @NotNull
    private String customerCode;

    @NotNull
    private String meterNumber;

    @NotNull
    private Integer year;

    @NotNull
    private Integer month;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date startDate;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date endDate;

    @NotNull
    private Integer oldIndex;

    @NotNull
    private Integer newIndex;

    @NotNull
    private Integer consumedAmount;

    @NotNull
    private Float paymentAmount;

    @NotNull
    private boolean paid;
}
