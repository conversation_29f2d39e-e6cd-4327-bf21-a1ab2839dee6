package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "flattenedDossier")
public class FlattenedDossier implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private String dossierCode;  // mã hồ sơ

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId procedureId; // ID thủ tục

    private String procedureName; // Tên thủ tục

    private String procedureCode; // Mã thủ tục

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId sectorId; // ID lĩnh vực

    private String sectorName; // Tên lĩnh vực

    private String agencyLevelName; // Cấp thủ tục/cơ quan

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyLevelId; // Cấp thủ tục/cơ quan

    private String procedureLevelName; // Mức độ thủ tục

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId procedureLevelId; // Mức độ thủ tục

    private String applicantName; // Tên người nộp

    private String applicantPhoneNumber; // SDT người nộp

    private String accepterName; // Tên cán bộ tiếp nhận

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskAssigneeId; // Id cán bộ xử lý hiện tại
    
    private String currentTaskAssigneeName; // Tên cán bộ xử lý hiện tại

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskAgencyId; // Id cơ quan xử lý hiện tại
    
    private String currentTaskAgencyName; // Tên cơ quan xử lý hiện tại

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskAgencyAncestorsLevel1Id;    // Cơ quan tổ tiên (cơ quan cha của agency)

    private String currentTaskAgencyAncestorsLevel1Name;    // Cơ quan tổ tiên (cơ quan cha của agency)

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskAgencyAncestorsLevel2Id;    // Cơ quan tổ tiên ( cha của level 1)

    private String currentTaskAgencyAncestorsLevel2Name;    // Cơ quan tổ tiên ( cha của level 1)

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskAgencyAncestorsLevel3Id;    // Cơ quan tổ tiên ( cha của level 2)

    private String currentTaskAgencyAncestorsLevel3Name;    // Cơ quan tổ tiên ( cha của level 2)

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskAgencyAncestorsLevel4Id;    // Cơ quan tổ tiên ( cha của level 3)

    private String currentTaskAgencyAncestorsLevel4Name;    // Cơ quan tổ tiên ( cha của level 3)

    private Integer dosserStatusId;    // Trạng thái hồ sơ

    private String dossierStatusName;    // Tên Trạng thái hồ sơ

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId dossierTaskStatusId;    // Trạng thái xử lý hồ sơ

    private String dossierTaskStatusName;    // Tên Trạng thái xử lý hồ sơ

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId dossierMenuTaskRemindId;    // Trạng thái nhắc việc

    private String dossierMenuTaskRemindName;    // Tên Trạng thái  nhắc việc

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;    // Cơ quan thực hiện

    private String agencyName;    // Cơ quan thực hiện
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyAncestorsLevel1Id;    // Cơ quan tổ tiên (cơ quan cha của agency)

    private String agencyAncestorsLevel1Name;    // Cơ quan tổ tiên (cơ quan cha của agency)

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyAncestorsLevel2Id;    // Cơ quan tổ tiên ( cha của level 1)

    private String agencyAncestorsLevel2Name;    // Cơ quan tổ tiên ( cha của level 1)

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyAncestorsLevel3Id;    // Cơ quan tổ tiên ( cha của level 2)

    private String agencyAncestorsLevel3Name;    // Cơ quan tổ tiên ( cha của level 2)

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyAncestorsLevel4Id;    // Cơ quan tổ tiên ( cha của level 3)

    private String agencyAncestorsLevel4Name;    // Cơ quan tổ tiên ( cha của level 3)

    private Integer applyMethodId; // ID Hình thức tiếp nhận 0 trực tuyến/ 1 trực tiếp

    private String applyMethodName; // tên Hình thức tiếp nhận

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId dossierReceivingKindId; // ID hình thức nhận kết quả

    private String dossierReceivingKindName; // tên hình thức nhận kết quả

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId paymentMethodId; // ID Phương thức thanh toán

    private String paymentMethodName; // tên Phương thức thanh toán

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;                                   // ngay nop

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createDate;                                   // ngay tao

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;                                  // ngay tiep nhan

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;                               // ngay hen tra

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;                                 // ngay co ket qua

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date returnedDate;                                  // ngay tra ket qua

    /// xử lý logic sẵn, nếu đúng = 1, sai = 0
    private Integer isProcedureLevel12 = 0;  // TTHC có mức độ 1,2: Thông tin trực tuyến

    private Integer isProcedureLevel3 = 0;  // TTHC có mức độ 3: Một phần

    private Integer isProcedureLevel4 = 0;  // TTHC có mức độ 4: Toàn trình

    private Integer isNoFees = 0;           // Hồ sơ không có lệ phí

    private Integer isPaid = 0;             // Hồ sơ Đã thanh toán

    private Integer isUnPaid = 0;           // Hồ sơ Chưa thanh toán

    private Integer isPartiallyPaid = 0;    // Hồ sơ Đã thanh toán 1 phần

    private Integer receivedOnline = 0;    // tiepNhanTrucTuyen

    private Integer receivedDirect = 0;    // tiepNhanTrucTiep


    private Integer resolved = 0;          // Đã xử lý

    /// chi 1 trường trong (*) = 1, tất cả = 0
    private Integer resolvedEarly = 0;     // (*) Đã xử lý trước hạn completedDate < appointmentDate

    private Integer resolvedOnTime = 0;    // (*) Đã xử lý đúng hạn completedDate = appointmentDate

    private Integer resolvedOverdue = 0;   // (*) Đã xử lý quá hạn completedDate > appointmentDate

    private Integer unresolved = 0;        // Đang xử lý

    /// chi 1 trường trong (*) = 1, tất cả = 0
    private Integer unresolvedOnTime = 0;  // (*) Đang xử  lý trong hạn appointmentDate > toDate

    private Integer unresolvedOverdue = 0; // (*) Đang xử lý quá hạ appointmentDate < toDate

    private Integer paused = 0;            // (*) Tạm dừng xử lý

    private Integer suspended = 0;         // (*) Dừng xử lý

    private Integer cancelled = 0;         // (*) Đã hủy

}
