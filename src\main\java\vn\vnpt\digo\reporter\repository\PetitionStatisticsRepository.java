package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.PetitionStatistics;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface PetitionStatisticsRepository extends PagingAndSortingRepository<PetitionStatistics, ObjectId> {
    
    public Integer deleteObjectById(ObjectId id);
}
