package vn.vnpt.digo.reporter.repository;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.Agency;
import vn.vnpt.digo.reporter.dto.AgencyNameDto;
import vn.vnpt.digo.reporter.dto.GetDossierByAgencyDto;
import vn.vnpt.digo.reporter.dto.ProcedureQuantityBySectorDto;
import vn.vnpt.digo.reporter.dto.SectorByAgencyDto;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface AgencyRepository extends MongoRepository<Agency, ObjectId> {

    //Digo 1216
    @Query(value = "{$or: [{'placeId': ?0}, {'ancestorPlaceId': ?0}], 'procedure.activeQuantity': {'$gte': 1}}", fields = "{'originId': 1, 'name':1}")
    List<AgencyNameDto> getListByActiveProcedure(ObjectId placeId);

    //Digo 1206  
    @Query(value = "{$and:[ {'placeId': ?0, $or:[{'procedure.thirdLeverQuantity': {'$gte':1}},{'procedure.fourthLevelQuantity': {'$gte':1}}] }]}", fields = "{'originId': 1, 'name':1}")
    List<AgencyNameDto> getListByOnlineProcedure(ObjectId placeId, Short languageId);

    @Query(value = "{ 'originId': ?0 }", fields = "{'originId': 1, 'sector':1}")
    List<SectorByAgencyDto> getListSectorBySector(ObjectId originId);

    @Query(value = "{ 'placeId': ?0 }", fields = "{'originId': 1, 'name':1, 'sector':1}")
    List<ProcedureQuantityBySectorDto> getListProcedureQuantityBySector(ObjectId placeId);

    @Query(value = "{ $and: [ "
            + "{'originId': { :#{#originId != null ? '$eq' : '$ne'} : :#{#originId != null ? #originId : '0'} }},"
            + "{'tagAgency': { :#{#tagId != null ? '$eq' : '$ne'} : :#{#tagId != null ? #tagId : '0'} }},"
            + "{'dossier.year': { :#{#year != null ? '$eq' : '$ne'} : :#{#year != null ? #year : '0'} }},"
            + "{$or: ["
            + "{'parentId': { :#{#parentId != null ? '$eq' : '$ne'} : :#{#parentId != null ? #parentId : '0'} }},"
            + "{'ancestors.id': { :#{#parentId != null ? '$eq' : '$ne'} : :#{#parentId != null ? #parentId : '0'} }}"
            + "]},"
            + "{'deploymentId': :#{#deploymentId}}"
            + "]}")
    Slice<GetDossierByAgencyDto> getAgencyDossier(@Param("originId") ObjectId originId, @Param("tagId") ObjectId tagId, @Param("deploymentId") ObjectId deploymentId, @Param("parentId") ObjectId parentId, @Param("year") Integer year, Pageable pageable);

    @Query(value = "{'originId': :#{#originId}, 'deploymentId': :#{#deploymentId} }")
    Agency getAgency(@Param("originId") ObjectId originId, @Param("deploymentId") ObjectId deploymentId);

    @Query(value = "{$and: ["
            + "?#{ [0] == null ? { $where : 'true'} : { 'placeId' : [0] } }, "
            + "?#{ [1] == null ? { $where : 'true'} : { 'tagAgency' : [1] } }, "
            + "?#{ [2] == null ? { $where : 'true'} : { 'originId' : [2] } }, "
            + "?#{ [3] == null ? { $where : 'true'} : { 'ancestorPlaceId' : [3] } }, "
            + "]}")
    List<ProcedureQuantityBySectorDto> getListProcedureQuantityByTag(ObjectId placeId, ObjectId tagId, ObjectId agencyId, ObjectId ancestorId);

    @Query(value = "{ 'originId': ?0 }")
    ArrayList<Agency> getQuantityBySectorAgency(ObjectId agencyId);

}
