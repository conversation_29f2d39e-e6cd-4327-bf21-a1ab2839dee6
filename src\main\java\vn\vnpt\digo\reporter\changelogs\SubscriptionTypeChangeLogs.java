/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-05-21-08-45")
public class SubscriptionTypeChangeLogs {

    @ChangeSet(order = "2020-05-21-08-45", id = "SubscriptionTypeChangeLogs::create", author = "haimn")
    public void create(DB db) {
        db.createCollection("subscriptionType", null);
    }
}
