package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AgencyByProcedure implements Serializable {

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String name;

    private AgencyNameByProcedure level;

    private ObjectId parentId;
}
