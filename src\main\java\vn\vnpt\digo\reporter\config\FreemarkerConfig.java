/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.config;

import freemarker.core.TemplateClassResolver;
import freemarker.template.TemplateExceptionHandler;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "file")
public class FreemarkerConfig {

    private String uploadDir;

    @Bean
    public freemarker.template.Configuration getFreeMarkerConfiguration() throws IOException {

        freemarker.template.Configuration cfg = new freemarker.template.Configuration(freemarker.template.Configuration.VERSION_2_3_30);
        cfg.setDirectoryForTemplateLoading(new File(uploadDir));
        cfg.setDefaultEncoding(StandardCharsets.UTF_8.toString());
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        cfg.setLogTemplateExceptions(false);
        cfg.setWrapUncheckedExceptions(true);
        cfg.setAPIBuiltinEnabled(false);
        cfg.setNewBuiltinClassResolver(TemplateClassResolver.ALLOWS_NOTHING_RESOLVER);

        return cfg;
    }

}
