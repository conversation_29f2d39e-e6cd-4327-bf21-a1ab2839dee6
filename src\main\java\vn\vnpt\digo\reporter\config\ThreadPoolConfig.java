package vn.vnpt.digo.reporter.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Data
@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfig {
    @Value("${thread.corepoolsize}")
    private Integer corepoolsize;

    @Value("${thread.maxpoolsize}")
    private Integer maxpoolsize;

    @Value("${thread.queuecapacity}")
    private Integer queuecapacity;

    @Value("${thread.poolexecutor}")
    private Integer poolexecutor;
    
    @Bean(name = "taskExecutor")
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corepoolsize);
        executor.setMaxPoolSize(maxpoolsize);
        executor.setQueueCapacity(queuecapacity);
        //
        switch (poolexecutor) {
            case 1:
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
                break;
            case 2:
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                break;
            case 3:
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
                break;
            case 4:
                executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
                break;
        }
        //
        executor.initialize();

        return executor;
    }
}
