/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changestream.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;


/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierStatus implements Serializable {

    @Field("id")
    private int id;
    private ArrayList<TranslateName> name;
    @Null
    private String comment;

    private String description;
    
    private int numberDateRequire;
    
    private Date dateRequire;
    
    public DossierStatus(int id, ArrayList<TranslateName> name, String comment) {
        this.id = id;
        this.name = name;
        this.comment = comment;
    }
}
