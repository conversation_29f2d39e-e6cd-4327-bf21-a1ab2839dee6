/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.stream.messaging;

import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

/**
 *
 * <AUTHOR>
 */
public interface AgencyProducer {

    public static final String OUTPUT = "agencyProducerRequestOut";

    @Output(value = "agencyProducerRequestOut")
    public MessageChannel output();

    
}
