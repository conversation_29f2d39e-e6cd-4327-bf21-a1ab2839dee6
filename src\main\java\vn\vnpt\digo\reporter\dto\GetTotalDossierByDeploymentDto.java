/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetTotalDossierByDeploymentDto implements Serializable {
    
    @JsonProperty("agencyId")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    
    @JsonProperty("deploymentId")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    
    private Number total;
    
}
