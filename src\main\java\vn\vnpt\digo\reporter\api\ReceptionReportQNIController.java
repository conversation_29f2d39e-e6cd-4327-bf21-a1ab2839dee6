package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.document.AgencyFilterReportQni;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.service.AgencyFilterReportQniService;
import vn.vnpt.digo.reporter.service.ReceptionReportQNIService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/reception-report-qni")
@IcodeAuthorize("vnpt.permission.manageDigo")
public class ReceptionReportQNIController {
    @Autowired
    private ReceptionReportQNIService receptionReportQNIService;
    Logger logger = LoggerFactory.getLogger(GeneralReportQNIController.class);

    @PutMapping("/--update-deadline-accepteddate")
    public ImportResponseDto updateCompletedDate(
            HttpServletRequest request,
            @RequestParam(value = "from-date") String fromDate,
            @RequestParam(value = "to-date") String toDate,
            @RequestParam(value = "ignore-exist") Boolean checkExist,
            @RequestParam(value = "is-timesheet-v2") Boolean isTimeSheetV2
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return receptionReportQNIService.updateDealineAcceptedDate(fromDate, toDate, checkExist, isTimeSheetV2);
    }

    @PutMapping("/--update-deadline-accepteddate-v2")
    public ImportResponseDto updateCompletedDateV2(
            HttpServletRequest request,
            @RequestParam(value = "from-date") String fromDate,
            @RequestParam(value = "to-date") String toDate,
            @RequestParam(value = "ignore-exist") Boolean checkExist,
            @RequestParam(value = "is-timesheet-v2") Boolean isTimeSheetV2
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return receptionReportQNIService.updateDeadlineAcceptedDateV2(fromDate, toDate, checkExist, isTimeSheetV2);
    }

    @PutMapping("/--update-deadline-list-dossier")
    public ImportResponseDto updateDeadlineListDossier(
            HttpServletRequest request,
            @RequestParam(value = "is-sleep", defaultValue = "false") Boolean isSleep,
            @RequestParam(value = "sleep-time", defaultValue = "2") Integer sleepTime,
            @RequestParam(value = "is-timesheet-v2", defaultValue = "true") Boolean isTimeSheetV2,
            @RequestBody UpdateDeadlineDossierDto dto
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return receptionReportQNIService.updateDeadlineListDossier(dto, isSleep, sleepTime, isTimeSheetV2);
    }

    @GetMapping(value = "")
    public List<ReceptionReportDto> getProcedureQuantityByTag(HttpServletRequest request,
                                                              @RequestParam(value = "from", required = true) String fromDate,
                                                              @RequestParam(value = "to", required = true) String toDate,
                                                              @RequestParam(value = "arr-agency", required = true) List<String> agencyIds) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<ReceptionReportDto> reportResult = null;
        reportResult = receptionReportQNIService.findDossiersWithConditions(agencyIds, fromDate, toDate);
        logger.info("DIGO-Info: " + reportResult.size());
        return reportResult;
    }

    @GetMapping(value = "/--detail")
    public PageImpl<DetailReceptionDossierDto> getSlowReceptionDetail(HttpServletRequest request,
                                                                              @RequestParam(value = "from", required = true) String fromDate,
                                                                              @RequestParam(value = "to", required = true) String toDate,
                                                                              @RequestParam(value = "arr-agency", required = true) List<String> agencyIds,
                                                                              @RequestParam(value = "procedure", required = false) String procedureNames,
                                                                              @RequestParam(value = "type") int type,
                                                                              Pageable pageable
                                                           ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        PageImpl<DetailReceptionDossierDto> reportResult = null;
        reportResult = receptionReportQNIService.getSlowReceptionDetail(agencyIds, fromDate, toDate, procedureNames, type, pageable);
        logger.info("DIGO-Info: " + reportResult.getTotalElements());
        return reportResult;
    }

    @GetMapping("/--detail/--export")
    public ResponseEntity<Object> exportDossierReceptionDetail(
            HttpServletRequest request,
            @RequestParam(value = "from", required = true) String fromDate,
            @RequestParam(value = "to", required = true) String toDate,
            @RequestParam(value = "arr-agency", required = true) List<String> agencyIds,
            @RequestParam(value = "type", required = true) int type,
            @RequestParam(value = "procedure", required = false) String procedureName
    ){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        ResponseEntity<Object> result = null;
        result = receptionReportQNIService.exportDossierReceptionDetail(fromDate, toDate, agencyIds, type, procedureName);
        return result;
    }
}
