/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-07-24-04-40")
public class AgencyChangeLogs {
    @ChangeSet(order = "2020-07-24-04-40", id = "AgencyChangeLogs::create", author = "mongtt")
    public void create(DB db) {
        db.createCollection("agency", null);
    }
}
