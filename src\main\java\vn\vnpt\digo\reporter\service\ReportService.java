package vn.vnpt.digo.reporter.service;

import com.google.common.base.Strings;
import net.minidev.json.JSONObject;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.document.Reporter;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.repository.ReportRepository;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Microservice;
import vn.vnpt.digo.reporter.util.MicroserviceExchange;
import vn.vnpt.digo.reporter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Service
public class ReportService {


    @Value("${digo.microservice.gateway-url}")
    private String gatewayURL;

    private static RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private Microservice microservice;

    @Autowired
    private Translator translator;

    @Autowired
    private ReportRepository reportRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @CacheEvict(value = {"postReport"}, allEntries = true)
    public AffectedRowsDto postReport(PostReportDto body){
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        if(Objects.nonNull(body.getFile())){
            Reporter reporter = new Reporter();
            reporter.setCreatedDate(new Date());
            reporter.setUpdateDate(new Date());
            reporter.setFromDate(body.getFromDate());
            reporter.setToDate(body.getToDate());
            reporter.setFile(body.getFile());
            reporter.setKeyword(body.getKeyword());
            reporter.setReportType(body.getReportType());
            reporter.setReportName(body.getReportName());
            reporter.setUserAgencyId(body.getUserAgencyId());
            reporter.setNation(body.getNation());
            reporter.setDistrict(body.getDistrict());
            reporter.setProvince(body.getProvince());
            reporter.setWard(body.getWard());
            reporter.setSector(body.getSector());
            reporter.setProcedure(body.getProcedure());
            reporter.setReceivingService(body.getReceivingService());
            reporter.setApplyMethod(body.getApplyMethod());
            reporter.setSort(body.getSort());
            reporter.setHeaderArray(body.getHeaderArray());
            reporter.setAgencyName(body.getAgencyName());
            try {
                reportRepository.save(reporter);
                affectedRows = new AffectedRowsDto(1);
            } catch (Exception ex) {
                throw new ExceptionInInitializerError(ex.getMessage());
            }
        }else {
            throw new DigoHttpException(10008, new String[]{translator.toLocale("lang.word.report")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return affectedRows;
    }

    // Tạo báo cáo gồm tên, loại báo cáo, từ ngày đến ngày, trạng thái, đơn vị
    public String postReportProcessing(PostReportDto body) {
        Reporter reporter = new Reporter();
        reporter.setCreatedDate(new Date());
        reporter.setUpdateDate(new Date());
        reporter.setFromDate(body.getFromDate());
        reporter.setToDate(body.getToDate());
        reporter.setReportType(body.getReportType());
        reporter.setReportName(body.getReportName());
        reporter.setUserAgencyId(body.getUserAgencyId());
        reporter.setAgencyName(body.getAgencyName());
        reporter.setStatus("Đang tạo");

        reportRepository.save(reporter);
        return reporter.getId().toString();
    }

    public void updateReportFile(ObjectId id, PostReportDto body) {
        Reporter reporter = mongoTemplate.findById(id, Reporter.class);
        if (reporter != null) {
            reporter.setFile(body.getFile());
            reporter.setUpdateDate(new Date());
            reporter.setStatus("Tạo thành công");
            reportRepository.save(reporter);
        }
    }

    public void updateReportError(ObjectId id) {
        Reporter reporter = mongoTemplate.findById(id, Reporter.class);
        if (reporter != null) {
            reporter.setStatus("Tạo thất bại");
            reportRepository.save(reporter);
        }
    }

    public String checkReportExist(ObjectId id) {
        Reporter reporter = mongoTemplate.findById(id, Reporter.class);
        if (reporter != null) {
            return reporter.getId().toString();
        }
        return "0";
    }

    @CacheEvict(value = {"putReport"}, allEntries = true)
    public AffectedRowsDto putReportName(ObjectId id, PostReportDto body){
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        Reporter reporter = mongoTemplate.findById(id, Reporter.class);
        if(Objects.nonNull(reporter)){
            if(Objects.nonNull(body.getReportName())) {
                reporter.setReportName(body.getReportName());
                reporter.setUpdateDate(new Date());
                try {
                    reportRepository.save(reporter);
                    affectedRows = new AffectedRowsDto(1);
                } catch (Exception ex) {
                    throw new ExceptionInInitializerError(ex.getMessage());
                }
            }
        }else {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.report")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return affectedRows;
    }

    public AffectedRowsDto deleteReport(ObjectId id) {

        AffectedRowsDto affectedRows = new AffectedRowsDto();
        Reporter reporter = mongoTemplate.findById(id, Reporter.class);
        if(Objects.nonNull(reporter)){
            try {
                if (reporter.getFile() != null ) {
                    ObjectId fileId = reporter.getFile().getId();
                    reportRepository.delete(reporter);
                    String endpoint = this.gatewayURL + "/" + microservice.getPathCode("fileman") + "/file/"
                            + fileId.toHexString();
                    HashMap<String, Object> body = new HashMap<>();
                    JSONObject json = new JSONObject(body);
                    String token = Context.getJwtAuthenticationTokenValue();
                    if (token == null) {
                        token = MicroserviceExchange.getToken().getAccessToken();
                    }
                    Map<String, Object> params = new HashMap<>();
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("Content-Type", "application/json");
                    headers.setBearerAuth(token);
                    HttpEntity<String> request = new HttpEntity<>(json.toJSONString(), headers);
                    ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.DELETE, request, String.class);
                    affectedRows = new AffectedRowsDto(1);
                } else {
                    reportRepository.delete(reporter);
                    affectedRows = new AffectedRowsDto(1);
                }
            } catch (Exception ex) {
                throw new ExceptionInInitializerError(ex.getMessage());
            }
        }else {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.report")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return affectedRows;
    }

    public Slice<GetReporterDto> getReportByKeyword( String keyword, String userAgency, String reportType, Pageable pageable) {
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<Criteria>();
        criteriaList.add(Criteria.where("reportName").regex(keyword, "i"));
        if(Objects.nonNull(userAgency)){
            criteriaList.add(Criteria.where("userAgencyId").is(userAgency));
        }

        if (!Strings.isNullOrEmpty(reportType)) {
            criteriaList.add(Criteria.where("reportType").is(reportType));
        }

        if (criteriaList.size() > 0) {
            query.addCriteria(new Criteria().andOperator(
                    criteriaList.toArray(new Criteria[criteriaList.size()])));
        }
        query.with(pageable);
        long offset = pageable.getPageNumber() * pageable.getPageSize();
        query.skip(offset);
        query.limit(pageable.getPageSize());
        List<Reporter> listReport = mongoTemplate.find(query, Reporter.class);
        long totalCount = mongoTemplate.count(Query.of(query).limit(-1).skip(-1), Reporter.class);
        Page<GetReporterDto> getlistReportSlice = new PageImpl(listReport, pageable, totalCount);
        return getlistReportSlice;
    }
}
