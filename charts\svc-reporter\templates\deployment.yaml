kind: Deployment
apiVersion: apps/v1
metadata:
  name: svc-reporter
  namespace: {{namespace}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: svc-reporter
  template:
    metadata:
      labels:
        app: svc-reporter
    spec:
      volumes:
        - name: svc-reporter
          configMap:
            name: svc-reporter.hazelcast
            items:
              - key: hazelcast-config.xml
                path: hazelcast-config.xml
            defaultMode: 420
      containers:
        - name: svc-reporter
          image: 'hub.vnptioffice.vn:443/svc-reporter:{{version}}' 
          ports:
            - containerPort: 8080
              protocol: TCP
          envFrom:
            - configMapRef:
                name: svc-reporter.env
          imagePullPolicy: Always
          volumeMounts:
            - name: svc-reporter
              mountPath: /usr/local/digo
      imagePullSecrets:
        - name: private-hub