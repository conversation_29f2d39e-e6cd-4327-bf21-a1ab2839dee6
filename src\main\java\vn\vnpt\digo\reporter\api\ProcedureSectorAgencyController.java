/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.api;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import java.util.ArrayList;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.service.AgencyService;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.service.ProcedureSectorAgencyService;

/**
 *
 * <AUTHOR> PC
 */
@RestController
@RequestMapping("/procedure-sector-agency")
@IcodeAuthorize("vnpt.permission.manageDigo")
public class ProcedureSectorAgencyController {
    
    @Autowired
    private ProcedureSectorAgencyService procedureSectorAgencyService;

    Logger logger = LoggerFactory.getLogger(ProcedureSectorAgencyController.class);
    
    @PutMapping("/--update-procedure-sector")
    public AffectedRowsDto updateProcedureSector(HttpServletRequest request, 
            @RequestBody ArrayList<ProcedureAgencySectorDto> procedureAgencySectorDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        AffectedRowsDto listByActiveProcedure = procedureSectorAgencyService.updateProcedureSector(procedureAgencySectorDto);
        return listByActiveProcedure;
    }

    @PutMapping("/--update-procedure-sector-by-level")
    public AffectedRowsDto updateProcedureSectorByLevel(HttpServletRequest request,
                                                        @RequestBody ArrayList<ProcedureAgencySectorDto> procedureAgencySectorDto,
                                                        @RequestParam(value = "agency-level-id", required = false) String agencyLevelId
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        AffectedRowsDto listByActiveProcedure = procedureSectorAgencyService.updateProcedureSectorByLevel(procedureAgencySectorDto, agencyLevelId);
        return listByActiveProcedure;
    }

    @PutMapping("/--update-quantity-sector-procedure")
    public AffectedRowsDto updateQuantitySectorProcedure(HttpServletRequest request,
                                                   @RequestBody UpdateQuantityProcedureDto updateQuantityProcedure) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        return procedureSectorAgencyService.updateQuantitySectorProcedure(updateQuantityProcedure);
    }
    
    @GetMapping(value = "/--procedure-quantity-by-tag")
    public List<GetAgencyQuantityByProcedureSectorDto> getProcedureQuantityByTag(HttpServletRequest request, 
            @RequestParam(value = "tag-id", required = false) ObjectId tagId,
            @RequestParam(value = "agency-id", required = false) ObjectId agencyId,
            @RequestParam(value = "agency-level-id", required = false) ArrayList<ObjectId> agencyLevelId,
            @RequestParam(value = "not-agency-level-id", required = false) ArrayList<ObjectId> notAgencyLevelId,
            @RequestParam(value = "ancestor-id", required = false) ObjectId ancestorId,
            @RequestParam(value = "place-id", required = false) ObjectId placeId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetAgencyQuantityByProcedureSectorDto> listSector = procedureSectorAgencyService.getListProcedureQuantityByTag(placeId, tagId, agencyId,ancestorId,agencyLevelId,notAgencyLevelId);

        logger.info("DIGO-Info: " + listSector.size());
        return listSector;
    }

    @PutMapping("/--update-sector-by-agency")
    public AffectedRowsDto updateSectorByAgency(
            HttpServletRequest request,
            @RequestParam(value = "is-replace", required = false) Boolean isReplace,
            @RequestBody UpdateSectorByAgencyDto dto
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return procedureSectorAgencyService.updateSectorByAgency(dto, isReplace);
    }
    
}
