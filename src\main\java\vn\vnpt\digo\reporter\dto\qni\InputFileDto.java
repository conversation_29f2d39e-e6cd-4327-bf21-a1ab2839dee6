/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InputFileDto {
    
    @NotNull
    private MultipartFile file;

    private List<MultipartFile> formFiles;
    
}
