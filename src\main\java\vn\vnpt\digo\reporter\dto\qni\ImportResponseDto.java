package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportResponseDto {

    private int successRows;

    private String successMessage;

    private String insertedRows;

    private List<String> errorMessages = new ArrayList<>();

}

