package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.dto.DossierCountingDto;
import vn.vnpt.digo.reporter.dto.DossierSimpleDataDto;
import vn.vnpt.digo.reporter.pojo.Attachment;
import vn.vnpt.digo.reporter.pojo.DossierCountingData;
import vn.vnpt.digo.reporter.pojo.DossierOutOfDate;
import vn.vnpt.digo.reporter.util.HideSecurityInformationHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "dossierCountingLog")
public class DossierCountingLog {

  @Id
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId id;

  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId dossierId;

  @JsonSerialize(using = ToStringSerializer.class)
  private List<ObjectId> dossierCountingId;
  private String sectorName;
  private String procedureName;
  private String dossierCode;
  private String dossierStatus;
  private String phone;
  private String fullName;
  private String organization;
  private Date acceptedDate;
  private String address;
  private String totalImplementDate;
  private String agency;
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId agencyId;
  private int outOfDateCode; // 0: don't care, 1: OutOfDate at TaxAgency,2: OutOfDate at remaining Agency
  private List<DossierOutOfDate> listDossierOutOfDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date appointmentDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date completedDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date returnedDate;

  private String applyMethod;
  private int applyMethodId;
  // 0 : Inprogress On Time
  // 1 : Inprogress Out Of Due
  // 2 : Completed  Early
  // 3 : Completed On Time
  // 4 : Completed Out Of Due
  // 5 : Canceled
  // 6 : Suspended
  private int  dossierDetailStatus;
  private String implementer;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date createdDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date updatedDate;

  private ArrayList<Attachment> attachment;

  @Override
  public int hashCode() {
    return Objects.hash(dossierId);
  }

  @Override
  public boolean equals(Object o) {

    if (o == this) return true;
    if (!(o instanceof DossierCountingLog)) {
      return false;
    }
    DossierCountingLog doss= (DossierCountingLog) o;
    return Objects.equals(dossierId, doss.dossierId) && (dossierDetailStatus == doss.getDossierDetailStatus());
//    return Objects.equals(dossierId, doss.dossierId) && (dossierDetailStatus ==doss.dossierDetailStatus );
  }
  public  String convertDossierIdToString(){
    return this.dossierId.toString();
  }

  public  void setHideSecurityInformation(String fullName){
    this.fullName = HideSecurityInformationHelper.setFullnameSecurity(fullName);
  }
}
