package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.pojo.ProcedureTranslate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "procedure")
public class ProcedureDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @JsonIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId originId;
    
    @JsonIgnore
    private List<ProcedureTranslate> translate;
    private String name;

    private Integer dossierQuantity;

    private Integer order = 0;

    public void setTransName(Short localeId) {
        this.translate.forEach(item -> {
            if (item.getLanguageId().equals(localeId)) {
                this.name = item.getName();
            }
        });
    }
}
