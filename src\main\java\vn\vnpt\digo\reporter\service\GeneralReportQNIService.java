/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.mongodb.MongoClient;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.reporter.document.QNIETLDossier;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.util.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.google.common.base.Strings.isNullOrEmpty;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static vn.vnpt.digo.reporter.util.MicroserviceExchange.postNoAuth;

@Service
public class GeneralReportQNIService {
    Logger logger = org.slf4j.LoggerFactory.getLogger(GeneralReportQNIService.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    private static final RestTemplate restTemplate = new RestTemplate();
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    TimeZone timezone = TimeZone.getTimeZone("GMT");
    @Autowired
    private MongoConverter mongoConverter;
    @Autowired
    private Microservice microservice;
    @Autowired
    private Translator translator;

    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI)
    private Resource resourceTemplateDossierStatisticAssigneeQNI;

    @Autowired
    private MongoClient client;

    @Value("${digo.dossier.off-time}")
    private String offTime;

    @Autowired
    private AgencyFilterReportQniService agencyFilterReportQniService;

    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;

    @Value("${digo.enable.hide.fullname}")
    private boolean enableHideName;

    private final Gson gson = new Gson();

    private static List<ObjectId> convertToObjectIdList(List<String> hexStrings) {
        List<ObjectId> objectIdList = new ArrayList<>();
        for (String hexString : hexStrings) {
            objectIdList.add(new ObjectId(hexString));
        }
        return objectIdList;
    }

    public Document documentDangGiaiQuyetQuaHan(Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                                new Document("$lte", Arrays.asList("$acceptedDate", toDate))
                        )),
                        new Document("$or", Arrays.asList(
                                new Document("$ne", Arrays.asList("$processingTime", 0)),
                                new Document("$eq", Arrays.asList("$processingTime", -1))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$gt", Arrays.asList("$cancelledDate", toDate))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$cancelledDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$gt", Arrays.asList("$withdrawDate", toDate))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                new Document("$or", Arrays.asList(
                                        new Document("$or", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                        new Document("$lte", Arrays.asList("$pauseDate", toDate))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                        new Document("$lte", Arrays.asList("$extendDate", toDate))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        )),
                                                        new Document("$lte", Arrays.asList(
                                                                new Document("$arrayElemAt", Arrays.asList("$additionalDate", 1)), toDate))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$gt", Arrays.asList(toDate, "$appointmentDateNew"))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$gt", Arrays.asList("$financialObligationsDate", "$appointmentDateNew"))
                                                )),
                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                new Document("$gt", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                        ))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                ))
                                                        ))
                                                )),
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                new Document("$gt", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                        ))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                ))
                                                        ))
                                                ))
                                        ))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$lt", Arrays.asList(toDate, "$completedDate")),
                                new Document("$or", Arrays.asList(
                                        new Document("$or", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                        new Document("$lte", Arrays.asList("$pauseDate", toDate))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                        new Document("$lte", Arrays.asList("$extendDate", toDate))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        )),
                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 1)), toDate))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$gt", Arrays.asList(toDate, "$appointmentDateNew"))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$gt", Arrays.asList("$financialObligationsDate", "$appointmentDateNew"))
                                                )),
                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                new Document("$gt", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                        ))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                ))
                                                        ))
                                                )),
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                new Document("$gt", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                        ))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                ))
                                                        ))
                                                ))
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getDangGiaiQuyetQuaHan(Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDangGiaiQuyetQuaHan(toDate),
                1, 0)
        );
    }

    public Document documentTiepNhanTrucTuyen(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$eq", Arrays.asList("$applyMethod._id", 0)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$appointmentDate", false))
        ));
    }

    public Document getTiepNhanTrucTuyen(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanTrucTuyen(fromDate, toDate),
                1, 0)
        );
    }

    public Document documentAppointmentDateNew() {
        Document condStage = new Document("$cond", new Document()
                .append("if", new Document("$and", Arrays.asList(
                        new Document("$ifNull", Arrays.asList("$originalappointmentDateNew", false)),
                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$originalappointmentDateNew"))
                )))
                .append("then", "$originalappointmentDateNew")
                .append("else", "$appointmentDate"));

        return condStage;
    }

    public Document documentTiepNhanKyTruocGiaiQuyetQuaHan(Date previousDate) {
        return new Document("$and", Arrays.asList(
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                                new Document("$lte", Arrays.asList(
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$acceptedDate")),
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate))))
                        )),
                        new Document("$or", Arrays.asList(
                                new Document("$ne", Arrays.asList("$processingTime", 0)),
                                new Document("$eq", Arrays.asList("$processingTime", -1))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$gt", Arrays.asList(
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$cancelledDate")),
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate))))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$cancelledDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$gt", Arrays.asList(
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$withdrawDate")),
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate))))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                                        new Document("$and", Arrays.asList(
                                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                                new Document("$gt", Arrays.asList(
                                                                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)),
                                                                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$appointmentDateNew"))))
                                                                        ))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$gt", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                        new Document("$gt", Arrays.asList("$financialObligationsDate", "$appointmentDateNew"))
                                                                        //new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                ))
                                                        )),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                                        new Document("$lte", Arrays.asList("$pauseDate", previousDate))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                                        new Document("$lte", Arrays.asList("$extendDate", previousDate))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                                        new Document("$gte", Arrays.asList(
                                                                                new Document("$size", "$additionalDate"),
                                                                                2
                                                                        )),
                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 1)), previousDate))
                                                                ))
                                                        ))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                        new Document("$lt", Arrays.asList(
                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)),
                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$completedDate"))))
                                                )),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$gt", Arrays.asList(
                                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)),
                                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$appointmentDateNew"))))
                                                                ))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$gt", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$gt", Arrays.asList("$financialObligationsDate", "$appointmentDateNew"))
                                                                //new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                        )),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                                        new Document("$lte", Arrays.asList("$pauseDate", previousDate))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                                        new Document("$lte", Arrays.asList("$extendDate", previousDate))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                                        new Document("$gte", Arrays.asList(
                                                                                new Document("$size", "$additionalDate"),
                                                                                2
                                                                        )),
                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 1)), previousDate))
                                                                ))
                                                        ))
                                                ))
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getTiepNhanKyTruocGiaiQuyetQuaHan(Date previousDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanKyTruocGiaiQuyetQuaHan(previousDate),
                1, 0)
        );
    }

    public Document documentTiepNhanKyTruocGiaiQuyetTrongHan(Date previousDate) {
        return new Document("$and", Arrays.asList(
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$lte", Arrays.asList(
                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$acceptedDate")),
                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)))),
                        new Document("$and", Arrays.asList(
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$pauseDate", false))),
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                new Document("$gt", Arrays.asList("$pauseDate", previousDate))
                                        ))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDate", false))),
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                new Document("$gt", Arrays.asList("$extendDate", previousDate))
                                        ))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false))),
                                        new Document("$or", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$lte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                1
                                                        ))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        )),
                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 1)), previousDate))
                                                ))
                                        ))

                                ))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$gt", Arrays.asList(
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$cancelledDate")),
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate))))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$cancelledDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$gt", Arrays.asList(
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$withdrawDate")),
                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate))))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$processingTime", 0)),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                new Document("$gt", Arrays.asList(
                                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$completedDate")),
                                                        new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate))))
                                        )),
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false)))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$or", Arrays.asList(
                                        new Document("$ne", Arrays.asList("$processingTime", 0)),
                                        new Document("$eq", Arrays.asList("$processingTime", -1))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                                        new Document("$lte", Arrays.asList(
                                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)),
                                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$appointmentDateNew"))))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                        new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false)))
                                                                ))
                                                        ))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                        new Document("$lt", Arrays.asList(
                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)),
                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$completedDate"))))
                                                )),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList(
                                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", previousDate)),
                                                                                new Document("$dateToString", new Document("format", "%Y-%m-%d").append("date", "$appointmentDateNew"))))
                                                                ))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew")),
                                                                new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                        ))
                                                ))
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getTiepNhanKyTruoc(Date previousDate) {
        return new Document("$or", Arrays.asList(documentTiepNhanKyTruocGiaiQuyetQuaHan(previousDate), documentTiepNhanKyTruocGiaiQuyetTrongHan(previousDate)));
    }

    public Document getTiepNhan(Date fromDate, Date toDate, Date previousDate) {
        return new Document("$or", Arrays.asList(documentTiepNhanTrucTuyen(fromDate, toDate),
                documentHoSoTiepNhanConLai(fromDate, toDate),
                documentTiepNhanKyTruocGiaiQuyetQuaHan(previousDate),
                documentTiepNhanKyTruocGiaiQuyetTrongHan(previousDate)));
    }

    public Document getDangXuLy(Date toDate) {
        return new Document("$or", Arrays.asList(documentDangGiaiQuyetTrongHan(toDate), documentDangGiaiQuyetQuaHan(toDate)));
    }

    public Document getDaXuLy(Date fromDate, Date toDate) {
        return new Document("$or", Arrays.asList(documentDaXuLyDungHanCondition(fromDate, toDate), documentDaXuLySomHanCondition(fromDate, toDate), documentDaXuLyQuaHanCondition(fromDate, toDate)));
    }

    public Document getTiepNhanKyTruocGiaiQuyetTrongHan(Date previousDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanKyTruocGiaiQuyetTrongHan(previousDate),
                1, 0)
        );
    }

    private Document documentHoSoTiepNhanConLai(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                new Document("$ne", Arrays.asList("$applyMethod._id", 0)),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$ifNull", Arrays.asList("$appointmentDate", false))
        ));
    }

    public Document getHoSoTiepNhanConLai(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoTiepNhanConLai(fromDate, toDate),
                1, 0)
        );
    }

    public Document documentDangGiaiQuyetTrongHan(Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                        ))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                        new Document("$and", Arrays.asList(
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$pauseDate", false))),
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                new Document("$gt", Arrays.asList("$pauseDate", toDate))
                                        ))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDate", false))),
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                new Document("$gt", Arrays.asList("$extendDate", toDate))
                                        ))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false))),
                                        new Document("$or", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$lte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                1
                                                        ))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        )),
                                                        new Document("$gt", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 1)), toDate))
                                                ))
                                        ))

                                ))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$gt", Arrays.asList("$cancelledDate", toDate))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$cancelledDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$gt", Arrays.asList("$withdrawDate", toDate))
                        )),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$processingTime", 0)),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                new Document("$lt", Arrays.asList(toDate, "$completedDate"))
                                        )),
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false)))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$or", Arrays.asList(
                                        new Document("$ne", Arrays.asList("$processingTime", 0)),
                                        new Document("$eq", Arrays.asList("$processingTime", -1))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                ))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew"))
                                                                )),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                        new Document("$lte", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                                )),
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                                ))
                                                                        ))
                                                                )),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                        new Document("$lte", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                                )),
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                                ))
                                                                        ))
                                                                ))
                                                        ))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                        new Document("$lt", Arrays.asList(toDate, "$completedDate"))
                                                )),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false))),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                ))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$lte", Arrays.asList("$financialObligationsDate", "$appointmentDateNew"))
                                                                )),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false)),
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                        new Document("$lte", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                                )),
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                                ))
                                                                        ))
                                                                )),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$financialObligationsDate", false)),
                                                                        new Document("$ifNull", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), false))
                                                                )),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                        new Document("$or", Arrays.asList(
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$ne", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew")),
                                                                                        new Document("$lte", Arrays.asList(toDate, "$appointmentDateNew"))
                                                                                )),
                                                                                new Document("$and", Arrays.asList(
                                                                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 1)),
                                                                                        new Document("$lte", Arrays.asList(new Document("$arrayElemAt", Arrays.asList("$additionalDate", 0)), "$appointmentDateNew"))
                                                                                ))
                                                                        ))
                                                                ))
                                                        ))
                                                ))
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getDangGiaiQuyetTrongHan(Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDangGiaiQuyetTrongHan(toDate),
                1, 0)
        );
    }

    public Document documentDaXuLySomHanCondition(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                                new Document("$eq", Arrays.asList("$processingTime", 0)),
                                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                                new Document("$and", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$pauseDate", false))),
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDate", false))),
                                        new Document("$or", Arrays.asList(
                                                new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false))),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$lte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                1
                                                        ))
                                                ))
                                        ))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$or", Arrays.asList(
                                        new Document("$ne", Arrays.asList("$processingTime", 0)),
                                        new Document("$eq", Arrays.asList("$processingTime", -1))
                                )),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                                                new Document("$eq", Arrays.asList("$processingTime", 0)),
                                                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                                                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                                                new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$pauseDate", false))),
                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDate", false))),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false))),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                                        new Document("$lte", Arrays.asList(
                                                                                new Document("$size", "$additionalDate"),
                                                                                1
                                                                        ))
                                                                ))
                                                        ))
                                                ))
                                        )),
                                        new Document("$and", Arrays.asList(
                                                new Document("$or", Arrays.asList(
                                                        new Document("$ne", Arrays.asList("$processingTime", 0)),
                                                        new Document("$eq", Arrays.asList("$processingTime", -1))
                                                )),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$pauseDate", false))),
                                                        new Document("$not", new Document("$ifNull", Arrays.asList("$extendDate", false))),
                                                        new Document("$or", Arrays.asList(
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false))),
                                                                new Document("$and", Arrays.asList(
                                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                                        new Document("$lte", Arrays.asList(
                                                                                new Document("$size", "$additionalDate"),
                                                                                1
                                                                        ))
                                                                ))
                                                        ))
                                                )),
                                                new Document("$or", Arrays.asList(
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                                                                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                                                                new Document("$lt", Arrays.asList("$completedDate", "$appointmentDateNew"))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$gte", Arrays.asList("$cancelledDate", fromDate)),
                                                                new Document("$lte", Arrays.asList("$cancelledDate", toDate)),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                                                new Document("$lt", Arrays.asList("$cancelledDate", "$appointmentDateNew"))
                                                        )),
                                                        new Document("$and", Arrays.asList(
                                                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                                                new Document("$gte", Arrays.asList("$cancelledDate", fromDate)),
                                                                new Document("$lte", Arrays.asList("$cancelledDate", toDate)),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false))),
                                                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                                                new Document("$lt", Arrays.asList("$cancelledDate", "$appointmentDateNew"))
                                                        ))
                                                ))
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getDaXuLySomHanCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDaXuLySomHanCondition(fromDate, toDate),
                1, 0)
        );
    }

    public Document documentDaXuLyDungHanCondition(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$processingTime", 0)),
                        new Document("$eq", Arrays.asList("$processingTime", -1))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                        new Document("$and", Arrays.asList(
                                new Document("$not", new Document("$ifNull", Arrays.asList("$pauseDate", false))),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$extendDate", false))),
                                new Document("$or", Arrays.asList(
                                        new Document("$not", new Document("$ifNull", Arrays.asList("$additionalDate", false))),
                                        new Document("$and", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                new Document("$lte", Arrays.asList(
                                                        new Document("$size", "$additionalDate"),
                                                        1
                                                ))
                                        ))
                                ))
                        ))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                                new Document("$eq", Arrays.asList("$completedDate", "$appointmentDateNew"))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                new Document("$gte", Arrays.asList("$cancelledDate", fromDate)),
                                new Document("$lte", Arrays.asList("$cancelledDate", toDate)),
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$eq", Arrays.asList("$cancelledDate", "$appointmentDateNew")),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false)))
                        ))
                ))
        ));
    }

    public Document getDaXuLyDungHanCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDaXuLyDungHanCondition(fromDate, toDate),
                1, 0)
        );
    }

    public Document documentDaXuLyQuaHanCondition(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                        new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false)))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$ne", Arrays.asList("$processingTime", 0)),
                        new Document("$eq", Arrays.asList("$processingTime", -1))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate))
                )),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$completedDate", false)),
                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                new Document("$gte", Arrays.asList("$completedDate", fromDate)),
                                new Document("$lte", Arrays.asList("$completedDate", toDate)),
                                new Document("$or", Arrays.asList(
                                        new Document("$or", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        ))
                                                ))
                                        )),
                                        new Document("$gt", Arrays.asList("$completedDate", "$appointmentDateNew"))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                new Document("$gte", Arrays.asList("$withdrawDate", fromDate)),
                                new Document("$lte", Arrays.asList("$withdrawDate", toDate)),
                                new Document("$gte", Arrays.asList("$cancelledDate", fromDate)),
                                new Document("$lte", Arrays.asList("$cancelledDate", toDate)),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                new Document("$or", Arrays.asList(
                                        new Document("$gt", Arrays.asList("$withdrawDate", "$appointmentDateNew")),
                                        new Document("$or", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        ))
                                                ))
                                        ))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                new Document("$gte", Arrays.asList("$withdrawDate", fromDate)),
                                new Document("$lte", Arrays.asList("$withdrawDate", toDate)),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$cancelledDate", false))),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                new Document("$or", Arrays.asList(
                                        new Document("$or", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        ))
                                                ))
                                        )),
                                        new Document("$gt", Arrays.asList("$withdrawDate", "$appointmentDateNew"))
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                new Document("$ifNull", Arrays.asList("$cancelledDate", false)),
                                new Document("$ifNull", Arrays.asList("$appointmentDateNew", false)),
                                new Document("$gte", Arrays.asList("$cancelledDate", fromDate)),
                                new Document("$lte", Arrays.asList("$cancelledDate", toDate)),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$withdrawDate", false))),
                                new Document("$not", new Document("$ifNull", Arrays.asList("$completedDate", false))),
                                new Document("$or", Arrays.asList(
                                        new Document("$or", Arrays.asList(
                                                new Document("$ifNull", Arrays.asList("$pauseDate", false)),
                                                new Document("$ifNull", Arrays.asList("$extendDate", false)),
                                                new Document("$and", Arrays.asList(
                                                        new Document("$ifNull", Arrays.asList("$additionalDate", false)),
                                                        new Document("$gte", Arrays.asList(
                                                                new Document("$size", "$additionalDate"),
                                                                2
                                                        ))
                                                ))
                                        )),
                                        new Document("$gt", Arrays.asList("$cancelledDate", "$appointmentDateNew"))
                                ))
                        ))
                ))
        ));
    }

    public Document getDaXuLyQuaHanCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDaXuLyQuaHanCondition(fromDate, toDate),
                1, 0)
        );
    }

    public Document getHoSoRutCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$ifNull", Arrays.asList("$withdrawDate", false)),
                        new Document("$gte", Arrays.asList("$withdrawDate", fromDate)),
                        new Document("$lte", Arrays.asList("$withdrawDate", toDate)),
                        new Document("$ifNull", Arrays.asList("$appointmentDate", false))
                )),
                1, 0)
        );
    }

    public Document getHoSoTrucTiepCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$eq", Arrays.asList("$applyMethod._id", 1)),
                        new Document("$ne", Arrays.asList("$applicant.hinhThucNop", 3)),
                        new Document("$ne", Arrays.asList("$applicant.hinhThucNop", 4)),
                        new Document("$ne", Arrays.asList("$applicant.hinhThucNop", 5)),
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                        new Document("$or", Arrays.asList(
                                new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$and", Arrays.asList(
                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                        new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                                ))
                        )),
                        new Document("$ifNull", Arrays.asList("$appointmentDate", false))
                )),
                1, 0)
        );
    }

    public Document getHoSoBuuChinhCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$eq", Arrays.asList("$applyMethod._id", 1)),
                        new Document("$eq", Arrays.asList("$applicant.hinhThucNop", 3)),
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                        new Document("$or", Arrays.asList(
                                new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$and", Arrays.asList(
                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                        new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                                ))
                        )),
                        new Document("$ifNull", Arrays.asList("$appointmentDate", false))
                )),
                1, 0)
        );
    }

    public Document getHoSoBuuChinhCongIchCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$eq", Arrays.asList("$applyMethod._id", 1)),
                        new Document("$eq", Arrays.asList("$applicant.hinhThucNop", 4)),
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                        new Document("$or", Arrays.asList(
                                new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$and", Arrays.asList(
                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                        new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                                ))
                        )),
                        new Document("$ifNull", Arrays.asList("$appointmentDate", false))
                )),
                1, 0)
        );
    }

    public Document getHoSoSmartPhoneCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$eq", Arrays.asList("$applyMethod._id", 0)),
                        new Document("$eq", Arrays.asList("$applicant.hinhThucNop", 5)),
                        new Document("$ifNull", Arrays.asList("$acceptedDate", false)),
                        new Document("$gte", Arrays.asList("$acceptedDate", fromDate)),
                        new Document("$lte", Arrays.asList("$acceptedDate", toDate)),
                        new Document("$or", Arrays.asList(
                                new Document("$ne", Arrays.asList("$dossierStatus._id", 6)),
                                new Document("$and", Arrays.asList(
                                        new Document("$eq", Arrays.asList("$dossierStatus._id", 6)),
                                        new Document("$ifNull", Arrays.asList("$withdrawDate", false))
                                ))
                        )),
                        new Document("$ifNull", Arrays.asList("$appointmentDate", false))
                )),
                1, 0)
        );
    }

    public String keyAddField(Integer reportType) {

        String keyAddField = "";
        switch (reportType) {
            case 0:
                keyAddField = "ten_don_vi_cha";
                break;
            case 1:
                keyAddField = "ten_linh_vuc";
                break;
            case 2:
                keyAddField = "ten_thu_tuc";
                break;
            default:
                break;
        }

        return keyAddField;
    }

    public String valueAddField(Integer reportType) {

        String valueAddField = "";
        switch (reportType) {
            case 0:
                valueAddField = "$agency.parent.name";
                break;
            case 1:
                valueAddField = "$sector.name";
                break;
            case 2:
                valueAddField = "$procedure.name";
                break;
            default:
                break;
        }

        return valueAddField;
    }

    public Criteria buildCriteria(List<ObjectId> agencyIds,
                                  Integer reportType,
                                  List<String> sectorId,
                                  List<String> procedureId,
                                  String[] option) throws JSONException {
        List<Criteria> criteriaList = new ArrayList<>();
        Criteria criteriaAgency = null;

        criteriaAgency = new Criteria().orOperator(
                Criteria.where("agency._id").in(agencyIds)
        );
        criteriaList.add(criteriaAgency);

        switch (reportType) {
            case 0: // tổng hợp
                option[0] = "agencyBaby.idAgency";
                option[1] = "ten_don_vi_cha";
                option[2] = "agency.name";
                option[3] = "agency._id";
                break;
            case 1: // lĩnh vực
                option[0] = "sector.idSector";
                option[1] = "ten_linh_vuc";
                option[2] = "sector.name";
                option[3] = "sector._id";

                if (sectorId != null && !sectorId.isEmpty()) {
                    List<ObjectId> sectorObjectIds = convertToObjectIdList(sectorId);
                    Criteria additionalCriteria = new Criteria().andOperator(
                            Criteria.where("sector._id").in(sectorObjectIds)
                    );

                    criteriaList.add(additionalCriteria);
                }

                break;
            case 2: // thủ tục
                option[0] = "procedure.idProcedure";
                option[1] = "ten_thu_tuc";
                option[2] = "procedure.name";
                option[3] = "procedure._id";

                if (procedureId != null && !procedureId.isEmpty()) {
                    List<ObjectId> procedureObjectIds = convertToObjectIdList(procedureId);
                    Criteria additionalCriteria = new Criteria().andOperator(
                            Criteria.where("procedure._id").in(procedureObjectIds)
                    );

                    criteriaList.add(additionalCriteria);
                }

                break;
            default:
                option[0] = "agencyBaby.idAgency";
                option[1] = "ten_don_vi_cha";
                option[2] = "agency.name";
                option[3] = "agency._id";
                break;
        }

        Criteria criteria = new Criteria();
        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));

        return criteria;
    }

    public Document docunemtMatch(Integer type, Date fromDate, Date toDate, Date previousDate) {

        Document document = new Document();

        switch (type) {
            case 3: // tổng tiếp nhận
                document = getTiepNhan(fromDate, toDate, previousDate);
                break;
            case 4: // tiếp nhận trực tuyến
                document = getTiepNhanTrucTuyen(fromDate, toDate);
                break;
            case 5: // lấy hồ sơ tiếp nhận còn lại
                document = getHoSoTiepNhanConLai(fromDate, toDate);
                break;
            case 6: // lấy hồ sơ tiếp nhận kỳ trước
                document = getTiepNhanKyTruoc(previousDate);
                break;
            case 7: // lấy hồ sơ đã giải quyết
                document = getDaXuLy(fromDate, toDate);
                break;
            case 8: // lấy hồ sơ đã giải quyết trước hạn
                document = getDaXuLySomHanCondition(fromDate, toDate);
                break;
            case 9: // lấy hồ sơ đã giải quyết đúng hạn
                document = getDaXuLyDungHanCondition(fromDate, toDate);
                break;
            case 89: // trước hạn + đúng hạn
                break;
            case 10:
                document = getDaXuLyQuaHanCondition(fromDate, toDate);
                break;
            case 11: // lấy hồ sơ đang giải quyết
                document = getDangXuLy(toDate);
                break;
            case 12: // lấy hồ sơ đang giải quyết trong hạn
                document = getDangGiaiQuyetTrongHan(toDate);
                break;
            case 13: // lấy hồ sơ đang giải quyết quá hạn
                document = getDangGiaiQuyetQuaHan(toDate);
                break;
            case 14: // lấy hồ sơ dừng xử lý
                break;
            case 15: // lấy hồ sơ rút
                document = getHoSoRutCondition(fromDate, toDate);
                break;
            case 1415:
                break;
            case 16:
                break;
            case 17: // lấy hồ sơ nhận trực tiếp
                document = getHoSoTrucTiepCondition(fromDate, toDate);
                break;
            case 18: // lấy hồ sơ bưu chính
                document = getHoSoBuuChinhCondition(fromDate, toDate);
                break;
            case 19: // lấy hồ sơ bưu chính công ích
                document = getHoSoBuuChinhCongIchCondition(fromDate, toDate);
                break;
            case 20: // láy hồ sơ smartphone
                document = getHoSoSmartPhoneCondition(fromDate, toDate);
                break;
            case 21: // láy hồ sơ smartphone
                document = getHoSoRutCondition(fromDate, toDate);
                break;
        }

        return document;
    }

    public Aggregation getAggregationDetail(Date fromDate,
                                            Date toDate,
                                            List<ObjectId> agencyIds,
                                            Date previousDate,
                                            Integer reportType,
                                            List<String> sectorId,
                                            List<String> procedureId,
                                            Integer type,
                                            Pageable pageable,
                                            Boolean ignorePagination) throws JSONException {

        String[] option = new String[4];

        AggregationOperation matchOperation = Aggregation.match(buildCriteria(agencyIds, reportType, sectorId, procedureId, option));

        // addfield appointmentDate
        AggregationOperation projectAppointmentDateNew = context -> {
            Document projection = new Document();
            projection.put("appointmentDateNew", documentAppointmentDateNew());

            return new Document("$addFields", projection);
        };

        Document documentExpr = new Document("$expr", docunemtMatch(type, fromDate, toDate, previousDate));
        AggregationOperation matchCondition = context -> {
            return new Document("$match", documentExpr);
        };

        AggregationOperation groupOperation = Aggregation.group("_id")
                .first("code").as("dossierCode")
                .first("acceptedDate").as("acceptedDate")
                .first("appointmentDate").as("appointmentDate")
                .first("completedDate").as("completedDate")
                .first("applicant.ownerFullName").as("ownerFullName")
                .first("applicant.noiDungYeuCauGiaiQuyet").as("noiDungYeuCauGiaiQuyet")
                .first("applicant.phoneNumber").as("phoneNumber")
                .first("currentTask.assignee.fullname").as("assigneeFullname")
                .first("sector.name").as("sectorName")
                .first("procedure.name").as("procedureName")
                .first("currentTask.agency.name").as("curTaskAgencyName")
                .first("dossierStatus.name").as("dossierStatusName");

        AggregationOperation finalProjectOperation = Aggregation.project()
                .andInclude("_id")
                .and(DateOperators.DateToString.dateOf("acceptedDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("acceptedDate")
                .and(DateOperators.DateToString.dateOf("appointmentDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("appointmentDate")
                .and(DateOperators.DateToString.dateOf("completedDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("completedDate")
                .and("dossierCode").as("dossierCode")
                .and("ownerFullName").as("applicantOwnerFullName")
                .and("noiDungYeuCauGiaiQuyet").as("noiDungYeuCauGiaiQuyet")
                .and("phoneNumber").as("applicantPhoneNumber")
                .and("assigneeFullname").as("assigneeFullname")
                .and("sectorName").as("sectorName")
                .and("procedureName").as("procedureName")
                .and("curTaskAgencyName").as("curTaskAgencyName")
                .and("dossierStatusName").as("dossierStatusName");

        List<AggregationOperation> aggregationOperations = null;
        if (ignorePagination) {
            aggregationOperations = Arrays.asList(
                    projectAppointmentDateNew,
                    matchOperation,
                    matchCondition,
                    groupOperation,
                    finalProjectOperation
            );
        } else {
            AggregationOperation skipStage = Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize());
            AggregationOperation limitStage = Aggregation.limit(pageable.getPageSize());

            AggregationOperation totalCountGroupOperation = Aggregation.group().count().as("totalCount");
            AggregationOperation totalCountProjectOperation = Aggregation.project()
                    .andExclude("_id")
                    .andInclude("totalCount");

            FacetOperation pagedFacet = new FacetOperation().and(groupOperation, finalProjectOperation, skipStage, limitStage).as("pagedResults")
                    .and(totalCountGroupOperation, totalCountProjectOperation).as("totalCount");

            aggregationOperations = Arrays.asList(
                    projectAppointmentDateNew,
                    matchOperation,
                    matchCondition,
                    pagedFacet
            );
        }

        AggregationOptions options = AggregationOptions.builder()
                .allowDiskUse(true)
                .build();
        return Aggregation.newAggregation(aggregationOperations).withOptions(options);
    }

    private List<String> getRootAgencies(List<String> agencyIds) throws JSONException {
        String getObjUrl;
        String jsonString;
        JSONArray jsonArr;

        List<String> rootAgencyIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(agencyIds)) {
            getObjUrl = "qni-agency/--find-root?id=" + String.join(",", agencyIds);
            getObjUrl = microservice.basedataUri(getObjUrl).toUriString();
            try {
                jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
                jsonArr = new JSONArray(jsonString);
                for (int i = 0; i < jsonArr.length(); i++) {
                    rootAgencyIds.add(jsonArr.getJSONObject(i).get("id").toString());
                }
            } catch (Exception ex) {
                logger.error("Can not find root agencies ", ex);
            }
        }
        return rootAgencyIds;
    }

    private List<GeneralReportDto.CustomSummary> getReportWithAgency(List<String> agencyIds, List<GeneralReportDto> generalReportDto) throws JSONException {
        Comparator<GeneralReportDto.CustomSummary> agencyComparator = Comparator.comparing(agency -> agency.getAgency().getName());
        var rootAgencys = getRootAgencies(agencyIds);
        var getObjUrl = microservice.basedataUri("agency/--by-parent-agency?arr-id=" + String.join(",", rootAgencys)).toUriString();
        var jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
        var jsonArr = new JSONArray(jsonString);

        List<GeneralReportDto.Agency> agencyTransList = new ArrayList<>();

        if (Objects.nonNull(jsonArr)) {
            for (int i = 0; i < jsonArr.length(); i++) {
                String agencyIdTemp = jsonArr.getJSONObject(i).get("id").toString();
                var target = new GeneralReportDto.Agency();
                target.setIdAgency(agencyIdTemp);
                target.setName(jsonArr.getJSONObject(i).get("name").toString());

                agencyTransList.add(target);
            }
        }

        List<GeneralReportDto.CustomSummary> resultsAgency = new ArrayList<>();
        for (GeneralReportDto.Agency agency : agencyTransList) {
            var target = new GeneralReportDto.CustomSummary(null, null, agency, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
            for (GeneralReportDto generalReport : generalReportDto) {

                AgencyFilterReportQniResponse agencyTemp = null;
                List<String> agencyTemps = null;
                try {
                    agencyTemp = agencyFilterReportQniService.getDocumentsByAgencyId(agency.getId());
                    if (agencyTemp != null) {
                        agencyTemps = new ArrayList<>(uniqueStringAgency(agencyTemp.getIdFilter()));
                    }
                } catch (Exception e) {
                    continue;
                }

                if (agencyTemps.contains(generalReport.getAgencyBaby().getId())) {
                    target.setReceivedOnline(target.getReceivedOnline() + generalReport.getReceivedOnline());
                    target.setReceivedDirect(target.getReceivedDirect() + generalReport.getReceivedDirect());
                    target.setReceived(target.getReceived() + generalReport.getReceived());
                    target.setReceivedOld(target.getReceivedOld() + generalReport.getReceivedOld());
                    target.setUnresolvedOnTime(target.getUnresolvedOnTime() + generalReport.getUnresolvedOnTime());
                    target.setUnresolvedOverdue(target.getUnresolvedOverdue() + generalReport.getUnresolvedOverdue());
                    target.setResolvedEarly(target.getResolvedEarly() + generalReport.getResolvedEarly());
                    target.setResolvedOnTime(target.getResolvedOnTime() + generalReport.getResolvedOnTime());
                    target.setResolvedOverdue(target.getResolvedOverdue() + generalReport.getResolvedOverdue());
                    target.setWithdraw(target.getWithdraw() + generalReport.getWithdraw());
                    target.setResolved(target.getResolved() + generalReport.getResolved());
                    target.setUnresolved(target.getUnresolved() + generalReport.getUnresolved());
                    target.setDirect(target.getDirect() + generalReport.getDirect());
                    target.setReceivedPostal(target.getReceivedPostal() + generalReport.getReceivedPostal());
                    target.setReceivedPublicPostal(target.getReceivedPublicPostal() + generalReport.getReceivedPublicPostal());
                    target.setReceivedSmartphone(target.getReceivedSmartphone() + generalReport.getReceivedSmartphone());
                }
            }

            resultsAgency.add(target);
        }

        return resultsAgency.stream().filter(x ->
                x.getReceivedOnline() > 0 ||
                        x.getReceivedDirect() > 0 ||
                        x.getReceived() > 0 ||
                        x.getReceivedOld() > 0 ||
                        x.getUnresolvedOnTime() > 0 ||
                        x.getUnresolvedOverdue() > 0 ||
                        x.getResolvedEarly() > 0 ||
                        x.getResolvedOnTime() > 0 ||
                        x.getResolvedOverdue() > 0 ||
                        x.getWithdraw() > 0 ||
                        x.getResolved() > 0 ||
                        x.getUnresolved() > 0 ||
                        x.getDirect() > 0 ||
                        x.getReceivedPostal() > 0 ||
                        x.getReceivedPublicPostal() > 0 ||
                        x.getReceivedSmartphone() > 0
        ).sorted(
                agencyComparator
        ).collect(Collectors.toList());

    }

    public Aggregation getAggregation(Date fromDate, Date toDate, List<ObjectId> agencyIds, Date previousDate, Integer reportType, List<String> sectorId, List<String> procedureId) throws JSONException {

        String[] option = new String[6];

        AggregationOperation matchOperation = Aggregation.match(buildCriteria(agencyIds, reportType, sectorId, procedureId, option));

        // addfield appointmentDate
        AggregationOperation projectAppointmentDateNew = context -> {
            Document projection = new Document();
            projection.put("appointmentDateNew", documentAppointmentDateNew());

            return new Document("$addFields", projection);
        };

        AggregationOperation projectOperation = context -> {
            Document projection = new Document();
            projection.put("tiep_nhan_truc_tuyen", getTiepNhanTrucTuyen(fromDate, toDate));
            projection.put("tiep_nhan_con_lai", getHoSoTiepNhanConLai(fromDate, toDate));
            projection.put("tiep_nhan_ky_truoc_giai_quyet_trong_han", getTiepNhanKyTruocGiaiQuyetTrongHan(previousDate));
            projection.put("tiep_nhan_ky_truoc_giai_quyet_qua_han", getTiepNhanKyTruocGiaiQuyetQuaHan(previousDate));
            projection.put("dang_giai_quyet_trong_han", getDangGiaiQuyetTrongHan(toDate));
            projection.put("dang_giai_quyet_qua_han", getDangGiaiQuyetQuaHan(toDate));
            projection.put("da_xu_ly_som_han", getDaXuLySomHanCondition(fromDate, toDate));
            projection.put("da_xu_ly_dung_han", getDaXuLyDungHanCondition(fromDate, toDate));
            projection.put("da_xu_ly_qua_han", getDaXuLyQuaHanCondition(fromDate, toDate));
            projection.put("ho_so_rut", getHoSoRutCondition(fromDate, toDate));
            projection.put("ho_so_truc_tiep", getHoSoTrucTiepCondition(fromDate, toDate));
            projection.put("ho_so_buu_chinh", getHoSoBuuChinhCondition(fromDate, toDate));
            projection.put("ho_so_buu_chinh_cong_ich", getHoSoBuuChinhCongIchCondition(fromDate, toDate));
            projection.put("ho_so_smart_phone", getHoSoSmartPhoneCondition(fromDate, toDate));

            projection.put(keyAddField(reportType), valueAddField(reportType));

            // add agency
            projection.put("ma_don_vi_cha", "$agency.parent._id");
            projection.put("ten_don_vi_con", "$agency.name");

            return new Document("$addFields", projection);
        };

        AggregationOperation groupOperation = Aggregation.group(option[3])
                .sum("tiep_nhan_truc_tuyen").as("receivedOnline")
                .sum("tiep_nhan_con_lai").as("receivedDirect")
                .sum("tiep_nhan_ky_truoc_giai_quyet_trong_han").as("tiep_nhan_ky_truoc_giai_quyet_trong_han")
                .sum("tiep_nhan_ky_truoc_giai_quyet_qua_han").as("tiep_nhan_ky_truoc_giai_quyet_qua_han")
                .sum("dang_giai_quyet_trong_han").as("unresolvedOnTime")
                .sum("dang_giai_quyet_qua_han").as("unresolvedOverdue")
                .sum("da_xu_ly_som_han").as("resolvedEarly")
                .sum("da_xu_ly_dung_han").as("resolvedOnTime")
                .sum("da_xu_ly_qua_han").as("resolvedOverdue")
                .sum("ho_so_rut").as("withdraw")
                .sum("ho_so_truc_tiep").as("direct")
                .sum("ho_so_buu_chinh").as("receivedPostal")
                .sum("ho_so_buu_chinh_cong_ich").as("receivedPublicPostal")
                .sum("ho_so_smart_phone").as("receivedSmartphone")
                .first(keyAddField(reportType)).as(keyAddField(reportType))
                .first("ma_don_vi_cha").as("ma_don_vi_cha")
                .first("ten_don_vi_con").as("ten_don_vi_con");

        AggregationOperation finalProjectOperation = Aggregation.project()
                .andExclude("_id")
                .and("_id").as(option[0])
                .and(option[1]).as(option[2])
                .and("ma_don_vi_cha").as("agency.idAgency")
                .and("ten_don_vi_con").as("agencyBaby.name")
                .and("receivedOnline").as("receivedOnline")
                .and("receivedDirect").as("receivedDirect")
                .andExpression("tiep_nhan_ky_truoc_giai_quyet_trong_han + tiep_nhan_ky_truoc_giai_quyet_qua_han + receivedOnline + receivedDirect").as("received")
                .and("tiep_nhan_ky_truoc_giai_quyet_trong_han").plus("tiep_nhan_ky_truoc_giai_quyet_qua_han").as("receivedOld")
                .and("unresolvedOnTime").as("unresolvedOnTime")
                .and("unresolvedOverdue").as("unresolvedOverdue")
                .and("resolvedEarly").as("resolvedEarly")
                .and("resolvedOnTime").as("resolvedOnTime")
                .and("resolvedOverdue").as("resolvedOverdue")
                .and("withdraw").as("withdraw")
                .andExpression("resolvedEarly + resolvedOnTime + resolvedOverdue").as("resolved")
                .andExpression("unresolvedOnTime + unresolvedOverdue").as("unresolved")
                .and("direct").as("direct")
                .and("receivedPostal").as("receivedPostal")
                .and("receivedPublicPostal").as("receivedPublicPostal")
                .and("receivedSmartphone").as("receivedSmartphone");

        return Aggregation.newAggregation(
                matchOperation,
                projectAppointmentDateNew,
                projectOperation,
                groupOperation,
                finalProjectOperation
        );
    }

    private Set<ObjectId> uniqueObjectIdAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<ObjectId> uniqueObjectIds = new HashSet<>();
        Set<String> uniqueStringIds = new HashSet<>();
        for (String objectIdString : objectIdStrings) {
            try {
                ObjectId objectId = new ObjectId(objectIdString);
                uniqueObjectIds.add(objectId);
                uniqueStringIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    private Set<String> uniqueStringAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<String> uniqueObjectIds = new HashSet<>();

        for (String objectIdString : objectIdStrings) {
            try {
                uniqueObjectIds.add(objectIdString);
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid ObjectId: " + objectIdString);
            }
        }

        return uniqueObjectIds;
    }

    public List<GeneralReportDto.CustomSummary> getGeneralReportDto(List<String> agencyIds, String fromDateString, String toDateString, Integer reportType, List<String> sectorId, List<String> procedureId) {
        List<GeneralReportDto.CustomSummary> results = new ArrayList<>();
        logger.info("Begin getGeneralReportByAgencyDto");

        try {

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }

            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);
            this.df.setTimeZone(this.timezone);

            Date fromDate = this.df.parse(fromDateString);
            Date toDate = this.df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            Date previousDate = calendar.getTime();

            if (reportType == null) {
                reportType = 0;
            }

            Aggregation aggregation = getAggregation(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId);
            AggregationResults<GeneralReportDto> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", GeneralReportDto.class);
            List<GeneralReportDto> generalReportDto = resultAggregation.getMappedResults();

            Comparator<GeneralReportDto.CustomSummary> procedureComparator = Comparator.comparing(procedure -> procedure.getProcedure().getName());
            Comparator<GeneralReportDto.CustomSummary> sectorComparator = Comparator.comparing(sector -> sector.getSector().getName());

            if (reportType == 0)
                return getReportWithAgency(agencyIds, generalReportDto);

            var generalReportDtoModify = generalReportDto.stream().filter(x ->
                    x.getReceivedOnline() > 0 ||
                            x.getReceivedDirect() > 0 ||
                            x.getReceived() > 0 ||
                            x.getReceivedOld() > 0 ||
                            x.getUnresolvedOnTime() > 0 ||
                            x.getUnresolvedOverdue() > 0 ||
                            x.getResolvedEarly() > 0 ||
                            x.getResolvedOnTime() > 0 ||
                            x.getResolvedOverdue() > 0 ||
                            x.getWithdraw() > 0 ||
                            x.getResolved() > 0 ||
                            x.getUnresolved() > 0 ||
                            x.getDirect() > 0 ||
                            x.getReceivedPostal() > 0 ||
                            x.getReceivedPublicPostal() > 0 ||
                            x.getReceivedSmartphone() > 0
            );

            results = generalReportDtoModify
                    .map(GeneralReportDto::toCustomObject)
                    .sorted(
                            reportType == 1 ? sectorComparator : procedureComparator
                    )
                    .collect(Collectors.toList());

            if (reportType == 1) {
                Map<String, GeneralReportDto.CustomSummary> resultMap = results.stream()
                        .collect(Collectors.toMap(
                                data -> data.getSector().getName(),
                                data -> data,
                                (data1, data2) -> {

                                    return new GeneralReportDto.CustomSummary(
                                            null,
                                            new GeneralReportDto.Sector(
                                                    data1.getSector().getIdSector() + "," + data2.getSector().getIdSector(),
                                                    data1.getSector().getName(),
                                                    data1.getSector().getCode()
                                            ),
                                            null,
                                            data1.getReceivedOnline() + data2.getReceivedOnline(),
                                            data1.getReceivedDirect() + data2.getReceivedDirect(),
                                            data1.getReceived() + data2.getReceived(),
                                            data1.getReceivedOld() + data2.getReceivedOld(),
                                            data1.getUnresolvedOnTime() + data2.getUnresolvedOnTime(),
                                            data1.getUnresolvedOverdue() + data2.getUnresolvedOverdue(),
                                            data1.getResolvedEarly() + data2.getResolvedEarly(),
                                            data1.getResolvedOnTime() + data2.getResolvedOnTime(),
                                            data1.getResolvedOverdue() + data2.getResolvedOverdue(),
                                            data1.getWithdraw() + data2.getWithdraw(),
                                            data1.getResolved() + data2.getResolved(),
                                            data1.getUnresolved() + data2.getUnresolved(),
                                            data1.getDirect() + data2.getDirect(),
                                            data1.getReceivedPostal() + data2.getReceivedPostal(),
                                            data1.getReceivedPublicPostal() + data2.getReceivedPublicPostal(),
                                            data1.getReceivedSmartphone() + data2.getReceivedSmartphone(),
                                            data1.getProcedureUsed() + data2.getProcedureUsed()
                                    );
                                },
                                LinkedHashMap::new
                        ));

                results = new ArrayList<>(resultMap.values());
            } else if (reportType == 2) {
                Map<String, GeneralReportDto.CustomSummary> resultMap = results.stream()
                        .collect(Collectors.toMap(
                                data -> data.getProcedure().getName(),
                                data -> data,
                                (data1, data2) -> {
                                    return new GeneralReportDto.CustomSummary(
                                            new GeneralReportDto.Procedure(
                                                    data1.getProcedure().getIdProcedure() + "," + data2.getProcedure().getIdProcedure(),
                                                    data1.getProcedure().getName(),
                                                    data1.getProcedure().getCode()
                                            ),
                                            null,
                                            null,
                                            data1.getReceivedOnline() + data2.getReceivedOnline(),
                                            data1.getReceivedDirect() + data2.getReceivedDirect(),
                                            data1.getReceived() + data2.getReceived(),
                                            data1.getReceivedOld() + data2.getReceivedOld(),
                                            data1.getUnresolvedOnTime() + data2.getUnresolvedOnTime(),
                                            data1.getUnresolvedOverdue() + data2.getUnresolvedOverdue(),
                                            data1.getResolvedEarly() + data2.getResolvedEarly(),
                                            data1.getResolvedOnTime() + data2.getResolvedOnTime(),
                                            data1.getResolvedOverdue() + data2.getResolvedOverdue(),
                                            data1.getWithdraw() + data2.getWithdraw(),
                                            data1.getResolved() + data2.getResolved(),
                                            data1.getUnresolved() + data2.getUnresolved(),
                                            data1.getDirect() + data2.getDirect(),
                                            data1.getReceivedPostal() + data2.getReceivedPostal(),
                                            data1.getReceivedPublicPostal() + data2.getReceivedPublicPostal(),
                                            data1.getReceivedSmartphone() + data2.getReceivedSmartphone(),
                                            data1.getProcedureUsed() + data2.getProcedureUsed()

                                    );
                                },
                                LinkedHashMap::new
                        ));

                results = new ArrayList<>(resultMap.values());
            }

            return results;

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportByAgencyDto");
        return results;
    }

    public Page<DetailGeneralReportDto.PageResult> getGeneralReportDetailDto(List<String> agencyIds,
                                                                             String fromDateString,
                                                                             String toDateString,
                                                                             Integer reportType,
                                                                             List<String> sectorId,
                                                                             List<String> procedureId,
                                                                             Integer type,
                                                                             Pageable pageable) {
        logger.info("Begin getGeneralReportDetailDto");

        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            Date previousDate = calendar.getTime();

            if (reportType == null) {
                reportType = 0;
            }

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }

            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId, type, pageable, false);

            AggregationResults<DetailGeneralReportDto> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", DetailGeneralReportDto.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults().get(0).getPagedResults();
            long totalCount = 0;
            if (!results.isEmpty()) {
                totalCount = resultAggregation.getMappedResults().get(0).getTotalCount().get(0).getTotalCount();
            }
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                // Ẩn thông tin bảo mật và gán số thứ tự
                int startIndex = pageable.getPageNumber() * pageable.getPageSize();
                for (int i = 0; i < results.size(); i++) {
                    DetailGeneralReportDto.PageResult item = results.get(i);
                    item.setHideSecurityInformation(item.getApplicantOwnerFullName(), item.getAssigneeFullname());
                    item.setNo(startIndex + i + 1);
                }
            }else{
            IntStream.range(0, results.size())
                    .forEach(i -> results.get(i).setNo((pageable.getPageNumber() * pageable.getPageSize()) + i + 1));
            }
            return new PageImpl<>(results, pageable, totalCount);

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportDetailDto");
        return null;
    }

    public List<DetailGeneralReportDto.PageResult> getAllGeneralReportDetailDto(List<String> agencyIds,
                                                                             String fromDateString,
                                                                             String toDateString,
                                                                             Integer reportType,
                                                                             List<String> sectorId,
                                                                             List<String> procedureId,
                                                                             Integer type) {
        logger.info("Begin getAllGeneralReportDetailDto");

        try {
            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            Date previousDate = calendar.getTime();

            if (reportType == null) {
                reportType = 0;
            }

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }

            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId, type, null, true);

            AggregationResults<DetailGeneralReportDto.PageResult> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", DetailGeneralReportDto.PageResult.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults();
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                // Ẩn thông tin bảo mật và gán số thứ tự
                for (int i = 0; i < results.size(); i++) {
                    DetailGeneralReportDto.PageResult item = results.get(i);
                    item.setHideSecurityInformation(item.getApplicantOwnerFullName(), item.getAssigneeFullname());
                    item.setNo(i + 1);
                }
            }else{
            IntStream.range(0, results.size())
                    .forEach(i -> results.get(i).setNo(i + 1));
            }

            return results;

        } catch (Exception e) {
            logger.error("Error getAllGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getAllGeneralReportDetailDto");
        return null;
    }

    public List<DetailGeneralReportDto.PageResult> getDataExcelDto(List<String> agencyIds,
                                                                   String fromDateString,
                                                                   String toDateString,
                                                                   Integer reportType,
                                                                   List<String> sectorId,
                                                                   List<String> procedureId,
                                                                   Integer type) {
        logger.info("Begin getGeneralReportDetailDto");

        try {

            TimeZone timezone = TimeZone.getTimeZone("GMT");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            df.setTimeZone(timezone);

            Date fromDate = df.parse(fromDateString);
            Date toDate = df.parse(toDateString);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fromDate);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            Date previousDate = calendar.getTime();

            if (reportType == null) {
                reportType = 0;
            }

            StringBuilder agencyStringBuilder = new StringBuilder();
            for (String agencyTemp : agencyIds) {
                try {
                    var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                    if (Objects.isNull(target))
                        continue;
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                } catch (Exception ex) {

                }
            }

            var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());

            List<ObjectId> agencyObjectIds = new ArrayList<>(uniqueAgency);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId, type, null, true);

            AggregationResults<DetailGeneralReportDto.PageResult> resultAggregation = mongoTemplate.aggregate(aggregation, "qniETLDossier", DetailGeneralReportDto.PageResult.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults();
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
                // Ẩn thông tin bảo mật và gán số thứ tự
                for (int i = 0; i < results.size(); i++) {
                    DetailGeneralReportDto.PageResult item = results.get(i);
                    item.setHideSecurityInformation(item.getApplicantOwnerFullName(), item.getAssigneeFullname());
                    item.setNo(i + 1);
                }
            }else{
            IntStream.range(0, results.size())
                    .forEach(i -> results.get(i).setNo(i + 1));
            }
            return results;

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportDetailDto");
        return null;
    }

    public ResponseEntity<Object> exportGeneralReportDetail(String fromDate, String toDate,
                                                            List<String> agencyIds,
                                                            List<String> sectorId, List<String> procedureId,
                                                            Integer type, Integer reportType) {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfTimestamp = new SimpleDateFormat("yyyyMMddHHmm");
        DateFormat dfCurrentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dfTimestamp.setTimeZone(timezone);
        dfCurrentDate.setTimeZone(timezone);
        String timestamp = dfTimestamp.format(date);
        String currentDate = dfCurrentDate.format(date);
        String fromDateReport = fromDate.substring(8, 10) + "/" + fromDate.substring(5, 7) + "/" + fromDate.substring(0, 4);
        String toDateReport = toDate.substring(8, 10) + "/" + toDate.substring(5, 7) + "/" + toDate.substring(0, 4);
        String filename = timestamp + "-danh-sach-ho-so.xlsx";
        byte[] resource = new byte[0];
        try {
            InputStream is = resourceTemplateDossierStatisticAssigneeQNI.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            var itemDtos = getDataExcelDto(agencyIds, fromDate, toDate, reportType, sectorId, procedureId, type);

            context.putVar("textBanner", translator.toLocale("lang.word.gov"));
            context.putVar("subtextBanner", translator.toLocale("lang.word.ifh"));

            context.putVar("title", translator.toLocale("lang.word.dossier-statistic-title"));
            context.putVar("subTitle", translator.toLocale("lang.word.dossier-statistic-fromdate-todate", new String[]{fromDateReport, toDateReport}));
            context.putVar("currentDate", translator.toLocale("lang.word.dossier-statistic-current-date", new String[]{currentDate}));
            context.putVar("no", translator.toLocale("lang.word.no"));
            context.putVar("dossierCode", translator.toLocale("lang.word.dossier-statistic-dossier-code"));
            context.putVar("procedureName", translator.toLocale("lang.word.dossier-statistic-procedure-name"));
            context.putVar("sectorName", translator.toLocale("lang.word.dossier-statistic-sector-name"));
            context.putVar("noiDungYeuCauGiaiQuyet", translator.toLocale("lang.word.dossier-statistic-noidungyeucaugiaiquyet"));
            context.putVar("acceptedDate", translator.toLocale("lang.word.dossier-statistic-accepted-date"));
            context.putVar("appointmentDate", translator.toLocale("lang.word.dossier-statistic-appointment-date"));
            context.putVar("completedDate", translator.toLocale("lang.word.dossier-statistic-completed-date"));
            context.putVar("applicantOwnerFullName", translator.toLocale("lang.word.dossier-statistic-applicant-ownerfullname"));
            context.putVar("applicantPhoneNumber", translator.toLocale("lang.word.dossier-statistic-applicant-phonenumber"));
            context.putVar("assigneeFullname", translator.toLocale("lang.word.dossier-statistic-assignee-fullname"));
            context.putVar("dossierStatusName", translator.toLocale("lang.word.dossier-statistic-status-name"));
            context.putVar("writer", translator.toLocale("lang.word.dossier-statistic-writer"));
            context.putVar("reporter", translator.toLocale("lang.word.dossier-statistic-reporter"));
            context.putVar("itemDtos", itemDtos);

            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            resource = outputStream.toByteArray();
        } catch (Exception ex) {
            logger.info("exportDossierStatistic012020Detail error:" + ex.getMessage());
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(resource);
    }

    public Slice<QNIETLDossier> getETLDossierList(String fromDate, String toDate, Pageable pageable) {
        LocalDateTime fromDateConvert = null;
        LocalDateTime toDateConvert = null;

        try {
            fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
            toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        Query query = new Query().addCriteria(
                Criteria.where("acceptedDate").gte(fromDateConvert).lte(toDateConvert)
        ).with(pageable);
        List<QNIETLDossier> resultList = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");
        Slice<QNIETLDossier> slice = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), QNIETLDossier.class));
        List<String> rolesToCheck = List.of(adminRoles.split(","));
        boolean  isAdmin  = Context.getListPemission(rolesToCheck);
       if(!isAdmin && enableHideName){
            slice.getContent().forEach(QNIETLDossier::setHideSecurity);
        }
        return slice;
    }

    public QNIETLDossier getETLDossier(String id) {
        ObjectId newId;
        try {
            newId = new ObjectId(id);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Query query = new Query().addCriteria(
                Criteria.where("_id").is(newId)
        );
        QNIETLDossier dossier = mongoTemplate.findOne(query, QNIETLDossier.class);

        if(Objects.nonNull(dossier)){
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
           dossier.setHideSecurity();
           }
        }
        return dossier;
    }

    public List<IdPojo> getETLDossierIdList(String fromDate, String toDate) {
        LocalDateTime fromDateConvert = null;
        LocalDateTime toDateConvert = null;
        try {
            fromDateConvert = !isNullOrEmpty(fromDate) ? df.parse(fromDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
            toDateConvert = !isNullOrEmpty(toDate) ? df.parse(toDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Aggregation aggregation = Aggregation.newAggregation(
                match(
                        Criteria.where("acceptedDate").gte(fromDateConvert).lte(toDateConvert)
                ),
                project(
                        "_id"
                )
        );
        AggregationResults<IdPojo> groupResults = mongoTemplate.aggregate(aggregation, "qniETLDossier", IdPojo.class);
        return groupResults.getMappedResults();
    }

    private Map<String, String> getTranslatedValue(String id, String collectionName) {
        String url = buildUrl("procedure", id);

        String get = microservice.basepadUri(url).toUriString();
        String response = MicroserviceExchange.get(restTemplate, get, String.class);
        JsonObject object = new JsonParser().parse(response).getAsJsonObject();

        return getTranslatedName(collectionName, object);
    }

    private QNIETLDossier.Parent getAgencyParent(String id, String collectionName) {
        String url = buildUrl(collectionName, id);

        String get = microservice.basedataUri(url).toUriString();
        String response = MicroserviceExchange.get(restTemplate, get, String.class);
        JsonObject object = new JsonParser().parse(response).getAsJsonObject();

        var parentId = object.get("parent").getAsString();
        String urlParent = buildUrl(collectionName, parentId);

        String getParent = microservice.basedataUri(urlParent).toUriString();
        String responseParent = MicroserviceExchange.get(restTemplate, getParent, String.class);
        JsonObject objectParent = new JsonParser().parse(responseParent).getAsJsonObject();

        var result = new QNIETLDossier.Parent();
        var idParent = objectParent.get("id").getAsString();
        var nameParent = objectParent.get("name").getAsJsonArray().get(0).getAsJsonObject().get("name").getAsString();
        result.setId(idParent);
        result.setName(nameParent);

        return result;
    }

    private Map<String, String> getTranslatedName(String collectionName, JsonObject object) {
        Map<String, String> dictionary = new HashMap<>();

        if (collectionName.equals("procedure")) {
            var name = object.get("name").getAsString();
            dictionary.put("name", name);

            return dictionary;
        } else {
            var name = object.getAsJsonObject("sector").get("name").getAsString();
            var id = object.getAsJsonObject("sector").get("id").getAsString();
            dictionary.put("name", name);
            dictionary.put("id", id);

            return dictionary;
        }
    }

    private String buildUrl(String collectionName, String id) {
        return "/" +
                collectionName +
                "/" +
                id;
    }

    private QNIETLDossier setTranslatedName(String collectionName, QNIETLDossier object, String value) {
        if (collectionName.equals("procedure")) {
            object.getProcedure().setName(value);
        } else {
            object.getSector().setName(value);
        }

        return object;
    }

    private vn.vnpt.digo.reporter.dto.qni.AffectedRowsDto updateProcedureOrSectorLongText(String nameFilter, String nameUpdate, String collectionName) {
        var affectedRowsDto = new vn.vnpt.digo.reporter.dto.qni.AffectedRowsDto(0, "");

        try {
            String formatCollectionField = collectionName + "." + "name";

            Query query = new Query(Criteria.where(formatCollectionField).is(nameFilter));
            Update update = new Update().set(formatCollectionField, nameUpdate);

            UpdateResult updateResult = mongoTemplate.updateMulti(query, update, "qniETLDossier");
            affectedRowsDto.setAffectedRows((int) updateResult.getModifiedCount());
        } catch (Exception e) {
            logger.error("error updateProcedureOrSectorLongText!");
            affectedRowsDto.setMessage(e.getMessage());
        }

        return affectedRowsDto;
    }

    public AffectedRowsDto updateFieldById(String code, String fieldNameToUpdate, String newValue, String type) {
        var affectedRowsDto = new AffectedRowsDto();

        Query query = new Query(Criteria.where("code").is(code));
        Object convertedValue = null;

        try {
            switch (type) {
                case "string":
                    convertedValue = convertValue(newValue, String.class);
                    break;
                case "boolean":
                    convertedValue = convertValue(newValue, Boolean.class);
                    break;
                case "date":
                    convertedValue = convertValue(newValue, Date.class);
                    break;
                case "object":
                    convertedValue = convertValue(newValue, Object.class);
                    break;
                case "double":
                    convertedValue = convertValue(newValue, Double.class);
                    break;
                case "array":
                    convertedValue = convertValue(newValue, Arrays.class);
                    break;
                case "objectid":
                    convertedValue = convertValue(newValue, ObjectId.class);
                    break;
            }

            Update update = new Update().set(fieldNameToUpdate, convertedValue);

            // Cập nhật trường trong document
            mongoTemplate.updateFirst(query, update, QNIETLDossier.class);

            affectedRowsDto.setAffectedRows(1);
        } catch (Exception e) {
            logger.error("updateFieldById: " + e.getMessage() + "code:" + code);
        }

        return affectedRowsDto;
    }

    private Object convertValue(String value, Class<?> targetType) {
        // Kiểm tra xem targetType có phải là kiểu Date không
        if (Date.class.isAssignableFrom(targetType)) {
            return convertToDate(value);
        }

        // Nếu không phải kiểu Date, sử dụng chuyển đổi mặc định của MongoConverter
        return mongoConverter.getConversionService().convert(value, targetType);
    }

    private Date convertToDate(String value) {
        // Sử dụng định dạng ngày giờ ISO 8601
        TimeZone timezone = TimeZone.getTimeZone("GMT");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timezone);

        try {
            // Chuyển đổi chuỗi sang kiểu Date
            return dateFormat.parse(value);
        } catch (ParseException e) {
            // Xử lý lỗi chuyển đổi
            throw new IllegalArgumentException("Invalid date format: " + value, e);
        }
    }

    public ImportResponseDto importFromExcelUpdateCompletedDate(MultipartFile excellFile) {
        List<String> acceptExtension = new ArrayList<>(Arrays.asList("xlsx", "xls", "csv"));
        String fileExtension = FilenameUtils.getExtension(excellFile.getOriginalFilename());
        ImportResponseDto responseDto = new ImportResponseDto();
        if (!acceptExtension.contains(fileExtension)) {
            throw new DigoHttpException(10104, HttpServletResponse.SC_BAD_REQUEST);
        }
        int currentRowIndex = 2;
        String lineError = translator.toLocale("digo.import-error.1000");
        String codeInvalidError = translator.toLocale("digo.import-error.1003");
        int totalSuccess = 0;
        int maxErrorDisplay = 50;
        List<String> errorMessage = new ArrayList<>();

        try {
            Workbook workbook = WorkbookFactory.create(excellFile.getInputStream());
            if (workbook.getNumberOfSheets() < 1) {
                throw new DigoHttpException(10104, HttpServletResponse.SC_BAD_REQUEST);
            }
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();
            Date now = new Date();
            List<String> listCode = new ArrayList<>();
            if (rows.hasNext()) {
                rows.next();
            }
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                String code = ExcelHelper.getStringCell(currentRow.getCell(0));
                String filerNameToUpdate = ExcelHelper.getStringCell(currentRow.getCell(1));
                String newValue = ExcelHelper.getStringCell(currentRow.getCell(2));
                String type = ExcelHelper.getStringCell(currentRow.getCell(3));
                if (code.isBlank()) {
                    if (responseDto.getErrorMessages().size() < maxErrorDisplay) {
                        responseDto.getErrorMessages().add(
                                String.format("%s %d: %s", lineError, currentRowIndex, codeInvalidError)
                        );
                    }
                    currentRowIndex++;
                    continue;
                }
                var result = this.updateFieldById(code, filerNameToUpdate, newValue, type);
                var resultCount = result.getAffectedRows();
                if (resultCount > 0) {
                    totalSuccess = totalSuccess + resultCount;
                } else {
                    errorMessage.add(String.format("Lỗi dòng: %s, message error: %s", currentRowIndex));
                }

                currentRowIndex++;
            }
            responseDto.setSuccessRows(totalSuccess);
            responseDto.setSuccessMessage(
                    translator.toLocale("digo.import-success.1000", new String[]{String.valueOf(totalSuccess)})
            );
            responseDto.setErrorMessages(errorMessage);
            return responseDto;
        } catch (IOException ex) {
            logger.info(ex.getMessage());
            throw new DigoHttpException(
                    10015,
                    HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    public ImportResponseDto upDateNameSectorOrProcedureEmpty(String collectionName, Boolean updateSectorByDate,
                                                              String fromDateNeedSectorUpdate,
                                                              String toDateNeedSectorUpdate) {
        ImportResponseDto result = new ImportResponseDto();

        logger.info("Begin upDateNameSectorOrProcedure !");

        try {
            if (!collectionName.equals("procedure") && !collectionName.equals("sector") && !collectionName.equals("agency")) {
                result.setSuccessRows(0);
                result.setSuccessMessage(
                        "Dữ liệu collection name không hợp lệ"
                );

                return result;
            }

            Criteria criteria = null;

            if (collectionName.equals("procedure")) {
                criteria = Criteria.where("").orOperator(
                        Criteria.where("procedure.name").exists(false),
                        Criteria.where("").orOperator(
                                Criteria.where("procedure.name").is(null),
                                Criteria.where("procedure.name").is(""))
                );
            } else if (collectionName.equals("sector")) {
                if (updateSectorByDate) {
                    this.df.setTimeZone(this.timezone);
                    Date fromDate = this.df.parse(fromDateNeedSectorUpdate);
                    Date toDate = this.df.parse(toDateNeedSectorUpdate);

                    criteria = Criteria.where("acceptedDate").gte(fromDate).lte(toDate);
                } else {
                    criteria = Criteria.where("").orOperator(
                            Criteria.where("sector.name").exists(false),
                            Criteria.where("").orOperator(
                                    Criteria.where("sector.name").is(null),
                                    Criteria.where("sector.name").is(""))
                    );
                }
            } else if (collectionName.equals("agency")) {
                criteria = Criteria.where("").orOperator(
                        Criteria.where("agency.parent.name").exists(false),
                        Criteria.where("").orOperator(
                                Criteria.where("agency.parent.name").is(null),
                                Criteria.where("agency.parent.name").is(""))
                );
            }

            Query query = new Query().addCriteria(
                    criteria
            );
            List<QNIETLDossier> qniEtlDossiers = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");

            var tempResult = 0;
            List<String> errorMessages = new ArrayList<>();
            if (qniEtlDossiers != null && !qniEtlDossiers.isEmpty()) {
                for (QNIETLDossier dossier : qniEtlDossiers) {
                    try {
                        if (collectionName.equals("agency")) {
                            QNIETLDossier.Parent parent = getAgencyParent(dossier.getAgency().getId(), collectionName);
                            dossier.getAgency().setParent(parent);
                            mongoTemplate.save(dossier);
                        } else {
                            var value = getTranslatedValue(dossier.getProcedure().getId(), collectionName);
                            var target = setTranslatedName(collectionName, dossier, value.get("name"));

                            if (collectionName.equals("sector")) {
                                target.getSector().setId(value.get("id"));
                            }

                            mongoTemplate.save(target);
                        }
                    } catch (Exception ex) {
                        errorMessages.add(String.format("Error message: %s Code: %s Collection: %s",
                                ex.getMessage(), dossier.getCode(), collectionName));
                    }

                    tempResult++;
                }
            }

            result.setErrorMessages(errorMessages);
            result.setSuccessRows(tempResult);
        } catch (Exception e) {
            logger.error("error upDateNameSectorOrProcedure: " + e.getMessage());
        }

        return result;
    }

    public ImportResponseDto updateProcedureOrSectorLongtext(MultipartFile excelFile, String collectionName) {
        List<String> acceptExtension = new ArrayList<>(Arrays.asList("xlsx", "xls", "csv"));
        String fileExtension = FilenameUtils.getExtension(excelFile.getOriginalFilename());
        ImportResponseDto responseDto = new ImportResponseDto();
        if (!acceptExtension.contains(fileExtension)) {
            throw new DigoHttpException(10104, HttpServletResponse.SC_BAD_REQUEST);
        }
        int currentRowIndex = 2;
        String lineError = translator.toLocale("digo.import-error.1000");
        String codeInvalidError = translator.toLocale("digo.import-error.1003");
        int totalSuccess = 0;
        int maxErrorDisplay = 50;
        List<String> errorMessage = new ArrayList<>();

        try {
            Workbook workbook = WorkbookFactory.create(excelFile.getInputStream());
            if (workbook.getNumberOfSheets() < 1) {
                throw new DigoHttpException(10104, HttpServletResponse.SC_BAD_REQUEST);
            }
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();
            if (rows.hasNext()) {
                rows.next();
            }
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                String nameFilter = ExcelHelper.getStringCell(currentRow.getCell(0));
                String nameUpdated = ExcelHelper.getStringCell(currentRow.getCell(1));

                if (nameFilter.isBlank()) {
                    if (responseDto.getErrorMessages().size() < maxErrorDisplay) {
                        responseDto.getErrorMessages().add(
                                String.format("%s %d: %s", lineError, currentRowIndex, codeInvalidError)
                        );
                    }
                    currentRowIndex++;
                    continue;
                }
                var result = this.updateProcedureOrSectorLongText(nameFilter, nameUpdated, collectionName);
                var resultCount = result.getAffectedRows();
                if (resultCount > 0) {
                    totalSuccess = totalSuccess + resultCount;
                } else {
                    errorMessage.add(String.format("Lỗi dòng: %s, message error: %s", currentRowIndex, result.getMessage()));
                }

                currentRowIndex++;
            }
            responseDto.setSuccessRows(totalSuccess);
            responseDto.setSuccessMessage(
                    translator.toLocale("digo.import-success.1000", new String[]{String.valueOf(totalSuccess)})
            );
            responseDto.setErrorMessages(errorMessage);
            return responseDto;
        } catch (IOException ex) {
            logger.info(ex.getMessage());
            throw new DigoHttpException(
                    10015,
                    HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    public AffectedRowsDto deleteDossierTest(String text){
        var result = new AffectedRowsDto();

        try {
            logger.info("Begin deleteDossierTest");

            Criteria criteria = Criteria.where("").orOperator(
                    Criteria.where("applicant.fullname").regex(text, "i"),
                    Criteria.where("applicant.ownerFullName").regex(text, "i")
            );
            Query query = new Query().addCriteria(criteria);
            result.setAffectedRows((int) mongoTemplate.remove(query, QNIETLDossier.class).getDeletedCount());
        } catch (Exception e) {
            result.setMessage(e.getMessage());
            logger.error("Error deleteDossierTest: " + e.getMessage());
        }

        logger.info("End deleteDossierTest");
        return result;
    }

    public AffectedRowsDto deleteDossierVbdlisCancel() {
        var result = new AffectedRowsDto();

        try {
            logger.info("Begin deleteDossierVbdlisCancel");

            Criteria criteria = Criteria.where("dossierStatus._id").is(19);
            Query query = new Query().addCriteria(criteria);
            result.setAffectedRows((int) mongoTemplate.remove(query, QNIETLDossier.class).getDeletedCount());
        } catch (Exception e) {
            result.setMessage(e.getMessage());
            logger.error("Error deleteDossierTest: " + e.getMessage());
        }

        logger.info("End deleteDossierVbdlisCancel");
        return result;
    }

    public ImportResponseDto updateAppointmentDate(String fromDateNeedUpdate, String toDateNeedUpdate) {
        ImportResponseDto result = new ImportResponseDto();
        List<String> errorMessages = new ArrayList<>();

        try {
            logger.info("Begin updateAppointmentDate");
            this.df.setTimeZone(this.timezone);
            Date fromDate = this.df.parse(fromDateNeedUpdate);
            Date toDate = this.df.parse(toDateNeedUpdate);

            Criteria criteria = null;

            criteria = Criteria.where("").andOperator(
                    Criteria.where("acceptedDate").gte(fromDate).lte(toDate),
                    Criteria.where("").orOperator(
                            Criteria.where("appointmentDate").exists(false),
                            Criteria.where("").orOperator(
                                    Criteria.where("appointmentDate").is(null),
                                    Criteria.where("appointmentDate").is(""))
                    )
            );

            Query query = new Query().addCriteria(criteria);
            List<QNIETLDossier> qniEtlDossiers = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");
            var tempResult = 0;
            for (QNIETLDossier dossier : qniEtlDossiers) {
                try {
                    dossier.setAppointmentDate(getDueDate(new ObjectId(dossier.getId()), dossier));
                    mongoTemplate.save(dossier);
                } catch (Exception ex) {
                    errorMessages.add(String.format("Error message: %s Code: %s",
                            ex.getMessage(), dossier.getCode()));
                }
                tempResult++;
            }

            result.setErrorMessages(errorMessages);
            result.setSuccessRows(tempResult);
        } catch (Exception e) {
            logger.error("Error deleteDossierTest: " + e.getMessage());
        }

        logger.info("End updateAppointmentDate");
        return result;
    }

    private JsonObject getDossier(String code) {
        var urlDossier = String.format("/dossier/%s/--by-code", code);
        String getDossier = microservice.padmanUri(urlDossier).toUriString();
        String responseDossier = MicroserviceExchange.get(restTemplate, getDossier, String.class);

        return new JsonParser().parse(responseDossier).getAsJsonObject();
    }

    public Date getDueDate(ObjectId dossierId, QNIETLDossier dossier) {

        logger.info("Begin getDueDate!");
        String path = "timesheet-gen/--by-dossier-id";
        String url = microservice.basecatUri(path).build().toUriString();
        TimeSheetGenPayloadDto timeSheetGenPayloadDto = new TimeSheetGenPayloadDto();
        timeSheetGenPayloadDto.setData(dossierId,
                new ObjectId("623849e3e63b54793b9ff57e"),
                0,
                dossier.getAcceptedDate(),
                null);
        timeSheetGenPayloadDto.setCheckOffDay(true);

        JsonObject object = getDossier(dossier.getCode());
        String processingTimeUnit = object.get("processingTimeUnit").getAsString();
        timeSheetGenPayloadDto.setProcessingTimeUnit(processingTimeUnit);
        timeSheetGenPayloadDto.setOffTime(offTime);
        timeSheetGenPayloadDto.setDuration(getDuration(processingTimeUnit, dossier.getProcessingTime()));

        List<TimeSheetGenPayloadDto> payload = new ArrayList<>();
        payload.add(timeSheetGenPayloadDto);
        TimesheetGenResponseDto[] responseParent = MicroserviceExchange.postJson(restTemplate, url, payload, TimesheetGenResponseDto[].class);

        for (var item : responseParent) {
            if (Objects.nonNull(item.getDue())) {
                System.out.println(item.getDue());
                return item.getDue();
            }
        }

        logger.info("End getDueDate!");
        return null;
    }

    private double getDuration(String processingTimeUnit, double processingTimeWithUnit) {

        double result = 0;

        switch (processingTimeUnit) {
            case "y":
                result = processingTimeWithUnit * 365;
                break;
            case "M":
                result = processingTimeWithUnit * 30;
                break;
            case "d":
                result = processingTimeWithUnit;
                break;
            case "H:m:s":
                result = processingTimeWithUnit / 24;
                break;
            case "h":
                result = processingTimeWithUnit / 24;
                break;
            case "m":
                result = processingTimeWithUnit / (24 * 60);
                break;
        }

        return result;
    }

    public List<QNIETLDossier> getETLDossierListInvalid(String fieldInvalid) {
        List<QNIETLDossier> resultList = new ArrayList<>();

        try {
            logger.info("Begin getETLDossierListInvalid!");
            Criteria criteria = Criteria.where("").orOperator(
                    Criteria.where(fieldInvalid).exists(false),
                    Criteria.where("").orOperator(
                            Criteria.where(fieldInvalid).is(null),
                            Criteria.where(fieldInvalid).is(""))
            );

            Query query = new Query().addCriteria(criteria);
            resultList = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");
            List<String> rolesToCheck = List.of(adminRoles.split(","));
            boolean  isAdmin  = Context.getListPemission(rolesToCheck);
            if(!isAdmin && enableHideName){
            resultList.forEach(i -> i.setHideSecurity());
            }
                logger.info("End getETLDossierListInvalid!");
        } catch (Exception e) {
            logger.error("End getETLDossierListInvalid with error: " + e.getMessage());
        }

        return resultList;
    }

    public AffectedRowsDto deleteDossierNotaccept() {
        var result = new AffectedRowsDto();

        try {
            logger.info("Begin deleteDossierNotaccept");

            Criteria criteria = Criteria.where("dossierTaskStatus._id").is(new ObjectId("60ebf14b09cbf91d41f87f8d"));
            Query query = new Query().addCriteria(criteria);
            result.setAffectedRows((int) mongoTemplate.remove(query, QNIETLDossier.class).getDeletedCount());
        } catch (Exception e) {
            result.setMessage(e.getMessage());
            logger.error("Error deleteDossierNotaccept: " + e.getMessage());
        }

        logger.info("End deleteDossierNotaccept");
        return result;
    }

    public ImportResponseDto updateAgencyNull() {
        ImportResponseDto result = new ImportResponseDto();
        List<String> errorMessages = new ArrayList<>();

        try {
            logger.info("Begin updateAgencyNull");

            Criteria criteria = null;

            criteria = Criteria.where("").orOperator(
                    Criteria.where("agency._id").exists(false),
                    Criteria.where("").orOperator(
                            Criteria.where("agency._id").is(null),
                            Criteria.where("agency._id").is(""))
            );

            Query query = new Query().addCriteria(criteria);
            List<QNIETLDossier> qniEtlDossiers = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");
            var tempResult = 0;
            for (QNIETLDossier dossier : qniEtlDossiers) {
                try {
                    JsonObject object = getDossier(dossier.getCode());
                    var agencyId =
                            object.getAsJsonArray("task").get(0).getAsJsonObject().get("agency").getAsJsonObject().get("id").getAsString();
                    var agencyName =
                            object.getAsJsonArray("task").get(0).getAsJsonObject().get("agency").
                                    getAsJsonObject().getAsJsonArray("name").get(0).getAsJsonObject().get("name").getAsString();
                    dossier.getAgency().setId(agencyId);
                    dossier.getAgency().setName(agencyName);
                    mongoTemplate.save(dossier);
                } catch (Exception ex) {
                    errorMessages.add(String.format("Error message: %s Code: %s",
                            ex.getMessage(), dossier.getCode()));
                }
                tempResult++;
            }

            result.setErrorMessages(errorMessages);
            result.setSuccessRows(tempResult);
        } catch (Exception e) {
            logger.error("Error updateAgencyNull: " + e.getMessage());
        }

        logger.info("End updateAgencyNull");
        return result;
    }

    public ImportResponseDto updateWithdrawDate() {
        ImportResponseDto result = new ImportResponseDto();
        List<String> errorMessages = new ArrayList<>();

        try {
            logger.info("Begin updateWithdrawDate");

            Criteria criteria = null;

            criteria = Criteria.where("").andOperator(
                    Criteria.where("dossierStatus._id").is(6),
                    Criteria.where("completedDate").exists(true)
            );

            Query query = new Query().addCriteria(criteria);
            List<QNIETLDossier> qniEtlDossiers = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");
            var tempResult = 0;
            for (QNIETLDossier dossier : qniEtlDossiers) {
                try {
                    JsonObject object = getDossier(dossier.getCode());
                    var withdrawDateString =
                            object.get("withdrawDate").getAsString();

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
                    OffsetDateTime offsetDateTime = OffsetDateTime.parse(withdrawDateString, formatter);
                    dossier.setCompletedDate(null);
                    dossier.setWithdrawDate(Date.from(offsetDateTime.toInstant()));
                    dossier.getDossierStatus().setName(dossier.getDossierTaskStatus().getName());
                    mongoTemplate.save(dossier);
                } catch (Exception ex) {
                    errorMessages.add(String.format("Error message: %s Code: %s",
                            ex.getMessage(), dossier.getCode()));
                }
                tempResult++;
            }

            result.setErrorMessages(errorMessages);
            result.setSuccessRows(tempResult);
        } catch (Exception e) {
            logger.error("Error updateWithdrawDate: " + e.getMessage());
        }

        logger.info("End updateWithdrawDate");
        return result;
    }

    public ImportResponseDto updateMissDate(String fieldName) {
        ImportResponseDto result = new ImportResponseDto();
        List<String> errorMessages = new ArrayList<>();

        try {
            logger.info("Begin updateMissDate");

            Criteria criteria = null;

            switch (fieldName) {
                case "cancelledDate":
                    criteria = Criteria.where("").andOperator(
                            Criteria.where("dossierTaskStatus._id").is(new ObjectId("61ee30eada2d36b037e00005")),
                            Criteria.where("completedDate").exists(false),
                            Criteria.where("withdrawDate").exists(false),
                            Criteria.where("cancelledDate").exists(false)
                    );
                    break;
                case "withdrawDate":
                    criteria = Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("dossierTaskStatus._id").is(new ObjectId("6147e4c4ebfba925f1e89bf0")),
                                    Criteria.where("dossierTaskStatus._id").is(new ObjectId("6151c771ba2a04299f949875"))
                            ),
                            Criteria.where("completedDate").exists(false),
                            Criteria.where("withdrawDate").exists(false),
                            Criteria.where("cancelledDate").exists(false)
                    );
                    break;
                case "additionalDate":
                    criteria = Criteria.where("").andOperator(
                            Criteria.where("dossierTaskStatus._id").is(new ObjectId("60ebf03109cbf91d41f87f8b")),
                            Criteria.where("additionalDate").exists(false)
                    );
                    break;
                case "financialObligationsDate":
                    criteria = Criteria.where("").andOperator(
                            Criteria.where("dossierTaskStatus._id").is(new ObjectId("631e4a0967411b0b0d000003")),
                            Criteria.where("financialObligationsDate").exists(false)
                    );
                    break;
            }

            Query query = new Query().addCriteria(criteria);
            List<QNIETLDossier> qniEtlDossiers = mongoTemplate.find(query, QNIETLDossier.class, "qniETLDossier");
            var tempResult = 0;
            for (QNIETLDossier dossier : qniEtlDossiers) {
                try {
                    JsonObject object = getDossier(dossier.getCode());
                    var idDossier =
                            object.get("id").getAsString();

                    var stringFormatLog = String.format("comment?page=0&size=50&group-id=2&item-id=%s", idDossier);
                    String getLogManUrl = microservice.messengerUri(stringFormatLog).toUriString();
                    String logJson = MicroserviceExchange.getLogMan(restTemplate, getLogManUrl, String.class);
                    var dossierJsonObject = new org.springframework.boot.configurationprocessor.json.JSONObject(logJson);
                    var contentArray = dossierJsonObject.getJSONArray("content");
                    ObjectMapper objectMapper = new ObjectMapper();

                    ArrayList<CommentMessenger> getComments = new ArrayList<>();
                    if (contentArray != null) {
                        for (int k = 0; k < contentArray.length(); k++) {
                            org.springframework.boot.configurationprocessor.json.JSONObject itemObject = contentArray.getJSONObject(k);
                            CommentMessenger getComment = objectMapper.readValue(itemObject.toString(), CommentMessenger.class);
                            getComments.add(getComment);
                        }
                    }

                    if (getComments.size() <= 0) continue;

                    switch (fieldName) {
                        case "cancelledDate":
                            for (int j = 0; j < getComments.size(); j++) {
                                if (getComments.get(j).getContent() == null && getComments.get(j).getContent() != "")
                                    continue;

                                var upperCaseContent = getComments.get(j).getContent().toUpperCase().replaceAll("[$#:^&]", "");
                                if (upperCaseContent.contains("ĐỒNG Ý PHÊ DUYỆT YÊU CẦU DỪNG XỬ LÝ HỒ SƠ") || upperCaseContent.startsWith("PHÊ DUYỆT YÊU CẦU DỪNG XỬ LÝ HỒ SƠ")) {
                                    dossier.setCancelledDate(getComments.get(j).getCreatedDate());
                                    tempResult++;
                                    break;
                                }
                            }

                            errorMessages.add("Không tìm thấy ngày dừng xử lý: " + dossier.getCode());
                            break;
                        case "withdrawDate":
                            for (int j = 0; j < getComments.size(); j++) {
                                if (getComments.get(j).getContent() == null && getComments.get(j).getContent() != "")
                                    continue;

                                var upperCaseContent = getComments.get(j).getContent().toUpperCase().replaceAll("[$#:^&]", "");
                                if (upperCaseContent.contains("LÝ DO RÚT HỒ SƠ") || upperCaseContent.contains("ĐÃ RÚT")) {
                                    dossier.setWithdrawDate(getComments.get(j).getCreatedDate());
                                    tempResult++;
                                    break;
                                }
                            }
                            errorMessages.add("Không tìm thấy ngày rút hồ sơ: " + dossier.getCode());
                            break;
                        case "additionalDate":
                            List<Date> dateList = new ArrayList<>();
                            var flag = false;
                            for (int j = 0; j < getComments.size(); j++) {
                                if (getComments.get(j).getContent() == null && getComments.get(j).getContent() != "")
                                    continue;

                                var upperCaseContent = getComments.get(j).getContent().toUpperCase().replaceAll("[$#:^&]", "");
                                if (upperCaseContent.contains("ĐỒNG Ý PHÊ DUYỆT YÊU CẦU BỔ SUNG HỒ SƠ") || upperCaseContent.startsWith("PHÊ DUYỆT YÊU CẦU BỔ SUNG HỒ SƠ")) {
                                    dateList.add(getComments.get(j).getCreatedDate());
                                    flag = true;
                                }
                            }

                            if (!flag)
                                errorMessages.add("Không tìm thấy ngày bổ sung hồ sơ: " + dossier.getCode());
                            else tempResult++;
                            dossier.setAdditionalDate(dateList);
                            break;
                        case "financialObligationsDate":
                            for (int j = 0; j < getComments.size(); j++) {
                                if (getComments.get(j).getContent() == null && getComments.get(j).getContent() != "")
                                    continue;

                                var upperCaseContent = getComments.get(j).getContent().toUpperCase().replaceAll("[$#:^&]", "");
                                if (upperCaseContent.contains("THỰC HIỆN NGHĨA VỤ TÀI CHÍNH")) {
                                    dossier.setFinancialObligationsDate(getComments.get(j).getCreatedDate());
                                    tempResult++;
                                    break;
                                }
                            }
                            errorMessages.add("Không tìm thấy ngày thực hiện nghĩa vụ tài chính: " + dossier.getCode());
                            break;
                    }

                    mongoTemplate.save(dossier);
                } catch (Exception ex) {
                    errorMessages.add(String.format("Error message: %s Code: %s",
                            ex.getMessage(), dossier.getCode()));
                }
            }

            result.setErrorMessages(errorMessages);
            result.setSuccessRows(tempResult);
        } catch (Exception e) {
            logger.error("Error updateMissDate: " + e.getMessage());
        }

        logger.info("End updateMissDate");
        return result;
    }

    public UpdateDateDossierResponseQni updateCompletedDate(String _fromDate, String _toDate) {
        UpdateDateDossierResponseQni result =  new UpdateDateDossierResponseQni();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        List<String> updateSuccessDossierCode = new ArrayList<>();
        List<String> errorDossierCode = new ArrayList<>();
        List<String> updateErrorMessage = new ArrayList<>();
        AtomicInteger successRow = new AtomicInteger(0);
        Criteria criteria = new Criteria();
        criteria.and("acceptedDate").gte(parseDate(_fromDate)).lte(parseDate(_toDate));
        criteria.andOperator(
                Criteria.where("withdrawDate").exists(false),
                Criteria.where("completedDate").exists(false),
                Criteria.where("cancelledDate").exists(false)
        );
        Query query = new Query(criteria);
        List<QNIETLDossier> list = mongoTemplate.find(query, QNIETLDossier.class);
        list.forEach(element -> {
            errorDossierCode.add(element.getCode());

            try {
                String getDossierDetailTemplateUrl = microservice.padmanUri("dossier/" + element.getCode() + "/--by-code").toUriString();
                String getDossierDetailRawResponse = MicroserviceExchange.get(restTemplate, getDossierDetailTemplateUrl, String.class);
                JsonObject dossierDetailData = gson.fromJson(getDossierDetailRawResponse, JsonObject.class);
                JsonObject statusObject = dossierDetailData.get("dossierStatus").getAsJsonObject();
                int statusId = statusObject.has("id") ? statusObject.get("id").getAsInt() : -1;
                if (statusId == 4) {
                    element.setCompletedDate(dateFormat.parse(dossierDetailData.get("completedDate").getAsString()));
                } else if (statusId == 5) {
                    element.setCompletedDate(dateFormat.parse(dossierDetailData.get("completedDate").getAsString()));
                    element.setReturnedDate(dateFormat.parse(dossierDetailData.get("returnedDate").getAsString()));
                }
                if (statusId == 4 || statusId == 5) {
                    //update status
                    QNIETLDossier.DossierStatus updateDossierStatus = new QNIETLDossier.DossierStatus();
                    updateDossierStatus.setId(statusObject.get("id").getAsInt());
                    updateDossierStatus.setName(statusObject.get("name").getAsJsonArray().get(0).getAsJsonObject().get("name").getAsString());
                    element.setDossierStatus(updateDossierStatus);
                    //result
                    successRow.addAndGet(1);
                    updateSuccessDossierCode.add(element.getCode());
                    mongoTemplate.save(element);
                }

            }
            catch(Exception ex){
                updateErrorMessage.add(String.format("Error message: %s Code: %s",
                        ex.getMessage(), element.getCode()));

            }

        });

        result.setUpdateSuccessDossierCode(updateSuccessDossierCode);
        result.setErrorDossierCode(errorDossierCode);
        result.setSuccessRows(successRow.get());
        result.setUpdateErrorMessage(updateErrorMessage);

        return result;
    }
    private Date parseDate(String dateStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
