package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.reporter.document.DossierCountingLog;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierCountingIdAndCodeDto implements Serializable {

  @Field("id")
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId id;
  private List<ObjectId> listDossierCountingId;
  private int code;
  @Override
  public int hashCode() {
    return Objects.hash(id,code);
  }
  @Override
  public boolean equals(Object o) {

    if (o == this) return true;
    if (!(o instanceof DossierCountingIdAndCodeDto)) {
      return false;
    }
    DossierCountingIdAndCodeDto doss= (DossierCountingIdAndCodeDto) o;
    return (Objects.equals(id, doss.id)==true) && (code ==doss.code );
  }
}
