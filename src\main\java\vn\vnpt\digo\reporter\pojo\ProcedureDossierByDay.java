/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProcedureDossierByDay implements Serializable {
    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private String code;

    private ArrayList<NameDossierByDay> translate;

    private String procedureName;

    public void setProcedureName(short langId) {
        if (this.translate != null && !this.translate.isEmpty()) {
            this.translate.forEach(trans -> {
                if (trans.getLanguageId().equals(langId)) {
                    this.procedureName = trans.getName();
                }
            });
        }

    }
}
