package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.ProcedureAgency;
import vn.vnpt.digo.reporter.pojo.ProcedureImplementer;
import vn.vnpt.digo.reporter.pojo.ProcedureSector;
import vn.vnpt.digo.reporter.pojo.ProcedureTranslate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PutProcedureDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId originId;

    private ArrayList<ProcedureAgency> agency;

    private ProcedureSector sector;

    private List<ProcedureTranslate> translate;

    private Integer dossierQuantity;

    private List<ProcedureImplementer> implementer;

    private ObjectId deploymentId;

    private Integer order;
    
    public void setTranslateName(String name){
        ProcedureTranslate translate = new ProcedureTranslate();
        translate.setName(name);
        translate.setLanguageId((short)228);
        List<ProcedureTranslate> translateList = new ArrayList<>();
        translateList.add(translate);
        this.translate = translateList;
    }
}
