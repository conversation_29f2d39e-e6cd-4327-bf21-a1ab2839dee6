package vn.vnpt.digo.reporter.repository;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.digo.reporter.document.ElectricCustomer;
import vn.vnpt.digo.reporter.dto.GetCustomerCodeByUserIdDto;

/**
 *
 * <AUTHOR>
 */
public interface ElectricCustomerRepository extends MongoRepository<ElectricCustomer, ObjectId> {

    @Query(value = "{'userId': ?0, 'customerCode': ?1 }")
    ElectricCustomer findUserIdAndCustomerCode(ObjectId userId, String customerCode);

    @Query(value = "{'userId': ?0}", sort = "{'updatedDate': -1}")
    List<GetCustomerCodeByUserIdDto> getCustomerCodeByUserId(ObjectId userId);
}
