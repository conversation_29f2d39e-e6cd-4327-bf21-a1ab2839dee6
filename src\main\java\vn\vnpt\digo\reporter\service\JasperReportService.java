package vn.vnpt.digo.reporter.service;

import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.dto.JasperReportExportDto;
import vn.vnpt.digo.reporter.repository.TemplateRepository;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;


@Service
public class JasperReportService {

    private static final Logger log = LoggerFactory.getLogger(JasperReportService.class);

    @Autowired
    private TemplateRepository templateRepository;

    @Value("${file.upload-dir}")
    private String uploadDir;

    public <D> ByteArrayResource exportFilePdf(String _id, JasperReportExportDto<D> param) throws FileNotFoundException {

        ObjectId id = (_id != "" && _id != null) ? new ObjectId(_id) : null;
        vn.vnpt.digo.reporter.document.Template templateReporter = templateRepository.findOneById(id);
        Resource resource = new FileSystemResource(uploadDir + templateReporter.getFile().getPath());
        Map<String, Object> parameter = reflection(param);
        return compileJasperReport(parameter, resource);
    }

    private <D> Map<String, Object> reflection(JasperReportExportDto<D> param) {

        Map<String, Object> result = new LinkedHashMap<>();
        if (param.getParameters() != null) {
            param.getParameters().forEach(result::put);
        }

        if (param.getData() != null && param.getData().size() != 0) {
            result.put("dataSource", param.getData());
        }

        return result;
    }

    private ByteArrayResource compileJasperReport(Map<String, Object> parameters, Resource template) {

        try {

            InputStream is = template.getInputStream();
            JasperReport jasperReport = JasperCompileManager.compileReport(is);
            JasperPrint jasperPrint = null;
            if (parameters.get("dataSource") != null) {
                jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, new JRBeanCollectionDataSource((Collection<?>) parameters.get("dataSource")));
            }else {
                jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, new JREmptyDataSource());
            }

            JRPdfExporter pdfExporter = new JRPdfExporter();
            pdfExporter.setExporterInput(new SimpleExporterInput(jasperPrint));
            ByteArrayOutputStream pdfReportStream = new ByteArrayOutputStream();
            pdfExporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfReportStream));
            pdfExporter.exportReport();

            return new ByteArrayResource(pdfReportStream.toByteArray());
        } catch (JRException | IOException e) {
            log.error("Export to pdf error: ", e);
            throw new RuntimeException("Failed to export pdf!", e);
        }

    }
}
