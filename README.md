# svc-reporter:1.0.0-beta.1

Dịch vụ dữ liệu báo cáo.

- version template: 1.3.0

[Xem danh sách API](https://digo-sgui.vnptigate.vn/1.0.0-beta.1)

#### Hướng dẫn đóng gói docker image

Build Jar file:

```bash
gradlew build
```

Build docker image:

```bash
docker build . -t svc-reporter:<version>
```

**L<PERSON>u ý:** trước khi build docker image cần phải đảm bảo EOL Conversion của file `docker-entrypoint.sh` là `Unix (LF)`.

#### Hướng dẫn deploy trên Kubernetes

```bash
kubectl apply -f charts/svc-reporter/templates/configmap-env.yaml
kubectl apply -f charts/svc-reporter/templates/configmap-hazelcast.yaml
kubectl apply -f charts/svc-reporter/templates/deployment.yaml
kubectl apply -f charts/svc-reporter/templates/service.yaml
kubectl apply -f charts/svc-reporter/templates/ingress.yaml
```

**<PERSON><PERSON><PERSON> ý:** c<PERSON>n thay thế các biến trong các file resource trên thành các giá trị thích hợp.