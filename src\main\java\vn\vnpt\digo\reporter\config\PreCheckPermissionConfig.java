package vn.vnpt.digo.reporter.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Component
public class PreCheckPermissionConfig implements WebMvcConfigurer {
    
    @Autowired
    AuthorizeFilter productInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(productInterceptor);
    }
}
