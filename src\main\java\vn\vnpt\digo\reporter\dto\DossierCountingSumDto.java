package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierCountingSumDto implements Serializable {
  private DossierAgencyDto agency;

  private long received;  // da tiep nhan

  private long inProgress; // dang xu ly

  private long inProgressAndOnTime; // hs chua den han, dang xu ly

  private long inProgressAndOutOfDue; // hs đang xử lý và quá hạn

  private long completed;  // da hoan thanh (cả trể và ko trể)

  private long completedOnTime; // hs hoàn thành đúng hạn

  private long completedEarly; // hs hoàn thành som han

  private long completedOutOfDue; // hs hoàn thành quá hạn

  private long onlineReceived; // tiếp nhận trực tuyến

  private long directReceived; // tiếp nhận trực tiếp

  private long cancelled;  // DossierStatus = 6

  private long suspended; // DossierStatus = 3
}
