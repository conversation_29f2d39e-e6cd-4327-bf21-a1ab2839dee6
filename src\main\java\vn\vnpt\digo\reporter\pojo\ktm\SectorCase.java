package vn.vnpt.digo.reporter.pojo.ktm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.NameDossierByDay;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SectorCase {
    @JsonProperty("id")
    private String id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("name")
    private List<NameDossierByDay> name;
    private List<ObjectId> completedEarlyList = new ArrayList<>();
    private List<ObjectId> completedOntimeList = new ArrayList<>();
    private List<ObjectId> completedLatelyList = new ArrayList<>();
    private List<ObjectId> inprogressEarlyList = new ArrayList<>();
    private List<ObjectId> inprogressLatelyList = new ArrayList<>();
    private List<ObjectId> onlineReceivingList = new ArrayList<>();
    private List<ObjectId> directlyReceivingList = new ArrayList<>();
}

