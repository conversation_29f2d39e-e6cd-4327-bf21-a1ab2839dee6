/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import com.google.gson.Gson;
import com.mongodb.DuplicateKeyException;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.Template;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.IdResponse;
import vn.vnpt.digo.reporter.pojo.LogAction;
import vn.vnpt.digo.reporter.pojo.LogGroup;
import vn.vnpt.digo.reporter.repository.TemplateRepository;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.KafkaExchange;
import vn.vnpt.digo.reporter.util.Translator;
import vn.vnpt.digo.reporter.util.GsonHelper;

/**
 *
 * <AUTHOR>
 */
@Service
public class TemplateService {

    private final String CONST_PAGEABLE_TYPE = "page";

    @Autowired
    private TemplateRepository templateRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private Translator translator;

    @Autowired
    private MinioService minioService;
            
    @Value("${file.upload-dir}")
    private String uploadDir;

    @Value("#{'${file.upload-malformed-regex}'.split(',')}") 
    private List<String> uploadMalformedRegex;
    
    private static final Logger logger = LoggerFactory.getLogger(TemplateService.class);

    public Slice<GetTemplateDto> getListTemplate(String keyword, String _subsystemId, String _typeId, String spec, Pageable pageable) {
        ObjectId subsystemId = (_subsystemId != "" && _subsystemId != null) ? new ObjectId(_subsystemId) : null;
        ObjectId typeId = (_typeId != "" && _typeId != null) ? new ObjectId(_typeId) : null;
        ObjectId deploymentId = Context.getDeploymentId();
        if (deploymentId != null) {
            Query query = new Query();
            query.with(pageable);
            query.with(Sort.by(Sort.Direction.DESC, "appliedDate"));
            List<Criteria> criterias = new ArrayList<>();
            criterias.add(Criteria.where("deploymentId").is(deploymentId));

            //search by keyword
            if(Objects.nonNull(keyword) && keyword.length() != 0){
                Pattern p = Pattern.compile("[\\.\\*\\+\\?\\^\\${}\\(\\)|\\]\\[\\\\]");
                Matcher m = p.matcher(keyword);
                keyword = m.replaceAll("\\\\$0");
                criterias.add(new Criteria().orOperator(
                        Criteria.where("name").regex(keyword),
                        Criteria.where("type.name").regex(keyword)
                ));
            }

            if(Objects.nonNull(subsystemId)){
                criterias.add(Criteria.where("subsystem.id").is(subsystemId));
            }

            if(Objects.nonNull(typeId)){
                criterias.add(Criteria.where("type.id").is(typeId));
            }

            Criteria crFinal = new Criteria();
            crFinal.andOperator(criterias.toArray(new Criteria[criterias.size()]));
            query.addCriteria(crFinal);


            if (CONST_PAGEABLE_TYPE.equals(spec)) {
                query.limit(pageable.getPageSize());
                List<GetTemplateDto> result = mongoTemplate.find(query,GetTemplateDto.class, "template");
                long totalCount = mongoTemplate.count(Query.of(query).limit(-1).skip(-1), GetTemplateDto.class, "template");
                Page<GetTemplateDto> GetTemplateDtoSlice = new PageImpl(result, pageable, totalCount);
                return GetTemplateDtoSlice;
//                return templateRepository.searchPage(keyword, subsystemId, typeId, deploymentId, pageable).map(GetTemplateDto::fromDocument);
            } else {
                query.with(pageable).limit(pageable.getPageSize() + 1);
                List<GetTemplateDto> getTemplateDtoList = mongoTemplate.find(query,GetTemplateDto.class, "template");
                boolean hasNext;
                if (getTemplateDtoList.size()==0) {
                    long totalCount = mongoTemplate.count(Query.of(query).limit(-1).skip(-1), GetTemplateDto.class, "template");
                    long totalPage = (totalCount - 1) / pageable.getPageSize();
                    totalPage = totalPage <= 0 ? 0 : totalPage;
                    Pageable newPageable = PageRequest.of((int) totalPage,pageable.getPageSize(), pageable.getSort());
                    query.with(newPageable).skip((totalPage)*pageable.getPageSize()).limit(pageable.getPageSize());
                    List<GetTemplateDto> lastGetTemplateDtoList = mongoTemplate.find(query, GetTemplateDto.class, "template");
                    Slice<GetTemplateDto> lastGetTemplateDtoSlice = new SliceImpl(lastGetTemplateDtoList, newPageable, false);
                    return lastGetTemplateDtoSlice;
                }
                hasNext = getTemplateDtoList.size() > pageable.getPageSize();
                Slice<GetTemplateDto> getTemplateDtoSlice = new SliceImpl(hasNext ? getTemplateDtoList.subList(0, pageable.getPageSize()) : getTemplateDtoList, pageable, hasNext);
                return getTemplateDtoSlice;
//                return templateRepository.searchSlice(keyword, subsystemId, typeId, deploymentId, pageable).map(GetTemplateDto::fromDocument);
            }
        }
        throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_NOT_FOUND);
    }

    public List<GetTemplateDto> getAllListTemplate(String keyword, String _subsystemId, String _typeId) {
        ObjectId subsystemId = (_subsystemId != "" && _subsystemId != null) ? new ObjectId(_subsystemId) : null;
        ObjectId typeId = (_typeId != "" && _typeId != null) ? new ObjectId(_typeId) : null;
        ObjectId deploymentId = Context.getDeploymentId();
        if (deploymentId != null) {
            return templateRepository.searchAll(keyword, subsystemId, typeId, deploymentId).stream().map(GetTemplateDto::fromDocument).collect(Collectors.toList());
        }
        throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_NOT_FOUND);
    }

    public IdResponse postTemplate(PostTemplateDto templateDto) {
        logger.info("postTemplate Begin");
        try {
            ObjectId deploymentId = Context.getDeploymentId();
            if (deploymentId != null) {
                //Check file
                if (templateDto.getFile() != null && templateDto.getFile().getFilename() != null && !templateDto.getFile().getFilename().isEmpty()) {
                    logger.info("postTemplate payload: " + templateDto.getFile().toString());
                    //Check match path
                    String matchFilename = "NULL";
                    if (templateDto.getFile().getPath().length() > 24) {
                        matchFilename = templateDto.getFile().getPath().substring(0, 24);
                        //check path
                        for(String regex : uploadMalformedRegex){
                            templateDto.getFile().setPath(templateDto.getFile().getPath().replace(regex, ""));
                        }
                    }
                    logger.info("postTemplate matchFilename: " + matchFilename);
                    if (matchFilename.equals(deploymentId.toHexString())) {
                        logger.info("postTemplate payload: " + templateDto.getFile().toString());
                        //Check file
                        File file = new File(uploadDir + templateDto.getFile().getPath());
                        if ((file.exists() && !file.isDirectory()) || java.util.Objects.nonNull(templateDto.getFile().getMinioAccess())) {
                            Template template = GsonHelper.copyObject(templateDto, Template.class);
                            Gson g = new Gson();
                            Object objFromJson = g.fromJson(templateDto.getListVariableString(), Object.class);
                            template.setListVariable(objFromJson);
                            if (Objects.nonNull(templateDto.getSignEnable()) && templateDto.getSignEnable() != null) {
                                template.setSignEnable(templateDto.getSignEnable());
                            }
                            if (Objects.nonNull(templateDto.getCode()) && templateDto.getCode() != null) {
                                template.setCode(templateDto.getCode());
                            }
                            template.setDeploymentId(deploymentId);
                            template.setCreatedDate(new Date());
                            template.setUpdatedDate(new Date());
                            templateRepository.save(template);
                            KafkaExchange.pushLog(LogGroup.GROUP_TEMPLATE, LogAction.LOG_POST, template.getId(), null, template);
                            return new IdResponse(template.getId());
                        }
                        else {
                            //File not found
                            throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.file")}, HttpServletResponse.SC_NOT_FOUND);
                        }
                    }
                    else {
                        //Invalid path
                        throw new DigoHttpException(10008, new String[]{translator.toLocale("lang.word.data")}, HttpServletResponse.SC_BAD_REQUEST);
                    }
                }
                else {
                    // File null
                    throw new DigoHttpException(10013, HttpServletResponse.SC_BAD_REQUEST);
                }
            }
        } catch (Exception ex) {
            logger.info("postTemplate Exception: " + ex.getMessage());
            if (ex.getMessage() != null && ex.getMessage().contains("E11000")) {
                throw new DigoHttpException(10007, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_CONFLICT);
            }
        }
        throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_NOT_FOUND);
    }

    public AffectedRowsDto deleteTemplate(ObjectId id) {
        try {
            int affected = templateRepository.deleteTemplateById(id);
            if (affected > 0) {
                KafkaExchange.pushLog(LogGroup.GROUP_TEMPLATE, LogAction.LOG_DEL, id, null, null);
            }
            return new AffectedRowsDto(affected);
        } catch (Exception e) {
            logger.info("deleteTemplate - Delete template failed. Exception" + e.getMessage());
        }
        return new AffectedRowsDto(0);
    }

    public AffectedRowsDto putTemplateFileSignId(ObjectId id, TemplateFileSignDto templatePayload) {
        ObjectId deploymentId = Context.getDeploymentId();
        List<TemplateFileSignDto> templateFileSignList = new ArrayList<>();
        if (deploymentId != null) {
            try {
                Template oldTemplate = templateRepository.findOneById(id);
                if (Objects.nonNull(oldTemplate.getFileSign()) && oldTemplate.getFileSign().size() > 0) {
                    templateFileSignList = oldTemplate.getFileSign();
                }
                if (Objects.nonNull(templatePayload.getFileSignId()) && templatePayload.getFileSignId() != null && Objects.nonNull(templatePayload.getFileSignName()) && templatePayload.getFileSignName() != null) {
                    TemplateFileSignDto newTemplateFileSignDto = new TemplateFileSignDto(templatePayload.getFileSignId(), templatePayload.getFileSignName());
                    templateFileSignList.add(newTemplateFileSignDto);
                }
                logger.info("putTemplateFileSignId - templateFileSignList: " + templateFileSignList);
                oldTemplate.setFileSign(templateFileSignList);
                templateRepository.save(oldTemplate);
                return new AffectedRowsDto(1);
            } catch (Exception e) {
            }
        }
        return new AffectedRowsDto(0);
    }

    public AffectedRowsDto putTemplate(ObjectId id, PutTemplateDto templatePayload) {
        ObjectId deploymentId = Context.getDeploymentId();
        logger.info("putTemplate Begin");
        if (deploymentId != null) {
            try {
                //Check file
                if (templatePayload.getFile() != null && templatePayload.getFile().getFilename() != null && !templatePayload.getFile().getFilename().isEmpty()) {
                    logger.info("putTemplate payload: " + templatePayload.getFile().toString());
                    //Check match path
                    String matchFilename = "NULL";
                    if (templatePayload.getFile().getPath().length() > 24) {
                        matchFilename = templatePayload.getFile().getPath().substring(0, 24);
                        //check path
                        for(String regex : uploadMalformedRegex){
                            templatePayload.getFile().setPath(templatePayload.getFile().getPath().replace(regex, ""));
                        }
                    }
                    logger.info("putTemplate matchFilename: " + matchFilename);
                    if (matchFilename.equals(deploymentId.toHexString())) {
                        logger.info("putTemplate payload: " + templatePayload.getFile().toString());
                        //Check file
                        File file = new File(uploadDir + templatePayload.getFile().getPath());
                        if (file.exists() && !file.isDirectory()) {
                            logger.info("putTemplate file exist with path: " + uploadDir + templatePayload.getFile().getPath());
                            Template oldTemplate = templateRepository.findOneById(id);
                            if (oldTemplate != null) {
                                Template template = GsonHelper.copyObject(templatePayload, Template.class);

                                Gson g = new Gson();
                                Object objFromJson = g.fromJson(templatePayload.getListVariableString(), Object.class);
                                template.setListVariable(objFromJson);
                                if (Objects.nonNull(templatePayload.getSignEnable()) && templatePayload.getSignEnable() != null) {
                                    template.setSignEnable(templatePayload.getSignEnable());
                                }
                                if (Objects.nonNull(templatePayload.getCode()) && templatePayload.getCode() != null) {
                                    template.setCode(templatePayload.getCode());
                                }
                                template.setId(oldTemplate.getId());
                                template.setDeploymentId(oldTemplate.getDeploymentId());
                                template.setCreatedDate(oldTemplate.getCreatedDate());
                                template.setUpdatedDate(new Date());
                                templateRepository.save(template);
                                KafkaExchange.pushLog(LogGroup.GROUP_TEMPLATE, LogAction.LOG_PUT, template.getId(), oldTemplate, template);
                                return new AffectedRowsDto(1);
                            }
                        }
                        else {
                            //File not found
                            throw new DigoHttpException(10002, new String[]{translator.toLocale("lang.word.file")}, HttpServletResponse.SC_NOT_FOUND);
                        }
                    }
                    else {
                        //Invalid path
                        throw new DigoHttpException(10009, new String[]{translator.toLocale("lang.word.data")}, HttpServletResponse.SC_BAD_REQUEST);
                    }
                }
                else {
                    // File null
                    throw new DigoHttpException(10013, HttpServletResponse.SC_BAD_REQUEST);
                }
            } catch(DigoHttpException digoe){
                throw digoe;
            } catch (DuplicateKeyException ex) {
                throw new DigoHttpException(10007, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_CONFLICT);
            }
        }
        return new AffectedRowsDto(0);
    }

    public GetTemplateByIdDto getDetailTemplate(ObjectId id) {
        ObjectId deploymentId = Context.getDeploymentId();
        if (deploymentId != null) {
            Template template = templateRepository.findOneById(id);

            if (template != null) {
                GetTemplateByIdDto result = GsonHelper.copyObject(template, GetTemplateByIdDto.class);
                return result;
            } else {
                throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.template")}, HttpServletResponse.SC_NOT_FOUND);
            }
        } else {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }

}
