package vn.vnpt.digo.reporter.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrintToBirtViewerDto implements Serializable{
    
    private String report;

    @NotNull
    private String apiGateway;

    private ObjectId dossierId;

    private ObjectId id;

    private String endDate;

    private String  receiptCode;

    private String agencyUser;

    private String transferredUser;

    private String numberUser;

    private String addressUser;

    private String signingPlace;
}
