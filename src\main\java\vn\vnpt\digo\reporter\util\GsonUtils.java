package vn.vnpt.digo.reporter.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.google.gson.*;
import org.bson.types.ObjectId;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class GsonUtils {

    private static final GsonBuilder gsonBuilder = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
            .registerTypeAdapter(ObjectId.class, new JsonSerializer<ObjectId>() {
                @Override
                public JsonElement serialize(ObjectId src, Type typeOfSrc, JsonSerializationContext context) {
                    return new JsonPrimitive(src.toHexString());
                }
            })
            .registerTypeAdapter(ObjectId.class, new JsonDeserializer<ObjectId>() {
                @Override
                public ObjectId deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                        throws JsonParseException {
                    return new ObjectId(json.getAsString());
                }
            });

    public static Gson getGson() {
        return gsonBuilder.create();
    }

    public static <S extends Object> String getJson(S s) {
        return getGson().toJson(s);
    }

    public static ObjectMapper getObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }

    @SuppressWarnings("unchecked")
    public static <S extends Object> S copyObject(S s) {
        return (S) getGson().fromJson(getJson(s), s.getClass());
    }

    public static <S extends Object, T extends Object> T copyObject(S s, Class<T> classOfT) {
        return getGson().fromJson(getJson(s), classOfT);
    }

    public static <T> List<T> copyList(String json, Class<T> classOfT)
            throws JsonMappingException, JsonProcessingException {
        CollectionType listType = getObjectMapper().getTypeFactory().constructCollectionType(ArrayList.class, classOfT);
        return getObjectMapper().readValue(json, listType);
    }

    public static <T> List<T> copyList(List<?> list, Class<T> classOfT)
            throws JsonMappingException, JsonProcessingException {
        return copyList(getJson(list), classOfT);
    }
}
