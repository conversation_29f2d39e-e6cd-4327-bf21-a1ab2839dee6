package vn.vnpt.digo.reporter.stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import vn.vnpt.digo.reporter.dto.KafkaProcedureDossierQuantityDto;
import vn.vnpt.digo.reporter.stream.messaging.ProcedureProducer;

/**
 *
 * <AUTHOR>
 */
public class ProcedureProducerStream {

    @Autowired
    private ProcedureProducer procedureProducer;

    public boolean push(KafkaProcedureDossierQuantityDto input) {
        Message<KafkaProcedureDossierQuantityDto> message = MessageBuilder.withPayload(input).build();
        return procedureProducer.output().send(message);
    }
}
