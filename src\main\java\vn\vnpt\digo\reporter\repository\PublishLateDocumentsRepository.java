package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.EvaluationResultForm;
import vn.vnpt.digo.reporter.document.PublishLateDocuments;

@Repository
public interface PublishLateDocumentsRepository extends MongoRepository<PublishLateDocuments, ObjectId> {
    public  Integer deletePublishLateDocumentsById(ObjectId id);
}
