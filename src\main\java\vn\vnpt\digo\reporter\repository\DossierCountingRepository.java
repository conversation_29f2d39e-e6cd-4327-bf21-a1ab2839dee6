package vn.vnpt.digo.reporter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.DossierCounting;
import vn.vnpt.digo.reporter.document.Procedure;

@Repository
public interface DossierCountingRepository extends MongoRepository<DossierCounting, ObjectId> {
  @Query(value = "{'_id': :#{#id} }")
  DossierCounting getDossierCounting(@Param("id") ObjectId id);

  int deleteDossierCountingById(@Param("id") ObjectId id);
}
