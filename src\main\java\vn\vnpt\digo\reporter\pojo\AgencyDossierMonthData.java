/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AgencyDossierMonthData implements Serializable {

    private Integer received = 0;
    private Integer resolved = 0;
    private Integer early = 0;
    private Integer onTime = 0;
    private Integer overdue = 0;
    private Integer canceled = 0;
}
