package vn.vnpt.digo.reporter.api;

import javax.servlet.http.HttpServletRequest;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.FrequentProcedureDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.PutProcedureDto;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.service.ProcedureService;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/procedure")
@IcodeAuthorize("vnpt.permission.procedure")
public class ProcedureController {

    @Autowired
    private ProcedureService procedureService;

    Logger logger = LoggerFactory.getLogger(AgencyController.class);

    // Create: Task DIGO-3192    
    @GetMapping("/--frequent")
    public FrequentProcedureDto getFrequentList(HttpServletRequest request, @RequestParam(value = "agency-id", required = false) ObjectId agencyId,
            @RequestParam(value = "citizen-id", required = false) ObjectId citizensId,
            @RequestParam(value = "enterprise-id", required = false) ObjectId enterpriseId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        FrequentProcedureDto frequentProcedureDto = procedureService.getFrequentList(agencyId, citizensId, enterpriseId);
        logger.info("DIGO-Info: " + frequentProcedureDto.toString());
        return frequentProcedureDto;
    }

    @GetMapping("/--frequent-v2")
    public List<ProcedureDto> getFrequentListV2(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<ProcedureDto> frequentProcedureDto = procedureService.getFrequentListV2();
        logger.info("DIGO-Info: " + frequentProcedureDto.toString());
        return frequentProcedureDto;
    }

    @PutMapping("{id}/--frequent-quantity")
    public AffectedRowsDto updateProcedureFrequentQuantity(HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id,
            @RequestParam(value = "quantity", required = true) Integer quantity,
            @RequestParam(value = "name", required = true) String name) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRows = procedureService.updateProcedureFrequentQuantity(id, quantity, name);
        return affectedRows;
    }
    
    @PutMapping("{id}/--frequent")
    public AffectedRowsDto updateProcedureFrequent(HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id,
            @RequestBody PutProcedureDto putBody) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRows = procedureService.updateProcedureFrequent(id, putBody);
        return affectedRows;
    }
    
    @PostMapping("/--frequent")
    public PostResponseDto addProcedureFrequent(HttpServletRequest request,
            @RequestBody PutProcedureDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        PostResponseDto newCate = procedureService.addProcedureFrequent(body);
        logger.info(newCate.getId().toString());
        return newCate;
    }
    
    @DeleteMapping("/{id}/--frequent")
    public AffectedRowsDto deleteProcedureFrequent(HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto deletedDossierFormFile = procedureService.deleteProcedureFrequent(id);
        logger.info(deletedDossierFormFile.getAffectedRows() + "");
        return deletedDossierFormFile;
    }

    @GetMapping("/--find-all")
    public List<PutProcedureDto> getListAll(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return procedureService.getListAll();
    }
}
