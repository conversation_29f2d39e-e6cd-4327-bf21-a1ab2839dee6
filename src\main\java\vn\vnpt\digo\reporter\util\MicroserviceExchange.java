package vn.vnpt.digo.reporter.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody;
import vn.vnpt.digo.reporter.properties.DigoProperties;

import javax.annotation.PostConstruct;
import java.util.Objects;

@Component
public class MicroserviceExchange {
    @Autowired
    private DigoProperties prop;
    private static DigoProperties digoProperties;
    private static RestTemplate restTemplate = new RestTemplate();

    @Value("${digo.microservice.client-id}")
    private String client;

    @Value("${digo.microservice.client-secret}")
    private String secret;

    private static String clientId;
    private static String clientSecret;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String oidcPropertiesUrl;

    private static String oidcUrl;


    public static <T> T get(String token, RestTemplate restTemplate, String url, Class<T> returnType) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        //Get data
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, request, returnType);
        //response.getHeaders().getContentDisposition().getFilename();
        return response.getBody();
    }

    public static <T> T get(RestTemplate restTemplate, String url, Class<T> returnType) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        //Get data
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, request, returnType);
        //response.getHeaders().getContentDisposition().getFilename();
        return response.getBody();
    }

    public static <T> T getNoAuth(RestTemplate restTemplate, String url, Class<T> returnType) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        //Get data
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, request, returnType);
        //response.getHeaders().getContentDisposition().getFilename();
        return response.getBody();
    }
    @PostConstruct
    void init() {
        digoProperties = prop;
        oidcUrl = oidcPropertiesUrl;
        clientId = client;
        clientSecret = secret;
    }
    public static <T> T postMultipart(RestTemplate restTemplate, String url, MultiValueMap<String, Object> body, Class<T> returnType) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<T> result = restTemplate.exchange(url, HttpMethod.POST, request, returnType);
        //Return body
        return result.getBody();
    }

    public static <T> T post(String url,Object body ,Class<T> returnType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (Context.getJwtAuthenticationTokenValue() != null) {
            headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        } else {
            vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody tokenBody = getToken();
            headers.setBearerAuth(tokenBody.getAccessToken());
        }
        System.out.println(url);
        HttpEntity<?> request = new HttpEntity<>(body,headers);
        ResponseEntity<T> result = restTemplate.exchange(url, HttpMethod.POST, request, returnType);
        return result.getBody();
    }
    public static <T> T post(String token,String url,Object body ,Class<T> returnType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);
        // System.out.println(url);
        HttpEntity<?> request = new HttpEntity<>(body,headers);
        ResponseEntity<T> result = restTemplate.exchange(url, HttpMethod.POST, request, returnType);
        return result.getBody();
    }
    public static vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody getToken() {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        DigoProperties.Microservice.Auth auth = digoProperties.getMicroservice().getAuth();
        System.out.println(auth.getGrantType());
        System.out.println(auth.getClientId());
        System.out.println(auth.getUsername());
        System.out.println(auth.getPassword());
        System.out.println(auth.getClientSecret());

        map.add("grant_type", auth.getGrantType());
        map.add("client_id", auth.getClientId());
        map.add("username", auth.getUsername());
        map.add("password", auth.getPassword());
        map.add("client_secret", auth.getClientSecret());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity< MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        String url = oidcUrl + "/protocol/openid-connect/token";
        System.out.println(url);

        ResponseEntity<vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody> result = restTemplate.exchange(url, HttpMethod.POST, request, vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody.class);
        return result.getBody();
    }

    public static <T> T postJson(RestTemplate restTemplate, String url, Object body, Class<T> returnType) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<T> result = restTemplate.exchange(url, HttpMethod.POST, request, returnType);
        //Return body
        return result.getBody();
    }

    public static <T> T getLogMan(RestTemplate restTemplate, String url,  Class<T> returnType) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        //Get data
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, request, returnType);
        //response.getHeaders().getContentDisposition().getFilename();
        return response.getBody();
    }

    public static String getTokenMinio() {
        String token = null;
        try{
            JwtAuthenticationToken claims = Context.getJwtAuthenticationToken();
            token = claims.getToken().getTokenValue();
        } catch (Exception ex) {}

        if(Objects.isNull(token)){
            try{
                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("grant_type", "client_credentials");
                map.add("client_id", clientId);
                map.add("client_secret", clientSecret);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
                String url = oidcUrl + "/protocol/openid-connect/token";
                System.out.println(url);
                ResponseEntity<JWTGetTokenResponseBody> result = restTemplate.exchange(url, HttpMethod.POST, request, JWTGetTokenResponseBody.class);
                token = result.getBody().getAccessToken();
            } catch (Exception ex) {}
        }
        return token;
    }


    public static <T> T getMinio(RestTemplate restTemplate, String url, HttpHeaders headers, Class<T> returnType) {
        try{
            HttpEntity<?> request = new HttpEntity<>(headers);
            ResponseEntity<T> result = restTemplate.exchange(url, HttpMethod.GET, request, returnType);
            return result.getBody();
        } catch (Exception ex) {
            return null;
        }
    }

    public static <T> T postNoAuth(RestTemplate restTemplate, String url, Object body, Class<T> returnType) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(body, headers);
            System.out.println("Sending POST to " + url + " with body: " + body);

            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, request, returnType);
            System.out.println("Received response with status: " + response.getStatusCode());
            return response.getBody();
        } catch (HttpClientErrorException e) {
            System.err.println("HTTP Client Error: " + e.getStatusCode() + ", Response: " + e.getResponseBodyAsString());
            throw e;
        } catch (HttpServerErrorException e) {
            System.err.println("HTTP Server Error: " + e.getStatusCode() + ", Response: " + e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            System.err.println("Error in postNoAuth: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
