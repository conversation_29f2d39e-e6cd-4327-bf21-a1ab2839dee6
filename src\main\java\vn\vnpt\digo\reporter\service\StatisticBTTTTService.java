package vn.vnpt.digo.reporter.service;

import com.google.analytics.data.v1beta.*;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.oauth2.GoogleCredentials;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import springfox.documentation.annotations.Cacheable;
import vn.vnpt.digo.reporter.dto.statistics.AnalyticReportDto;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Service
public class StatisticBTTTTService {

    @Value("${analytics}")
    private String jsonSecret;

    @Cacheable("StatisticBTTTTService.analyticsReport")
    public AnalyticReportDto analyticsReport(String propertyId, String metrics, String startDate, String endDate) {
        try {
            AnalyticReportDto resultObject = new AnalyticReportDto();
            if(jsonSecret == null || jsonSecret.isEmpty()) {
                resultObject.setErr("<PERSON>ui lòng cấu hình tài kho<PERSON>n truy cập");
                return resultObject;
            }
            // create new stream tu text json
            InputStream stream = new ByteArrayInputStream(jsonSecret.getBytes());
            // Lay du lieu tu google analytics
            GoogleCredentials credentials = GoogleCredentials.fromStream(stream);
            // load the credentials
            BetaAnalyticsDataSettings betaAnalyticsDataSettings =
                    BetaAnalyticsDataSettings.newBuilder()
                            .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                            .build();
            // Create the Analytics Data client.
            try (BetaAnalyticsDataClient analyticsData = BetaAnalyticsDataClient.create(betaAnalyticsDataSettings)) {
                //The number of app screens or web pages your users viewed. Repeated views of a single page or screen are counted. (screen_view + page_view events).
                RunReportRequest request =
                        RunReportRequest.newBuilder()
                                .setProperty("properties/" + propertyId)
//                                .addDimensions(Dimension.newBuilder().setName("date"))
//                                .addDimensions(Dimension.newBuilder().setName("city"))
//                                .addDimensions(Dimension.newBuilder().setName("country"))
//                                .addDimensions(Dimension.newBuilder().setName("deviceCategory"))
//                                .addDimensions(Dimension.newBuilder().setName("pageTitle"))
                                .addMetrics(Metric.newBuilder().setName(metrics))
                                .addDateRanges(DateRange.newBuilder().setStartDate(startDate).setEndDate(endDate))
                                .build();

                // Make the request.
                RunReportResponse response = analyticsData.runReport(request);

                System.out.printf(String.valueOf(response));
                int total = 0;
                for (Row row : response.getRowsList()) {
                    total += Integer.parseInt(row.getMetricValues(0).getValue());
                }

                // make request to get total online
                RunRealtimeReportRequest request2 =
                        RunRealtimeReportRequest.newBuilder()
                                .setProperty("properties/" + propertyId)
                                .addMetrics(Metric.newBuilder().setName("activeUsers"))
                                .build();
                RunRealtimeReportResponse response2 = analyticsData.runRealtimeReport(request2);
                int totalOnline = 0;
                for (Row row : response2.getRowsList()) {
                    totalOnline += Integer.parseInt(row.getMetricValues(0).getValue());
                }
                // create result object
                resultObject.setTotal(total);
                resultObject.setTotalOnline(totalOnline);
                return resultObject;
            }
        } catch (Exception e) {
            AnalyticReportDto resultObject = new AnalyticReportDto();
            resultObject.setErr(e.toString());
            return resultObject;
        }
    }
}
