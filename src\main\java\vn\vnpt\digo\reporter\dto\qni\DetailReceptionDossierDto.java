package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.util.HideSecurityInformationHelper;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetailReceptionDossierDto implements Serializable {
    @JsonProperty("no")
    private Integer no;

    @JsonProperty("id")
    private String id;

    @JsonProperty("dossierCode")
    private String dossierCode;

    @JsonProperty("procedureName")
    private String procedureName;

    @JsonProperty("sectorName")
    private String sectorName;

    @JsonProperty("noiDungYeuCauGiaiQuyet")
    private String noiDungYeuCauGiaiQuyet;

    @JsonProperty("appliedDate")
    private String appliedDate;

    @JsonProperty("acceptedDate")
    private String acceptedDate;

    @JsonProperty("appointmentDate")
    private String appointmentDate;

    @JsonProperty("completedDate")
    private String completedDate;

    @JsonProperty("applicantOwnerFullName")
    private String applicantOwnerFullName;

    @JsonProperty("applicantPhoneNumber")
    private String applicantPhoneNumber;

    @JsonProperty("agencyName")
    private String agencyName;

    @JsonProperty("applyMethod")
    private String applyMethod;

    @JsonProperty("appliedDue")
    private String appliedDue;

    public void setHideSecurityInformation(String fullname){
        this.applicantOwnerFullName =HideSecurityInformationHelper.setFullnameSecurity(fullname);
    }
}
