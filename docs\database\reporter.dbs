<?xml version="1.0" encoding="UTF-8" ?>
<project name="digoReporter" id="Project_7f4c71" database="MongoDb" >
	<schema name="digoReporter" >
		<table name="Collection" />
		<table name="account" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_account" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
			<fk name="Fk_account_file" virtual="y" to_schema="digoReporter" to_table="agency" />
		</table>
		<table name="agency" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="originId" type="oid" jt="-8" mandatory="y" />
			<column name="parentId" type="oid" jt="-8" mandatory="y" />
			<column name="placeId" type="oid" jt="-8" mandatory="y" />
			<column name="ancestorPlaceId" type="array" jt="4999545" mandatory="y" />
			<column name="name" type="array" jt="4999545" mandatory="y" />
			<column name="sector" type="array" jt="4999545" mandatory="y" />
			<column name="tag" type="array" jt="4999545" />
			<column name="level" type="object" jt="4999544" />
			<column name="procedure" type="object" jt="4999544" mandatory="y" />
			<column name="dossier" type="array" jt="4999545" />
			<column name="deploymentId" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_file" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="agency.dossier" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="year" type="integer" jt="4" />
			<column name="month" type="array" jt="4999545" />
			<fk name="Fk_procedure.dossierStat_agency" virtual="y" to_schema="digoReporter" to_table="agency" >
				<fk_column name="*" pk="dossier" />
			</fk>
		</table>
		<table name="agency.dossier.month" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="m1" type="object" jt="4999544" />
			<fk name="Fk_agency.dossier.report_agency.dossier" virtual="y" to_schema="digoReporter" to_table="agency.dossier" >
				<fk_column name="*" pk="month" />
			</fk>
		</table>
		<table name="agency.dossier.month.m1" >
			<column name="*" type="object" jt="4999544" mandatory="y" />
			<column name="received" type="integer" jt="4" />
			<column name="resolved" type="integer" jt="4" />
			<column name="early" type="integer" jt="4" />
			<column name="onTime" type="integer" jt="4" />
			<column name="overdue" type="integer" jt="4" />
			<column name="canceled" type="integer" jt="4" />
			<column name="day" type="object" jt="4999544" />
			<fk name="Fk_agency.dossier.report.month_agency.dossier.report" virtual="y" to_schema="digoReporter" to_table="agency.dossier.month" >
				<fk_column name="*" pk="m1" />
			</fk>
		</table>
		<table name="agency.level" >
			<column name="*" type="object" jt="4999544" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_agency.tag_0_*" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_agency.tag_0_agency" virtual="y" to_schema="digoReporter" to_table="agency" >
				<fk_column name="*" pk="level" />
			</fk>
		</table>
		<table name="agency.name" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<fk name="Fk_agency.name_agency" virtual="y" to_schema="digoReporter" to_table="agency" >
				<fk_column name="*" pk="name" />
			</fk>
		</table>
		<table name="agency.procedure" >
			<column name="*" type="object" jt="4999544" mandatory="y" />
			<column name="activeQuantity" type="integer" jt="4" mandatory="y" />
			<column name="secondLevelQuantity" type="integer" jt="4" mandatory="y" />
			<column name="thirdLevelQuantity" type="integer" jt="4" mandatory="y" />
			<column name="fourthLevelQuantity" type="integer" jt="4" mandatory="y" />
			<index name="Idx_agency.procedure" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_agency.procedure_agency" virtual="y" to_schema="digoReporter" to_table="agency" >
				<fk_column name="*" pk="procedure" />
			</fk>
		</table>
		<table name="agency.sector" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<column name="procedureQuantity" type="integer" jt="4" mandatory="y" />
			<column name="transSector" type="array" jt="4999545" mandatory="y" />
			<fk name="Fk_agency.sector_agency" virtual="y" to_schema="digoReporter" to_table="agency" >
				<fk_column name="*" pk="sector" />
			</fk>
		</table>
		<table name="agency.sector.transSector" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<fk name="Fk_agency.sector.transSector_agency.sector" virtual="y" to_schema="digoReporter" to_table="agency.sector" >
				<fk_column name="*" pk="transSector" />
			</fk>
		</table>
		<table name="agency.tag" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<fk name="Fk_agency.tag_agency" virtual="y" to_schema="digoReporter" to_table="agency" >
				<fk_column name="*" pk="tag" />
			</fk>
		</table>
		<table name="agency.tag.name" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<fk name="Fk_agency.tag.name_agency.tag" virtual="y" to_schema="digoReporter" to_table="agency.tag" />
		</table>
		<table name="client" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_client" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
			<fk name="Fk_client_file" virtual="y" to_schema="digoReporter" to_table="agency" />
		</table>
		<table name="dossierByDay" >
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<column name="year" type="integer" jt="4" mandatory="y" />
			<column name="month" type="integer" jt="4" mandatory="y" />
			<column name="day" type="integer" jt="4" mandatory="y" />
			<column name="agency" type="object" jt="4999544" />
			<column name="agencyLevel" type="object" jt="4999544" />
			<column name="sector" type="object" jt="4999544" />
			<column name="procedureLevel" type="object" jt="4999544" />
			<column name="procedure" type="object" jt="4999544" />
			<column name="deploymentId" type="oid" jt="-8" mandatory="y" />
			<column name="updatedDate" type="date" jt="91" mandatory="y" />
			<column name="appliedOnline" type="integer" jt="4" />
			<column name="receivedOnline" type="integer" jt="4" />
			<column name="receivedDirect" type="integer" jt="4" />
			<column name="received" type="integer" jt="4" />
			<column name="resolved" type="integer" jt="4" />
			<column name="resolvedEarly" type="integer" jt="4" />
			<column name="resolvedOnTime" type="integer" jt="4" />
			<column name="resolvedOverdue" type="integer" jt="4" />
			<column name="unresolved" type="integer" jt="4" />
			<column name="unresolvedOverdue" type="integer" jt="4" />
			<column name="returnedOnTime" type="integer" jt="4" />
			<column name="returnedOverdue" type="integer" jt="4" />
			<column name="cancelled" type="integer" jt="4" />
			<column name="deleted" type="integer" jt="4" />
			<column name="suspended" type="integer" jt="4" />
		</table>
		<table name="dossierByDay.agency" >
			<column name="*" type="integer" jt="4" mandatory="y" />
			<column name="id" type="integer" jt="4" />
			<column name="name" type="array" jt="4999545" />
			<index name="Idx_dossierByDay.agency" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_dossierByDay.agency_dossierByDay" virtual="y" to_schema="digoReporter" to_table="dossierByDay" >
				<fk_column name="*" pk="agency" />
			</fk>
		</table>
		<table name="dossierByDay.agency.name" >
			<column name="*" type="string" jt="12" mandatory="y" />
			<column name="languageId" type="integer" jt="4" />
			<column name="name" type="string" jt="12" />
			<fk name="Fk_dossierByDay.agency.name_dossierByDay.agency" virtual="y" to_schema="digoReporter" to_table="dossierByDay.agency" >
				<fk_column name="*" pk="name" />
			</fk>
		</table>
		<table name="dossierByDay.agencyLevel" >
			<column name="*" type="integer" jt="4" mandatory="y" />
			<column name="id" type="integer" jt="4" />
			<column name="name" type="array" jt="4999545" />
			<index name="Idx_dossierByDay.agency_0" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_dossierByDay.agencyLevel_dossierByDay" virtual="y" to_schema="digoReporter" to_table="dossierByDay" >
				<fk_column name="*" pk="agencyLevel" />
			</fk>
		</table>
		<table name="dossierByDay.procedure" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="id" type="oid" jt="-8" />
			<column name="code" type="string" jt="12" />
			<column name="translate" type="array" jt="4999545" />
			<index name="Idx_dossierByDay.sector_1" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_dossierByDay.procedure_dossierByDay" virtual="y" to_schema="digoReporter" to_table="dossierByDay" >
				<fk_column name="*" pk="procedure" />
			</fk>
		</table>
		<table name="dossierByDay.procedure.translate" >
			<column name="*" type="string" jt="12" mandatory="y" />
			<column name="languageId" type="integer" jt="4" />
			<column name="name" type="string" jt="12" />
			<fk name="Fk_dossierByDay.procedure.translate_dossierByDay.procedure" virtual="y" to_schema="digoReporter" to_table="dossierByDay.procedure" >
				<fk_column name="*" pk="translate" />
			</fk>
		</table>
		<table name="dossierByDay.procedureLevel" >
			<column name="*" type="integer" jt="4" mandatory="y" />
			<column name="id" type="oid" jt="-8" />
			<column name="name" type="array" jt="4999545" />
			<index name="Idx_dossierByDay.sector_0" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_dossierByDay.procedureLevel_dossierByDay" virtual="y" to_schema="digoReporter" to_table="dossierByDay" >
				<fk_column name="*" pk="procedureLevel" />
			</fk>
		</table>
		<table name="dossierByDay.procedureLevel.name" >
			<column name="*" type="string" jt="12" mandatory="y" />
			<column name="languageId" type="integer" jt="4" />
			<column name="name" type="string" jt="12" />
			<fk name="Fk_dossierByDay.procedureLevel.name_dossierByDay.procedureLevel" virtual="y" to_schema="digoReporter" to_table="dossierByDay.procedureLevel" >
				<fk_column name="*" pk="name" />
			</fk>
		</table>
		<table name="dossierByDay.sector" >
			<column name="*" type="integer" jt="4" mandatory="y" />
			<column name="id" type="oid" jt="-8" />
			<column name="name" type="array" jt="4999545" />
			<index name="Idx_dossierByDay.sector" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_dossierByDay.sector_dossierByDay" virtual="y" to_schema="digoReporter" to_table="dossierByDay" >
				<fk_column name="*" pk="sector" />
			</fk>
		</table>
		<table name="dossierByDay.sector.name" >
			<column name="*" type="string" jt="12" mandatory="y" />
			<column name="languageId" type="integer" jt="4" />
			<column name="name" type="string" jt="12" />
			<fk name="Fk_dossierByDay.sector.name_dossierByDay.sector" virtual="y" to_schema="digoReporter" to_table="dossierByDay.sector" >
				<fk_column name="*" pk="name" />
			</fk>
		</table>
		<table name="electricBill" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="billCode" type="string" jt="12" />
			<column name="userId" type="oid" jt="-8" />
			<column name="customerCode" type="string" jt="12" mandatory="y" />
			<column name="meterNumber" type="string" jt="12" mandatory="y" />
			<column name="year" type="integer" jt="4" mandatory="y" />
			<column name="month" type="integer" jt="4" mandatory="y" />
			<column name="startDate" type="date" jt="91" mandatory="y" />
			<column name="endDate" type="date" jt="91" mandatory="y" />
			<column name="oldIndex" type="integer" jt="4" mandatory="y" />
			<column name="newIndex" type="integer" jt="4" mandatory="y" />
			<column name="consumedAmount" type="integer" jt="4" mandatory="y" />
			<column name="paymentAmount" type="double" jt="8" mandatory="y" />
			<column name="paid" type="boolean" jt="16" mandatory="y" />
			<index name="Idx_electricBill_0" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="electricCustomer" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="userId" type="oid" jt="-8" mandatory="y" />
			<column name="customerCode" type="string" jt="12" mandatory="y" />
			<column name="updatedDate" type="date" jt="91" mandatory="y" />
			<index name="Idx_electricCustomer" unique="UNIQUE_KEY" >
				<column name="userId" />
				<column name="customerCode" />
			</index>
		</table>
		<table name="petitionStatistics" prior="Collection_0" >
			<column name="id" type="oid" jt="-8" />
			<column name="statusStatistics" type="array" jt="4999545" >
				<column name="status" type="object" jt="4999544" >
					<column name="statusNumber" type="integer" jt="4" />
					<column name="statusDescription" type="string" jt="12" />
				</column>
				<column name="statistics" type="object" jt="4999544" >
					<column name="today" type="long" jt="-1" />
					<column name="thisMonth" type="long" jt="-1" />
					<column name="systemTotal" type="long" jt="-1" />
				</column>
			</column>
			<column name="receptionMethodStatistics" type="array" jt="4999545" >
				<column name="receptionMethod" type="object" jt="4999544" >
					<column name="id" type="oid" jt="-8" />
					<column name="trans" type="array" jt="4999545" >
						<column name="languageId" type="integer" jt="4" />
						<column name="name" type="string" jt="12" />
					</column>
					<column name="transName" type="string" jt="12" />
				</column>
				<column name="statistics" type="object" jt="4999544" >
					<column name="today" type="long" jt="-1" />
					<column name="thisMonth" type="long" jt="-1" />
					<column name="systemTotal" type="long" jt="-1" />
				</column>
			</column>
			<column name="fieldStatistics" type="array" jt="4999545" >
				<column name="field" type="object" jt="4999544" >
					<column name="id" type="oid" jt="-8" />
					<column name="trans" type="array" jt="4999545" >
						<column name="languageId" type="integer" jt="4" />
						<column name="name" type="string" jt="12" />
					</column>
					<column name="transName" type="string" jt="12" />
				</column>
				<column name="statistics" type="object" jt="4999544" >
					<column name="today" type="long" jt="-1" />
					<column name="thisMonth" type="long" jt="-1" />
					<column name="systemTotal" type="long" jt="-1" />
				</column>
			</column>
			<column name="deploymentId" type="oid" jt="-8" />
			<column name="creadDate" type="date" jt="91" />
			<column name="updatedDate" type="date" jt="91" />
		</table>
		<table name="procedure" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="originId" type="oid" jt="-8" mandatory="y" />
			<column name="agency" type="object" jt="4999544" mandatory="y" />
			<column name="sector" type="object" jt="4999544" mandatory="y" />
			<column name="translate" type="array" jt="4999545" mandatory="y" />
			<column name="dossierQuantity" type="integer" jt="4" />
			<column name="implementer" type="array" jt="4999545" />
			<column name="deploymentId" type="oid" jt="-8" />
			<index name="Idx_mimeType" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
			<index name="Idx_procedure_originId" unique="UNIQUE_KEY" >
				<column name="originId" />
			</index>
		</table>
		<table name="procedure.agency" >
			<column name="*" type="oid" jt="-8" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_procedure.sector_*_0" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_procedure.agency_procedure" virtual="y" to_schema="digoReporter" to_table="procedure" >
				<fk_column name="*" pk="agency" />
			</fk>
		</table>
		<table name="procedure.implementer" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<fk name="Fk_procedure.implementer_procedure" virtual="y" to_schema="digoReporter" to_table="procedure" >
				<fk_column name="*" pk="implementer" />
			</fk>
		</table>
		<table name="procedure.name.name" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
		</table>
		<table name="procedure.sector" >
			<column name="*" type="oid" jt="-8" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_procedure.sector_*" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_procedure.sector_procedure" virtual="y" to_schema="digoReporter" to_table="procedure" >
				<fk_column name="*" pk="sector" />
			</fk>
		</table>
		<table name="procedure.translate" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<fk name="Fk_procedure.translate_procedure" virtual="y" to_schema="digoReporter" to_table="procedure" >
				<fk_column name="*" pk="translate" />
			</fk>
		</table>
		<table name="sector" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="originId" type="oid" jt="-8" mandatory="y" />
			<column name="translate" type="array" jt="4999545" mandatory="y" />
			<column name="agencyId" type="oid" jt="-8" mandatory="y" />
		</table>
		<table name="sector.translate" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<fk name="Fk_sector$2.transSector_sector$2" virtual="y" to_schema="digoReporter" to_table="sector" >
				<fk_column name="*" pk="translate" />
			</fk>
		</table>
		<table name="subscriptionType" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="idMapping" type="string" jt="12" mandatory="y" />
			<column name="name" type="array" jt="4999545" mandatory="y" />
			<index name="Idx_subcriptionType" unique="UNIQUE_KEY" >
				<column name="_id" />
				<column name="idMapping" />
			</index>
		</table>
		<table name="subscriptionType.name" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<fk name="Fk_subscriptionType.name_subscriptionType" virtual="y" to_schema="digoReporter" to_table="subscriptionType" >
				<fk_column name="*" pk="name" />
			</fk>
		</table>
		<table name="telecomCost" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="phoneNumber" type="string" jt="12" mandatory="y" />
			<column name="month" type="integer" jt="4" mandatory="y" />
			<column name="year" type="integer" jt="4" mandatory="y" />
			<column name="paymentCode" type="string" jt="12" />
			<column name="subscriptionCode" type="string" jt="12" mandatory="y" />
			<column name="subscriptionType" type="object" jt="4999544" mandatory="y" />
			<column name="amount" type="double" jt="8" mandatory="y" />
			<index name="Idx_telecomCost" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="telecomCost.subscriptionType" >
			<column name="*" type="object" jt="4999544" mandatory="y" />
			<column name="id" type="oid" jt="-8" mandatory="y" />
			<column name="name" type="array" jt="4999545" mandatory="y" />
			<index name="Idx_telecomCost.subscriptionType" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_telecomCost.subscriptionType_telecomCost" virtual="y" to_schema="digoReporter" to_table="telecomCost" >
				<fk_column name="*" pk="subscriptionType" />
			</fk>
		</table>
		<table name="telecomCost.subscriptionType.name" >
			<column name="*" type="array" jt="4999545" mandatory="y" />
			<column name="languageId" type="integer" jt="4" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<fk name="Fk_telecomCost.subscriptionType.name_telecomCost.subscriptionType" virtual="y" to_schema="digoReporter" to_table="telecomCost.subscriptionType" >
				<fk_column name="*" pk="name" />
			</fk>
		</table>
		<table name="template" prior="Collection_0" >
			<column name="_id" prior="*" type="oid" jt="-8" mandatory="y" />
			<column name="type" type="object" jt="4999544" />
			<column name="name" type="string" jt="12" />
			<column name="file" prior="files" type="string" jt="12" />
			<column name="deploymentId" type="oid" jt="-8" />
			<column name="updatedDate" type="date" jt="91" />
			<column name="createdDate" type="date" jt="91" />
		</table>
		<table name="template.file" prior="Collection_0" >
			<column name="*" type="object" jt="4999544" mandatory="y" />
			<column name="filename" type="string" jt="12" />
			<column name="size" type="long" jt="-1" />
			<column name="path" type="string" jt="12" >
				<comment><![CDATA[deploymenId/yyyy/mm/dd/objectId]]></comment>
			</column>
			<index name="Idx_template.file" unique="UNIQUE_KEY" >
				<column name="*" />
			</index>
			<fk name="Fk_template.file_template" virtual="y" to_schema="digoReporter" to_table="template" >
				<fk_column name="*" pk="file" />
			</fk>
		</table>
		<table name="template.type" prior="Collection_0" >
			<column name="*" type="object" jt="4999544" mandatory="y" />
			<column name="id" type="oid" jt="-8" />
			<column name="code" type="string" jt="12" />
			<column name="name" type="string" jt="12" />
			<fk name="Fk_template.type_template" virtual="y" to_schema="digoReporter" to_table="template" >
				<fk_column name="*" pk="type" />
			</fk>
		</table>
		<table name="waterBill" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="billCode" type="string" jt="12" />
			<column name="userId" type="oid" jt="-8" />
			<column name="customerCode" type="string" jt="12" mandatory="y" />
			<column name="meterNumber" type="string" jt="12" mandatory="y" />
			<column name="year" type="integer" jt="4" mandatory="y" />
			<column name="month" type="integer" jt="4" mandatory="y" />
			<column name="startDate" type="date" jt="91" mandatory="y" />
			<column name="endDate" type="date" jt="91" mandatory="y" />
			<column name="oldIndex" type="integer" jt="4" mandatory="y" />
			<column name="newIndex" type="integer" jt="4" mandatory="y" />
			<column name="consumedAmount" type="integer" jt="4" mandatory="y" />
			<column name="paymentAmount" type="double" jt="8" mandatory="y" />
			<column name="paid" type="boolean" jt="16" mandatory="y" />
			<index name="Idx_waterBill_0" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="waterCustomer" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="userId" type="oid" jt="-8" mandatory="y" />
			<column name="customerCode" type="string" jt="12" mandatory="y" />
			<column name="updatedDate" type="date" jt="91" mandatory="y" />
			<index name="Idx_waterCustomer" unique="UNIQUE_KEY" >
				<column name="userId" />
				<column name="customerCode" />
			</index>
		</table>
		<table name="workflow" >
			<column name="_id" type="oid" jt="-8" mandatory="y" />
			<column name="originId" type="oid" jt="-8" mandatory="y" />
			<column name="name" type="string" jt="12" mandatory="y" />
			<column name="procedureId" type="oid" jt="-8" mandatory="y" />
			<column name="agencyId" type="oid" jt="-8" mandatory="y" />
			<index name="Idx_detail" unique="UNIQUE_KEY" >
				<column name="_id" />
			</index>
		</table>
		<view name="petitionStatistics" >
			<view_script><![CDATA[CREATE VIEW ${name} AS SELECT ... FROM ...]]></view_script>
		</view>
	</schema>
	<layout name="Default Layout" id="Layout_31f5713" joined_routing="y" show_column_type="y" show_relation="name" >
		<entity schema="digoReporter" name="agency" color="C1D8EE" x="224" y="96" />
		<entity schema="digoReporter" name="agency.dossier" color="C1D8EE" x="224" y="384" />
		<entity schema="digoReporter" name="agency.dossier.month" color="C1D8EE" x="544" y="656" />
		<entity schema="digoReporter" name="agency.dossier.month.m1" color="C1D8EE" x="288" y="544" />
		<entity schema="digoReporter" name="agency.level" color="3986C1" x="80" y="80" />
		<entity schema="digoReporter" name="agency.name" color="C1D8EE" x="448" y="80" />
		<entity schema="digoReporter" name="agency.procedure" color="C1D8EE" x="560" y="400" />
		<entity schema="digoReporter" name="agency.sector" color="C1D8EE" x="624" y="192" />
		<entity schema="digoReporter" name="agency.sector.transSector" color="C1D8EE" x="768" y="80" />
		<entity schema="digoReporter" name="agency.tag" color="3986C1" x="512" y="320" />
		<entity schema="digoReporter" name="agency.tag.name" color="3986C1" x="784" y="336" />
		<entity schema="digoReporter" name="dossierByDay" color="C1D8EE" x="1072" y="656" />
		<entity schema="digoReporter" name="dossierByDay.agency" color="C1D8EE" x="1376" y="592" />
		<entity schema="digoReporter" name="dossierByDay.agency.name" color="3986C1" x="1792" y="512" />
		<entity schema="digoReporter" name="dossierByDay.agencyLevel" color="3986C1" x="1328" y="688" />
		<entity schema="digoReporter" name="dossierByDay.procedure" color="3986C1" x="1376" y="848" />
		<entity schema="digoReporter" name="dossierByDay.procedure.translate" color="C1D8EE" x="1600" y="896" />
		<entity schema="digoReporter" name="dossierByDay.procedureLevel" color="3986C1" x="1616" y="768" />
		<entity schema="digoReporter" name="dossierByDay.procedureLevel.name" color="3986C1" x="1872" y="768" />
		<entity schema="digoReporter" name="dossierByDay.sector" color="C1D8EE" x="1552" y="640" />
		<entity schema="digoReporter" name="dossierByDay.sector.name" color="3986C1" x="1792" y="640" />
		<entity schema="digoReporter" name="electricBill" color="CCFFCC" x="256" y="1024" />
		<entity schema="digoReporter" name="electricCustomer" color="CCFFCC" x="496" y="1040" />
		<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics.field" color="3986C1" x="1680" y="1664" />
		<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics" color="3986C1" x="1424" y="1696" />
		<entity schema="digoReporter" name="petitionStatistics" color="C1D8EE" x="1072" y="1440" />
		<entity schema="digoReporter" name="procedure" color="C1D8EE" x="1104" y="464" />
		<entity schema="digoReporter" name="procedure.agency" color="3986C1" x="1216" y="288" />
		<entity schema="digoReporter" name="procedure.implementer" color="3986C1" x="1424" y="336" />
		<entity schema="digoReporter" name="procedure.sector" color="3986C1" x="1216" y="384" />
		<entity schema="digoReporter" name="procedure.translate" color="3986C1" x="1392" y="240" />
		<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics.receptionMethod" color="3986C1" x="1680" y="1440" />
		<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics" color="3986C1" x="1392" y="1488" />
		<entity schema="digoReporter" name="sector" color="C1D8EE" x="80" y="384" />
		<entity schema="digoReporter" name="sector.translate" color="C1D8EE" x="80" y="512" />
		<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics.statistics" color="3986C1" x="1680" y="1552" />
		<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics.statistics" color="3986C1" x="1680" y="1792" />
		<entity schema="digoReporter" name="petitionStatistics" column="statusStatistics.statistics" color="3986C1" x="1664" y="1328" />
		<entity schema="digoReporter" name="petitionStatistics" column="statusStatistics.status" color="3986C1" x="1648" y="1216" />
		<entity schema="digoReporter" name="petitionStatistics" column="statusStatistics" color="3986C1" x="1424" y="1248" />
		<entity schema="digoReporter" name="subscriptionType" color="FF6666" x="336" y="912" />
		<entity schema="digoReporter" name="subscriptionType.name" color="FF6666" x="544" y="912" />
		<entity schema="digoReporter" name="telecomCost" color="FF6666" x="80" y="784" />
		<entity schema="digoReporter" name="telecomCost.subscriptionType" color="FF6666" x="304" y="784" />
		<entity schema="digoReporter" name="telecomCost.subscriptionType.name" color="FF6666" x="544" y="784" />
		<entity schema="digoReporter" name="template" color="C1D8EE" x="496" y="1360" />
		<entity schema="digoReporter" name="template.file" color="C1D8EE" x="752" y="1472" />
		<entity schema="digoReporter" name="template.type" color="C1D8EE" x="752" y="1280" />
		<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics.field.trans" color="3986C1" x="1888" y="1680" />
		<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics.receptionMethod.trans" color="3986C1" x="1888" y="1456" />
		<entity schema="digoReporter" name="waterBill" color="CCFFCC" x="704" y="912" />
		<entity schema="digoReporter" name="waterCustomer" color="CCFFCC" x="48" y="1024" />
		<entity schema="digoReporter" name="workflow" color="C1D8EE" x="80" y="624" />
		<group name="template" color="C4E0F9" >
			<entity schema="digoReporter" name="template" />
			<entity schema="digoReporter" name="template.type" />
			<entity schema="digoReporter" name="template.file" />
		</group>
		<group name="dossierByDay" color="C4E0F9" >
			<entity schema="digoReporter" name="dossierByDay" />
			<entity schema="digoReporter" name="dossierByDay.agency" />
			<entity schema="digoReporter" name="procedure" />
			<entity schema="digoReporter" name="dossierByDay.agencyLevel" />
			<entity schema="digoReporter" name="dossierByDay.sector" />
			<entity schema="digoReporter" name="dossierByDay.procedureLevel" />
			<entity schema="digoReporter" name="dossierByDay.procedure.translate" />
			<entity schema="digoReporter" name="dossierByDay.procedure" />
			<entity schema="digoReporter" name="dossierByDay.procedureLevel.name" />
			<entity schema="digoReporter" name="dossierByDay.sector.name" />
			<entity schema="digoReporter" name="dossierByDay.agency.name" />
			<entity schema="digoReporter" name="procedure.implementer" />
			<entity schema="digoReporter" name="procedure.translate" />
			<entity schema="digoReporter" name="procedure.agency" />
			<entity schema="digoReporter" name="procedure.sector" />
		</group>
		<group name="petitionStatistics" color="C4E0F9" >
			<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics.field" />
			<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics" />
			<entity schema="digoReporter" name="petitionStatistics" />
			<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics.receptionMethod" />
			<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics" />
			<entity schema="digoReporter" name="petitionStatistics" column="statusStatistics.statistics" />
			<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics.statistics" />
			<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics.statistics" />
			<entity schema="digoReporter" name="petitionStatistics" column="statusStatistics.status" />
			<entity schema="digoReporter" name="petitionStatistics" column="statusStatistics" />
			<entity schema="digoReporter" name="petitionStatistics" column="receptionMethodStatistics.receptionMethod.trans" />
			<entity schema="digoReporter" name="petitionStatistics" column="fieldStatistics.field.trans" />
		</group>
	</layout>
</project>