package vn.vnpt.digo.reporter.service;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.minio.MinioClient;
import io.minio.PutObjectOptions;
import io.minio.errors.*;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.Cacheable;
import vn.vnpt.digo.reporter.dto.Minio.ParamDto;
import vn.vnpt.digo.reporter.dto.Minio.UploadResponseDto;
import vn.vnpt.digo.reporter.dto.MinioAccessDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

@Service
public class MinioService {

    @Autowired
    private ConfigService configService;
//
//    @Value("${digo.checksum}")
//    private boolean validChecksum;
//

//    @Autowired
//    private SignatureHistoryService signatureHistoryService;

//    @Autowired
//    private FileService fileService;
//
//    @Autowired
//    private FileCacheService fileCacheService;

//    @Autowired
//    private FileRepository fileRepository;

//    @Value("${vnpt.permission.client.whilelist}")
//    private String clientWhilelist;

    @Value("${upload-path}")
    private String uploadPath;


    private Logger logger = LoggerFactory.getLogger(MinioService.class);

    private S3ObjectInputStream downloadS3Stream(MinioAccessDto minioAccess){
        S3Object obj = null;
        try{
            String bucketName = minioAccess.getBucketName();
            String filePath = minioAccess.getFilePath();
            AmazonS3 s3 = this.getS3(minioAccess.getServiceUrl(),minioAccess.getAccessKey(),minioAccess.getSecretKey());
            obj = s3.getObject(bucketName, filePath);
            S3ObjectInputStream stream = obj.getObjectContent();
            return stream;
        }catch (Exception ex){
            return null;
        }
    }

    public Resource downloadResource(MinioAccessDto minioAccess){
        try{
            S3ObjectInputStream s3stream = downloadS3Stream(minioAccess);
            Resource resource = new InputStreamResource(s3stream);
            return  resource;
        }catch (Exception ex){
            return null;
        }
    }

    public byte[] downloadBytes(MinioAccessDto minioAccess) {
        try {
            // Call the method to get the S3 stream
            S3ObjectInputStream s3stream = downloadS3Stream(minioAccess);

            // Read all bytes from the stream
            return StreamUtils.copyToByteArray(s3stream);

        } catch (Exception ex) {
            // Log the error if needed
            ex.printStackTrace();
            return null;
        }
    }

    public InputStream downloadInputStream(MinioAccessDto minioAccess) {
        try {
            S3ObjectInputStream s3stream = downloadS3Stream(minioAccess);
            return s3stream;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Cacheable("getS3")
    public AmazonS3 getS3(String s3Url, String accessKey, String secretKey){
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(Protocol.HTTP);
        AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
        AmazonS3 s3Client = AmazonS3ClientBuilder
                .standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(
                        s3Url,
                        Regions.DEFAULT_REGION.getName()))
                .withPathStyleAccessEnabled(true)
                .withClientConfiguration(clientConfig)
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .build();
        return s3Client;
    }

    private MinioAccessDto getMinioAccess() throws JsonProcessingException {
        List<ParamDto> params = configService.getParams(new ObjectId("5f7c16069abb62f511890036"));
        if(Objects.isNull(params)){
            throw new DigoHttpException(11004, HttpServletResponse.SC_BAD_REQUEST);
        }

        String s3Url = this.configService.getParam(params,"minioURL");
        String accessKey = this.configService.getParam(params,"minioAccessKey");
        String secretKey = this.configService.getParam(params,"minioSecretKey");
        String bucket = this.configService.getParam(params,"bucket");
        if(!(Objects.nonNull(bucket) && !bucket.isEmpty() && !bucket.isBlank())){
            throw new DigoHttpException(11016, HttpServletResponse.SC_BAD_REQUEST);
        }
        MinioAccessDto minioAccess = new MinioAccessDto();
        minioAccess.setServiceUrl(s3Url);
        minioAccess.setAccessKey(accessKey);
        minioAccess.setSecretKey(secretKey);
        minioAccess.setBucketName(bucket);
        return minioAccess;
    }

    public UploadResponseDto upload(byte[] fileBytes){
        try{
//            String hash = fileService.getHash(fileBytes);
            UploadResponseDto response = new UploadResponseDto();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            ObjectId minioFileId = new ObjectId();

            MinioAccessDto minioAccess = getMinioAccess();
            String s3Url = minioAccess.getServiceUrl();
            String accessKey = minioAccess.getAccessKey();
            String secretKey = minioAccess.getSecretKey();
            String bucketName = minioAccess.getBucketName();

            Path path = Paths.get(uploadPath + minioFileId.toString());
            if (Files.notExists(path.getParent())) {
                System.out.println("parent: "+path.getParent());
                Files.createDirectories(path.getParent());
            }
            logger.info("path upload: " + path);
            Files.copy(inputStream, path, StandardCopyOption.REPLACE_EXISTING);
            minioAccess.setFilePath(minioFileId.toString());

            MinioClient minioClient = this.getClient(s3Url, accessKey, secretKey, bucketName);

            try (InputStream fileInputStream = Files.newInputStream(path)){
                // Sử dụng phương thức putObject phù hợp
                PutObjectOptions options = new PutObjectOptions(fileInputStream.available(), -1);
                options.setContentType("application/octet-stream");

                // Gọi phương thức putObject
                minioClient.putObject(
                        bucketName,                      // Tên bucket
                        minioFileId.toString(),          // Tên đối tượng (hoặc tên tệp)
                        fileInputStream,                 // Dữ liệu (InputStream)
                        options                          // Thông tin thêm về đối tượng
                );
            }
            response.setMinioAccess(minioAccess);
//            response.setHash(hash);
//            List<SignatureHistoryDto> signs = signatureHistoryService.getHistories(fileBytes);
//            if(Objects.nonNull(signs)){
//                response.setSigns(signs);
//            }
            Files.deleteIfExists(path);
            return response;
        } catch (InvalidBucketNameException | NoSuchAlgorithmException | InsufficientDataException |
                ErrorResponseException | XmlParserException | InternalException | InvalidKeyException |
                IOException e) {
            logger.info("Lỗi khi tải lên tệp: " + e.getMessage());
            return null;
        } catch (Exception ex) {
            logger.info("Lỗi không xác định: " + ex.getMessage());
            return null;
        }
    }

    public UploadResponseDto upload(byte[] fileBytes,String _path){
        try{
//            String hash = fileService.getHash(fileBytes);
            UploadResponseDto response = new UploadResponseDto();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            ObjectId minioFileId = new ObjectId();

            MinioAccessDto minioAccess = getMinioAccess();
            String s3Url = minioAccess.getServiceUrl();
            String accessKey = minioAccess.getAccessKey();
            String secretKey = minioAccess.getSecretKey();
            String bucketName = minioAccess.getBucketName();

            Path path = Paths.get(uploadPath + minioFileId.toString());
            if (Files.notExists(path.getParent())) {
                System.out.println("parent: "+path.getParent());
                Files.createDirectories(path.getParent());
            }
            logger.info("path upload: " + path);
            Files.copy(inputStream, path, StandardCopyOption.REPLACE_EXISTING);
            minioAccess.setFilePath(_path);

            MinioClient minioClient = this.getClient(s3Url, accessKey, secretKey, bucketName);

            try (InputStream fileInputStream = Files.newInputStream(path)){
                // Sử dụng phương thức putObject phù hợp
                PutObjectOptions options = new PutObjectOptions(fileInputStream.available(), -1);
                options.setContentType("application/octet-stream");

                // Gọi phương thức putObject
                minioClient.putObject(
                        bucketName,                      // Tên bucket
                        _path,          // Tên đối tượng (hoặc tên tệp)
                        fileInputStream,                 // Dữ liệu (InputStream)
                        options                          // Thông tin thêm về đối tượng
                );
            }
            response.setMinioAccess(minioAccess);
//            response.setHash(hash);
            response.setPath(_path);
//            List<SignatureHistoryDto> signs = signatureHistoryService.getHistories(fileBytes);
//            if(Objects.nonNull(signs)){
//                response.setSigns(signs);
//            }
            Files.deleteIfExists(path);
            return response;
        } catch (InvalidBucketNameException | NoSuchAlgorithmException | InsufficientDataException |
                ErrorResponseException | XmlParserException | InternalException | InvalidKeyException |
                IOException e) {
            logger.info("Lỗi khi tải lên tệp: " + e.getMessage());
            return null;
        } catch (Exception ex) {
            logger.info("Lỗi không xác định: " + ex.getMessage());
            return null;
        }
    }

    public UploadResponseDto uploadWithInfo(byte[] fileBytes, MinioAccessDto minioAccess){
        try{
            UploadResponseDto response = new UploadResponseDto();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            ObjectId minioFileId = new ObjectId();

            String s3Url = minioAccess.getServiceUrl();
            String accessKey = minioAccess.getAccessKey();
            String secretKey = minioAccess.getSecretKey();
            String bucketName = minioAccess.getBucketName();

            Path path = Paths.get(uploadPath + minioFileId.toString());
            if (Files.notExists(path.getParent())) {
                System.out.println("parent: "+path.getParent());
                Files.createDirectories(path.getParent());
            }
            logger.info("path upload: " + path);
            Files.copy(inputStream, path, StandardCopyOption.REPLACE_EXISTING);
            minioAccess.setFilePath(minioFileId.toString());
            MinioClient minioClient = this.getClient(s3Url,accessKey,secretKey,bucketName);
            try (InputStream fileInputStream = Files.newInputStream(path)){
                // Sử dụng phương thức putObject phù hợp
                PutObjectOptions options = new PutObjectOptions(fileInputStream.available(), -1);
                options.setContentType("application/octet-stream");

                // Gọi phương thức putObject
                minioClient.putObject(
                        bucketName,                      // Tên bucket
                        minioFileId.toString(),          // Tên đối tượng (hoặc tên tệp)
                        fileInputStream,                 // Dữ liệu (InputStream)
                        options                          // Thông tin thêm về đối tượng
                );
            }
//            UploadObjectArgs args = UploadObjectArgs.builder().bucket(bucketName).object(minioFileId.toString())
//                    .filename(minioFileId.toString()).build();
//            minioClient.uploadObject(args);
            response.setMinioAccess(minioAccess);
            Files.deleteIfExists(path);
            return response;
        } catch (InvalidBucketNameException | NoSuchAlgorithmException | InsufficientDataException |
                ErrorResponseException | XmlParserException | InternalException | InvalidKeyException |
                IOException e) {
            logger.info("Lỗi khi tải lên tệp: " + e.getMessage());
            return null;
        } catch (Exception ex) {
            logger.info("Lỗi không xác định: " + ex.getMessage());
            return null;
        }
    }

    private UploadResponseDto uploadForSignVGCA(byte[] fileBytes, String minioURL, String minioAccessKey, String minioSecretKey, String bucketName){
        try{
//            String hash = fileService.getHash(fileBytes);
            UploadResponseDto response = new UploadResponseDto();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            ObjectId minioFileId = new ObjectId();

            MinioAccessDto minioAccess = getMinioAccessForSignVGCA(minioURL, minioAccessKey, minioSecretKey, bucketName);
            String s3Url = minioAccess.getServiceUrl();
            String accessKey = minioAccess.getAccessKey();
            String secretKey = minioAccess.getSecretKey();
            Path path = Paths.get(uploadPath + minioFileId.toString());
            if (Files.notExists(path.getParent())) {
                System.out.println("parent: "+path.getParent());
                Files.createDirectories(path.getParent());
            }
            logger.info("path upload: " + path);
            Files.copy(inputStream, path, StandardCopyOption.REPLACE_EXISTING);
            minioAccess.setFilePath(minioFileId.toString());
            MinioClient minioClient = this.getClient(s3Url,accessKey,secretKey, bucketName);
            try (InputStream fileInputStream = Files.newInputStream(path)){
                // Sử dụng phương thức putObject phù hợp
                PutObjectOptions options = new PutObjectOptions(fileInputStream.available(), -1);
                options.setContentType("application/octet-stream");

                // Gọi phương thức putObject
                minioClient.putObject(
                        bucketName,                      // Tên bucket
                        minioFileId.toString(),          // Tên đối tượng (hoặc tên tệp)
                        fileInputStream,                 // Dữ liệu (InputStream)
                        options                          // Thông tin thêm về đối tượng
                );
            }
            response.setMinioAccess(minioAccess);
//            response.setHash(hash);
//            List<SignatureHistoryDto> signs = signatureHistoryService.getHistories(fileBytes);
//            if(Objects.nonNull(signs)){
//                response.setSigns(signs);
//            }
            return response;
        } catch (InvalidBucketNameException | NoSuchAlgorithmException | InsufficientDataException |
                ErrorResponseException | XmlParserException | InternalException | InvalidKeyException |
                IOException e) {
            logger.info("Lỗi khi tải lên tệp: " + e.getMessage());
            return null;
        } catch (Exception ex) {
            logger.info("Lỗi không xác định: " + ex.getMessage());
            return null;
        }
    }

    public UploadResponseDto upload(MultipartFile file, String path){
        try{
            byte[] fileBytes = file.getBytes();
            UploadResponseDto response = upload(fileBytes,path);
            return response;
        }catch (Exception ex){
            logger.info("upload MultipartFile: "+ ex.getMessage());
            return null;
        }
    }

    public UploadResponseDto upload(String base64){
        byte[] fileBytes = Base64.getDecoder().decode(base64);
        UploadResponseDto response = upload(fileBytes);
        return response;
    }

    public UploadResponseDto uploadForSignVGCA(String base64,
                                               String minioURL,
                                               String minioAccessKey,
                                               String minioSecretKey,
                                               String bucketName){
        byte[] fileBytes = Base64.getDecoder().decode(base64);
        UploadResponseDto response = uploadForSignVGCA(fileBytes, minioURL, minioAccessKey, minioSecretKey,bucketName);
        return response;
    }

    @Cacheable("getClient")
    public MinioClient getClient(String s3Url, String accessKey, String secretKey, String bucketName){
        try{
            MinioClient minioClient = new MinioClient(s3Url, accessKey, secretKey);

            // Kiểm tra sự tồn tại của bucket bằng phương thức bucketExists
            boolean found = minioClient.bucketExists(bucketName);
            if (!found) {
                // Make a new bucket called 'asiatrip'.
                minioClient.makeBucket(bucketName);
            } else {
                System.out.println("Bucket 'asiatrip' already exists.");
            }
            return minioClient;
        } catch (InvalidEndpointException | InvalidPortException e) {
            System.out.println("Lỗi Endpoint/Port không hợp lệ: " + e.getMessage());
            return null;
        } catch (InvalidBucketNameException | NoSuchAlgorithmException | InsufficientDataException |
                ErrorResponseException | XmlParserException | InternalException | InvalidKeyException |
                RegionConflictException | IOException e) {
            System.out.println("Lỗi Minio: " + e.getMessage());
            return null;
        } catch (Exception ex) {
            System.out.println("Lỗi không xác định: " + ex.getMessage());
            return null;
        }
    }

    private MinioAccessDto getMinioAccessForSignVGCA(String minioURL, String minioAccessKey, String minioSecretKey, String bucket){
        MinioAccessDto minioAccess = new MinioAccessDto();
        minioAccess.setServiceUrl(minioURL);
        minioAccess.setAccessKey(minioAccessKey);
        minioAccess.setSecretKey(minioSecretKey);
        minioAccess.setBucketName(bucket);
        return minioAccess;
    }

    public void delete(MinioAccessDto minioAccess){
        String s3Url = minioAccess.getServiceUrl();
        String accessKey = minioAccess.getAccessKey();
        String secretKey = minioAccess.getSecretKey();
        String bucketName = minioAccess.getBucketName();
        String key = minioAccess.getFilePath();
        AmazonS3 s3 = this.getS3(s3Url,accessKey,secretKey);
        s3.deleteObject(bucketName,key);
    }
}
