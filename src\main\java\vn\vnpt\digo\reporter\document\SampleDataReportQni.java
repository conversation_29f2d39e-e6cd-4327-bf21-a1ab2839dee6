package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.dto.qni.SampleDataReportDto;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "sampleDataReportQni")
public class SampleDataReportQni implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    private SampleDataReportDto sampleData;

    private AgencyLevel agencyLevel;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgencyLevel implements Serializable {
        private String id;
        private String name;
    }
}