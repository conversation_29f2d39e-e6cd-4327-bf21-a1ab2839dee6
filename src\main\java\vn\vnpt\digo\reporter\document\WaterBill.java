package vn.vnpt.digo.reporter.document;

import java.util.Date;
import javax.validation.constraints.NotNull;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import vn.vnpt.digo.reporter.dto.WaterBillInputDto;

@Data
@NoArgsConstructor
@Document(collection = "waterBill")
@CompoundIndex(name = "unique_waterBill", def = "{'billCode': 1, 'userId': 1, 'customerCode': 1, 'year': 1, 'month': 1}", unique = true)
public class WaterBill {

    @Id
    @JsonSerialize
    private ObjectId id;
    private String billCode;
    private ObjectId userId;

    @NotNull
    private String customerCode;

    @NotNull
    private String meterNumber;
    
    @NotNull
    private int year;

    @NotNull
    private int month;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NotNull
    private Date startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NotNull
    private Date endDate;

    @NotNull
    private int oldIndex;

    @NotNull
    private int newIndex;

    @NotNull
    private int consumedAmount;

    @NotNull
    private float paymentAmount;

    @NotNull
    private boolean paid;

    public WaterBill(WaterBillInputDto waterBillInputDto) {
        this.billCode = waterBillInputDto.getBillCode();
        this.userId = waterBillInputDto.getUserId();
        this.customerCode = waterBillInputDto.getCustomerCode();
        this.meterNumber = waterBillInputDto.getMeterNumber();
        this.year = waterBillInputDto.getYear();
        this.month = waterBillInputDto.getMonth();
        this.startDate = waterBillInputDto.getStartDate();
        this.endDate = waterBillInputDto.getEndDate();
        this.oldIndex = waterBillInputDto.getOldIndex();
        this.newIndex = waterBillInputDto.getNewIndex();
        this.consumedAmount = waterBillInputDto.getConsumedAmount();
        this.paymentAmount = waterBillInputDto.getPaymentAmount();
        this.paid = waterBillInputDto.isPaid();
    }
}
