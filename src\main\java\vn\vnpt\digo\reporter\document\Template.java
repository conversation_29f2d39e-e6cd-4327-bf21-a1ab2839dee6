package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.annotation.FieldDescription;
import vn.vnpt.digo.reporter.dto.TemplateFileSignDto;
import vn.vnpt.digo.reporter.pojo.TemplateType;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.pojo.TemplateSubsystem;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "template")
public class Template {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    @FieldDescription(logKey = "lang.word.id")
    private ObjectId id;

    @FieldDescription(logKey = "lang.phrase.template-type")
    private TemplateType type;

    @NotNull
    @FieldDescription(logKey = "lang.word.name")
    private String name;

    @NotNull
    @FieldDescription(logKey = "lang.phrase.template-subsystem")
    private List<TemplateSubsystem> subsystem;

    @FieldDescription(logKey = "lang.phrase.list-variable")
    private Object listVariable;

    private String listVariableString;

    @FieldDescription(logKey = "lang.phrase.template-file")
    private TemplateFile file;

    private Integer signEnable;

    private List<TemplateFileSignDto> fileSign;
    
    private String code;
    
    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    @FieldDescription(logKey = "lang.phrase.deployment-id")
    private ObjectId deploymentId;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @FieldDescription(logKey = "lang.phrase.created-date")
    private Date createdDate;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @FieldDescription(logKey = "lang.phrase.updated-date")
    private Date updatedDate;

}
