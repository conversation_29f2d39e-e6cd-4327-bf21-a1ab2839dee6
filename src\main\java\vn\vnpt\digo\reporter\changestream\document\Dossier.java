package vn.vnpt.digo.reporter.changestream.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.changestream.pojo.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  This is the document used to read data from the dossier collection in the svc.padman database.
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dossier implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String code;

    private String codeOld;

    private String newCode;

    private String nationCode;
    
    private Procedure procedure;

    private ProcedureProcessDefinition procedureProcessDefinition;

    private ApplyMethod applyMethod;

    private DossierReceivingKind dossierReceivingKind;

    private DossierStatus dossierStatus;

    private DossierTaskStatus dossierTaskStatus = null;

    private IdName paymentMethod;


    private ApplicantEForm applicant;

    private Accepter accepter;

    private Agency agency;

    private String description;

    private Object receivingPlace;

    // 1: Chua den han
    // 2: Dung han
    // 3: Tre han
    // 4: Truoc han
    private Integer dossierProgress;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;                                  // ngay tiep nhan

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;                                   // ngay nop

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;                               // ngay hen tra

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;                                 // ngay co ket qua

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")     // ngay hien tai cua he thong
    private Date systemDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date dueDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date returnedDate;                                  // ngay tra ket qua

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date lastedAddreqDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date lastedImplDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date cancelledDate;

    private String receivingAddress;

    private ArrayList<Attachment> attachment;

    private ArrayList<AttachmentStorage> attachmentStorages;

    private ArrayList<AttachmentStorage> componentsStorages;

    private ArrayList<DossierTask> task;

    private ArrayList<DossierTask> currentTask;

    private List<DossierTask> previousTask;

    private BpmProcessDefinition bpmProcessDefinition;

    @Field("activitiProcessInstance")
    private Object activitiProcessInstance;

    @Field("eForm")
    @JsonProperty("eForm")
    private Object eForm;

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    @NotNull
    private int status;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;


    private AgencyLevel agencyLevel;

    private ProcedureLevel procedureLevel;

    private Object vilis;
    private Boolean procedureNationCodeSync;

    private int countExtendTime = 0;

    private int countPauses = 0;

    private ArrayList<Object> userExtendTime;

    private ArrayList<Object> userPauses;

    private ArrayList<Object> userRefuse;

    private String fptCode;

    private DossierTaskStatus dossierMenuTaskRemind;

    private DossierTaskStatus iOfficeRemind;

    private int undefindedCompleteTime = 0;

    private int isWithdraw = 0;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date withdrawDate;
    

    private Integer fromStoreNum = 0;
    private Integer toStorageNum = 0;
    private boolean degitalResult = false;
    private int digitizingStatus  = 0;// 0:chÆ°a Ä‘Æ°á»£c Ä‘á»“ng bá»™ Ä‘ang  1: Ä?Ã£ Ä‘Æ°á»£c Ä‘á»“ng bá»™ sang Kho chá»? sá»‘ hÃ³a 2: Ä?Ã£ xÃ¡c nháº­n hoÃ n thÃ nh sá»‘ hÃ³a
    private Integer saved2Storage; //1: LÆ°u sang kho

    //IGATESUPP-27792
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date waitUpdateDate; // Ngay yeu cau bo sung

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date waitDutyPayment; // Ngay yeu cau thuc hien nghia vu tai chinh

    private Boolean checkHasFileTPHS = false;

    
    private Boolean isDVCLT;

    private vn.vnpt.digo.reporter.changestream.pojo.Id logCSDLDC;
    

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDateAttachment;
    
    private int flatteningCount;

}
