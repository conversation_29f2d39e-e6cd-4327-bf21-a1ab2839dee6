package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.dto.DossierAgencyDto;
import vn.vnpt.digo.reporter.dto.DossierSectorDto;
import vn.vnpt.digo.reporter.pojo.DossierCountingData;
import vn.vnpt.digo.reporter.pojo.DossierSynthesisData;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "dossierSynthesis")
public class DossierSynthesis {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private Integer year;

    private Integer month;

    private Integer day;

    @JsonProperty("agency")
    private DossierAgencyDto agency;
    @JsonProperty("sector")
    private DossierSectorDto sector;

    private DossierSynthesisData synthesisData;

//  private ArrayList<DossierCountingDto> dossierDetail;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date synthesisDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private List<ObjectId> completedEarlyList = new ArrayList<>();
    private List<ObjectId> completedOntimeList = new ArrayList<>();
    private List<ObjectId> completedLatelyList = new ArrayList<>();
    private List<ObjectId> inprogressEarlyList = new ArrayList<>();
    private List<ObjectId> inprogressLatelyList = new ArrayList<>();
    private List<ObjectId> onlineReceivingList = new ArrayList<>();
    private List<ObjectId> directlyReceivingList = new ArrayList<>();
}
