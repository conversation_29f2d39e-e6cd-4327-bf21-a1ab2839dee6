/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "dossierByDay")
public class DossierByDay {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private Integer year;
    
    private Integer month;
    
    private Integer day;
    
    private AgencyDossierByDay agency;
    
    private AgencyDossierByDay agencyLevel;
    
    private SectorDossierByDay sector;
    
    private ProcedureLevelDossierByDay procedureLevel;
    
    private ProcedureDossierByDay procedure;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;
    
    private Integer appliedOnline;
    
    private Integer received;
    
    private Integer receivedOnline;
    
    private Integer receivedDirect;
    
    private Integer resolved;
    
    private Integer resolvedEarly;
    
    private Integer resolvedOverdue;
    
    private Integer unresolvedOverdue;
    
    private Integer cancelled;

    private Integer deleted;
    
    private Integer suspended;
    
    private Integer returnOnTime;
    
    private Integer returnOverdue;
    
    private Integer unresolved;
   
}
