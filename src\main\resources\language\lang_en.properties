label.id=ID
label.filename=File Name
label.path=File Path
label.size=Size {0}
label.updated-date=Updated Date
# DigoHttpException message
digo.http.response.error.10000={0} is required
digo.http.response.error.10002={0} is not found
digo.http.response.error.10003=Can not get {0}
digo.http.response.error.10004=Can not get data
digo.http.response.error.10005=Can not send data
digo.http.response.error.10006=Can not update data
digo.http.response.error.10007={0} has been exists
digo.http.response.error.10008=Can not add {0}
digo.http.response.error.10009=Can not update {0}
digo.http.response.error.10010=Can not delete {0}
digo.http.response.error.10011=The format of {0} must be {1}
digo.http.response.error.10012=Can not find {0} with id {1}
digo.http.response.error.10013=The entity is not found
digo.http.response.error.10014={0} is not successful
digo.http.response.error.11000=Can not find {0}
digo.http.response.error.10400=`{0}` invalid!
digo.http.response.error.11001=Can not find data
digo.http.response.error.11003=Error {0}
digo.http.response.error.10403=You do not have permission to do this action
digo.http.response.error.11400=The input date is out of range of Data
digo.http.response.error.11401=The tagAgencyId incorrect or empty!
vn.vnpt.digo.http.response.error.10400=Bad request
digo.http.response.error.12412=Request of content is not allowed.
digo.http.response.error.11004={0}
# others
digo.number=number
digo.word.agency=agency
lang.word.bill=bill
lang.word.agency=agency
#template
lang.word.template=m\u1eabu
#file
lang.word.file=file
lang.word.deploymentId=deploymentId
lang.word.procedure=Procedure
lang.word.report=report
lang.word.petition-statistics=petition statistics
#phrase
lang.phrase.petition-status.pending=Pending
lang.phrase.petition-status.in-progress=In progress
lang.phrase.petition-status.completed=Completed
lang.phrase.petition-status.canceled=Canceled
lang.phrase.petition-status.total=Total

lang.word.form-province-6a.form-no=Form No. II.06a / VPCP / KSTT
lang.word.form-province-6a.title=SITUATION AND RESULTS OF ADMINISTRATIVE PROCEDURES SETTLEMENT AT AGENCIES, UNITS DIRECTLY SETTLEING ADMINISTRATIVE PROCEDURES
lang.word.form-province-6a.reporting-unit=Reporting unit: {0}
lang.word.form-province-6a.unit-receiving-report=Unit receiving report: {0}
lang.word.form-province-6a.unit-number-of-dossiers=Unit: Number of dossiers {0}
lang.word.form-province-6a.reporting-period=Reporting period {0}
lang.word.form-province-6b.form-no=Form No. II.06b / VPCP / KSTT
lang.word.form-province-6b.title=SUMMARY OF SITUATION, RESOLUTION RESULTS ADMINISTRATIVE PROCEDURES OF DISTRICT LEVEL People's Committees
lang.word.form-province-6b.sub-title-agency-level=The situation and results of administrative settlement settlement fall within the competence of the People's Committee
lang.word.form-province-6c.title=SUMMARY OF SITUATION, RESOLUTION RESULTS ADMINISTRATIVE PROCEDURES OF PROVINCE LEVEL People's Committees
lang.word.form-province-6c.form-no=Form No. II.06c / VPCP / KSTT
lang.word.form-province-6c.tthc-province=The situation and results of handling administrative procedures fall within the competence of the People's Committee of the province
lang.word.form-province-6c.tthc-district=The situation and results of administrative settlement settlement fall under the jurisdiction of the People's Committee of the district
lang.word.form-province-6c.tthc-commune=Summary of situation and results of administrative settlement settlement within the competence of commune-level People's Committees
lang.word.agency-level-commune=commune
lang.word.agency-level-district=district
lang.word.agency-level-province=province
lang.word.form-province-6c.tthc=TTHC due to
lang.word.form-province-6c.tthc-receive=receive, deal with TTHC
lang.word.no=NO
lang.word.field-of-settlement=Field of settlement
lang.word.number-of-applications-received=Number of applications received
lang.word.in-the-period=In the period
lang.word.total=Total
lang.word.online=Online
lang.word.direct=Direct
lang.word.postal-services=Postal services
lang.word.from-the-previous-period=From the previous period
lang.word.number-of-cases-resolved=Number of cases resolved
lang.word.by-dead-time=By dead time
lang.word.on-time=On time
lang.word.out-of-date=Out of date
lang.word.in-due-date=In due date
lang.word.number-of-cases-being-processed=Number of cases being processed
lang.word.year=Year {0}
lang.word.fromDayToDay=(From {0} to {1})
lang.word.day=day {0} month {1} year {2}
lang.word.data=data

#KTM REPORTER
lang.word.ktm.national-brand =Government of the Socialist Republic of Viet Nam
lang.word.ktm.sub-national =Independence ? Liberty ? Happiness
lang.word.ktm.title = Reporter of dossier reception and processing
lang.word.ktm.current-date = Creation date: {0}
lang.word.ktm.sub-title = (From: {0} To: {1})
lang.word.ktm.no=NO
lang.word.ktm.agency=Agency
lang.word.ktm.sector=Sector
lang.word.ktm.procedure=Procedure
lang.word.ktm.dossier-code=Dossier code
lang.word.ktm.status=Status
lang.word.ktm.address=Address
lang.word.ktm.phone=Phone
lang.word.ktm.owner=Owner
lang.word.ktm.submitter=Submitter
lang.word.ktm.accepted-date=Accepted date
lang.word.ktm.appointment-date=Appointment date
lang.word.ktm.duration=Duration
lang.word.ktm.completed-date=Completed date
lang.word.ktm.returned-Date=Returned date
lang.word.ktm.dossier-type=Dossier type
lang.word.ktm.dossier-status=Dossier status
lang.word.ktm.implementer= Implementer
lang.word.ktm.note=Note
lang.word.ktm.late-at=Late reason
lang.word.ktm.creator= Creator
lang.word.ktm.reporter= Reporter

#QNI
lang.word.gov=THE SOCIALIST REPUBLIC OF VIET NAM
lang.word.ifh=Independence \u00ef\u00bf\u00bd Freedom - Happiness
lang.word.dossier-statistic-title=REPORT ON THE SITUATION OF RECEIVING AND SETTLEMENT OF GOING DOCUMENTS
lang.word.dossier-statistic-fromdate-todate=(From {0} to {1})
lang.word.dossier-statistic-current-date=Statistics at {0}
lang.word.dossier-statistic-dossier-code=Dossier code
lang.word.dossier-statistic-procedure-name=Procedure name
lang.word.dossier-statistic-sector-name=Sector name
lang.word.dossier-statistic-noidungyeucaugiaiquyet=Content to be resolved
lang.word.dossier-statistic-applied-date=Applied date
lang.word.dossier-statistic-accepted-date=Accepted date
lang.word.dossier-statistic-appointment-date=Appointment date
lang.word.dossier-statistic-completed-date=Completed date
lang.word.dossier-statistic-applicant-ownerfullname=Owner full name
lang.word.dossier-statistic-applicant-phonenumber=Phone number
lang.word.dossier-statistic-assignee-fullname=Officer is handling
lang.word.dossier-statistic-status-name=Dossier status
lang.word.dossier-statistic-agency-name=Agency name
lang.word.dossier-statistic-applied-due=Applied date
lang.word.dossier-statistic-due-date=days
lang.word.dossier-statistic-applied-method=Apply method
lang.word.dossier-statistic-writer=WRITER
lang.word.dossier-statistic-reporter=REPORT
digo.import-error.1000=Error at line
digo.import-error.1003=Code is invalid
digo.import-success.1000=Successfully imported {0} lines
