package vn.vnpt.digo.reporter.util;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.SsoToken;

import java.util.Date;
import java.util.Objects;
@Component
public class Oauth2RestTemplate{
    @Value(value = "${digo.microservice.auth.client-id}")
    private String clientId;

    @Value(value = "${digo.microservice.auth.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    @Autowired
    MongoTemplate mongoTemplate;
    
    private static SsoToken ssoTokenContext;
    
     public OAuth2RestTemplate getTokenNew() {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }
    private String createToken() {
        ssoTokenContext = new SsoToken();
        OAuth2AccessToken oAuth2AccessToken = getTokenNew().getAccessToken();
        ssoTokenContext.setExpiration(oAuth2AccessToken.getExpiration());
        ssoTokenContext.setClientId(this.clientId);
        ssoTokenContext.setAccessToken(oAuth2AccessToken.getValue());
        ssoTokenContext.setExpiresIn(oAuth2AccessToken.getExpiresIn());
        ssoTokenContext.setCreatedDate(new Date());
        mongoTemplate.save(ssoTokenContext);
        return ssoTokenContext.getAccessToken();
    }
    private SsoToken getTokenFromDB() {
        Query query = new Query();
        query.addCriteria(Criteria.where("clientId").is(clientId));
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return mongoTemplate.findOne(query, SsoToken.class);
    }
    public Boolean checkExpiredToken(SsoToken ssoToken){
        try{
            SignedJWT signedJWT = SignedJWT.parse(ssoToken.getAccessToken());
            JWTClaimsSet jwtClaims = signedJWT.getJWTClaimsSet();
            Date expirationTime = jwtClaims.getExpirationTime();
            return new Date().compareTo(expirationTime) >= 0;
        }catch (Exception e){
            return true;
        }
    }
    public String getToken(Boolean isNewToken){
        if(isNewToken){
            return getTokenNew().getAccessToken().getValue();
        }
        if (Objects.isNull(ssoTokenContext)){
            SsoToken ssoTokenDB = getTokenFromDB();
            if (Objects.isNull(ssoTokenDB)){
                return createToken();
            }else if (checkExpiredToken(ssoTokenDB)){
                return createToken();
            }else {
                ssoTokenContext = ssoTokenDB;
                return ssoTokenContext.getAccessToken();
            }
        }else if (checkExpiredToken(ssoTokenContext)) {
            SsoToken ssoTokenDB = getTokenFromDB();
            if (checkExpiredToken(ssoTokenDB)) {
                return createToken();
            } else {
                ssoTokenContext = ssoTokenDB;
                return ssoTokenDB.getAccessToken();
            }
        }else {
            return ssoTokenContext.getAccessToken();
        }
    }
}
