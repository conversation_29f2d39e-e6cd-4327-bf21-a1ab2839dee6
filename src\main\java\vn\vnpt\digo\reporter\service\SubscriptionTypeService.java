/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.dto.SubscriptionTypeDto;
import vn.vnpt.digo.reporter.repository.SubscriptionTypeRepository;
import vn.vnpt.digo.reporter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class SubscriptionTypeService {

    @Autowired
    private SubscriptionTypeRepository subscriptionTypeRepository;

    @Autowired
    private Translator translator;

    public List<SubscriptionTypeDto> getListSubscriptionType() {

        List<SubscriptionTypeDto> subscriptionTypeDto = subscriptionTypeRepository.getListSubscriptionType(translator.getCurrentLocaleId());
        subscriptionTypeDto.forEach((item) -> {
            item.setName(translator.getCurrentLocaleId());
        });
        return subscriptionTypeDto;
    }
}
