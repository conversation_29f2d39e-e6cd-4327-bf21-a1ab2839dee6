package vn.vnpt.digo.reporter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.document.Reporter;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.service.ReportService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/report")
@IcodeAuthorize("vnpt.permission.reporter")
public class ReporterController {

    @Autowired
    private ReportService reportService;

    Logger logger = LoggerFactory.getLogger(AgencyController.class);

    @PostMapping("/--post-report")
    public AffectedRowsDto addReport(HttpServletRequest request,
                                     @RequestBody PostReportDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRowsDto = reportService.postReport(body);
        return affectedRowsDto;
    }

    // Tạo entry báo cáo thống kê, hiển thị trạng thái Đang tạo
    @PostMapping("/--post-report-processing")
    public String addReportProcessing(HttpServletRequest request,
                                     @RequestBody PostReportDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return reportService.postReportProcessing(body);
    }

    // Cập nhật file, trạng thái tạo báo cáo Tạo thành công
    @PutMapping("--update-report-file/{id}")
    public void updateReportFile(HttpServletRequest request,
                                 @PathVariable(value = "id", required = true) ObjectId id,
                                 @RequestBody PostReportDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        reportService.updateReportFile(id, body);
    }

    // Lỗi trong quá trình tạo báo cáo
    @PutMapping("--update-report-error/{id}")
    public void updateReportError(HttpServletRequest request,
                                 @PathVariable(value = "id", required = true) ObjectId id,
                                 @RequestBody PostReportDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        reportService.updateReportError(id);
    }

    @PutMapping("/{id}/--update-name-report")
    public AffectedRowsDto updateReport(HttpServletRequest request,
                                        @PathVariable(value = "id", required = true) ObjectId id,
                                     @RequestBody PostReportDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRowsDto = reportService.putReportName(id,body);
        return affectedRowsDto;
    }

    @DeleteMapping("/{id}")
    //@IcodeAuthorize(permission = "manageDossierReceipt")
    public AffectedRowsDto deleteDossierReceiptById(
            HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto deleteReportById = reportService.deleteReport(id);
        logger.info(deleteReportById.getAffectedRows() + "");
        return deleteReportById;
    }


    @GetMapping("/--report")
    public Slice<GetReporterDto> getFrequentList(HttpServletRequest request,
                                                 @RequestParam(value = "key-word", required = false, defaultValue = "") String keyword,
                                                 @RequestParam(value = "user-agency", required = false) String userAgency,
                                                 @RequestParam(value = "reportType", required = false) String reportType,
                                                 Pageable pageable
                                              ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<GetReporterDto> listReport = reportService.getReportByKeyword(keyword, userAgency, reportType, pageable);

        return listReport;
    }

    @GetMapping("/--report-exist/{id}")
    public String getFrequentList(HttpServletRequest request,
                                  @PathVariable(value = "id", required = true) ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return reportService.checkReportExist(id);
    }
}
