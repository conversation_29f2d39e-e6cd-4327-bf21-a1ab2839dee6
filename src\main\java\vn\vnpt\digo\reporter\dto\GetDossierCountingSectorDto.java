package vn.vnpt.digo.reporter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.reporter.pojo.DossierCountingData;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetDossierCountingSectorDto extends GetDossierCountingDto {
    public GetDossierCountingSectorDto(DossierCountingData countingData, DossierAgencyDto agency, long previousPeriod, DossierSectorDto sector) {
        super(countingData, agency, previousPeriod);
        this.sector = sector;
    }
    private DossierSectorDto sector;
}
