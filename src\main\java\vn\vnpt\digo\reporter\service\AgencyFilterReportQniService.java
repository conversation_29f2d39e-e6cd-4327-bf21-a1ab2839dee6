package vn.vnpt.digo.reporter.service;

import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import vn.vnpt.digo.reporter.document.AgencyFilterReportQni;
import vn.vnpt.digo.reporter.dto.qni.AgencyFilterReportQniResponse;
import vn.vnpt.digo.reporter.dto.qni.ImportResponseDto;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Service
public class AgencyFilterReportQniService {

    @Autowired
    private MongoTemplate mongoTemplate;

    Logger logger = LoggerFactory.getLogger(AgencyFilterReportQniService.class);

    // Pattern for MongoDB ObjectId validation (24 hex characters)
    private static final Pattern OBJECT_ID_PATTERN = Pattern.compile("^[0-9a-fA-F]{24}$");

    public AgencyFilterReportQniResponse getDocumentsByAgencyId(String idAgency) {
        Query query = new Query(Criteria.where("idAgency").is(idAgency));
        AgencyFilterReportQni document = mongoTemplate.findOne(query, AgencyFilterReportQni.class);

        if (document != null) {
            return new AgencyFilterReportQniResponse(document);
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Document with ID " + idAgency + " not found");
        }
    }

    public List<AgencyFilterReportQniResponse> getAllDocuments() {
        var agencyFilterDBList = mongoTemplate.findAll(AgencyFilterReportQni.class);

        if (!agencyFilterDBList.isEmpty()) {
            return agencyFilterDBList.stream()
                    .map(AgencyFilterReportQniResponse::new)
                    .collect(Collectors.toList());
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No documents found");
        }
    }

    public AgencyFilterReportQniResponse getDocumentById(String id) {
        var agencyFilterDB = mongoTemplate.findById(id, AgencyFilterReportQni.class);

        if (agencyFilterDB != null) {
            return new AgencyFilterReportQniResponse(agencyFilterDB);
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Document with ID " + id + " not found");
        }
    }

    public AgencyFilterReportQni addDocument(AgencyFilterReportQni document) {
        return mongoTemplate.save(document);
    }

    public AgencyFilterReportQni updateDocument(String id, AgencyFilterReportQni updatedDocument) {
        updatedDocument.set_id(new ObjectId(id));
        return mongoTemplate.save(updatedDocument);
    }

    public long deleteDocument(String id) {
        Query query = Query.query(Criteria.where("_id").is(new ObjectId(id)));
        DeleteResult deleteResult = mongoTemplate.remove(query, AgencyFilterReportQni.class);

        return deleteResult.getDeletedCount();
    }

    public ImportResponseDto checkAgencyNotFilter(List<String> agencyIds){
        StringBuilder agencyStringBuilder = new StringBuilder();
        ImportResponseDto responseDto = new ImportResponseDto();
        List<String> errorMessages = new ArrayList<>();

        for (String agencyTemp : agencyIds) {
            try {
                var target = getDocumentsByAgencyId(agencyTemp);
                if (Objects.isNull(target))
                    continue;
                agencyStringBuilder.append(target.getIdFilter()).append(",");
            } catch (Exception ex) {
                errorMessages.add(ex.getMessage());
            }
        }

        responseDto.setErrorMessages(errorMessages);
        return responseDto;
    }

    public ImportResponseDto importFromExcel(List<AgencyFilterReportQni> documents) {
        ImportResponseDto responseDto = new ImportResponseDto();
        List<String> errorMessages = new ArrayList<>();
        List<AgencyFilterReportQni> successfulImports = new ArrayList<>();
        int totalRecords = documents.size();
        int successCount = 0;
        int errorCount = 0;

        for (int i = 0; i < documents.size(); i++) {
            int excelRowNumber = i + 2; // +2 because Excel is 1-based and we skip header row
            try {
                AgencyFilterReportQni document = documents.get(i);
                
                // Skip completely empty rows (all fields empty)
                if ((document.getAgencyName() == null || document.getAgencyName().trim().isEmpty()) &&
                    (document.getIdAgency() == null || document.getIdAgency().trim().isEmpty()) &&
                    (document.getIdFilter() == null || document.getIdFilter().trim().isEmpty())) {
                    continue; // Skip empty rows silently
                }
                
                // Validate required fields
                if (document.getAgencyName() == null || document.getAgencyName().trim().isEmpty()) {
                    errorMessages.add("Row " + excelRowNumber + ": Agency name is required");
                    errorCount++;
                    continue;
                }
                
                if (document.getIdAgency() == null || document.getIdAgency().trim().isEmpty()) {
                    errorMessages.add("Row " + excelRowNumber + ": Agency ID is required");
                    errorCount++;
                    continue;
                }

                // Validate idAgency format
                if (!isValidObjectId(document.getIdAgency().trim())) {
                    errorMessages.add("Row " + excelRowNumber + ": Invalid Agency ID format. Must be a valid ObjectId (24 hex characters)");
                    errorCount++;
                    continue;
                }

                // Validate idFilter format if provided
                if (document.getIdFilter() != null && !document.getIdFilter().trim().isEmpty()) {
                    String validationError = validateObjectIdString(document.getIdFilter().trim());
                    if (validationError != null) {
                        errorMessages.add("Row " + excelRowNumber + ": " + validationError);
                        errorCount++;
                        continue;
                    }
                }

                // Check if agency already exists
                Query query = new Query(Criteria.where("idAgency").is(document.getIdAgency()));
                AgencyFilterReportQni existingDocument = mongoTemplate.findOne(query, AgencyFilterReportQni.class);
                
                if (existingDocument != null) {
                    // Update existing document
                    existingDocument.setAgencyName(document.getAgencyName());
                    existingDocument.setIdFilter(document.getIdFilter());
                    mongoTemplate.save(existingDocument);
                    successfulImports.add(existingDocument);
                } else {
                    // Create new document
                    AgencyFilterReportQni savedDocument = mongoTemplate.save(document);
                    successfulImports.add(savedDocument);
                }
                
                successCount++;
                logger.info("Successfully imported row {}: {}", excelRowNumber, document.getAgencyName());
                
            } catch (Exception ex) {
                errorMessages.add("Row " + excelRowNumber + ": " + ex.getMessage());
                errorCount++;
                logger.error("Error importing row {}: {}", excelRowNumber, ex.getMessage());
            }
        }

        responseDto.setErrorMessages(errorMessages);
        responseDto.setSuccessRows(successCount);
        responseDto.setSuccessMessage("Import completed successfully. Total: " + totalRecords + ", Success: " + successCount + ", Errors: " + errorCount);
        responseDto.setInsertedRows(String.valueOf(successCount));
        
        return responseDto;
    }

    /**
     * Validate ObjectId string format
     * @param objectId String to validate
     * @return true if valid ObjectId format
     */
    private boolean isValidObjectId(String objectId) {
        return objectId != null && OBJECT_ID_PATTERN.matcher(objectId).matches();
    }

    /**
     * Validate ObjectId string that may contain multiple ObjectIds separated by commas
     * @param objectIdString String containing comma-separated ObjectIds
     * @return null if valid, error message if invalid
     */
    private String validateObjectIdString(String objectIdString) {
        if (objectIdString == null || objectIdString.trim().isEmpty()) {
            return null; // Empty is allowed
        }

        String[] objectIds = objectIdString.split(",");
        
        for (int i = 0; i < objectIds.length; i++) {
            String objectId = objectIds[i].trim();
            if (objectId.isEmpty()) {
                return "Invalid filter ID format: Empty ObjectId found at position " + (i + 1);
            }
            
            if (!isValidObjectId(objectId)) {
                return "Invalid filter ID format: '" + objectId + "' is not a valid ObjectId (must be 24 hex characters)";
            }
        }
        
        return null; // All ObjectIds are valid
    }

}
