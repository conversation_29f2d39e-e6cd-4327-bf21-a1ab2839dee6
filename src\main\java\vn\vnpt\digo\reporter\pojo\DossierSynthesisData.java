package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierSynthesisData {
    private long received;  // da tiep nhan

    private long inProgress; // dang xu ly

    private long inProgressEarly; // hs chua den han, dang xu ly

    private long inProgressLately; // hs đang xử lý và quá hạn

    private long completed;  // da hoan thanh (cả trể và ko trể)

    private long completedOnTime; // hs hoàn thành dung han

    private long completedEarly; // hs hoàn thành som han

    private long completedLately; // hs hoàn thành quá hạn

    private long onlineReceived; // tiếp nhận trực tuyến

    private long directReceived; // tiếp nhận trực tiếp
}
