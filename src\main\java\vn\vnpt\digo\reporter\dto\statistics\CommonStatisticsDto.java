package vn.vnpt.digo.reporter.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonStatisticsDto implements Serializable {
    private Object thongKeTongHoSo;
    private Object thongKeTongHoSoTheoNam;
    private Object thongKeSoHoa;
    private Object thongKeThanhToanTrucTuyen;
    private Object tongHsLuuTphsVaoKho;
    private Object tongTphsLuuVaoKho;
    private Object tongHsSuDungGiayToTuKho;
    private Object tongTphsSuDungGiayToTuKho;
    private Object tongHsLuuKetQuaVaoKho;
    private String err;
    private long tongNguoiDanCoGiayToTrongKho;
    private long tongGiayToTrongKho = 0;
    private long tongDungLuongKhoDaSuDung;
    private String ngayDungKho;
}

