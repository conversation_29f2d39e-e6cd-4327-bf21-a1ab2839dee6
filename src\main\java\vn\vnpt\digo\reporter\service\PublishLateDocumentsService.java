package vn.vnpt.digo.reporter.service;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.EvaluationResultForm;
import vn.vnpt.digo.reporter.document.PublishLateDocuments;
import vn.vnpt.digo.reporter.dto.AffectedRowsDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.PublishLateDocumentsDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.pojo.EvaluationResultDto;
import vn.vnpt.digo.reporter.repository.EvaluationResultFormRepository;
import vn.vnpt.digo.reporter.repository.PublishLateDocumentsRepository;

import java.util.Date;
import java.util.List;


@Service
public class PublishLateDocumentsService {
    @Autowired
    private PublishLateDocumentsRepository publishLateDocumentsRepository;
    @Autowired
    private MongoTemplate mongoTemplate;

    public PostResponseDto postPublishLateDocuments(PublishLateDocumentsDto input){
        PublishLateDocuments ret =  new PublishLateDocuments();
        Date currentDate = new Date();
        ret.setActive(input.isActive());
        ret.setFile(input.getFile());
        ret.setCreatedDate(currentDate);
        ret.setUpdatedDate(currentDate);
        ret.setTitle(input.getTitle());
        publishLateDocumentsRepository.save(ret);
        PostResponseDto res = new PostResponseDto(ret.getId());
        // run function update activeevaluationResultForm
        updateActiveStatus(ret.getId());
        return res;
    }

    public Slice<PublishLateDocumentsDto> getListPublishLateDocuments(String keyword, Pageable pageable){
        Query query = new Query();
        // query.with(pageable);
        Criteria aCriteria = new Criteria();
        if (keyword != null && !keyword.isEmpty()) {
            keyword = keyword.replaceAll("[\\-\\_\\.\\!\\~\\*\\'\\(\\)\\[\\]\\{\\}\\|\\`\\!`\\@`\\#\\$\\%\\^\\&\\+\\=\\\\\\;\\:\\\"\\<\\.\\>\\,\\?\\/]", "\\\\$0");
            query.addCriteria(Criteria.where("").andOperator(
                    (keyword != null) ? Criteria.where("").orOperator(
                            Criteria.where("file.filename").regex(keyword, "i"),
                            Criteria.where("title").regex(keyword, "i")
                    ): aCriteria
            ));
        }

        List<PublishLateDocuments> listData = mongoTemplate.find(query, PublishLateDocuments.class);
        final int start = (int)pageable.getOffset();
        final int end = Math.min((start + pageable.getPageSize()), listData.size());
        Page<PublishLateDocuments> page = new PageImpl<PublishLateDocuments>(listData.subList(start, end), pageable, listData.size());
        return page.map(PublishLateDocumentsDto::fromDocument);
    }

    public PublishLateDocumentsDto getPublishLateDocumentsById(ObjectId id){
        PublishLateDocuments dataReturn = publishLateDocumentsRepository.findById(id).orElse(null);
        if (dataReturn!=null){
            return new PublishLateDocumentsDto(id,dataReturn.getFile(),dataReturn.getTitle(),dataReturn.isActive());
        } else{
            throw new DigoHttpException(10004, new String[]{});
        }
    }

    public AffectedRowsDto updatePublishLateDocumentsById(ObjectId id, PublishLateDocumentsDto input){
        PublishLateDocuments dataReturn = publishLateDocumentsRepository.findById(id).orElse(null);
        if (dataReturn!=null){
            dataReturn.setFile(input.getFile());
            dataReturn.setTitle(input.getTitle());
            if (dataReturn.isActive()==false && input.isActive()!=dataReturn.isActive()){ // convert isActive from False -> True
                dataReturn.setActive(input.isActive());
                updateActiveStatus(id);
            }
            dataReturn.setUpdatedDate(new Date());
            publishLateDocumentsRepository.save(dataReturn);
            return new AffectedRowsDto(1);
        } else{
            return new AffectedRowsDto(0);
        }
    }

    public AffectedRowsDto deletePublishLateDocumentsById(ObjectId id){
        PublishLateDocuments dataReturn = publishLateDocumentsRepository.findById(id).orElse(null);
        int affect = publishLateDocumentsRepository.deletePublishLateDocumentsById(id);
        if (dataReturn.isActive()==true){
            setActiveStatus();
        }
        return new AffectedRowsDto(affect);
    }


    private void updateActiveStatus(ObjectId id){
        List<PublishLateDocuments> listData = publishLateDocumentsRepository.findAll();
        for (var item : listData){
            if (!item.getId().equals(id)){
                item.setActive(false);
                item.setUpdatedDate(new Date());
                publishLateDocumentsRepository.save(item);
            }
        }
    }
    private void setActiveStatus(){
        List<PublishLateDocuments> listData = publishLateDocumentsRepository.findAll();
        Date max = listData.get(0).getCreatedDate() ;
        ObjectId setActiveId = listData.get(0).getId();
        for (var item : listData){
            if  (max.before(item.getCreatedDate())){
                max = item.getCreatedDate();
                setActiveId = item.getId();
            }
        }
        PublishLateDocuments dataReturn = publishLateDocumentsRepository.findById(setActiveId).orElse(null);
        if(dataReturn!=null){
            dataReturn.setActive(true);
            publishLateDocumentsRepository.save(dataReturn);
        }
    }
    public PublishLateDocumentsDto getPublishLateDocumentsActive(){
        Query query = new Query();
        query.addCriteria(Criteria.where("isActive").is(true));
        List<PublishLateDocuments> ret = mongoTemplate.find(query, PublishLateDocuments.class);
        if(ret!=null && !ret.isEmpty())
            return new PublishLateDocumentsDto(ret.get(0).getId(),ret.get(0).getFile(),ret.get(0).getTitle(),ret.get(0).isActive()) ;
        else return new PublishLateDocumentsDto();
    }
}
