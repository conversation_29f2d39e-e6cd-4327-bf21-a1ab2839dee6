package vn.vnpt.digo.reporter.service;

import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.dto.qni.*;
import vn.vnpt.digo.reporter.util.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
public class FinancialObligationReportQNIService {

    private static final Logger logger = LoggerFactory.getLogger(FinancialObligationReportQNIService.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private Translator translator;

    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI)
    private Resource resourceTemplateDossierStatisticAssigneeQNI;

    @Value(value = Constant.LOCATION_FINANCIAL_OBLIGATION_REPORT_QNI)
    private Resource resourceTemplateFinancialObligationReportQNI;

    @Autowired
    private AgencyFilterReportQniService agencyFilterReportQniService;

    // Chuyển đổi string agency IDs thành Set<ObjectId>
    private Set<ObjectId> uniqueObjectIdAgency(String stringIds) {
        String[] objectIdStrings = stringIds.split(",");
        Set<ObjectId> uniqueObjectIds = new HashSet<>();
        for (String objectIdString : objectIdStrings) {
            try {
                ObjectId objectId = new ObjectId(objectIdString);
                uniqueObjectIds.add(objectId);
            } catch (IllegalArgumentException e) {
                logger.warn("Invalid ObjectId: {}", objectIdString);
            }
        }
        return uniqueObjectIds;
    }

    // Chuyển đổi List<String> agency IDs thành List<ObjectId>
    public List<ObjectId> convertAgencyIdsToObjectIds(List<String> agencyIds) {
        StringBuilder agencyStringBuilder = new StringBuilder();
        for (String agencyId : agencyIds) {
            try {
                var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyId);
                if (Objects.isNull(target)) {
                    continue;
                }
                agencyStringBuilder.append(target.getIdFilter()).append(",");
            } catch (Exception e) {
                logger.warn("Error processing agencyId: {}", agencyId, e);
            }
        }
        return new ArrayList<>(uniqueObjectIdAgency(agencyStringBuilder.toString()));
    }

    // Parse string date thành Date với múi giờ UTC
    private Date parseDateToUTC(String dateStr) throws Exception {
        TimeZone timezone = TimeZone.getTimeZone("GMT");
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        df.setTimeZone(timezone);
        return df.parse(dateStr);
    }

    // Tạo Query với các điều kiện chung
    private Query buildDossierQuery(Date fromDate, Date toDate, List<ObjectId> agencyIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("acceptedDate").gte(fromDate).lte(toDate));
        query.addCriteria(new Criteria().andOperator(
                new Criteria().orOperator(
                        Criteria.where("dossierStatus._id").ne(6),
                        new Criteria().andOperator(
                                Criteria.where("dossierStatus._id").is(6),
                                Criteria.where("withdrawDate").ne(null)
                        )
                ),
                Criteria.where("appointmentDate").ne(null)
        ));
        query.addCriteria(Criteria.where("agency._id").in(agencyIds));
        query.addCriteria(Criteria.where("awaitingFinancialObligations").exists(true));
        return query;
    }

    // Chuyển đổi Document thành FinancialObligationDto
    private FinancialObligationDto convertDocumentToDto(Document doc, int index, int page, int size) {
        try {
            FinancialObligationDto response = new FinancialObligationDto();
            response.setStt(page >= 0 ? (index + 1 + (page * size)) : (index + 1)); // page = -1 khi không phân trang
            response.setCode(doc.getString("code"));

            ObjectId id = doc.getObjectId("_id");
            response.setId(id != null ? id.toString() : null);

            Document procedure = (Document) doc.get("procedure");
            response.setProcedure(procedure != null ? procedure.getString("name") : null);

            Document applicant = (Document) doc.get("applicant");
            response.setNoiDungYeuCauGiaiQuyet(applicant != null ? applicant.getString("noiDungYeuCauGiaiQuyet") : null);

            response.setAcceptedDate(doc.getDate("acceptedDate"));
            response.setAppointmentDate(doc.getDate("appointmentDate"));
            response.setCompletedDate(doc.getDate("completedDate"));

            Document dossierStatus = (Document) doc.get("dossierStatus");
            response.setDossierStatus(dossierStatus != null ? dossierStatus.getString("name") : null);

            response.setAwaitingFinancialObligations(doc.getDate("awaitingFinancialObligations"));

            if (response.getAcceptedDate() != null && response.getAwaitingFinancialObligations() != null) {
                long diffInMillies = response.getAwaitingFinancialObligations().getTime() - response.getAcceptedDate().getTime();
                response.setAwaitingFinancialCompareAcceptedDate(TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS));
            }

            response.setFinancialObligationsDate(doc.getDate("financialObligationsDate"));

            if (response.getAcceptedDate() != null && response.getFinancialObligationsDate() != null) {
                long diffInMillies = response.getFinancialObligationsDate().getTime() - response.getAcceptedDate().getTime();
                response.setFinancialCompareAcceptedDate(TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS));
            }

            return response;
        } catch (Exception e) {
            logger.error("Error processing document at index {}: {}", index, doc, e);
            return null;
        }
    }

    // Lấy dữ liệu từ MongoDB và chuyển đổi thành List<FinancialObligationDto>
    private List<FinancialObligationDto> fetchAndConvertDossiers(Query query, int page, int size) {
        List<Document> documents;
        try {
            documents = mongoTemplate.find(query, Document.class, "qniETLDossier");
            logger.info("Retrieved {} documents from MongoDB", documents.size());
        } catch (Exception e) {
            logger.error("Error querying documents from MongoDB with query: {}", query, e);
            throw new RuntimeException("Failed to query dossiers", e);
        }

        return IntStream.range(0, documents.size())
                .mapToObj(i -> convertDocumentToDto(documents.get(i), i, page, size))
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    }

    // API phân trang
    public Page<FinancialObligationDto> getDossiersWithAgency(
            String fromDate,
            String toDate,
            int page,
            int size,
            List<String> agencyIds) {
        List<ObjectId> agencyObjectIds = convertAgencyIdsToObjectIds(agencyIds);
        return getDossiers(fromDate, toDate, agencyObjectIds, page, size);
    }

    public Page<FinancialObligationDto> getDossiers(
            String fromDate,
            String toDate,
            List<ObjectId> agencyIds,
            int page,
            int size) {
        try {
            logger.info("Starting getDossiers with fromDate: {}, toDate: {}, agencyIds: {}, page: {}, size: {}",
                    fromDate, toDate, agencyIds, page, size);

            Date fromDateUTC = parseDateToUTC(fromDate);
            Date toDateUTC = parseDateToUTC(toDate);

            Query query = buildDossierQuery(fromDateUTC, toDateUTC, agencyIds);
            logger.debug("Query constructed: {}", query);

            long total;
            try {
                total = mongoTemplate.count(query, "qniETLDossier");
                logger.info("Total records found: {}", total);
            } catch (Exception e) {
                logger.error("Error counting records for query: {}", query, e);
                throw new RuntimeException("Failed to count dossiers", e);
            }

            PageRequest pageRequest = PageRequest.of(page, size);
            query.with(pageRequest);

            List<FinancialObligationDto> responses = fetchAndConvertDossiers(query, page, size);
            logger.info("Successfully processed {} DTOs", responses.size());

            return new PageImpl<>(responses, pageRequest, total);

        } catch (Exception e) {
            logger.error("Unexpected error in getDossiers with fromDate: {}, toDate: {}, agencyIds: {}, page: {}, size: {}",
                    fromDate, toDate, agencyIds, page, size, e);
            throw new RuntimeException("Failed to process dossier request", e);
        }
    }

    // Hàm export lấy toàn bộ dữ liệu (không phân trang)
    public List<FinancialObligationDto> getAllDossiersForExport(
            String fromDate,
            String toDate,
            List<String> agencyIds) {
        try {
            logger.info("Starting getAllDossiersForExport with fromDate: {}, toDate: {}, agencyIds: {}",
                    fromDate, toDate, agencyIds);

            Date fromDateUTC = parseDateToUTC(fromDate);
            Date toDateUTC = parseDateToUTC(toDate);

            List<ObjectId> agencyObjectIds = convertAgencyIdsToObjectIds(agencyIds);
            Query query = buildDossierQuery(fromDateUTC, toDateUTC, agencyObjectIds);
            logger.debug("Query constructed: {}", query);

            List<FinancialObligationDto> responses = fetchAndConvertDossiers(query, -1, -1); // Không phân trang
            logger.info("Successfully processed {} DTOs for export", responses.size());

            return responses;

        } catch (Exception e) {
            logger.error("Unexpected error in getAllDossiersForExport with fromDate: {}, toDate: {}, agencyIds: {}",
                    fromDate, toDate, agencyIds, e);
            throw new RuntimeException("Failed to process dossier export request", e);
        }
    }
}
