package vn.vnpt.digo.reporter.util;

import java.net.URI;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 *
 * <AUTHOR>
 */
@Component
public class DigoRestTemplate {

    private final Logger logger = LoggerFactory.getLogger(DigoRestTemplate.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Translator translator;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    MultiValueMap<String, String> body;

    HttpMethod httpMethod;

    URI uri;

    public boolean call() {
        // Get token request
        String tokenValue = Context.getOAuth2AccessTokenValue();
        // Header request
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(tokenValue);
        headers.set("Accept-Language", translator.getCurrentLocale().getLanguage());
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
        // Logging DigoRestTemplate calls
        logger.info("Call API -->" + uri.toString());
        logger.info("API send requests-->" + request);
        // Call DigoRestTemplate
        ResponseEntity<String> response = restTemplate.exchange(uri, httpMethod, request, String.class);
        // Print response
        logger.info("API send responses-->" + response);
        return response.getStatusCode() == HttpStatus.OK;
    }

    public String callWithResponse(String clientToken) {
        // Get token request
        String tokenValue;
        if (clientToken != null) {
            tokenValue = clientToken;
        } else {
            tokenValue = Context.getOAuth2AccessTokenValue();
        }
        // Header request
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(tokenValue);
        headers.set("Accept-Language", translator.getCurrentLocale().getLanguage());
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
        // Logging DigoRestTemplate calls
        logger.info("Call API -->" + uri.toString());
        logger.info("API send requests-->" + request);
        // Call DigoRestTemplate
        ResponseEntity<String> response = restTemplate.exchange(uri, httpMethod, request, String.class);
        // Print response
        logger.info("API send responses-->" + response);
        return response.getBody();
    }

    public boolean exchange(URI uri, MultiValueMap<String, String> body, HttpMethod httpMethod) {
        this.uri = uri;
        this.body = body;
        this.httpMethod = httpMethod;
        return call();
    }

    public String exchangeWithResponse(String token, URI uri, MultiValueMap<String, String> body, HttpMethod httpMethod) {
        this.uri = uri;
        this.body = body;
        this.httpMethod = httpMethod;
        return callWithResponse(token);
    }

    public RestTemplate getRestTemplateQni(){
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }
}
