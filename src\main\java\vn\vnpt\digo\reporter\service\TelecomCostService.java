/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.digo.reporter.document.TelecomCost;
import vn.vnpt.digo.reporter.dto.GetTelecomCostByMonthDto;
import vn.vnpt.digo.reporter.dto.GetTelecomCostByYearDto;
import vn.vnpt.digo.reporter.dto.InputTelecomCostDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.TelecomCostAndAmountByMonthDto;
import vn.vnpt.digo.reporter.dto.TelecomCostAndAmountByYearDto;
import vn.vnpt.digo.reporter.dto.TelecomCostAndAmountGroupByMonthDto;
import vn.vnpt.digo.reporter.pojo.SubscriptionType;
import vn.vnpt.digo.reporter.pojo.SubscriptionTypeName;
import vn.vnpt.digo.reporter.repository.TelecomCostRepository;
import vn.vnpt.digo.reporter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class TelecomCostService {

    @Autowired
    private TelecomCostRepository telecomCostRepository;

    @Autowired
    private Translator translator;

    @Transactional
    public PostResponseDto postTelecomCost(InputTelecomCostDto postTelecomCostData) {

        // New SubscriptionType
        SubscriptionTypeName subscriptionTypeName = new SubscriptionTypeName(translator.getCurrentLocaleId(), postTelecomCostData.getSubscriptionTypeName());

        List<SubscriptionTypeName> subscriptionTypeNames = new ArrayList<>();
        subscriptionTypeNames.add(subscriptionTypeName);
        SubscriptionType subscriptionType = new SubscriptionType(postTelecomCostData.getSubscriptionTypeId(), subscriptionTypeNames);
        TelecomCost newTelecomCost = new TelecomCost(postTelecomCostData.getPhoneNumber(), postTelecomCostData.getYear(), postTelecomCostData.getMonth(), postTelecomCostData.getPaymentCode(), postTelecomCostData.getSubscriptionCode(), subscriptionType, postTelecomCostData.getAmount());

        // Save telecom cost
        TelecomCost telecomCost = telecomCostRepository.save(newTelecomCost);
        PostResponseDto res = new PostResponseDto(telecomCost.getId());
        return res;

    }
    
    public TelecomCostAndAmountByMonthDto getTelecomCostByMonth(Integer month, Integer year, List<ObjectId> subscriptionTypeId) {
        // Get information of telecom cost by month
        List<GetTelecomCostByMonthDto> telecomCostDto;

        if (subscriptionTypeId != null) {
            telecomCostDto = telecomCostRepository.getTelecomCostByMonth(translator.getCurrentLocaleId(), month, year, subscriptionTypeId);
        } else {
            telecomCostDto = telecomCostRepository.getTelecomCostByMonthSimple(translator.getCurrentLocaleId(), month, year);
        }

        telecomCostDto.forEach((item) -> {
            item.setSubscriptionTypeId();
            item.setSubscriptionTypeName(translator.getCurrentLocaleId());
        });

        // Get information of telecom cost and sum amount by month
        TelecomCostAndAmountByMonthDto telecomCostAndAmountDto = new TelecomCostAndAmountByMonthDto();
        telecomCostAndAmountDto.setTelecomCost(telecomCostDto);
        telecomCostAndAmountDto.setTotal();

        return telecomCostAndAmountDto;
    }

    public TelecomCostAndAmountByYearDto getTelecomCostByYear(Integer year, List<ObjectId> subscriptionTypeId) {

        // Get information of telecom cost by year
        List<GetTelecomCostByYearDto> getTelecomCost;
        if (subscriptionTypeId != null) {
            getTelecomCost = telecomCostRepository.getTelecomCostByYear(year, subscriptionTypeId);
        } else {
            getTelecomCost = telecomCostRepository.getTelecomCostByYearSimple(year);
        }

        // Groupping by month
        List<TelecomCostAndAmountGroupByMonthDto> telecomCostGroupByMonthDto = new ArrayList<>();
        for (GetTelecomCostByYearDto item : getTelecomCost) {

            //  Check out the existence of a month
            boolean existMonth = TelecomCostAndAmountGroupByMonthDto.existMonth(item.getMonth(), telecomCostGroupByMonthDto);
            if (existMonth) {
                TelecomCostAndAmountGroupByMonthDto.setTotalAmount(item.getMonth(), item.getAmount(), telecomCostGroupByMonthDto);
            } else {
                // Add new object TelecomCostAndAmountGroupByMonthDto
                TelecomCostAndAmountGroupByMonthDto newTelecomCostGroupByMonth = new TelecomCostAndAmountGroupByMonthDto(item.getMonth(), item.getAmount());
                telecomCostGroupByMonthDto.add(newTelecomCostGroupByMonth);
            }
        }

        // Get information of telecom cost and sum amount by year
        TelecomCostAndAmountByYearDto telecomCostByYear = new TelecomCostAndAmountByYearDto();
        telecomCostByYear.setTelecomCost(telecomCostGroupByMonthDto);
        telecomCostByYear.setTotal();

        return telecomCostByYear;
    }
}
