package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.featurestatistics.FeatureStatisticsDto;
import vn.vnpt.digo.reporter.dto.featurestatistics.FeatureStatisticsInputDto;
import vn.vnpt.digo.reporter.service.FeatureStatisticsService;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/feature-statistics")
@IcodeAuthorize("vnpt.permission.feature-statistics")
public class FeatureStatisticsController {

    private final FeatureStatisticsService featureStatisticsService;

    private final Logger logger = LoggerFactory.getLogger(FeatureStatisticsController.class);

    public FeatureStatisticsController(FeatureStatisticsService featureStatisticsService) {
        this.featureStatisticsService = featureStatisticsService;
    }

    @PostMapping()
    public ResponseEntity<Object> create(
            HttpServletRequest request,
            @Validated @RequestBody List<FeatureStatisticsInputDto> input
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        List<PostResponseDto> res = featureStatisticsService.create(input);
        logger.info("DIGO-Response: " + res.size());
        return ResponseEntity.ok(res);
    }

    @GetMapping()
    public ResponseEntity<Object> statistics(
            HttpServletRequest request,
            @RequestParam("app-code") String appCode,
            @RequestParam(name = "start-date", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ") Date startDate,
            @RequestParam(name = "end-date", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ") Date endDate
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        List<FeatureStatisticsDto> res = featureStatisticsService.statistics(appCode, startDate, endDate);
        logger.info("DIGO-Response: " + res.size());
        return ResponseEntity.ok(res);
    }

}
