/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.dto.KafkaAgencyStatisticDto;
import vn.vnpt.digo.reporter.stream.messaging.AgencyProducer;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(AgencyProducer.class)
public class AgencyProducerStream {

    @Autowired
    private AgencyProducer agencyProducer;

    public boolean push(KafkaAgencyStatisticDto input){
        Message<KafkaAgencyStatisticDto> message = MessageBuilder.withPayload(input).build();
        return agencyProducer.output().send(message);
    }
    
}
