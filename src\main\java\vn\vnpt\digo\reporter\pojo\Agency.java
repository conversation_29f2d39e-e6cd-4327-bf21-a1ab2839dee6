package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Agency implements Serializable {

    @Field("id")
    private Long id;

    private Long parentId;

    private Long placeId;

    @JsonIgnore
    private List<Long> ancestorPlaceId;

    @JsonIgnore
    private List<AgencyName> name;
}
