package vn.vnpt.digo.reporter.changestream;

import com.mongodb.MongoClient;
import com.mongodb.MongoClientURI;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.changestream.ChangeStreamDocument;
import org.bson.BsonDocument;
import org.bson.BsonTimestamp;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.ChangeStreamEvent;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import static com.mongodb.client.model.Filters.eq;


@Component
public class InitChangeStream {

    private static final Logger logger = LoggerFactory.getLogger(InitChangeStream.class);

    private static final String CURRENT_DB_NAME = "svcReporter";
    public static final String PADMAN = "svcPadman";

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PadmanChangeStreamProcess padmanChangeStream;

    @Value("${spring.data.mongodb.uri}")
    private String mongodbUri;
    
    @Value("${digo.change-stream.enable}")
    private Boolean changeStreamEnable;

    private final ExecutorService executorService = Executors.newFixedThreadPool(4); // Đa luồng nếu có nhiều stream

    @PostConstruct
    public void initChangeStreams() {
        MongoClientURI uri = new MongoClientURI(mongodbUri);
        MongoClient mongoClient = new MongoClient(uri);
        
        // Sau này có thể gọi thêm các stream khác tại đây
        if (Boolean.TRUE.equals(changeStreamEnable)) {
            initPadmanStream(mongoClient);
        }
    }

    private void initPadmanStream(MongoClient mongoClient) {
        try {
            String targetDbName = mongoTemplate.getDb().getName().replace(CURRENT_DB_NAME, PADMAN);
            MongoDatabase database = mongoClient.getDatabase(targetDbName);

            // Danh sách collection muốn theo dõi
            String[] collectionsToWatch = new String[]{
                    "dossier",
//                    "dossierFee",
//                    "dossierPayment",
//                    "dossierFormFile"
            };

            for (String collectionName : collectionsToWatch) {
                MongoCollection<Document> collection = database.getCollection(collectionName);

                executorService.submit(() -> {
                    logger.info(" --- Listening to PADMAN collection [{}] changes...", collectionName);
                    try {
                        BsonDocument resumeToken = getLastResumeToken("svc.padman", collectionName);

                        var watch = collection.watch();
                        if (resumeToken != null) {
                            watch = watch.resumeAfter(resumeToken);
                            logger.info("   --- Resuming from last token for [{}]", collectionName);
                        }

                        try (var cursor = watch.iterator()) {
                            while (cursor.hasNext()) {
                                ChangeStreamDocument<Document> change = cursor.next();
                                logger.info("   --- [{}] Change type: {}", collectionName, change.getOperationType().getValue());

                                try {
                                    String type = change.getOperationType().getValue();
                                    ObjectId itemId = change.getDocumentKey().getObjectId("_id").getValue();
                                    ObjectId idSaved = findByItemId(itemId);

                                    Document fullDoc = change.getFullDocument();
                                    if ("update".equals(type) && fullDoc == null) {
                                        fullDoc = database.getCollection(collectionName).find(eq("_id", itemId)).first();
                                    }

                                    ChangeStreamEvent changeStreamEvent = new ChangeStreamEvent(
                                            idSaved,
                                            "svc.padman",
                                            collectionName,
                                            type,
                                            itemId,
                                            fullDoc,
                                            change.getResumeToken().toJson(),
                                            convertTime(change.getClusterTime()),
                                            0,
                                            null,
                                            0
                                    );

                                    mongoTemplate.save(changeStreamEvent);
                                } catch (Exception e) {
                                    logger.error("Save event error: {}", e.getMessage(), e);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        logger.error(" --- Listen change stream [{}] error {}", collectionName, ex);
                    }
                });
            }

        } catch (Exception e) {
            logger.error(" --- Connect to db padman error!", e);
        }
    }
    
    private Date convertTime(BsonTimestamp clusterTime) {
        if (clusterTime == null) {
            return null;
        }
        // clusterTime.getTime() trả về epoch seconds → nhân 1000 để ra milliseconds
        return new Date(clusterTime.getTime() * 1000L);
    }
    
    private ObjectId findByItemId (ObjectId itemId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("itemId").is(itemId));
        ChangeStreamEvent item = mongoTemplate.findOne(query, ChangeStreamEvent.class);
        if (item != null) return item.getId();
        else return new ObjectId();
    }

    private BsonDocument getLastResumeToken(String service, String collection) {
        Query query = new Query(
                Criteria.where("service").is(service)
                        .and("collection").is(collection))
                .with(Sort.by(Sort.Direction.DESC, "createdDate")) // Sắp xếp mới nhất
                .limit(1);

        ChangeStreamEvent item = mongoTemplate.findOne(query, ChangeStreamEvent.class);

        return (item != null) ? BsonDocument.parse(item.getResumeToken()) : null;
    }
}
