package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.util.HideSecurityInformationHelper;

import java.io.Serializable;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Objects;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "qniETLDossier")
public class QNIETLDossier {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;

    private Agency agency;

    private Sector sector;

    private Procedure procedure;

    private ProcedureLevel procedureLevel;

    private String code;

    private DossierStatus dossierStatus;

    private DossierTaskStatus dossierTaskStatus;

    private Applicant applicant;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date returnedDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;

    private double processingTime;

    private ApplyMethod applyMethod;

    private Task currentTask;

    private ExtendDigitizing extendDigitizing;

    private List<Task> task;

    private List<Attachment> attachment;

    private List<FormFile> listDossierFormFile;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date pauseDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date withdrawDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date cancelledDate; // ngay dung xu ly

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date financialObligationsDate; // ngay nghia vu tai chinh

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private List<Date> additionalDate; // ngay bo sung ho so

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date extendDate; // ngay gia han

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date originalAppointmentDate; // ngay hen tra dau tien

    private Boolean isPaymentOnline;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date deadlineAcceptedDate;

    private Boolean iSlowToReceive;

    private Boolean procedureHavePayment;

    // Phương thức để kiểm tra điều kiện financialObligationOnline
    public boolean getFinancialObligationOnlineCondition() {
        return this.procedureHavePayment != null && this.procedureHavePayment
                && this.applyMethod.getId() == 0
                && (this.dossierStatus.getId() != 6 || (this.dossierStatus.getId() == 6 && this.withdrawDate != null))
                && this.appointmentDate != null;
    }

    public boolean getFinancialObligationDirectCondition() {
        return this.procedureHavePayment != null && this.procedureHavePayment
                && this.applyMethod.getId() != 0
                && (this.dossierStatus.getId() != 6 || (this.dossierStatus.getId() == 6 && this.withdrawDate != null))
                && this.appointmentDate != null;
    }

    public boolean getPaymentOnlineCondition() {
        return this.isPaymentOnline != null
                && this.isPaymentOnline
                && this.procedureHavePayment != null && this.procedureHavePayment
                && (this.dossierStatus.getId() != 6 || (this.dossierStatus.getId() == 6 && this.withdrawDate != null))
                && this.appointmentDate != null;
    }

    public boolean getSlowReceptionCondition() {
        if (this.acceptedDate != null && this.deadlineAcceptedDate != null) {
            return this.acceptedDate.after(this.deadlineAcceptedDate)
                    && (this.dossierStatus.getId() != 6 || (this.dossierStatus.getId() == 6 && this.withdrawDate != null))
                    && this.appointmentDate != null;
        }

        Instant utcNow = Instant.now();
        Date currentUtcDate = Date.from(utcNow);
        return this.deadlineAcceptedDate != null && currentUtcDate.after(this.deadlineAcceptedDate)
                && (this.dossierStatus.getId() != 6 || (this.dossierStatus.getId() == 6 && this.withdrawDate != null))
                && this.appointmentDate != null;
    }
    public void setHideSecurity() {
        if(Objects.nonNull(this.applicant)){
       this.applicant.fullname = HideSecurityInformationHelper.setFullnameSecurity(this.applicant.getFullname());
       this.applicant.ownerFullName = HideSecurityInformationHelper.setFullnameSecurity(this.applicant.getOwnerFullName());
        }
        if(Objects.nonNull(this.currentTask) && Objects.nonNull(this.currentTask.assignee)){
            Assignee assignee =  this.currentTask.assignee;
            this.currentTask.assignee.fullname = HideSecurityInformationHelper.setFullnameSecurity(assignee.getFullname());
        }
        if(Objects.nonNull(this.task) && this.task.size() > 0){
            for(Task task: this.task){
                if(Objects.nonNull(task) && Objects.nonNull(task.assignee)){
                    Assignee assignee =  this.currentTask.assignee;
                    task.assignee.fullname = HideSecurityInformationHelper.setFullnameSecurity(assignee.getFullname());
                }
            }
        }

    }

    public boolean getOnTimeReceptionCondition() {
        return !this.getSlowReceptionCondition();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Agency {
        private String id;

        private String name;

        private Parent parent;

        private List<Ancestors> ancestors;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Sector {
        private String id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Procedure {
        private String id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierStatus {
        private int id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierTaskStatus {
        private String id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Applicant {

        @Id
        private String eformId; //this is eformId

        private String address;

        private String phoneNumber;

        private String fullname;

        private String organization;

        private Integer hinhThucNop;

        private String ownerFullName;

        private String noiDungYeuCauGiaiQuyet;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplyMethod {
        private int id;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Task {
        private Assignee assignee;

        private Agency agency;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date assignedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date dueDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date createdDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date updatedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date completedDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Assignee {
        private String id;
        private String fullname;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Parent {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ancestors {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureLevel {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attachment {
        private String id;
        private String filename;
        private String group;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormFile implements Serializable {
        @JsonProperty("file")
        List<File> file;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class File implements Serializable {
        @Id
        @JsonSerialize(using = ToStringSerializer.class)
        private String id;

        private String filename;

        private Integer size;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtendDigitizing implements Serializable {
        private Boolean isReuseFile;
        private Boolean isSignsTPHS;
        private Boolean isAttachmentStorages;
        private Boolean isComponentsStorages;
        private Boolean isSignsAttachment;
        private Boolean isHaveAttachment;
        private Boolean isHaveTPHS;
        private Boolean isUseCheckCitizen;
    }
}
