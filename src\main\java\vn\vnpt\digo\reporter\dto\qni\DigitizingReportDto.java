package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DigitizingReportDto {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Agency agency;

    private int totalReceiver;
    private int totalComplete;
    private int totalReceiverHavingFile;
    private int totalCompleteHavingFile;
    private int totalCompleteStorage;
    private int totalReceiverStorage;

    public int getTotalCompleteStorage() {
        return totalCompleteStorage;
    }

    public void setTotalCompleteStorage(int totalCompleteStorage) {
        this.totalCompleteStorage = totalCompleteStorage;
    }

    public int getTotalReceiverStorage() {
        return totalReceiverStorage;
    }

    public void setTotalReceiverStorage(int totalReceiverStorage) {
        this.totalReceiverStorage = totalReceiverStorage;
    }

    public Agency getAgency() {
        return agency;
    }

    public void setAgency(Agency agency) {
        this.agency = agency;
    }

    public int getTotalReceiver() {
        return totalReceiver;
    }

    public void setTotalReceiver(int totalReceiver) {
        this.totalReceiver = totalReceiver;
    }

    public int getTotalComplete() {
        return totalComplete;
    }

    public void setTotalComplete(int totalComplete) {
        this.totalComplete = totalComplete;
    }

    public int getTotalReceiverHavingFile() {
        return totalReceiverHavingFile;
    }

    public void setTotalReceiverHavingFile(int totalReceiverHavingFile) {
        this.totalReceiverHavingFile = totalReceiverHavingFile;
    }

    public int getTotalCompleteHavingFile() {
        return totalCompleteHavingFile;
    }

    public void setTotalCompleteHavingFile(int totalCompleteHavingFile) {
        this.totalCompleteHavingFile = totalCompleteHavingFile;
    }

    public int getTotalReceiverNopeFile() {
        return totalReceiverNopeFile;
    }

    public void setTotalReceiverNopeFile(int totalReceiverNopeFile) {
        this.totalReceiverNopeFile = totalReceiverNopeFile;
    }

    public int getTotalCompleteNopeFile() {
        return totalCompleteNopeFile;
    }

    public void setTotalCompleteNopeFile(int totalCompleteNopeFile) {
        this.totalCompleteNopeFile = totalCompleteNopeFile;
    }

    public int getTotalReused() {
        return totalReused;
    }

    public void setTotalReused(int totalReused) {
        this.totalReused = totalReused;
    }

    public double getPercentTotalReceiverHavingFile() {
        return percentTotalReceiverHavingFile;
    }

    public void setPercentTotalReceiverHavingFile(double percentTotalReceiverHavingFile) {
        this.percentTotalReceiverHavingFile = percentTotalReceiverHavingFile;
    }

    public double getPercentTotalCompleteHavingFile() {
        return percentTotalCompleteHavingFile;
    }

    public void setPercentTotalCompleteHavingFile(double percentTotalCompleteHavingFile) {
        this.percentTotalCompleteHavingFile = percentTotalCompleteHavingFile;
    }

    public int getTotalReusedCSDLDC() {
        return totalReusedCSDLDC;
    }

    public void setTotalReusedCSDLDC(int totalReusedCSDLDC) {
        this.totalReusedCSDLDC = totalReusedCSDLDC;
    }

    private int totalReceiverNopeFile;
    private int totalCompleteNopeFile;
    private int totalReused;
    private double percentTotalReceiverHavingFile;
    private double percentTotalCompleteHavingFile;
    private int totalReusedCSDLDC;

    public DigitizingReportDto(Agency agency,
                               Agency agencyBaby,
                               int totalReceiver,
                               int totalComplete,
                               int totalReceiverHavingFile,
                               int totalCompleteHavingFile,
                               int totalReceiverNopeFile,
                               int totalCompleteNopeFile,
                               int totalReused,
                               double percentTotalReceiverHavingFile,
                               double percentTotalCompleteHavingFile,
                               int totalCompleteStorage,
                               int totalReceiverStorage,
                               int totalReusedCSDLDC) {
        this.agency = agency;
        this.totalReceiver = totalReceiver;
        this.totalComplete = totalComplete;
        this.totalReceiverHavingFile = totalReceiverHavingFile;
        this.totalCompleteHavingFile = totalCompleteHavingFile;
        this.totalReceiverNopeFile = totalReceiverNopeFile;
        this.totalCompleteNopeFile = totalCompleteNopeFile;
        this.totalReused = totalReused;
        this.percentTotalReceiverHavingFile = percentTotalReceiverHavingFile;
        this.percentTotalCompleteHavingFile = percentTotalCompleteHavingFile;
        this.agency = agency;
        this.totalCompleteStorage = totalCompleteStorage;
        this.totalReceiverStorage = totalReceiverStorage;
        this.totalReusedCSDLDC = totalReusedCSDLDC;
    }

    // Getter and Setter methods

    public static class Agency {
        @JsonProperty("id")
        private String idAgency;
        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getId() {
            return idAgency;
        }

        public void setIdAgency(String idAgency) {
            this.idAgency = idAgency;
        }
    }

    public static class CustomSummary {
        private int totalReceiver;
        private int totalComplete;

        public int getTotalReceiver() {
            return totalReceiver;
        }

        public void setTotalReceiver(int totalReceiver) {
            this.totalReceiver = totalReceiver;
        }

        public int getTotalComplete() {
            return totalComplete;
        }

        public void setTotalComplete(int totalComplete) {
            this.totalComplete = totalComplete;
        }

        public int getTotalReceiverHavingFile() {
            return totalReceiverHavingFile;
        }

        public void setTotalReceiverHavingFile(int totalReceiverHavingFile) {
            this.totalReceiverHavingFile = totalReceiverHavingFile;
        }

        public int getTotalCompleteHavingFile() {
            return totalCompleteHavingFile;
        }

        public void setTotalCompleteHavingFile(int totalCompleteHavingFile) {
            this.totalCompleteHavingFile = totalCompleteHavingFile;
        }

        public int getTotalReceiverNopeFile() {
            return totalReceiverNopeFile;
        }

        public void setTotalReceiverNopeFile(int totalReceiverNopeFile) {
            this.totalReceiverNopeFile = totalReceiverNopeFile;
        }

        public int getTotalCompleteNopeFile() {
            return totalCompleteNopeFile;
        }

        public void setTotalCompleteNopeFile(int totalCompleteNopeFile) {
            this.totalCompleteNopeFile = totalCompleteNopeFile;
        }

        public int getTotalReused() {
            return totalReused;
        }

        public void setTotalReused(int totalReused) {
            this.totalReused = totalReused;
        }

        public double getPercentTotalReceiverHavingFile() {
            return percentTotalReceiverHavingFile;
        }

        public void setPercentTotalReceiverHavingFile(double percentTotalReceiverHavingFile) {
            this.percentTotalReceiverHavingFile = percentTotalReceiverHavingFile;
        }

        public double getPercentTotalCompleteHavingFile() {
            return percentTotalCompleteHavingFile;
        }

        public void setPercentTotalCompleteHavingFile(double percentTotalCompleteHavingFile) {
            this.percentTotalCompleteHavingFile = percentTotalCompleteHavingFile;
        }

        public int getTotalCompleteStorage() {
            return totalCompleteStorage;
        }

        public void setTotalCompleteStorage(int totalCompleteStorage) {
            this.totalCompleteStorage = totalCompleteStorage;
        }

        public int getTotalReceiverStorage() {
            return totalReceiverStorage;
        }

        public void setTotalReceiverStorage(int totalReceiverStorage) {
            this.totalReceiverStorage = totalReceiverStorage;
        }

        public int getTotalReusedCSDLDC() {
            return totalReusedCSDLDC;
        }

        public void setTotalReusedCSDLDC(int totalReusedCSDLDC) {
            this.totalReusedCSDLDC = totalReusedCSDLDC;
        }

        private int totalReceiverHavingFile;
        private int totalCompleteHavingFile;
        private int totalReceiverNopeFile;
        private int totalCompleteNopeFile;
        private int totalReused;
        private double percentTotalReceiverHavingFile;
        private double percentTotalCompleteHavingFile;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private int totalCompleteStorage;
        private int totalReceiverStorage;
        private Agency agency;
        private int totalReusedCSDLDC;

        public CustomSummary(Agency agency,
                             int totalReceiver,
                             int totalComplete,
                             int totalReceiverHavingFile,
                             int totalCompleteHavingFile,
                             int totalReceiverNopeFile,
                             int totalCompleteNopeFile,
                             int totalReused,
                             double percentTotalReceiverHavingFile,
                             double percentTotalCompleteHavingFile,
                             int totalCompleteStorage,
                             int totalReceiverStorage,
                             int totalReusedCSDLDC) {
            this.totalReceiver = totalReceiver;
            this.totalComplete = totalComplete;
            this.totalReceiverHavingFile = totalReceiverHavingFile;
            this.totalCompleteHavingFile = totalCompleteHavingFile;
            this.totalReceiverNopeFile = totalReceiverNopeFile;
            this.totalCompleteNopeFile = totalCompleteNopeFile;
            this.totalReused = totalReused;
            this.percentTotalReceiverHavingFile = percentTotalReceiverHavingFile;
            this.percentTotalCompleteHavingFile = percentTotalCompleteHavingFile;
            this.agency = agency;
            this.totalCompleteStorage = totalCompleteStorage;
            this.totalReceiverStorage = totalReceiverStorage;
            this.totalReusedCSDLDC = totalReusedCSDLDC;
        }

        public Agency getAgency() {
            return agency;
        }

        public void setAgency(Agency agency) {
            this.agency = agency;
        }
    }

}

