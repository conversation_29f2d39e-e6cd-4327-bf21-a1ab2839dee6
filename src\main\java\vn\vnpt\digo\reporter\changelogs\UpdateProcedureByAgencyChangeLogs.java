package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;
import com.mongodb.DBCollection;
import java.util.HashMap;
import org.bson.types.ObjectId;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-08-31-11-50")
public class UpdateProcedureByAgencyChangeLogs {

    private HashMap<Long, ObjectId> mapAgency = new HashMap<>();
    private HashMap<Long, ObjectId> mapAgencyParent = new HashMap<>();
    private HashMap<Long, ObjectId> mapPlace = new HashMap<>();
    private HashMap<Long, ObjectId> mapPlaceAncestor = new HashMap<>();

    public UpdateProcedureByAgencyChangeLogs() {
        mapAgency.put(1L, new ObjectId("5f39fb555224cf235e134c6a"));
        mapAgency.put(2L, new ObjectId("5f39fb555224cf235e134c6a"));
        mapAgencyParent.put(1L, new ObjectId("5f39fa505224cf235e134c69"));
        mapAgencyParent.put(2L, new ObjectId("5f39fa505224cf235e134c69"));
        mapPlace.put(1L, new ObjectId("5def47c5f47614018c128291"));
        mapPlace.put(2L, new ObjectId("5def47c5f47614018c128291"));
        mapPlaceAncestor.put(1L, new ObjectId("5def47c5f47614018c000082"));
        mapPlaceAncestor.put(2L, new ObjectId("5def47c5f47614018c000082"));
    }

    @ChangeSet(order = "2020-08-31-11-50", id = "UpdateProcedureByAgencyChangeLogs::updateProcedureByAgency", author = "haimn")
    public void updateTagPetition(DB db) {

        DBCollection procedureByAgencyCollection = db.getCollection("procedureByAgency");
        procedureByAgencyCollection.find().forEach(data -> {
            // Map to BasicDBObject
            BasicDBObject updateProcedureByAgency = (BasicDBObject) data;
            // Get agency as BasicDBObject
            BasicDBObject updateAgency = (BasicDBObject) updateProcedureByAgency.get("agency");
            // Set new agency id
            Long agencyId = updateAgency.getLong("id");
            updateAgency.put("id", mapAgency.getOrDefault(agencyId, new ObjectId("5f39fb555224cf235e134c6a")));
            // Set new agency parent id
            updateAgency.put("parentId", mapAgencyParent.getOrDefault(agencyId, new ObjectId("5f39fa505224cf235e134c69")));
            // Set new place id
            updateAgency.put("placeId", mapPlace.getOrDefault(agencyId, new ObjectId("5def47c5f47614018c128291")));
            // Set new ancestor place id
            List<ObjectId> ancestorPlaceIds = new ArrayList<>();
            ancestorPlaceIds.add(mapPlaceAncestor.getOrDefault(agencyId, new ObjectId("5def47c5f47614018c000082")));
            updateAgency.put("ancestorPlaceId", ancestorPlaceIds);
            // Set new sector
            BasicDBList sectors = (BasicDBList) updateProcedureByAgency.get("sector");
            sectors.forEach(sectorItem -> {
                BasicDBObject sector = (BasicDBObject) sectorItem;
                sector.put("id", new ObjectId());
            });

            procedureByAgencyCollection.save(updateProcedureByAgency);
        });
    }
}
