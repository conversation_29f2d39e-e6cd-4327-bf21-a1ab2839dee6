package vn.vnpt.digo.reporter.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import com.aspose.words.SaveFormat;
import org.apache.commons.io.FilenameUtils;
import org.springframework.http.MediaType;

public class OfficeUtils {
    public static int getFormat(String extension){
        switch(extension.toLowerCase()){
            case "docx": return SaveFormat.DOCX;
            case "doc": return SaveFormat.DOC;
            case "odt": return SaveFormat.ODT;
            default: return SaveFormat.PDF;
        }
    }

    public static String getSaveFilename(String filename, String extension){
        String resultName = FilenameUtils.getBaseName(filename) + "-" + new SimpleDateFormat("yyyyMMddHHmm").format(new Date());
        switch(extension.toLowerCase()){
            case "docx": return resultName + ".docx";
            case "doc": return resultName + ".doc";
            case "odt": return resultName + ".odt";
            default: return resultName + ".pdf";
        }
    }

    public static MediaType getMediaType(String extension){
        switch(extension.toLowerCase()){
            case "docx": return MediaType.valueOf("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            case "doc": return MediaType.valueOf("application/msword");
            case "odt": return MediaType.valueOf("application/vnd.oasis.opendocument.text");
            default: return MediaType.APPLICATION_PDF;
        }
    }
}
