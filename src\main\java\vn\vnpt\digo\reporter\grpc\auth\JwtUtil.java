package vn.vnpt.digo.reporter.grpc.auth;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.grpc.Context;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class JwtUtil {

    public static final Context.Key<String> TOKEN = Context.key("token");

    public static final Context.Key<String> CONTEXT_KEY_DEPLOYMENT_ID = Context.key("deployment_id");

    public static String getJwtParameterValue(String token, String parameter) {
        // Get username from JWT token
        String jwtToken = token;
        String[] split_string = jwtToken.split("\\.");
        String base64EncodedBody = split_string[1];
        byte[] decodedBytes = Base64.getUrlDecoder().decode(base64EncodedBody);
        String decodedString = new String(decodedBytes);
        ObjectMapper mapper = new ObjectMapper();
        try {
            Map<String, Object> payload = mapper.readValue(decodedString, new TypeReference<Map<String, Object>>() {
                //Do nothing here
            });
            return payload.get(parameter).toString();
        } catch (IOException | NullPointerException ex) {
            // Do nothing
//            logger.info(("Parameter Error: "+parameter+" with message "+ex.getMessage()));
        }
        return null;
    }
}