package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
public class ReceptionReportDto {
    private String agencyIdAgency;
    private String agencyName;
    private List<ProcedureReceptionDto> procedureReception;

    public ReceptionReportDto() {
    }

    public ReceptionReportDto(String agencyIdAgency,
                              String agencyName,
                              List<ProcedureReceptionDto> procedureReception) {
        this.agencyIdAgency = agencyIdAgency;
        this.agencyName = agencyName;
        this.procedureReception = procedureReception;
    }

    public String getAgencyIdAgency() {
        return agencyIdAgency;
    }

    public void setAgencyIdAgency(String agencyIdAgency) {
        this.agencyIdAgency = agencyIdAgency;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public List<ProcedureReceptionDto> getProcedureReception() {
        return procedureReception;
    }

    public void setProcedureReception(List<ProcedureReceptionDto> procedureReception) {
        this.procedureReception = procedureReception;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcedureReceptionDto {
        private double slowReception;
        private double onTimeReception;
        private double toTal;
        private String procedureName;
        private String procedureId;

        public ProcedureReceptionDto() {
        }

        // Constructors, getters, and setters
        public ProcedureReceptionDto(double slowReception, double onTimeReception, double toTal,
                                   String procedureName, String procedureId) {
            this.slowReception = slowReception;
            this.onTimeReception = onTimeReception;
            this.toTal = toTal;
            this.procedureName = procedureName;
            this.procedureId = procedureId;
        }
        // Getters and setters...

        public double getSlowReception() {
            return slowReception;
        }

        public void setSlowReception(double slowReception) {
            this.slowReception = slowReception;
        }

        public double getOnTimeReception() {
            return onTimeReception;
        }

        public void setOnTimeReception(double onTimeReception) {
            this.onTimeReception = onTimeReception;
        }

        public double getToTal() {
            return toTal;
        }

        public void setToTal(double toTal) {
            this.toTal = toTal;
        }

        public String getProcedureName() {
            return procedureName;
        }

        public void setProcedureName(String procedureName) {
            this.procedureName = procedureName;
        }

        public String getProcedureId() {
            return procedureId;
        }

        public void setProcedureId(String procedureId) {
            this.procedureId = procedureId;
        }
    }
}

