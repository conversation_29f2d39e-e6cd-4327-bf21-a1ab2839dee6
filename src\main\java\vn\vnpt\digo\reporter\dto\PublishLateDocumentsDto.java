package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import vn.vnpt.digo.reporter.document.EvaluationResultForm;
import vn.vnpt.digo.reporter.document.PublishLateDocuments;
import vn.vnpt.digo.reporter.pojo.EvaluationResultDto;
import vn.vnpt.digo.reporter.pojo.File;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PublishLateDocumentsDto {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private File file;
    private String title;
    @JsonProperty("isActive")
    private boolean isActive;
    public static PublishLateDocumentsDto fromDocument(PublishLateDocuments input) {
        PublishLateDocumentsDto result = new PublishLateDocumentsDto();
        result.setId(input.getId());
        result.setFile(input.getFile());
        result.setActive(input.isActive());
        result.setTitle(input.getTitle());
        return result;
    }
    public PublishLateDocumentsDto(File file, String title,  boolean isActive){
        this.setFile(file);
        this.setActive(isActive);
        this.setTitle(title);
    }
}
