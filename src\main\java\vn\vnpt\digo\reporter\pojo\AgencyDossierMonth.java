/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AgencyDossierMonth implements Serializable {
    
    private AgencyDossierMonthData m1;
    private AgencyDossierMonthData m2;
    private AgencyDossierMonthData m3;
    private AgencyDossierMonthData m4;
    private AgencyDossierMonthData m5;
    private AgencyDossierMonthData m6;
    private AgencyDossierMonthData m7;
    private AgencyDossierMonthData m8;
    private AgencyDossierMonthData m9;
    private AgencyDossierMonthData m10;
    private AgencyDossierMonthData m11;
    private AgencyDossierMonthData m12;
}
