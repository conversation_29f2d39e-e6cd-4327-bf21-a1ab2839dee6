/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.lang.reflect.Type;

/**
 *
 * <AUTHOR>
 */
public class GsonHelper {

    public static final Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").create();

    public static <S extends Object> S copyObject(S s) {
        String jsonString = gson.toJson(s);
        return (S) gson.fromJson(jsonString, s.getClass());
    }

    public static <S extends Object, T extends Object> T copyObject(S s, Class<T> type) {
        String jsonString = gson.toJson(s);
        return gson.fromJson(jsonString, type);
    }

    public static <S extends Object> String getJson(S s) {
        return gson.toJson(s);
    }

}
