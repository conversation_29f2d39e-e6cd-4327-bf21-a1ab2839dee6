package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.dto.DossierAgencyDto;
import vn.vnpt.digo.reporter.dto.DossierCountingDto;
import vn.vnpt.digo.reporter.dto.DossierSectorDto;
import vn.vnpt.digo.reporter.dto.DossierSimpleDataDto;
import vn.vnpt.digo.reporter.pojo.*;

import java.util.ArrayList;
import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "dossierCounting")
public class DossierCounting {
  @Id
  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId id;

  @JsonSerialize(using = ToStringSerializer.class)
  private ObjectId previousDossierCountingId;
  private Integer year;

  private Integer month;

  private Integer day;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date countingDate;

  @JsonProperty("agency")
  private DossierAgencyDto agency;
  @JsonProperty("sector")
  private DossierSectorDto sector;

  private DossierCountingData countingData;

//  private ArrayList<DossierCountingDto> dossierDetail;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date createdDate;

  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
  private Date updatedDate;


//  public void parserDataDossierCountingDto(){
//    long inProgressAndOnTime = 0;
//    long inProgressAndOutOfDue = 0;
//    long completedEarly = 0;
//    long completedOnTime = 0;
//    long completedOutOfDue = 0;
//    long cancelled = 0;
//    long suspended = 0;
//    long onlineReceived = 0;
//    long directReceived = 0;
//
//    for (DossierCountingDto item : dossierDetail){
//
//      switch (item.getCode()){
//        case 0:  //inProgressAndOnTime
//          inProgressAndOnTime = item.getCount();
//          break;
//        case 1:  //inProgressAndOutOfDue
//          inProgressAndOutOfDue = item.getCount();
//          break;
//        case 2: //completedEarly
//          completedEarly = item.getCount();
//          break;
//        case 3: //completedOnTime
//          completedOnTime = item.getCount();
//          break;
//        case 4: //completedOutOfDue
//          completedOutOfDue = item.getCount();
//          break;
//        case 5: //Canceled
//          cancelled = item.getCount();
//          break;
//        case 6: //susppend
//          suspended = item.getCount();
//          break;
//        case 7: //online
//          onlineReceived = item.getCount();
//          break;
//        case 8: // direct
//          directReceived = item.getCount();
//          break;
//
//      }
//    }
//    long inProgress = inProgressAndOnTime+ inProgressAndOutOfDue;
//    long completed = completedEarly+ completedOnTime +completedOutOfDue;
//    long received = inProgress + completed +suspended +cancelled;
//    this.countingData =  new DossierCountingData(
//            received,
//            inProgress,
//            inProgressAndOnTime,
//            inProgressAndOutOfDue,
//            completed,
//            completedOnTime,
//            completedEarly,
//            completedOutOfDue,
//            onlineReceived,
//            directReceived,
//            cancelled,
//            suspended
//    );
//  }

}
