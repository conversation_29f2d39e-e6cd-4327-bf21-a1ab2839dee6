package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Agency implements Serializable{

   
    @JsonSerialize(using = ToStringSerializer.class)
    @Field("id")
    private ObjectId id;

    private String code;

    @NotNull
    private ArrayList<TranslateName> name;
    
    @NotNull
    @Field("parent")
    private Agency parent;

    @NotNull
    private ArrayList<Agency> ancestors;

    @NotNull
    private ArrayList<Tag> tag;

    @JsonIgnore
    private String agencyName;
    @JsonIgnore
    private String localCode;
    @JsonIgnore
    private String agencyParentName;
    
}
