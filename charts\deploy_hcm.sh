#!/bin/bash
# Auto re-deploy project script
# sh "/bin/bash ./charts/deploy.sh $BRANCH_NAME ${rancherUrl} ${TOKEN_RANCHER}"
# sh "/bin/bash ./charts/deploy.sh $BRANCH_NAME ${rancherUrl} ${nexus_registry}/${project}:${version} ${namespace} ${deployment}
# rancherUrl = "https://rancher/v3/project/c-29cgn:p-p88vd/workloads/deployment:${Namespace_K8S}:${Workload}"

rancherServers=(
'master|https://rancher-dev:8443/v3/project/c-ppb4p:p-28kmk/workload/deployment:orimx-module:orimx-search|token-cc8bz:z9qxv9mdk999pvfw2796znz877k85zhd6rz8pbc69knjl7z57ldl44'
'1.1.41|https://rancher4t.vnpt.vn/p/local:p-n66st/workload/deployment:applications-s4t:svc-padman|kubeconfig-user-28h78:wrpzw2qknqwh6885k897qjtx5ldf6mr24h9j6s45ngzgpt5zk9td8q'
)

branch="$1"
deployment="$5"
if [ -z $1 ]; then
	echo "Specify branch empty, no branch to deploy!"
else
	for i in "${rancherServers[@]}";do
		dBranch=$(echo $i | cut -d'|' -f1)
		# rancherUrl=$(echo $i | cut -d'|' -f2)
		rancherUrl="$2"
		# token=$(echo $i | cut -d'|' -f3)
		# token="$3"
		if [ $dBranch = $branch ]; then
			echo "Deploy branch $branch at $rancherUrl"
			# data="{\"annotations\":{\"cattle.io/timestamp\":\"$(date -u +'%Y-%m-%dT%H:%M:%SZ')\"}}"
			# data="{\"spec\":{\"template\":{\"spec\":{\"containers\":{\"image\":\"crelease.devops.bdg.vn:10141/applications-bdg-svc-basecat:1.1.39-20220620153522\"}}}}}"
			# curl -ik -X PUT $rancherUrl -H "Authorization: Bearer $token" -H 'content-type: application/json' -d "$data"
			kubectl config use-context localhcm
			kubectl set image deployment/$deployment $5=$3 --namespace=$4
			exit
		fi
	done
	echo "Branch $branch was not define rancher server to deploy!"
fi
