package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierCountingMonitoringDto implements Serializable {
  @JsonProperty("day")
  private String day;
  @JsonProperty("month")
  private String month;
  @JsonProperty("year")
  private String year;
  @JsonProperty("count")
  private long count;
}
