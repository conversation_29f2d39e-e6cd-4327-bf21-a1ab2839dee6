package vn.vnpt.digo.reporter.dto.qni;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SampleDataReportDto implements Serializable {
    private int receivedOnline;
    private int receivedDirect;
    private int received;
    private int receivedOld;
    private int unresolvedOnTime;
    private int unresolvedOverdue;
    private int resolvedEarly;
    private int resolvedOnTime;
    private int resolvedOverdue;
    private int withdraw;
    private int resolved;
    private int unresolved;
    private int direct;
    private int receivedPostal;
    private int receivedPublicPostal;
    private int receivedSmartphone;
    private int procedureUsed;
    private Agency agency;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Agency implements Serializable {
        private String id;
        private String name;
    }
}
