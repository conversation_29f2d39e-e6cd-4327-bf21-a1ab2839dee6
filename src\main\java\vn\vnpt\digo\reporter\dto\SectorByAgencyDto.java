package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.AgencySector;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SectorByAgencyDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId originId;

    private List<AgencySector> sector;
}
