package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusTotal implements Serializable {
    private long waitingReception = 0;

    private long inprogress = 0;

    private long completed = 0;

    private long canceled = 0;
}
