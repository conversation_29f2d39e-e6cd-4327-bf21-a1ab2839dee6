package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import com.mongodb.DBCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexOptions;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */

@ChangeLog(order = "2021-12-10-21-25")
public class FeatureStatisticsChangeLogs {

    private final Logger logger = LoggerFactory.getLogger(FeatureStatisticsChangeLogs.class);

    @ChangeSet(order = "2021-12-10-21-25", id = "FeatureStatisticsChangeLogs::create", author = "haimn")
    public void create(DB db) {
        DBCollection collection = db.getCollection("featureStatistics");
        if (collection == null) {
            db.createCollection("featureStatistics", new BasicDBObject());
        }
    }

    @ChangeSet(order = "2021-12-10-21-25", id = "FeatureStatisticsChangeLogs::createIndex", author = "haimn")
    public void createIndex(MongoDatabase db) {

        Document commonIndex = new Document();
        commonIndex.put("appCode", 1);
        commonIndex.put("deploymentId", 1);
        try {
            db.getCollection("featureStatistics").dropIndex(commonIndex);
        } catch (Exception ex) {
            logger.error("Index " + commonIndex + " don't exist");
        }
        db.getCollection("featureStatistics").createIndex(commonIndex, new IndexOptions().sparse(true).background(true));

        Document statisticsIndex = new Document();
        statisticsIndex.put("appCode", 1);
        statisticsIndex.put("deploymentId", 1);
        statisticsIndex.put("createdDate", 1);
        try {
            db.getCollection("featureStatistics").dropIndex(statisticsIndex);
        } catch (Exception ex) {
            logger.error("Index " + commonIndex + " don't exist");
        }
        db.getCollection("featureStatistics").createIndex(statisticsIndex, new IndexOptions().sparse(true).background(true));

        Document createdDateIndex = new Document();
        createdDateIndex.put("createdDate", 1);
        try {
            db.getCollection("featureStatistics").dropIndex(createdDateIndex);
        } catch (Exception ex) {
            logger.error("Index " + createdDateIndex + " don't exist");
        }
        db.getCollection("featureStatistics").createIndex(createdDateIndex, new IndexOptions().sparse(true).background(true));

    }

}
