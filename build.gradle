plugins {
    id "org.sonarqube" version "3.0"
    id 'org.springframework.boot' version '2.2.2.RELEASE'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'com.google.protobuf' version '0.9.4'    // gRPC
    id 'java'
}

ext['spring.version'] = '5.2.21.RELEASE'
ext['spring-framework.version'] = '5.2.21.RELEASE'

subprojects {
    sonarqube {
        properties {
            property "sonar.sources", "src"
        }
    }
}

// gRPC
protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.25.1"
    }
    generatedFilesBaseDir = "$projectDir/src/generated"
    clean {
        delete generatedFilesBaseDir
    }
    plugins {
        grpc {
            artifact = 'io.grpc:protoc-gen-grpc-java:1.65.0'
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
    }
}

sonarqube {
    properties {
        property "sonar.java.binaries", "build/classes/"
    }
}

sonarqube {
    properties {
        property "sonar.java.libraries", "src/main/resources/libs/*.jar"
    }
}


bootJar {
    baseName = 'svc-reporter'
    mainClassName = "vn.vnpt.digo.Application"
    version =  '1.0.0-beta.1'
}

group = 'vn.vnpt.digo'
sourceCompatibility = '11'
description = 'Digo Microservice :: Reporter'

repositories {
    mavenCentral()
    maven{url "https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/"}
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    //implementation 'org.springframework.boot:spring-boot-starter-data-rest'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-actuator', version: '2.7.5'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.cloud:spring-cloud-starter-stream-kafka:3.0.1.RELEASE'
    implementation 'com.github.dalet-oss:mongobee:1.0.4'
    implementation 'com.hazelcast:hazelcast-kubernetes:1.3'
    implementation 'com.hazelcast:hazelcast-spring'
    implementation 'org.apache.httpcomponents:httpclient:4.5.10'
    implementation 'org.springframework.security.oauth.boot:spring-security-oauth2-autoconfigure:2.1.5.RELEASE'
    implementation 'org.springframework.cloud:spring-cloud-starter-stream-kafka:3.0.1.RELEASE'
    implementation 'javax.xml.bind:jaxb-api'
    implementation 'com.google.code.gson:gson:2.7'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'io.springfox:springfox-swagger-ui:3.0.0'
    implementation 'io.springfox:springfox-swagger2:3.0.0'
    implementation 'io.springfox:springfox-spring-web:3.0.0'
    implementation 'io.springfox:springfox-oas:3.0.0'
    implementation 'io.swagger:swagger-annotations:1.5.21'
    implementation 'io.swagger:swagger-models:1.5.21'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    implementation 'org.freemarker:freemarker:2.3.30'
    implementation 'net.sf.jasperreports:jasperreports:6.16.0'
    implementation 'net.sf.jasperreports:jasperreports-fonts:6.0.0'
    implementation 'org.apache.commons:commons-collections4:4.1'
    implementation 'org.springframework.boot:spring-boot-configuration-processor:2.4.3'
    implementation 'org.springframework:spring-aspects:5.3.4'
    implementation 'io.jsonwebtoken:jjwt:0.9.1'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'commons-io:commons-io:2.11.0'
    implementation group: 'org.jxls', name: 'jxls-poi', version: '2.12.0'
    implementation(files('src\\main\\resources\\libs\\wordToPdf.jar'))
    implementation 'com.google.apis:google-api-services-oauth2:v2-rev20200213-2.0.0'
    implementation 'com.google.analytics:google-analytics-data:0.41.0'
    implementation 'io.grpc:grpc-netty-shaded'      // gRPC server
    implementation 'io.grpc:grpc-protobuf'          // gRPC server
    implementation 'io.grpc:grpc-stub'              // gRPC server
    implementation 'net.devh:grpc-spring-boot-starter:2.15.0.RELEASE'   // gRPC
    implementation 'io.minio:minio:7.0.2'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    implementation platform('com.amazonaws:aws-java-sdk-bom:1.11.415')
    implementation 'com.amazonaws:aws-java-sdk-s3'
    implementation 'org.json:json:20211205'
    implementation 'net.javacrumbs.shedlock:shedlock-spring:4.29.0'
    implementation 'net.javacrumbs.shedlock:shedlock-provider-mongo:4.29.0'
}
