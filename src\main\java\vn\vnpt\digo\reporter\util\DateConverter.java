package vn.vnpt.digo.reporter.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.properties.DigoProperties;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

@Component
public class DateConverter {

    @Autowired
    private Translator translator;

    public String convertStringDateToDateFormatExcel(String dateStr){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        try {
            Date date = dateFormat.parse(dateStr);
            LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            return translator.toLocale("lang.word.day", new String[]{localDate.getDayOfMonth() + "", localDate.getMonthValue() + "", localDate.getYear() + ""});
        }catch (Exception ex){
            LoggerFactory.getLogger(DateConverter.class).error("DIGO: Can not convert string date to date format excel", ex);
        }
        return StringUtils.EMPTY;
    }
}
