package vn.vnpt.digo.reporter.service;

import java.util.List;
import java.util.Objects;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.WaterCustomer;
import vn.vnpt.digo.reporter.dto.GetCustomerCodeByUserIdDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.dto.WaterCustomerInputDto;
import vn.vnpt.digo.reporter.repository.WaterCustomerRepository;

@Service
public class WaterCustomerService {

    @Autowired
    private WaterCustomerRepository waterCustomerRepository;

    public PostResponseDto insertOrUpdateWaterCustomer(WaterCustomerInputDto waterCustomerInputDto) {
        WaterCustomer customerMapping = new WaterCustomer();

        try {
            customerMapping = waterCustomerRepository.findUserIdAndCustomerCode(waterCustomerInputDto.getUserId(), waterCustomerInputDto.getCustomerCode());
            if (Objects.equals(customerMapping, null)) {
                customerMapping = new WaterCustomer(waterCustomerInputDto.getUserId(), waterCustomerInputDto.getCustomerCode());
                customerMapping = waterCustomerRepository.save(customerMapping);
            } else {
                customerMapping.setUpdateDate();
                customerMapping = waterCustomerRepository.save(customerMapping);
            }
        } catch (Exception e) {

        }
        return new PostResponseDto(customerMapping.getId());
    }

    public List<GetCustomerCodeByUserIdDto> getCustomerCodeByUserId(ObjectId userId) {
        List<GetCustomerCodeByUserIdDto> customerCode = waterCustomerRepository.getCustomerCodeByUserId(userId);

        return customerCode;
    }
}
