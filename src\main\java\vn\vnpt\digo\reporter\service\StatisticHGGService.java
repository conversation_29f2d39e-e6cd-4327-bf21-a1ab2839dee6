package vn.vnpt.digo.reporter.service;

import com.google.analytics.data.v1beta.*;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.oauth2.GoogleCredentials;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;

@Service
public class StatisticHGGService {

    @Value("${analytics.hhg}")
    private String jsonSecret;

    public Object analyticsReport(String propertyId, String metrics, String startDate, String endDate, boolean wayDiff) {
        try {
            if (jsonSecret == null || jsonSecret.isEmpty()) {
                Map<String, Object> resultObject = new HashMap<>();
                resultObject.put("err", "Vui lòng cấu hình tài khoản truy cập");
                return resultObject;
            }
            // create new stream tu text json
            InputStream stream = new ByteArrayInputStream(jsonSecret.getBytes());
            // Lay du lieu tu google analytics
            GoogleCredentials credentials = GoogleCredentials.fromStream(stream);
            // load the credentials
            BetaAnalyticsDataSettings betaAnalyticsDataSettings =
                    BetaAnalyticsDataSettings.newBuilder()
                            .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                            .build();
            // Create the Analytics Data client.
            try (BetaAnalyticsDataClient analyticsData = BetaAnalyticsDataClient.create(betaAnalyticsDataSettings)) {
                //The number of app screens or web pages your users viewed. Repeated views of a single page or screen are counted. (screen_view + page_view events).
                // create date string from 1st day of week
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                Date dateWeek = calendar.getTime();
                String startDateWeek = dateWeek.getYear() + 1900 + "-" + (dateWeek.getMonth() + 1) + "-" + dateWeek.getDate();
                // create date string from 1st day of month
                Date date = new Date();
                String startDateMonth = date.getYear() + 1900 + "-" + (date.getMonth() + 1) + "-1";
                int total = 0;
                int totalMonth = 0;
                int totalWeek = 0;
                int totalDay = 0;
                if (!wayDiff) {
                    RunReportRequest request =
                            RunReportRequest.newBuilder()
                                    .setProperty("properties/" + propertyId)
                                    .addMetrics(Metric.newBuilder().setName(metrics))
                                    .addDateRanges(DateRange.newBuilder().setStartDate(startDate).setEndDate(endDate))
                                    .addDateRanges(DateRange.newBuilder().setStartDate(startDateMonth).setEndDate("today"))
                                    .addDateRanges(DateRange.newBuilder().setStartDate(startDateWeek).setEndDate("today"))
                                    .addDateRanges(DateRange.newBuilder().setStartDate("today").setEndDate("today"))
                                    .build();
                    // Make the request.
                    RunReportResponse response = analyticsData.runReport(request);
                    var result = response.getRowsList();
                    try {
                        total = Integer.parseInt(result.get(0).getMetricValues(0).getValue());
                    } catch (Exception e) {
                    }
                    try {
                        totalMonth = Integer.parseInt(result.get(1).getMetricValues(0).getValue());
                    } catch (Exception e) {
                    }
                    try {
                        totalWeek = Integer.parseInt(result.get(2).getMetricValues(0).getValue());
                    } catch (Exception e) {
                    }
                    try {
                        totalDay = Integer.parseInt(result.get(3).getMetricValues(0).getValue());
                    } catch (Exception e) {
                    }
                } else {
                    RunReportRequest request =
                            RunReportRequest.newBuilder()
                                    .setProperty("properties/" + propertyId)
                                    .addMetrics(Metric.newBuilder().setName(metrics))
                                    .addDateRanges(DateRange.newBuilder().setStartDate(startDate).setEndDate(endDate))
                                    .build();
                    RunReportResponse response = analyticsData.runReport(request);
                    for (Row row : response.getRowsList()) {
                        total += Integer.parseInt(row.getMetricValues(0).getValue());
                    }
                    //make request to get total day
                    RunReportRequest requestDay =
                            RunReportRequest.newBuilder()
                                    .setProperty("properties/" + propertyId)
                                    .addMetrics(Metric.newBuilder().setName(metrics))
                                    .addDateRanges(DateRange.newBuilder().setStartDate("today").setEndDate("today"))
                                    .build();
                    RunReportResponse responseDay = analyticsData.runReport(requestDay);
                    for (Row row : responseDay.getRowsList()) {
                        totalDay += Integer.parseInt(row.getMetricValues(0).getValue());
                    }
                    // make request to get total week
                    RunReportRequest requestWeek =
                            RunReportRequest.newBuilder()
                                    .setProperty("properties/" + propertyId)
                                    .addMetrics(Metric.newBuilder().setName(metrics))
                                    .addDateRanges(DateRange.newBuilder().setStartDate(startDateWeek).setEndDate("today"))
                                    .build();
                    RunReportResponse responseWeek = analyticsData.runReport(requestWeek);
                    for (Row row : responseWeek.getRowsList()) {
                        totalWeek += Integer.parseInt(row.getMetricValues(0).getValue());
                    }
                    // make request to get total month
                    RunReportRequest requestMonth =
                            RunReportRequest.newBuilder()
                                    .setProperty("properties/" + propertyId)
                                    .addMetrics(Metric.newBuilder().setName(metrics))
                                    .addDateRanges(DateRange.newBuilder().setStartDate(startDateMonth).setEndDate("today"))
                                    .build();
                    RunReportResponse responseMonth = analyticsData.runReport(requestMonth);
                    for (Row row : responseMonth.getRowsList()) {
                        totalMonth += Integer.parseInt(row.getMetricValues(0).getValue());
                    }
                }

//               
                // make request to get total online
                RunRealtimeReportRequest requestOnline =
                        RunRealtimeReportRequest.newBuilder()
                                .setProperty("properties/" + propertyId)
                                .addMetrics(Metric.newBuilder().setName("activeUsers"))
                                .build();
                RunRealtimeReportResponse responseOnline = analyticsData.runRealtimeReport(requestOnline);
                int totalOnline = 0;
                for (Row row : responseOnline.getRowsList()) {
                    totalOnline += Integer.parseInt(row.getMetricValues(0).getValue());
                }
                // create result object
                Map<String, Object> resultObject = new HashMap<>();
                resultObject.put("total", total);
                resultObject.put("totalDay", totalDay);
                resultObject.put("totalWeek", totalWeek);
                resultObject.put("totalMonth", totalMonth);
                resultObject.put("totalOnline", totalOnline);
                return resultObject;
            }
        } catch (Exception e) {
            Map<String, Object> resultObject = new HashMap<>();
            resultObject.put("err", e);
            return resultObject;
        }
    }


    public Object analyticsReportDay(String propertyId, String dimension, String metrics, String startDate, String endDate) {
        try {
            if (jsonSecret == null || jsonSecret.isEmpty()) {
                Map<String, Object> resultObject = new HashMap<>();
                resultObject.put("err", "Vui lòng cấu hình tài khoản truy cập");
                return resultObject;
            }
            // create new stream tu text json
            InputStream stream = new ByteArrayInputStream(jsonSecret.getBytes());
            // Lay du lieu tu google analytics
            GoogleCredentials credentials = GoogleCredentials.fromStream(stream);
            // load the credentials
            BetaAnalyticsDataSettings betaAnalyticsDataSettings =
                    BetaAnalyticsDataSettings.newBuilder()
                            .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                            .build();
            // Create the Analytics Data client.
            try (BetaAnalyticsDataClient analyticsData = BetaAnalyticsDataClient.create(betaAnalyticsDataSettings)) {
                //The number of app screens or web pages your users viewed. Repeated views of a single page or screen are counted. (screen_view + page_view events).
                RunReportRequest request =
                        RunReportRequest.newBuilder()
                                .setProperty("properties/" + propertyId)
                                .addDimensions(Dimension.newBuilder().setName(dimension))
                                .addMetrics(Metric.newBuilder().setName(metrics))
                                .addDateRanges(DateRange.newBuilder().setStartDate(startDate).setEndDate(endDate))
                                .addOrderBys(OrderBy.newBuilder().setDimension(OrderBy.DimensionOrderBy.newBuilder().setDimensionName(dimension)).build())
                                .build();
                // Make the request.
                RunReportResponse response = analyticsData.runReport(request);
                List<Map<String, Object>> resultObject = new ArrayList<>();
                System.out.printf(String.valueOf(response));
                for (Row row : response.getRowsList()) {
                    Map<String, Object> object = new HashMap<>();
                    object.put("total", Integer.parseInt(row.getMetricValues(0).getValue()));
                    object.put("date", row.getDimensionValues(0).getValue());
                    resultObject.add(object);
                }
                return resultObject;
            }
        } catch (Exception e) {
            Map<String, Object> resultObject = new HashMap<>();
            resultObject.put("err", e);
            return resultObject;
        }
    }
}
