package vn.vnpt.digo.reporter.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.reporter.pojo.AgencyName;
import vn.vnpt.digo.reporter.pojo.AgencySector;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.reporter.pojo.TagAgencyKTM;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "procedureSectorAgency")
public class GetAgencyQuantityByProcedureSectorDto implements Serializable {
    
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonProperty("agencyId")
    private ObjectId originId;

    @JsonIgnore
    private List<AgencyName> name;

    private String agencyName;

    private List<AgencySector> sector;

    private String parentId;

    private List<TagAgencyKTM> tagAgency;


    public void setAgencyName(short langId) {
        if(name != null) {
            name.forEach(trans -> {
                if (trans.getLanguageId().equals(langId)) {
                    this.agencyName = trans.getName();
                }
            });
        }
    }
}
