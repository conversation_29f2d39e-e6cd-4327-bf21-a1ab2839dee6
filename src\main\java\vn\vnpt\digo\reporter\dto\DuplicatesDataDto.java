package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DuplicatesDataDto implements Serializable {

  @Field("_id")
  private DuplicateIdDto id;

  @Field("dups")
  @JsonSerialize(using = ToStringSerializer.class)
  private ArrayList<ObjectId> dups;

  @JsonProperty("count")
  private String count;
}
