package vn.vnpt.digo.reporter.util;

import java.util.Map;
import javax.servlet.ServletRequest;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.web.servlet.mvc.method.annotation.ExtendedServletRequestDataBinder;

public class ParamNameDataBinder extends ExtendedServletRequestDataBinder {

    private final Map<String, String> paramMappings;

    public ParamNameDataBinder(Object target, String objectName, Map<String, String> paramMappings) {
        super(target, objectName);
        this.paramMappings = paramMappings;
    }

    @Override
    protected void addBindValues(MutablePropertyValues mutablePropertyValues, ServletRequest request) {
        super.addBindValues(mutablePropertyValues, request);
        paramMappings.entrySet().forEach((entry) -> {
            String paramName = entry.getKey();
            String fieldName = entry.getValue();
            if (mutablePropertyValues.contains(paramName)) {
                mutablePropertyValues.add(fieldName, mutablePropertyValues.getPropertyValue(paramName).getValue());
            }
        });
    }

}