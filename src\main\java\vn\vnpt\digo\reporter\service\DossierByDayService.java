/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.service;

import static com.google.common.base.Strings.isNullOrEmpty;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.*;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.pojo.*;
import vn.vnpt.digo.reporter.repository.DossierByDayRepository;
import vn.vnpt.digo.reporter.util.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.cache.annotation.Cacheable;

import static java.util.stream.Collectors.groupingBy;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import org.springframework.http.HttpEntity;

import vn.vnpt.digo.reporter.document.DossierByDay;
import vn.vnpt.digo.reporter.exception.DigoHttpException;

@Service
public class DossierByDayService {

    @Autowired
    private DossierByDayRepository dossierByDayRepository;

    @Autowired
    private Translator translator;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DateConverter dateConverter;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private DigoRestTemplate digoRestTemplate;

    @Value(value = Constant.LOCATION_PROVINCE_6A)
    private Resource resourceTemplateProvince6A;

    @Value(value = Constant.LOCATION_PROVINCE_6B)
    private Resource resourceTemplateProvince6B;

    @Value(value = Constant.LOCATION_PROVINCE_6C)
    private Resource resourceTemplateProvince6C;

    @Value(value = Constant.LOCATION_PROVINCE_6B_2020)
    private Resource resourceTemplateProvince6B2020;

    @Value(value = Constant.LOCATION_PROVINCE_6C_2020)
    private Resource resourceTemplateProvince6C2020;

    @Value("${digo.microservice.gateway-url}")
    private String gatewayURL;

    Logger logger = org.slf4j.LoggerFactory.getLogger(DossierByDayService.class);


    public Slice<GetDossierByDay> getReportDossierByDay(Pageable pageable, String fromDate, String toDate, String sectorIdStr, String agencyIdIdStr, String procedureLevelStr, String spect) throws ParseException {
        Slice<GetDossierByDay> dossierByDay;
        Slice<GetDossierByDay> listProcedure;
        ObjectId sectorId = (sectorIdStr != null && sectorIdStr != "") ? new ObjectId(sectorIdStr) : null;
        ObjectId agencyId = (agencyIdIdStr != null && agencyIdIdStr != "") ? new ObjectId(agencyIdIdStr) : null;
        ObjectId procedureLevel = (procedureLevelStr != null && procedureLevelStr != "") ? new ObjectId(procedureLevelStr) : null;

        String startDate = (fromDate != null && fromDate != "") ? new String(fromDate) : null;
        String endDate = (toDate != null && toDate != "") ? new String(toDate) : null;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        Query query = new Query();
        query.with(pageable);
        Criteria aCriteria = new Criteria();
        Aggregation agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        agencyId != null ? Criteria.where("").orOperator(Criteria.where("agency.id").is(agencyId), Criteria.where("agency.parent.id").is(agencyId), Criteria.where("agency.ancestors.id").is(agencyId)): aCriteria,
                        sectorId != null ? Criteria.where("sector.id").is(sectorId): aCriteria,
                        procedureLevel != null ? Criteria.where("procedureLevel.id").is(procedureLevel): aCriteria,
                        startDate != null ? Criteria.where("updatedDate").gte(df.parse(startDate)): aCriteria,
                        endDate != null ? Criteria.where("updatedDate").lte(df.parse(endDate)): aCriteria
                )),
                group("procedure.id")
                        .first("procedure").as("procedure")
                        .first("year").as("year")
                        .first("month").as("month")
                        .first("day").as("day")
                        .first("agency").as("agency")
                        .first("agencyLevel").as("agencyLevel")
                        .first("sector").as("sector")
                        .first("procedureLevel").as("procedureLevel")
                        .first("deploymentId").as("deploymentId")
                        .first("updatedDate").as("updatedDate")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("receivedDirect").as("receivedDirect")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved"),
                sort(DESC, "updatedDate"),
                skip(pageable.getPageNumber() * pageable.getPageSize()),
                limit(pageable.getPageSize())
        );
        Aggregation agg2 = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        agencyId != null ? Criteria.where("agency.id").is(agencyId): aCriteria,
                        sectorId != null ? Criteria.where("sector.id").is(sectorId): aCriteria,
                        procedureLevel != null ? Criteria.where("procedureLevel.id").is(procedureLevel): aCriteria,
                        startDate != null ? Criteria.where("updatedDate").gte(df.parse(startDate)): aCriteria,
                        endDate != null ? Criteria.where("updatedDate").lte(df.parse(endDate)): aCriteria
                )),
                group("procedure.id")
                        .first("procedure").as("procedure")
        );
        AggregationResults<GetDossierByDay> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class);
        List<GetDossierByDay> resultList = results.getMappedResults();

        List<GetDossierByDay> resultList2 = mongoTemplate.aggregate(agg2, "dossierByDay", GetDossierByDay.class).getMappedResults();
        System.out.println("----------Dem so---------");
        System.out.println(resultList2.size());
//        System.out.println("Tess thong ke");
//        for(int l=0; l < resultList.size(); l++){
//            System.out.println(resultList.get(l).getProcedure().getId());
//        }
        System.out.println("--------Tess thong ke----------");
        System.out.println(resultList.size());
        if (spect.equals("page")) {
            Page<GetDossierByDay> page = new PageImpl<GetDossierByDay>(resultList, pageable, resultList2.size());
            return page;
        } else {
            Slice<GetDossierByDay> slice = new SliceImpl(resultList, pageable, true);
            return slice;
        }
    }

    public Slice<GetDossierByDay> getReportDossierByDayProcedure(Pageable pageable, String fromDate, String toDate, String sectorIdStr,
                                                                 String agencyIdIdStr,List<ObjectId> arrAgencyId, String procedureLevelStr,  String keyword, Boolean procedureCheck, String statusProcedure, String spec) throws ParseException,JSONException {
        Criteria criteria = this.reportDossierByDayProcedureCriteria(fromDate, toDate, sectorIdStr, agencyIdIdStr,arrAgencyId, procedureLevelStr, keyword, procedureCheck, statusProcedure);
        Aggregation agg = (Aggregation) newAggregation(
                match(criteria),
                group("procedure.id", "sector", "agency", "procedureLevel")
                        .first("procedure").as("procedure")
                        .first("year").as("year")
                        .first("month").as("month")
                        .first("day").as("day")
                        .first("agency").as("agency")
                        .first("agencyLevel").as("agencyLevel")
                        .first("sector").as("sector")
                        .first("procedureLevel").as("procedureLevel")
                        .first("deploymentId").as("deploymentId")
                        .first("updatedDate").as("updatedDate")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("receivedDirect").as("receivedDirect")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved"),
                sort(DESC, "updatedDate"),
                skip(pageable.getPageNumber() * pageable.getPageSize()),
                limit(pageable.getPageSize())
        );

        AggregationResults<GetDossierByDay> results = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class);
        List<GetDossierByDay> resultList = results.getMappedResults();

        // total
        Aggregation aggTotal = (Aggregation) newAggregation(
                match(criteria),
                group("procedure.id", "sector", "agency", "procedureLevel")
                        .first("procedure").as("procedure")
        );
        List<GetDossierByDay> resultListTotal = mongoTemplate.aggregate(aggTotal, "dossierByDay", GetDossierByDay.class).getMappedResults();

        List<GetDossierByDay> resultListGetDossierDay = mapNameForGetDossierByDay(resultList);
        if (spec.equals("page")) {
            Page<GetDossierByDay> page = new PageImpl<GetDossierByDay>(resultListGetDossierDay, pageable, resultListTotal.size());
            return page;
        } else {
            Slice<GetDossierByDay> slice = new SliceImpl(resultListGetDossierDay, pageable, true);
            return slice;
        }
    }

    public List<GetDossierByDay> getReportDossierByDayProcedureAll(String fromDate, String toDate, String sectorIdStr,
                                                                   String agencyIdIdStr,List<ObjectId> arrAgencyId, String procedureLevelStr,  String keyword, Boolean procedureCheck, String statusProcedure) throws ParseException,JSONException {
        Criteria criteria = this.reportDossierByDayProcedureCriteria(fromDate, toDate, sectorIdStr, agencyIdIdStr,arrAgencyId, procedureLevelStr, keyword, procedureCheck, statusProcedure);
        Aggregation agg = (Aggregation) newAggregation(
                match(criteria),
                group("procedure.id", "sector", "agency", "procedureLevel")
                        .first("procedure").as("procedure")
                        .first("year").as("year")
                        .first("month").as("month")
                        .first("day").as("day")
                        .first("agency").as("agency")
                        .first("agencyLevel").as("agencyLevel")
                        .first("sector").as("sector")
                        .first("procedureLevel").as("procedureLevel")
                        .first("deploymentId").as("deploymentId")
                        .first("updatedDate").as("updatedDate")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("receivedDirect").as("receivedDirect")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved"),
                sort(DESC, "updatedDate")
        );

        AggregationResults<GetDossierByDay> results = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class);
        List<GetDossierByDay> resultList = results.getMappedResults();
        List<GetDossierByDay> resultListGetDossierDay = mapNameForGetDossierByDay(resultList);
        return resultListGetDossierDay;
    }

    private List<GetDossierByDay> mapNameForGetDossierByDay(List<GetDossierByDay> resultList){
        List<GetDossierByDay> resultListGetDossierDay = new ArrayList<>();
        for(GetDossierByDay item: resultList){
            AgencyDossierByDay agency = item.getAgency();
            agency.setNameAgencyData(translator.getCurrentLocaleId());
            item.setAgency(agency);

            AgencyDossierByDay agencyLevel = item.getAgencyLevel();
            agencyLevel.setNameAgencyData(translator.getCurrentLocaleId());
            item.setAgencyLevel(agencyLevel);

            SectorDossierByDay sector = item.getSector();
            sector.setSectorName(translator.getCurrentLocaleId());
            item.setSector(sector);

            ProcedureLevelDossierByDay procedureLevelDossierByDay = item.getProcedureLevel();
            procedureLevelDossierByDay.setProcedureLevelName(translator.getCurrentLocaleId());
            item.setProcedureLevel(procedureLevelDossierByDay);

            ProcedureDossierByDay procedureDossierByDay = item.getProcedure();
            procedureDossierByDay.setProcedureName(translator.getCurrentLocaleId());

            item.setProcedure(procedureDossierByDay);

            resultListGetDossierDay.add(item);
        }
        return resultListGetDossierDay;
    }
    public List<ObjectId> getListAgencyShowId(List<ObjectId> agencyIds) throws JSONException {
        List<ObjectId> listAgencyShowId = new ArrayList<>();
        String token = Context.getJwtAuthenticationTokenValue();
        if (token == null) {
            token = MicroserviceExchange.getToken().getAccessToken();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.setBearerAuth(token);
        net.minidev.json.JSONObject json = new net.minidev.json.JSONObject();
        HttpEntity<String> request = new HttpEntity<>(json.toJSONString(), headers);
        List<String> listAgencySearch = new ArrayList();
        // Get agencys
        String path = "agency/--by-parent-agency";
        int checkExistSubAgency = 0;
        if (Objects.nonNull(agencyIds) && !agencyIds.isEmpty()) {
            String s = "";
            for (ObjectId a : agencyIds) {
                s += a.toString() + ",";
            }
            path += "?arr-id=" + s.substring(0, s.length() - 1);
            checkExistSubAgency = 1;
        } else if (Objects.nonNull(agencyIds)) {
            path += "?arr-id=" + agencyIds;
        }
        String getAgencyUrl = microservice.basedataUri(path).toUriString();
        ResponseEntity<String> response = restTemplate.exchange(getAgencyUrl, HttpMethod.GET, request, String.class);
        String agencyJson = response.getBody();
        JSONArray agencyObj = new JSONArray(agencyJson);
        if (checkExistSubAgency == 1) {
            listAgencyShowId.addAll(agencyIds);
        } else {
            for (int i = 0; i < agencyObj.length(); i++) {
                ObjectId agencyIdOj = new ObjectId(agencyObj.getJSONObject(i).get("id").toString());
                listAgencyShowId.add(agencyIdOj);
            }
        }

        return listAgencyShowId;
    }
    private Criteria reportDossierByDayProcedureCriteria(String fromDate, String toDate, String sectorIdStr,
                                                         String agencyIdIdStr,List<ObjectId> arrAgencyId, String procedureLevelStr,  String keyword, Boolean procedureCheck, String statusProcedure) throws ParseException,JSONException {
        ObjectId sectorId = (sectorIdStr != null && sectorIdStr != "") ? new ObjectId(sectorIdStr) : null;
        ObjectId agencyId = (agencyIdIdStr != null && agencyIdIdStr != "") ? new ObjectId(agencyIdIdStr) : null;
        ObjectId procedureLevel = (procedureLevelStr != null && procedureLevelStr != "") ? new ObjectId(procedureLevelStr) : null;

        if (Objects.nonNull(arrAgencyId) && !arrAgencyId.isEmpty()) {
            arrAgencyId = this.getListAgencyShowId(arrAgencyId);
        }

        String startDate = (fromDate != null && fromDate != "") ? new String(fromDate) : null;
        String endDate = (toDate != null && toDate != "") ? new String(toDate) : null;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        Criteria criteria = new Criteria();
        ObjectId deploymentId = Context.getDeploymentId();
        // List query param
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("deploymentId").is(deploymentId));

        if (Objects.nonNull(arrAgencyId) && !arrAgencyId.isEmpty()) {
            Criteria criteriaAgency = Criteria.where("").orOperator(Criteria.where("agency.id").in(arrAgencyId),
                    Criteria.where("agency.parent.id").in(arrAgencyId),
                    Criteria.where("agency.ancestors.id").in(arrAgencyId));
            criteriaList.add(criteriaAgency);
        }
        else if (Objects.nonNull(agencyId)) {
            Criteria criteriaAgency = Criteria.where("").orOperator(Criteria.where("agency.id").is(agencyId), Criteria.where("agency.parent.id").is(agencyId), Criteria.where("agency.ancestors.id").is(agencyId));
            criteriaList.add(criteriaAgency);
        }

        if (Objects.nonNull(sectorId)) {
            criteriaList.add(Criteria.where("sector.id").is(sectorId));
        }
        if (Objects.nonNull(procedureLevel)) {
            criteriaList.add(Criteria.where("procedureLevel.id").is(procedureLevel));
        }
        if (Objects.nonNull(startDate)) {
            criteriaList.add(Criteria.where("updatedDate").gte(df.parse(startDate)));
        }
        if (Objects.nonNull(endDate)) {
            criteriaList.add(Criteria.where("updatedDate").lte(df.parse(endDate)));
        }
        if(Objects.nonNull(keyword) && !keyword.isEmpty()){
            Criteria criteriaNameLang = Criteria.where("languageId").is(translator.getCurrentLocaleId())
                    .and("name").regex(keyword);
            Criteria criteriaName = new Criteria();

            List<Criteria> criteriaListSearchKeyword = new ArrayList<>();
            Criteria criteriaNameProcedure = Criteria.where("procedure.translate").elemMatch(criteriaNameLang);
            criteriaListSearchKeyword.add(criteriaNameProcedure);

            Criteria criteriaNameSector = Criteria.where("sector.name").elemMatch(criteriaNameLang);
            criteriaListSearchKeyword.add(criteriaNameSector);

            Criteria criteriaNameAgency = Criteria.where("agency.name").elemMatch(criteriaNameLang);
            criteriaListSearchKeyword.add(criteriaNameAgency);

            Criteria criteriaNameProcedureLevel = Criteria.where("procedureLevel.name").elemMatch(criteriaNameLang);
            criteriaListSearchKeyword.add(criteriaNameProcedureLevel);

            Criteria criteriaProcedureCode = Criteria.where("procedure.code").regex(keyword);
            criteriaListSearchKeyword.add(criteriaProcedureCode);

            criteriaName = criteriaName.orOperator(criteriaListSearchKeyword.toArray(new Criteria[0]));

            criteriaList.add(criteriaName);
        }
        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        return criteria;
    }

//    public Slice<GetDossierByDay> getReportDossierByDay(Pageable pageable, String fromDate, String toDate, String sectorIdStr, String agencyIdIdStr, String procedureLevelStr, String spect) throws ParseException {
//        Slice<GetDossierByDay> dossierByDay;
//        Slice<GetDossierByDay> listProcedure;
//        ObjectId sectorId = (sectorIdStr != null && sectorIdStr != "") ? new ObjectId(sectorIdStr) : null;
//        ObjectId agencyId = (agencyIdIdStr != null && agencyIdIdStr != "") ? new ObjectId(agencyIdIdStr) : null;
//        ObjectId procedureLevel = (procedureLevelStr != null && procedureLevelStr != "") ? new ObjectId(procedureLevelStr) : null;
//
//        String startDate = (fromDate != null && fromDate != "") ? new String(fromDate) : null;
//        String endDate = (toDate != null && toDate != "") ? new String(toDate) : null;
//        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
//        if (spect.equals("page")) {
//            listProcedure = dossierByDayRepository.getPageListProcedure(PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("updateDate").descending()), sectorId, agencyId, procedureLevel);
//            for (GetDossierByDay item : listProcedure.getContent()) {
//                if (startDate == null || endDate == null) {
//                    dossierByDay = dossierByDayRepository.getPageReportDossierWithoutDay(item.getProcedure().getId(), item.getSector().getId(), item.getAgency().getId(), item.getProcedureLevel().getId(), PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("updateDate").descending()));
//                } else {
//                    dossierByDay = dossierByDayRepository.getPageReportDossierByDay(df.parse(startDate), df.parse(endDate), item.getProcedure().getId(), item.getSector().getId(), item.getAgency().getId(), item.getProcedureLevel().getId(), PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("updateDate").descending()));
//                }
//                Integer appliedOnline = 0;
//                Integer received = 0;
//                Integer receivedOnline = 0;
//                Integer receivedDirect = 0;
//                Integer resolved = 0;
//                Integer resolvedEarly = 0;
//                Integer resolvedOverdue = 0;
//                Integer cancelled = 0;
//                Integer deleted = 0;
//                Integer suspended = 0;
//                Integer total = 0;
//                for (GetDossierByDay subItem : dossierByDay.getContent()) {
//                    appliedOnline = appliedOnline + subItem.getAppliedOnline();
//                    received = received + subItem.getReceived();
//                    receivedOnline = receivedOnline + subItem.getReceivedOnline();
//                    receivedDirect += subItem.getReceivedDirect();
//                    resolved += subItem.getResolved();
//                    resolvedEarly += subItem.getResolvedEarly();
//                    resolvedOverdue += subItem.getResolvedOverdue();
//                    cancelled += subItem.getCancelled();
//                    deleted += subItem.getDeleted();
//                    suspended += subItem.getSuspended();
//                }
//                item.setAppliedOnline(appliedOnline);
//                item.setReceived(received);
//                item.setReceivedOnline(receivedOnline);
//                item.setReceivedDirect(receivedDirect);
//                item.setResolved(resolved);
//                item.setResolvedEarly(resolvedEarly);
//                item.setResolvedOverdue(resolvedOverdue);
//                item.setCancelled(cancelled);
//                item.setDeleted(deleted);
//                item.setSuspended(suspended);
//
//                if (!isEmpty(item.getAgency())) {
//                    AgencyDossierByDay newAgency = new AgencyDossierByDay();
//                    newAgency.setId(item.getAgency().getId());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getAgency().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newAgency.setName(newName);
//                    item.setAgency(newAgency);
//                }
//
//                if (item.getAgencyLevel().getId() != null) {
//                    AgencyDossierByDay newAgencyLevel = new AgencyDossierByDay();
//                    newAgencyLevel.setId(item.getAgency().getId());
//                    ArrayList<NameDossierByDay> newNameLevel = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getAgencyLevel().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newNameLevel.add(name);
//                        }
//                    }
//                    newAgencyLevel.setName(newNameLevel);
//                    item.setAgencyLevel(newAgencyLevel);
//                }
//
//                if (!isEmpty(item.getSector())) {
//                    SectorDossierByDay newSector = new SectorDossierByDay();
//                    newSector.setId(item.getSector().getId());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getSector().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newSector.setName(newName);
//                    item.setSector(newSector);
//                }
//
//                if (item.getProcedureLevel().getId() != null) {
//                    ProcedureLevelDossierByDay newProcedureLevel = new ProcedureLevelDossierByDay();
//                    newProcedureLevel.setId(item.getProcedureLevel().getId());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getProcedureLevel().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newProcedureLevel.setName(newName);
//                    item.setProcedureLevel(newProcedureLevel);
//                }
//
//                if (!isEmpty(item.getProcedure())) {
//                    ProcedureDossierByDay newProcedure = new ProcedureDossierByDay();
//                    newProcedure.setId(item.getProcedure().getId());
//                    newProcedure.setCode(item.getProcedure().getCode());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getProcedure().getTranslate()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newProcedure.setTranslate(newName);
//                    item.setProcedure(newProcedure);
//                }
//
//            }
//
//        } else {
//            listProcedure = dossierByDayRepository.getPageListProcedure(PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("updateDate").descending()), sectorId, agencyId, procedureLevel);
//            for (GetDossierByDay item : listProcedure.getContent()) {
//                if (startDate == null || endDate == null) {
//                    dossierByDay = dossierByDayRepository.getPageReportDossierWithoutDay(item.getProcedure().getId(), item.getSector().getId(), item.getAgency().getId(), item.getProcedureLevel().getId(), PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("updateDate").descending()));
//                } else {
//                    dossierByDay = dossierByDayRepository.getPageReportDossierByDay(df.parse(startDate), df.parse(endDate), item.getProcedure().getId(), item.getSector().getId(), item.getAgency().getId(), item.getProcedureLevel().getId(), PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("updateDate").descending()));
//                }
//                Integer appliedOnline = 0;
//                Integer received = 0;
//                Integer receivedOnline = 0;
//                Integer receivedDirect = 0;
//                Integer resolved = 0;
//                Integer resolvedEarly = 0;
//                Integer resolvedOverdue = 0;
//                Integer cancelled = 0;
//                Integer deleted = 0;
//                Integer suspended = 0;
//                for (GetDossierByDay subItem : dossierByDay.getContent()) {
//                    appliedOnline += subItem.getAppliedOnline();
//                    received += subItem.getReceived();
//                    receivedOnline += subItem.getReceivedOnline();
//                    receivedDirect += subItem.getReceivedDirect();
//                    resolved += subItem.getResolved();
//                    resolvedEarly += subItem.getResolvedEarly();
//                    resolvedOverdue += subItem.getResolvedOverdue();
//                    cancelled += subItem.getCancelled();
//                    deleted += subItem.getDeleted();
//                    suspended += subItem.getSuspended();
//                }
//                item.setAppliedOnline(appliedOnline);
//                item.setReceived(received);
//                item.setReceivedOnline(receivedOnline);
//                item.setReceivedDirect(receivedDirect);
//                item.setResolved(resolved);
//                item.setResolvedEarly(resolvedEarly);
//                item.setResolvedOverdue(resolvedOverdue);
//                item.setCancelled(cancelled);
//                item.setDeleted(deleted);
//                item.setSuspended(suspended);
//
//                if (!isEmpty(item.getAgency())) {
//                    AgencyDossierByDay newAgency = new AgencyDossierByDay();
//                    newAgency.setId(item.getAgency().getId());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getAgency().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newAgency.setName(newName);
//                    item.setAgency(newAgency);
//                }
//
//                if (!isEmpty(item.getAgencyLevel())) {
//                    AgencyDossierByDay newAgencyLevel = new AgencyDossierByDay();
//                    newAgencyLevel.setId(item.getAgency().getId());
//                    ArrayList<NameDossierByDay> newNameLevel = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getAgencyLevel().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newNameLevel.add(name);
//                        }
//                    }
//                    newAgencyLevel.setName(newNameLevel);
//                    item.setAgencyLevel(newAgencyLevel);
//                }
//
//                if (!isEmpty(item.getSector())) {
//                    SectorDossierByDay newSector = new SectorDossierByDay();
//                    newSector.setId(item.getSector().getId());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getSector().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newSector.setName(newName);
//                    item.setSector(newSector);
//                }
//
//                if (!isEmpty(item.getProcedureLevel())) {
//                    ProcedureLevelDossierByDay newProcedureLevel = new ProcedureLevelDossierByDay();
//                    newProcedureLevel.setId(item.getProcedureLevel().getId());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getProcedureLevel().getName()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newProcedureLevel.setName(newName);
//                    item.setProcedureLevel(newProcedureLevel);
//                }
//
//                if (!isEmpty(item.getProcedure())) {
//                    ProcedureDossierByDay newProcedure = new ProcedureDossierByDay();
//                    newProcedure.setId(item.getProcedure().getId());
//                    newProcedure.setCode(item.getProcedure().getCode());
//                    ArrayList<NameDossierByDay> newName = new ArrayList<NameDossierByDay>();
//                    for (NameDossierByDay name : item.getProcedure().getTranslate()) {
//                        if (Objects.equals(translator.getCurrentLocaleId(), name.getLanguageId())) {
//                            newName.add(name);
//                        }
//                    }
//                    newProcedure.setTranslate(newName);
//                    item.setProcedure(newProcedure);
//                }
//            }
//        }
//
//        return listProcedure;
//    }


    public Page<SectorOverdue> getSectorOverdue(Pageable pageable, String fromDate, String toDate, String _agencyId, String spec) throws ParseException {
        ObjectId agencyId = null;
        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;

        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        if (!isNullOrEmpty(_agencyId) && _agencyId != null) {
            agencyId = new ObjectId(_agencyId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("agency.id").is(agencyId),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("sector.id")
                            .first("sector").as("sector")
                            .first("unresolvedOverdue").as("unresolvedOverdue")
            );
        } else {
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("sector.id")
                            .first("sector").as("sector")
                            .first("unresolvedOverdue").as("unresolvedOverdue")
            );
        }
        AggregationResults<SectorOverdue> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SectorOverdue.class);
        List<SectorOverdue> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SectorOverdue> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    public Page<SectorOverdue> getProcedure(Pageable pageable, String fromDate, String toDate, String _agencyId, String spec) throws ParseException {
        ObjectId agencyId = null;
        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;

        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        if (!isNullOrEmpty(_agencyId) && _agencyId != null) {
            agencyId = new ObjectId(_agencyId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("agency.id").is(agencyId),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("sector.id", "procedure")
                            .first("sector").as("sector")
                            .first("procedure").as("procedure")
            );
        } else {
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("sector.id", "procedure")
                            .first("sector").as("sector")
                            .first("procedure").as("procedure")
            );
        }
        AggregationResults<SectorOverdue> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SectorOverdue.class);
        List<SectorOverdue> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SectorOverdue> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    @Cacheable("dossierGetForm6a")
    public Page<SimpleDossier> getForm6a(Pageable pageable, String fromDate, String toDate, String _agencyId, String _agencyParentId, String spec) throws ParseException {
        ObjectId agencyId = null;
        ObjectId agencyParentId = null;
        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;

        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        if (!isNullOrEmpty(_agencyId)) {
            agencyId = new ObjectId(_agencyId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyId),
                                    Criteria.where("agency.parent.id").is(agencyId),
                                    Criteria.where("agency.ancestors.id").is(agencyId)
                            ),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("sector.id")
                            .first("sector").as("sector")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("receivedDirect").as("receivedDirect")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        } else {
            agencyParentId = new ObjectId(_agencyParentId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyParentId),
                                    Criteria.where("agency.parent.id").is(agencyParentId),
                                    Criteria.where("agency.ancestors.id").is(agencyParentId)
                            ),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("sector.id")
                            .first("sector").as("sector")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("receivedDirect").as("receivedDirect")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        }

        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SimpleDossier> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    @Cacheable("dossierGetForm6d")
    public Page<SimpleDossier> getForm6d(Pageable pageable, String fromDate, String toDate, String _agencyId, String _agencyParentId, String spec) throws ParseException {
        ObjectId agencyId = null;
        ObjectId agencyParentId = null;
        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;
        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        if (!isNullOrEmpty(_agencyId)) {
            agencyId = new ObjectId(_agencyId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyId),
                                    Criteria.where("agency.parent.id").is(agencyId),
                                    Criteria.where("agency.ancestors.id").is(agencyId)
                            ),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("agencyLevel", "agency", "sector.id")
                            .first("agency").as("agency")
                            .first("sector").as("sector")
                            .first("agencyLevel").as("agencyLevel")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("receivedDirect").as("receivedDirect")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        } else {
            agencyParentId = new ObjectId(_agencyParentId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyParentId),
                                    Criteria.where("agency.parent.id").is(agencyParentId),
                                    Criteria.where("agency.ancestors.id").is(agencyParentId)
                            ),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("agencyLevel", "agency", "sector.id")
                            .first("agency").as("agency")
                            .first("sector").as("sector")
                            .first("agencyLevel").as("agencyLevel")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        }
        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SimpleDossier> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    @Cacheable("dossierGetForm6b")
    public Page<SimpleDossier> getForm6b(Pageable pageable, String fromDate, String toDate, String _agencyId, String _agencyParentId, String spec) throws ParseException {
        ObjectId agencyId = null;
        ObjectId agencyParentId = null;
        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;
        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        if (!isNullOrEmpty(_agencyId)) {
            agencyId = new ObjectId(_agencyId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyId),
                                    Criteria.where("agency.parent.id").is(agencyId),
                                    Criteria.where("agency.ancestors.id").is(agencyId)
                            ),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("agencyLevel", "sector.id")
                            .first("agencyLevel").as("agencyLevel")
                            .first("sector").as("sector")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("receivedDirect").as("receivedDirect")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        } else {
            agencyParentId = new ObjectId(_agencyParentId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyParentId),
                                    Criteria.where("agency.parent.id").is(agencyParentId),
                                    Criteria.where("agency.ancestors.id").is(agencyParentId)
                            ),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp)
                    )),
                    group("agencyLevel", "sector.id")
                            .first("agencyLevel").as("agencyLevel")
                            .first("sector").as("sector")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("receivedDirect").as("receivedDirect")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        }
        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SimpleDossier> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    public Page<SimpleDossier> getReportForDeployment(Pageable pageable, String _deploymentId, String spec) {
        ObjectId deploymentId = null;
        Aggregation agg = null;
        if (!isNullOrEmpty(_deploymentId) && _deploymentId != null) {
            deploymentId = new ObjectId(_deploymentId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("deploymentId").is(deploymentId)
                    )),
                    group("deploymentId")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .first("deploymentId").as("deploymentId")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        } else {
            agg = (Aggregation) newAggregation(
                    group("deploymentId")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .first("deploymentId").as("deploymentId")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );
        }
        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SimpleDossier> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    public Page<SimpleDossier> getDossierByAgency(Pageable pageable, String _agencyId, String _deploymentId, String spec) {
        ObjectId agencyId = null;
        ObjectId deploymentId = null;
        if (!isNullOrEmpty(_deploymentId) && _deploymentId != null) {
            deploymentId = new ObjectId(_deploymentId);
        }
        if (!isNullOrEmpty(_agencyId) && _agencyId != null) {
            agencyId = new ObjectId(_agencyId);
        }
        Aggregation agg = null;

        agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        Criteria.where("agency.id").is(agencyId),
                        Criteria.where("deploymentId").is(deploymentId)
                )),
                group("agency.id")
                        .first("agency").as("agency")
                        .first("sector").as("sector")
                        .first("agencyLevel").as("agencyLevel")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
        );
        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SimpleDossier> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    public AffectedRowsDto updateReportDossierByDay(PutDossierByDayDto body) {
        AffectedRowsDto affectedRows = new AffectedRowsDto(0);
        ObjectId deploymentId = Context.getDeploymentId();

        if (deploymentId != null) {
            int year = 0;
            int month = 0;
            int day = 0;
            ObjectId sectorId = null;
            ObjectId agencyId = null;
            ObjectId procedureId = null;

            try {
                year = body.getYear();
            } catch (Exception e) {
                System.err.println(e);
            }
            try {
                month = body.getMonth();
            } catch (Exception e) {
                System.err.println(e);
            }
            try {
                day = body.getDay();
            } catch (Exception e) {
                System.err.println(e);
            }
            try {
                sectorId = body.getSector().getId();
            } catch (Exception e) {
                System.err.println(e);
            }
            try {
                agencyId = body.getAgency().getId();
            } catch (Exception e) {
                System.err.println(e);
            }
            try {
                procedureId = body.getProcedure().getId();
            } catch (Exception e) {
                System.err.println(e);
            }

            DossierByDay dossierByDay = dossierByDayRepository.getDossierByDayInfo(year, month, day, sectorId, agencyId, procedureId, deploymentId);
            if (dossierByDay != null) {
                if (body.getField().isEmpty() == false) {
                    for (String field : body.getField()) {
                        switch (field) {
                            case "appliedOnline":
                                dossierByDay.setAppliedOnline(dossierByDay.getAppliedOnline() + body.getNumber());
                                break;
                            case "received":
                                dossierByDay.setReceived(dossierByDay.getReceived() + body.getNumber());
                                break;
                            case "receivedOnline":
                                dossierByDay.setReceivedOnline(dossierByDay.getReceivedOnline() + body.getNumber());
                                break;
                            case "receivedDirect":
                                dossierByDay.setReceivedDirect(dossierByDay.getReceivedDirect() + body.getNumber());
                                break;
                            case "resolved":
                                dossierByDay.setResolved(dossierByDay.getResolved() + body.getNumber());
                                break;
                            case "resolvedEarly":
                                dossierByDay.setResolvedEarly(dossierByDay.getResolvedEarly() + body.getNumber());
                                break;
                            case "resolvedOverdue":
                                dossierByDay.setResolvedOverdue(dossierByDay.getResolvedOverdue() + body.getNumber());
                                break;
                            case "unresolvedOverdue":
                                dossierByDay.setUnresolvedOverdue(dossierByDay.getUnresolvedOverdue() + body.getNumber());
                                break;
                            case "cancelled":
                                dossierByDay.setCancelled(dossierByDay.getCancelled() + body.getNumber());
                                break;
                            case "deleted":
                                dossierByDay.setDeleted(dossierByDay.getDeleted() + body.getNumber());
                                break;
                            case "suspended":
                                dossierByDay.setSuspended(dossierByDay.getSuspended() + body.getNumber());
                                break;
                            case "returnOnTime":
                                dossierByDay.setReturnOnTime(dossierByDay.getReturnOnTime() + body.getNumber());
                                break;
                            case "returnOverdue":
                                dossierByDay.setReturnOverdue(dossierByDay.getReturnOverdue() + body.getNumber());
                                break;
                            case "unresolved":
                                dossierByDay.setUnresolved(dossierByDay.getUnresolved() + body.getNumber());
                                break;
                        }
                    }
                }
                if(body != null && body.getAgency() != null){
                    AgencyDossierByDay dataAgency = this.calladdAgency(body.getAgency().getId());
                    if(dataAgency != null){
                        System.out.println(dataAgency);
                        dossierByDay.setAgency(dataAgency);
                    }
                    else{
                        dossierByDay.setAgency(body.getAgency());
                    }
                }
                dossierByDayRepository.save(dossierByDay);
                affectedRows.setAffectedRows(1);
            } else {
                DossierByDay newDossierByDay = this.addNewDossierByDay(year, month, day, body);
                dossierByDayRepository.save(newDossierByDay);
                affectedRows.setAffectedRows(1);
                this.updateReportDossierByDay(body);
            }
        } else {
            throw new DigoHttpException(10000, new String[]{translator.toLocale("lang.word.deploymentId")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        return affectedRows;
    }

    public AgencyDossierByDay calladdAgency(ObjectId originId){
        try {
            ObjectId deploymentId = Context.getDeploymentId();
            System.out.println("-----------DIGO-Info: Add new Agency from updateAgencyDossier-----------");
            String endpoint = this.gatewayURL + "/" + microservice.getPathCode("basedata") + "/agency/" + originId + "/--fully";
            String token = Context.getJwtAuthenticationTokenValue();
            Map<String, Object> params = new HashMap<>();
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            headers.setBearerAuth(token);
            HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<AgencyDossierByDay> result = restTemplate.exchange(endpoint, HttpMethod.GET, request, AgencyDossierByDay.class, params);
            AgencyDossierByDay data = result.getBody();
            return data;
        } catch (Exception e) {
            System.err.println(e);
            return null;
        }
    };

    public DossierByDay addNewDossierByDay(Integer year, Integer month, Integer day, PutDossierByDayDto body) {
        DossierByDay newDossierByDay = new DossierByDay();
        if (Objects.nonNull(body.getAgency())) {
            AgencyDossierByDay dataAgency = this.calladdAgency(body.getAgency().getId());
            if(dataAgency != null){
                newDossierByDay.setAgency(dataAgency);
            }
            else{
                newDossierByDay.setAgency(body.getAgency());
            }
//            newDossierByDay.setAgency(body.getAgency());
        }
        if (Objects.nonNull(body.getSector())) {
            newDossierByDay.setSector(body.getSector());
        }
        if (Objects.nonNull(body.getProcedure())) {
            newDossierByDay.setProcedure(body.getProcedure());
        }
        if (Objects.nonNull(body.getAgencyLevel())) {
            newDossierByDay.setAgencyLevel(body.getAgencyLevel());
        }
        if (Objects.nonNull(body.getProcedureLevel())) {
            newDossierByDay.setProcedureLevel(body.getProcedureLevel());
        }
        newDossierByDay.setUpdatedDate(new Date());
        newDossierByDay.setDeploymentId(Context.getDeploymentId());
        newDossierByDay.setAppliedOnline(0);
        newDossierByDay.setReceived(0);
        newDossierByDay.setReceivedDirect(0);
        newDossierByDay.setReceivedOnline(0);
        newDossierByDay.setResolved(0);
        newDossierByDay.setResolvedEarly(0);
        newDossierByDay.setResolvedOverdue(0);
        newDossierByDay.setUnresolvedOverdue(0);
        newDossierByDay.setCancelled(0);
        newDossierByDay.setDeleted(0);
        newDossierByDay.setSuspended(0);
        newDossierByDay.setReturnOnTime(0);
        newDossierByDay.setReturnOverdue(0);
        newDossierByDay.setUnresolved(0);
        newDossierByDay.setYear(year);
        newDossierByDay.setMonth(month);
        newDossierByDay.setDay(day);
        return newDossierByDay;
    }

    public GetTotalDossierByDeploymentDto getListDossierByDeploymentId(String _fromDate, String _toDate, String deployment, String _agencyId) throws ParseException {
        GetTotalDossierByDeploymentDto result = new GetTotalDossierByDeploymentDto();
        List<GetDossierByDay> listResult = new ArrayList<GetDossierByDay>();
        Date fromDate = null;
        if (_fromDate != null && _fromDate != "") {
            fromDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_fromDate);
        }

        Date toDate = null;
        if (_toDate != null && _toDate != "") {
            toDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_toDate);
        }
        ObjectId deploymentId = (deployment != "" && deployment != null) ? new ObjectId(deployment) : null;
        ObjectId agencyId = (_agencyId != "" && _agencyId != null) ? new ObjectId(_agencyId) : null;

        listResult = dossierByDayRepository.getListByDeploymentId(deploymentId, fromDate, toDate, agencyId);
        if (listResult.size() > 0 && listResult != null) {
            var total = 0;
            for (GetDossierByDay list : listResult) {
                result.setAgencyId(list.getAgency().getId());
                result.setDeploymentId(list.getDeploymentId());
                total += list.getAppliedOnline().intValue() + list.getReceived().intValue() + list.getResolved().intValue() + list.getUnresolved().intValue() + list.getUnresolvedOverdue().intValue() + list.getCancelled().intValue() + list.getDeleted().intValue() + list.getSuspended().intValue() + list.getReturnOnTime().intValue() + list.getReturnOverdue().intValue();
            }
            result.setTotal(total);
        }
        return result;
    }

    public GetDossierStatisticalByAgencyIdDto getDossierStatisticalByAgencyId(String _fromDate, String _toDate, String deployment, String _agencyId) throws ParseException {
        GetDossierStatisticalByAgencyIdDto result = new GetDossierStatisticalByAgencyIdDto();
        List<GetDossierByDay> listResult = new ArrayList<GetDossierByDay>();
        Date fromDate = null;
        if (_fromDate != null && _fromDate != "") {
            fromDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_fromDate);
        }

        Date toDate = null;
        if (_toDate != null && _toDate != "") {
            toDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_toDate);
        }
        ObjectId deploymentId = (deployment != "" && deployment != null) ? new ObjectId(deployment) : null;
        ObjectId agencyId = (_agencyId != "" && _agencyId != null) ? new ObjectId(_agencyId) : null;

        listResult = dossierByDayRepository.getListByDeploymentId(deploymentId, fromDate, toDate, agencyId);
        if (listResult.size() > 0 && listResult != null) {
            var total = 0;
            var totalOnline = 0;
            var totalDirect = 0;
            for (GetDossierByDay list : listResult) {
                totalOnline = totalOnline + list.getReceivedOnline();
                totalDirect = totalDirect + list.getReceivedDirect();
            }
            result.setId(agencyId);
            result.setTotalOfReceivedOnline(totalOnline);
            result.setTotalOfReceivedDirect(totalDirect);
            result.setTotal(totalOnline + totalDirect);
        }
        return result;
    }

    public List<GetStatisticDossierByLevelDto> getStatisticDossierByLevel(String _fromDate, String _toDate, String deployment, String _agencyId, String _ancestorId) throws ParseException {
        ArrayList<GetStatisticDossierByLevelDto> arrResult = new ArrayList<GetStatisticDossierByLevelDto>();

        List<GetDossierByDay> listResult = new ArrayList<GetDossierByDay>();
        Date fromDate = null;
        if (_fromDate != null && _fromDate != "") {
            fromDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_fromDate);
        }

        Date toDate = null;
        if (_toDate != null && _toDate != "") {
            toDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_toDate);
        }
        ObjectId deploymentId = Context.getDeploymentId();
        ObjectId agencyId = (_agencyId != "" && _agencyId != null) ? new ObjectId(_agencyId) : null;
        ObjectId ancestorId = (_ancestorId != "" && _ancestorId != null) ? new ObjectId(_ancestorId) : null;

        listResult = dossierByDayRepository.getListByDeploymentIdWithAncestor(deploymentId, fromDate, toDate, agencyId, ancestorId);
        SectorDossierByDay newSector = new SectorDossierByDay();

        if (listResult.size() > 0 && listResult != null) {
            for (GetDossierByDay d : listResult) {
                GetStatisticDossierByLevelDto result = new GetStatisticDossierByLevelDto();
                if (d.getAgency() != null) {
                    result.setAgency(d.getAgency());
                }
                if (d.getProcedure() != null) {
                    result.setProcedure(d.getProcedure());
                }
                if (d.getProcedureLevel() != null) {
                    result.setProcedureLevel(d.getProcedureLevel());
                }
                if (d.getSector() != null) {
                    result.setSector(d.getSector());
                }
                result.setTotalReceive(d.getReceived());
                arrResult.add(result);
            }
        }
        return arrResult;
    }

    public List<GetStatisticDossierBySectorDto> getStatisticDossierBySector(String _fromDate, String _toDate, String deployment, String _agencyId, String _sectorId, String _procedureId) throws ParseException {
        ArrayList<GetStatisticDossierBySectorDto> arrResult = new ArrayList<GetStatisticDossierBySectorDto>();

        List<GetDossierByDay> listResult = new ArrayList<GetDossierByDay>();
        Date fromDate = null;
        if (_fromDate != null && _fromDate != "") {
            fromDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_fromDate);
        }

        Date toDate = null;
        if (_toDate != null && _toDate != "") {
            toDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_toDate);
        }
        ObjectId deploymentId = (deployment != "" && deployment != null) ? new ObjectId(deployment) : null;
        ObjectId agencyId = (_agencyId != "" && _agencyId != null) ? new ObjectId(_agencyId) : null;
        ObjectId sectorId = (_sectorId != "" && _sectorId != null) ? new ObjectId(_sectorId) : null;
        ObjectId procedureId = (_procedureId != "" && _procedureId != null) ? new ObjectId(_procedureId) : null;

        listResult = dossierByDayRepository.getListDossierBySector(deploymentId, fromDate, toDate, agencyId, sectorId, procedureId);
        if (listResult.size() > 0 && listResult != null) {
            for (GetDossierByDay d : listResult) {
                if (arrResult.size() == 0) {
                    GetStatisticDossierBySectorDto result = new GetStatisticDossierBySectorDto();

                    if (d.getAgency() != null) {
                        result.setAgency(d.getAgency());
                    }
                    if (d.getProcedure() != null) {
                        result.setProcedure(d.getProcedure());
                    }
                    if (d.getProcedureLevel() != null) {
                        result.setProcedureLevel(d.getProcedureLevel());
                    }
                    if (d.getSector() != null) {
                        result.setSector(d.getSector());
                    }
                    result.setAppliedOnline(d.getAppliedOnline());
                    result.setReceived(d.getReceived());
                    result.setReceivedOnline(d.getReceivedOnline());
                    result.setReceivedDirect(d.getReceivedDirect());
                    result.setResolved(d.getReceived());
                    result.setResolvedEarly(d.getResolvedEarly());
                    result.setResolvedOverdue(d.getResolvedOverdue());
                    result.setUnresolved(d.getUnresolved());
                    result.setUnresolvedOverdue(d.getUnresolvedOverdue());
                    result.setCancelled(d.getCancelled());
                    result.setSuspended(d.getSuspended());
                    result.setDeleted(d.getDeleted());
                    result.setReturnOnTime(d.getReturnOnTime());
                    result.setReturnOverdue(d.getReturnOverdue());
                    arrResult.add(result);
                } else {
                    var count = 0;
                    if (d.getAgency() != null && d.getSector() != null && d.getProcedure() != null) {
                        for (GetStatisticDossierBySectorDto r : arrResult) {
                            if (d.getAgency().getId().equals(r.getAgency().getId()) && d.getSector().getId().equals(r.getSector().getId()) && d.getProcedure().getId().equals(r.getProcedure().getId())) {
                                var appliedOnline = r.getAppliedOnline() + d.getAppliedOnline();
                                r.setAppliedOnline(appliedOnline);

                                var received = r.getReceived() + d.getReceived();
                                r.setReceived(received);

                                var receivedOnline = r.getReceivedOnline() + d.getReceivedOnline();
                                r.setReceivedOnline(receivedOnline);

                                var receivedDirect = r.getReceivedDirect() + d.getReceivedDirect();
                                r.setReceivedDirect(receivedDirect);

                                var resolved = r.getResolved() + d.getResolved();
                                r.setResolved(resolved);

                                var resolvedEarly = r.getResolvedEarly() + d.getResolvedEarly();
                                r.setResolvedEarly(resolvedEarly);

                                var resolvedOverdue = r.getResolvedOverdue() + d.getResolvedOverdue();
                                r.setResolvedOverdue(resolvedOverdue);

                                var unresolved = r.getUnresolved() + d.getUnresolved();
                                r.setUnresolved(unresolved);

                                var unresolvedOverdue = r.getUnresolvedOverdue() + d.getUnresolvedOverdue();
                                r.setUnresolvedOverdue(unresolvedOverdue);
                                count++;
                            }
                        }
                        if (count == 0) {
                            GetStatisticDossierBySectorDto result = new GetStatisticDossierBySectorDto();

                            if (d.getAgency() != null) {
                                result.setAgency(d.getAgency());
                            }
                            if (d.getProcedure() != null) {
                                result.setProcedure(d.getProcedure());
                            }
                            if (d.getProcedureLevel() != null) {
                                result.setProcedureLevel(d.getProcedureLevel());
                            }
                            if (d.getSector() != null) {
                                result.setSector(d.getSector());
                            }
                            result.setAppliedOnline(d.getAppliedOnline());
                            result.setReceived(d.getReceived());
                            result.setReceivedOnline(d.getReceivedOnline());
                            result.setReceivedDirect(d.getReceivedDirect());
                            result.setResolved(d.getReceived());
                            result.setResolvedEarly(d.getResolvedEarly());
                            result.setResolvedOverdue(d.getResolvedOverdue());
                            result.setUnresolved(d.getUnresolved());
                            result.setUnresolvedOverdue(d.getUnresolvedOverdue());
                            result.setCancelled(d.getCancelled());
                            result.setSuspended(d.getSuspended());
                            result.setDeleted(d.getDeleted());
                            result.setReturnOnTime(d.getReturnOnTime());
                            result.setReturnOverdue(d.getReturnOverdue());
                            arrResult.add(result);
                        }
                    }
                }
            }
        }
        return arrResult;
    }

    @Cacheable("dossierGetFormProvince6a")
    public Page<SimpleDossier> getFormProvince6a(Pageable pageable, String fromDate, String toDate, String _agencyId, String spec) throws ParseException{
        ObjectId agencyId = null;

        Date fromDateTemp = null;
        Date toDateTemp = null;

        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");

        Criteria criteria = new Criteria();
        // List query param
        List<Criteria> criteriaList = new ArrayList<>();

        ObjectId deploymentId = Context.getDeploymentId();
        criteriaList.add(Criteria.where("deploymentId").is(deploymentId));

        if (Objects.nonNull(fromDateTemp)) {
            criteriaList.add(Criteria.where("updatedDate").gte(fromDateTemp));
        }
        if (Objects.nonNull(toDateTemp)) {
            criteriaList.add(Criteria.where("updatedDate").lte(toDateTemp));
        }

        if (!isNullOrEmpty(_agencyId) && _agencyId != null) {
            agencyId = new ObjectId(_agencyId);
            criteriaList.add(Criteria.where("agency.id").is(agencyId));
        }

        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));

        Aggregation agg = (Aggregation) newAggregation(
                match(criteria),
                group("sector.id")
                        .first("sector").as("sector")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("receivedDirect").as("receivedDirect")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
        );
        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        Query query = new Query();
        query.with(pageable);
        Page<SimpleDossier> page = PageableExecutionUtils.getPage(resultList, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SimpleDossier.class));
        return page;
    }

    @Cacheable("dossierGetFormProvince6b")
    public List<SimpleDossier> getFormProvince6b(String fromDate, String toDate, String _agencyId,
                                                 List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict) throws ParseException {
        List<ObjectId> agencyIds = new ArrayList<>();
        if (!isNullOrEmpty(_agencyId) && _agencyId != null) {
            agencyIds.add(new ObjectId(_agencyId));
            try {
                StringBuilder codeArr = new StringBuilder();
                for(String tagId: tagIds){
                    codeArr.append("&tag-id=").append(tagId);
                }
                String shortUri = "/agency/--by-parent-id?parent-id=" + _agencyId + codeArr.toString();
                UriComponentsBuilder uriBuilder = microservice.basedataUri(shortUri);
                UriComponents uriComponents = uriBuilder.encode().build();
                MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
                String response = digoRestTemplate.exchangeWithResponse(null, uriComponents.toUri(), body, HttpMethod.GET);
                ObjectMapper mapper = new ObjectMapper();
                List<GetAgencyDto> getAgencyDtos;
                try {
                    getAgencyDtos = mapper.readValue(response, new TypeReference<List<GetAgencyDto>>() {});
                    List<ObjectId> getAgencyDtoIds = getAgencyDtos.stream().map(GetAgencyDto::getId).collect(Collectors.toList());
                    if(Objects.nonNull(getAgencyDtoIds) & getAgencyDtoIds.size() > 0){
                        agencyIds.addAll(getAgencyDtoIds);
                    }
                } catch (HttpClientErrorException err) {
                    //Do nothing
                    logger.info("HttpClientErrorException: " + err.toString());
                } catch (JsonProcessingException err) {
                    logger.info("JsonProcessingException: " + err.toString());
                }
            } catch (Exception e) {
                logger.info("Exception: " + e.toString());
            }
        }

        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;
        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");

        ObjectId agencyLevelCommuneId = null;
        ObjectId agencyLevelDistrictId = null;
        List<ObjectId> agencyLevels = new ArrayList<>();
        if(!isNullOrEmpty(agencyLevelCommune)){
            agencyLevelCommuneId = new ObjectId(agencyLevelCommune);
            agencyLevels.add(agencyLevelCommuneId);
        }
        if(!isNullOrEmpty(agencyLevelDistrict)){
            agencyLevelDistrictId = new ObjectId(agencyLevelDistrict);
            agencyLevels.add(agencyLevelDistrictId);
        }

        Criteria criteria = new Criteria();
        // List query param
        List<Criteria> criteriaList = new ArrayList<>();

        ObjectId deploymentId = Context.getDeploymentId();
        criteriaList.add(Criteria.where("deploymentId").is(deploymentId));

        if (!agencyIds.isEmpty()) {
            criteriaList.add(Criteria.where("agency.id").in(agencyIds));
        }
        if (!agencyLevels.isEmpty()) {
            criteriaList.add(Criteria.where("agencyLevel.id").in(agencyLevels));
        }
        if (Objects.nonNull(fromDateTemp)) {
            criteriaList.add(Criteria.where("updatedDate").gte(fromDateTemp));
        }
        if (Objects.nonNull(toDateTemp)) {
            criteriaList.add(Criteria.where("updatedDate").lte(toDateTemp));
        }
        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));

        agg = (Aggregation) newAggregation(
                match(criteria),
                group("agencyLevel", "sector.id")
                        .first("agencyLevel").as("agencyLevel")
                        .first("sector").as("sector")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("receivedDirect").as("receivedDirect")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
        );

        AggregationResults<SimpleDossier> results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        return resultList;
    }

    @Cacheable("dossierGetFormProvince6c")
    public List<SimpleDossier> getFormProvince6c(String fromDate, String toDate, String _agencyId,
                                                 List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict,
                                                 String agencyLevelProvince) throws ParseException {
        List<ObjectId> agencyIds = new ArrayList<>();
        if (!isNullOrEmpty(_agencyId)) {
            agencyIds.add(new ObjectId(_agencyId));
            // get agency commune, district relation with agencyId
            try {
                StringBuilder codeArr = new StringBuilder();
                for(String tagId: tagIds){
                    codeArr.append("&tag-id=").append(tagId);
                }
                String shortUri = "/agency/--by-parent-id?parent-id=" + _agencyId + codeArr.toString();
                UriComponentsBuilder uriBuilder = microservice.basedataUri(shortUri);
                UriComponents uriComponents = uriBuilder.encode().build();
                MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
                String response = digoRestTemplate.exchangeWithResponse(null, uriComponents.toUri(), body, HttpMethod.GET);
                ObjectMapper mapper = new ObjectMapper();
                List<GetAgencyDto> getAgencyDtos;
                try {
                    getAgencyDtos = mapper.readValue(response, new TypeReference<List<GetAgencyDto>>() {});
                    List<ObjectId> getAgencyDtoIds = getAgencyDtos.stream().filter(Objects::nonNull)
                            .map(GetAgencyDto::getId).collect(Collectors.toList());
                    if(Objects.nonNull(getAgencyDtoIds) & getAgencyDtoIds.size() > 0){
                        agencyIds.addAll(getAgencyDtoIds);
                    }
                } catch (HttpClientErrorException err) {
                    //Do nothing
                    logger.info("HttpClientErrorException: " + err.toString());
                } catch (JsonProcessingException err) {
                    logger.info("JsonProcessingException: " + err.toString());
                }
            } catch (Exception e) {
                logger.info("Exception: " + e.toString());
            }
        }

        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;
        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        ObjectId agencyLevelCommuneId = null;
        ObjectId agencyLevelDistrictId = null;
        ObjectId agencyLevelProvinceId = null;
        List<ObjectId> agencyLevels = new ArrayList<>();
        if(!isNullOrEmpty(agencyLevelCommune)){
            agencyLevelCommuneId = new ObjectId(agencyLevelCommune);
            agencyLevels.add(agencyLevelCommuneId);
        }
        if(!isNullOrEmpty(agencyLevelDistrict)){
            agencyLevelDistrictId = new ObjectId(agencyLevelDistrict);
            agencyLevels.add(agencyLevelDistrictId);
        }
        if(!isNullOrEmpty(agencyLevelProvince)){
            agencyLevelProvinceId = new ObjectId(agencyLevelProvince);
            agencyLevels.add(agencyLevelProvinceId);
        }

        Criteria criteria = new Criteria();
        // List query param
        List<Criteria> criteriaList = new ArrayList<>();

        ObjectId deploymentId = Context.getDeploymentId();
        criteriaList.add(Criteria.where("deploymentId").is(deploymentId));

        if (!agencyIds.isEmpty()) {
            criteriaList.add(Criteria.where("agency.id").in(agencyIds));
        }
        if (!agencyLevels.isEmpty()) {
            criteriaList.add(Criteria.where("agencyLevel.id").in(agencyLevels));
        }
        if (Objects.nonNull(fromDateTemp)) {
            criteriaList.add(Criteria.where("updatedDate").gte(fromDateTemp));
        }
        if (Objects.nonNull(toDateTemp)) {
            criteriaList.add(Criteria.where("updatedDate").lte(toDateTemp));
        }
        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));

        agg = (Aggregation) newAggregation(
                match(criteria),
                group("agencyLevel", "sector.id")
                        .first("agencyLevel").as("agencyLevel")
                        .first("sector").as("sector")
                        .first("agency").as("agency")
                        .sum("appliedOnline").as("appliedOnline")
                        .sum("receivedDirect").as("receivedDirect")
                        .sum("received").as("received")
                        .sum("receivedOnline").as("receivedOnline")
                        .sum("resolved").as("resolved")
                        .sum("resolvedEarly").as("resolvedEarly")
                        .sum("resolvedOverdue").as("resolvedOverdue")
                        .sum("cancelled").as("cancelled")
                        .sum("deleted").as("deleted")
                        .sum("suspended").as("suspended")
                        .sum("returnOnTime").as("returnOnTime")
                        .sum("returnOverdue").as("returnOverdue")
                        .sum("unresolved").as("unresolved")
                        .sum("unresolvedOverdue").as("unresolvedOverdue")
        );
        AggregationResults<SimpleDossier> results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        return results.getMappedResults();
    }


    public ResponseEntity<Object> statisticExport(Pageable pageable, String fromDate, String toDate,
                                                  String agencyId, String agencyName, String spec, Integer reportType,
                                                  List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict,
                                                  String agencyLevelProvince) throws IOException, ParseException {
        byte[] resource;
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timestamp = dateFormat.format(new Date());
        String filename = "";

        if (null == reportType) {
            filename = timestamp + "__baocaothongketoantinh6a.xlsx";
            resource = exportExcelFormProvince6a(pageable, fromDate, toDate, agencyId, agencyName,  spec);
        }else {
            switch (reportType) {
                case 5:
                    filename = timestamp + "__baocaothongketoantinh6a.xlsx";
                    resource = exportExcelFormProvince6a(pageable, fromDate, toDate, agencyId, agencyName, spec);
                    break;
                case 6:
                    filename = timestamp + "__baocaothongketoantinh6b.xlsx";
                    resource = exportExcelFormProvince6b( fromDate, toDate, agencyId, agencyName, tagIds, agencyLevelCommune, agencyLevelDistrict);
                    break;
                case 7:
                    filename = timestamp + "__baocaothongketoantinh6c.xlsx";
                    resource = exportExcelFormProvince6c(fromDate, toDate, agencyId, agencyName, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
                    break;
                case 8:
                    filename = timestamp + "__baocaothongke6b_2020.xlsx";
                    resource = exportExcelFormProvince6b2020( fromDate, toDate, agencyId, agencyName, tagIds, agencyLevelCommune, agencyLevelDistrict);
                    break;
                case 9:
                    filename = timestamp + "__baocaothongke6c_2020.xlsx";
                    resource = exportExcelFormProvince6c2020(fromDate, toDate, agencyId, agencyName, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
                    break;
                default:
                    filename = timestamp + "__baocaothongketoantinh6a.xlsx";
                    resource = exportExcelFormProvince6a(pageable, fromDate, toDate, agencyId, agencyName, spec);
                    break;
            }
        }
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(resource);
    }

    // exportExcelFormProvince6c
    public byte[] exportExcelFormProvince6c( String fromDate, String toDate, String agencyId, String agencyName,
                                             List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict, String agencyLevelProvince
    ) throws IOException, ParseException {
        try (InputStream is = resourceTemplateProvince6C.getInputStream()) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();
            ExcelFormProvince6cDto dto = getFormProvince6cExcelDto(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
            String nameReport = translator.toLocale("lang.word.form-province-6c.form-no");
            String titleTthcProvince = translator.toLocale("lang.word.form-province-6c.tthc-province");
            String titleTthcDistrict = translator.toLocale("lang.word.form-province-6c.tthc-district");
            String titleTthcCommune = translator.toLocale("lang.word.form-province-6c.tthc-commune");
            String title = translator.toLocale("lang.word.form-province-6c.title");
            String agencyReceivingReport = "";
            String reportingUnit = translator.toLocale("lang.word.form-province-6a.reporting-unit", new String[]{agencyName});
            String unitReceivingReport = translator.toLocale("lang.word.form-province-6a.unit-receiving-report", new String[]{agencyReceivingReport});
            String unitNumberOfDossiers = translator.toLocale("lang.word.form-province-6a.unit-number-of-dossiers", new String[]{"TTHC"});
            String reportingPeriod = translator.toLocale("lang.word.form-province-6a.reporting-period", new String[]{translator.toLocale("lang.word.year", new String[]{LocalDateTime.now().getYear()+""})});
            String fromDateStr = dateConverter.convertStringDateToDateFormatExcel(fromDate);
            String toDateStr = dateConverter.convertStringDateToDateFormatExcel(toDate);
            String fromDayToDay = translator.toLocale("lang.word.fromDayToDay", new String[]{fromDateStr, toDateStr});
            String no = translator.toLocale("lang.word.no");
            String fieldOfSettlemen = translator.toLocale("lang.word.field-of-settlement");
            String numberOfApplicationsReceived = translator.toLocale("lang.word.number-of-applications-received");
            String inThePeriod = translator.toLocale("lang.word.in-the-period");
            String total = translator.toLocale("lang.word.total");
            String online = translator.toLocale("lang.word.online");
            String direct = translator.toLocale("lang.word.direct");
            String postalServices = translator.toLocale("lang.word.postal-services");
            String fromThePreviousPeriod = translator.toLocale("lang.word.from-the-previous-period");
            String numberOfCasesResolved = translator.toLocale("lang.word.number-of-cases-resolved");
            String byDeadTime = translator.toLocale("lang.word.by-dead-time");
            String onTime = translator.toLocale("lang.word.on-time");
            String outOfDate = translator.toLocale("lang.word.out-of-date");
            String inDueDate = translator.toLocale("lang.word.in-due-date");
            String numberOfCasesBeingProcessed = translator.toLocale("lang.word.number-of-cases-being-processed");
            context.putVar("no", no);
            context.putVar("titleTthcProvince", titleTthcProvince);
            context.putVar("titleTthcDistrict", titleTthcDistrict);
            context.putVar("titleTthcCommune", titleTthcCommune);
            context.putVar("fieldOfSettlemen", fieldOfSettlemen);
            context.putVar("numberOfApplicationsReceived", numberOfApplicationsReceived);
            context.putVar("inThePeriod", inThePeriod);
            context.putVar("total", total);
            context.putVar("online", online);
            context.putVar("direct", direct);
            context.putVar("postalServices", postalServices);
            context.putVar("fromThePreviousPeriod", fromThePreviousPeriod);
            context.putVar("numberOfCasesResolved", numberOfCasesResolved);
            context.putVar("byDeadTime", byDeadTime);
            context.putVar("onTime", onTime);
            context.putVar("outOfDate", outOfDate);
            context.putVar("inDueDate", inDueDate);
            context.putVar("numberOfCasesBeingProcessed", numberOfCasesBeingProcessed);
            context.putVar("title", title);
            context.putVar("nameReport", nameReport);
            context.putVar("reportingUnit", reportingUnit);
            context.putVar("unitReceivingReport", unitReceivingReport);
            context.putVar("unitNumberOfDossiers", unitNumberOfDossiers);
            context.putVar("reportingPeriod", reportingPeriod);
            context.putVar("fromDayToDay", fromDayToDay);
            context.putVar("province", dto.getProvince());
            context.putVar("district", dto.getDistrict());
            context.putVar("commune", dto.getCommune());
            context.putVar("sum", dto.getSum());
            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            return outputStream.toByteArray();
        }
    }

    // exportExcelFormProvince6c2020
    public byte[] exportExcelFormProvince6c2020( String fromDate, String toDate, String agencyId, String agencyName,
                                                 List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict, String agencyLevelProvince
    ) throws IOException, ParseException {
        try (InputStream is = resourceTemplateProvince6C2020.getInputStream()) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();
            ExcelFormProvince6cDto dto = getFormProvince6cExcelDto(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
            String nameReport = translator.toLocale("lang.word.form-province-6c.form-no");
            String titleTthcProvince = translator.toLocale("lang.word.form-province-6c.tthc-province");
            String titleTthcDistrict = translator.toLocale("lang.word.form-province-6c.tthc-district");
            String titleTthcCommune = translator.toLocale("lang.word.form-province-6c.tthc-commune");
            String title = translator.toLocale("lang.word.form-province-6c.title");
            String agencyReceivingReport = "";
            String reportingUnit = translator.toLocale("lang.word.form-province-6a.reporting-unit", new String[]{agencyName});
            String unitReceivingReport = translator.toLocale("lang.word.form-province-6a.unit-receiving-report", new String[]{agencyReceivingReport});
            String unitNumberOfDossiers = translator.toLocale("lang.word.form-province-6a.unit-number-of-dossiers", new String[]{"TTHC"});
            String reportingPeriod = translator.toLocale("lang.word.form-province-6a.reporting-period", new String[]{translator.toLocale("lang.word.year", new String[]{LocalDateTime.now().getYear()+""})});
            String fromDateStr = dateConverter.convertStringDateToDateFormatExcel(fromDate);
            String toDateStr = dateConverter.convertStringDateToDateFormatExcel(toDate);
            String fromDayToDay = translator.toLocale("lang.word.fromDayToDay", new String[]{fromDateStr, toDateStr});
            String no = translator.toLocale("lang.word.no");
            String fieldOfSettlemen = translator.toLocale("lang.word.field-of-settlement");
            String numberOfApplicationsReceived = translator.toLocale("lang.word.number-of-applications-received");
            String inThePeriod = translator.toLocale("lang.word.in-the-period");
            String total = translator.toLocale("lang.word.total");
            String online = translator.toLocale("lang.word.online");
            String direct = translator.toLocale("lang.word.direct");
            String postalServices = translator.toLocale("lang.word.postal-services");
            String fromThePreviousPeriod = translator.toLocale("lang.word.from-the-previous-period");
            String numberOfCasesResolved = translator.toLocale("lang.word.number-of-cases-resolved");
            String byDeadTime = translator.toLocale("lang.word.by-dead-time");
            String onTime = translator.toLocale("lang.word.on-time");
            String outOfDate = translator.toLocale("lang.word.out-of-date");
            String inDueDate = translator.toLocale("lang.word.in-due-date");
            String numberOfCasesBeingProcessed = translator.toLocale("lang.word.number-of-cases-being-processed");
            context.putVar("no", no);
            context.putVar("titleTthcProvince", titleTthcProvince);
            context.putVar("titleTthcDistrict", titleTthcDistrict);
            context.putVar("titleTthcCommune", titleTthcCommune);
            context.putVar("fieldOfSettlemen", fieldOfSettlemen);
            context.putVar("numberOfApplicationsReceived", numberOfApplicationsReceived);
            context.putVar("inThePeriod", inThePeriod);
            context.putVar("total", total);
            context.putVar("online", online);
            context.putVar("direct", direct);
            context.putVar("postalServices", postalServices);
            context.putVar("fromThePreviousPeriod", fromThePreviousPeriod);
            context.putVar("numberOfCasesResolved", numberOfCasesResolved);
            context.putVar("byDeadTime", byDeadTime);
            context.putVar("onTime", onTime);
            context.putVar("outOfDate", outOfDate);
            context.putVar("inDueDate", inDueDate);
            context.putVar("numberOfCasesBeingProcessed", numberOfCasesBeingProcessed);
            context.putVar("title", title);
            context.putVar("nameReport", nameReport);
            context.putVar("reportingUnit", reportingUnit);
            context.putVar("unitReceivingReport", unitReceivingReport);
            context.putVar("unitNumberOfDossiers", unitNumberOfDossiers);
            context.putVar("reportingPeriod", reportingPeriod);
            context.putVar("fromDayToDay", fromDayToDay);
            context.putVar("province", dto.getProvince());
            context.putVar("district", dto.getDistrict());
            context.putVar("commune", dto.getCommune());
            context.putVar("sum", dto.getSum());
            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            return outputStream.toByteArray();
        }
    }

    public ExcelFormProvince6cDto getFormProvince6cExcelDto(String fromDate, String toDate, String agencyId, List<String> tagIds,
                                                            String agencyLevelCommune, String agencyLevelDistrict, String agencyLevelProvince) throws ParseException {
        List<SimpleDossier> simpleDossiers = getFormProvince6c(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
        ExcelFormProvince6cDto dto = new ExcelFormProvince6cDto();
        List<ExcelFormProvince6cLevelProvince> excelFormProvince6cLevelProvinces = buildExcelFormProvince6cLevelProvinces(simpleDossiers, agencyLevelProvince);
        List<ExcelFormDetail> excelFormDetailsDistrict = buildExcelFormDetail(simpleDossiers, agencyLevelDistrict);
        List<ExcelFormDetail> excelFormDetailsCommune = buildExcelFormDetail(simpleDossiers, agencyLevelCommune);
        dto.setProvince(excelFormProvince6cLevelProvinces);
        dto.setDistrict(excelFormDetailsDistrict);
        dto.setCommune(excelFormDetailsCommune);
        dto.setSum(buildSumProvince6c(excelFormProvince6cLevelProvinces, excelFormDetailsDistrict, excelFormDetailsCommune));
        return dto;
    }

    private ExcelFormDetail buildSumProvince6c(List<ExcelFormProvince6cLevelProvince> excelFormProvince6cLevelProvinces, List<ExcelFormDetail> excelFormDetailsDistrict, List<ExcelFormDetail> excelFormDetailsCommune) {
        List<ExcelFormDetail> excelFormDetails = new ArrayList<>();

        List<ExcelFormDetail> excelFormDetailProvince6cLevelProvinces = excelFormProvince6cLevelProvinces.stream()
                .filter(Objects::nonNull)
                .map(ExcelFormProvince6cLevelProvince::getItemDetails).flatMap(List::stream)
                .collect(Collectors.toList());

        excelFormDetails.addAll(excelFormDetailProvince6cLevelProvinces);
        excelFormDetails.addAll(excelFormDetailsDistrict);
        excelFormDetails.addAll(excelFormDetailsCommune);

        String sector = translator.toLocale("lang.word.total");
        Integer sum1 = 0;
        Integer acceptedOnl = 0;
        Integer receivedDirect = 0;
        Integer acceptedDirect = 0;
        Integer pastDossier = 0;
        Integer sum2 = 0;
        Integer resolvedEarly = 0;
        Integer returnedOnTime = 0;
        Integer resolvedOverdue = 0;
        Integer sum3 = 0;
        Integer unresolvedNoTime = 0;
        Integer unresolvedOvertime = 0;
        for (ExcelFormDetail excelFormDetail: excelFormDetails){
            sum1 += excelFormDetail.getSum1();
            acceptedOnl += excelFormDetail.getAcceptedOnl();
            receivedDirect += excelFormDetail.getReceivedDirect();
            acceptedDirect += excelFormDetail.getAcceptedDirect();
            pastDossier += excelFormDetail.getPastDossier() ;
            sum2 += excelFormDetail.getSum2();
            resolvedEarly += excelFormDetail.getResolvedEarly();
            returnedOnTime += excelFormDetail.getReturnedOnTime();
            resolvedOverdue += excelFormDetail.getResolvedOverdue();
            sum3 += excelFormDetail.getSum3();
            unresolvedNoTime += excelFormDetail.getUnresolvedNoTime();
            unresolvedOvertime += excelFormDetail.getUnresolvedOvertime();
        }
        ExcelFormDetail detail = new ExcelFormDetail();
        detail.setSector(sector);
        detail.setSum1(sum1);
        detail.setAcceptedOnl(acceptedOnl);
        detail.setReceivedDirect(receivedDirect);
        detail.setAcceptedDirect(acceptedDirect);
        detail.setPastDossier(pastDossier);
        detail.setSum2(sum2);
        detail.setResolvedEarly(resolvedEarly);
        detail.setReturnedOnTime(returnedOnTime);
        detail.setResolvedOverdue(resolvedOverdue);
        detail.setSum3(sum3);
        detail.setUnresolvedNoTime(unresolvedNoTime);
        detail.setUnresolvedOvertime(unresolvedOvertime);
        return detail;
    }

    private List<ExcelFormProvince6cLevelProvince> buildExcelFormProvince6cLevelProvinces(List<SimpleDossier> simpleDossiers, String agencyLevel){
        List<ExcelFormProvince6cLevelProvince> dtos = new ArrayList<>();
        List<SimpleDossier> simpleDossiersByAgencyLevel = getSimpleDossiersByAgencyLevel(simpleDossiers, agencyLevel);
        Map<ObjectId, List<SimpleDossier>> listMap = simpleDossiersByAgencyLevel.stream().filter(Objects::nonNull)
                .filter(item -> Objects.nonNull(item.getAgency()))
                .collect(groupingBy(item -> item.getAgency().getId()));
        int iRoman = 1;
        for (Map.Entry<ObjectId, List<SimpleDossier>> entry : listMap.entrySet()) {
            ExcelFormProvince6cLevelProvince dto = new ExcelFormProvince6cLevelProvince();
            dto.setNoRoman(Converter.intToRoman(iRoman));
            String agencyName = "";
            Optional<AgencyDossierByDay> agencyDossierByDay = entry.getValue().stream().filter(item -> Objects.nonNull(item.getAgency()))
                    .map(SimpleDossier::getAgency)
                    .findFirst();
            if(agencyDossierByDay.isPresent()){
                AgencyDossierByDay dossierByDay = agencyDossierByDay.get();
                dossierByDay.setNameAgencyData(translator.getCurrentLocaleId());
                agencyName = dossierByDay.getAgencyName();
            }
            String nameTitle = translator.toLocale("lang.word.form-province-6c.tthc") + " "+ agencyName + " "
                    + translator.toLocale("lang.word.form-province-6c.tthc-receive");
            dto.setAgencyName(nameTitle);
            dto.setItemDetails(buildExcelFormDetail(entry.getValue(), agencyLevel));
            dtos.add(dto);
            iRoman++;
        }
        return dtos;
    }

    private List<ExcelFormDetail> buildExcelFormDetail(List<SimpleDossier> simpleDossiers, String agencyLevel){
        AtomicLong atomicLong = new AtomicLong(1L);
        List<SimpleDossier> simpleDossiersByAgencyLevel = getSimpleDossiersByAgencyLevel(simpleDossiers, agencyLevel);
        return simpleDossiersByAgencyLevel.stream()
                .map(item -> convertSimpleDossierToExcelFormDetail(atomicLong.getAndIncrement(), item)).collect(Collectors.toList());
    }

    private List<SimpleDossier> getSimpleDossiersByAgencyLevel(List<SimpleDossier> simpleDossiers, String agencyLevel){
        ObjectId agencyLevelId = new ObjectId(agencyLevel);
        return simpleDossiers.stream()
                .filter(Objects::nonNull)
                .filter(item -> agencyLevelId.equals(item.getAgencyLevel().getId())).collect(Collectors.toList());
    }

    // exportExcelFormProvince6b
    public byte[] exportExcelFormProvince6b( String fromDate, String toDate, String agencyId, String agencyName,
                                             List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict
    ) throws IOException, ParseException {
        try (InputStream is = resourceTemplateProvince6B.getInputStream()) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            String nameReport = translator.toLocale("lang.word.form-province-6b.form-no");
            String title = translator.toLocale("lang.word.form-province-6b.title");
            String agencyReceivingReport = "";
            String reportingUnit = translator.toLocale("lang.word.form-province-6a.reporting-unit", new String[]{agencyName});
            String unitReceivingReport = translator.toLocale("lang.word.form-province-6a.unit-receiving-report", new String[]{agencyReceivingReport});
            String unitNumberOfDossiers = translator.toLocale("lang.word.form-province-6a.unit-number-of-dossiers", new String[]{"TTHC"});
            String reportingPeriod = translator.toLocale("lang.word.form-province-6a.reporting-period", new String[]{translator.toLocale("lang.word.year", new String[]{LocalDateTime.now().getYear()+""})});
            String fromDateStr = dateConverter.convertStringDateToDateFormatExcel(fromDate);
            String toDateStr = dateConverter.convertStringDateToDateFormatExcel(toDate);
            String fromDayToDay = translator.toLocale("lang.word.fromDayToDay", new String[]{fromDateStr, toDateStr});
            String no = translator.toLocale("lang.word.no");
            String fieldOfSettlemen = translator.toLocale("lang.word.field-of-settlement");
            String numberOfApplicationsReceived = translator.toLocale("lang.word.number-of-applications-received");
            String inThePeriod = translator.toLocale("lang.word.in-the-period");
            String total = translator.toLocale("lang.word.total");
            String online = translator.toLocale("lang.word.online");
            String direct = translator.toLocale("lang.word.direct");
            String postalServices = translator.toLocale("lang.word.postal-services");
            String fromThePreviousPeriod = translator.toLocale("lang.word.from-the-previous-period");
            String numberOfCasesResolved = translator.toLocale("lang.word.number-of-cases-resolved");
            String byDeadTime = translator.toLocale("lang.word.by-dead-time");
            String onTime = translator.toLocale("lang.word.on-time");
            String outOfDate = translator.toLocale("lang.word.out-of-date");
            String inDueDate = translator.toLocale("lang.word.in-due-date");
            String numberOfCasesBeingProcessed = translator.toLocale("lang.word.number-of-cases-being-processed");
            context.putVar("no", no);
            context.putVar("fieldOfSettlemen", fieldOfSettlemen);
            context.putVar("numberOfApplicationsReceived", numberOfApplicationsReceived);
            context.putVar("inThePeriod", inThePeriod);
            context.putVar("total", total);
            context.putVar("online", online);
            context.putVar("direct", direct);
            context.putVar("postalServices", postalServices);
            context.putVar("fromThePreviousPeriod", fromThePreviousPeriod);
            context.putVar("numberOfCasesResolved", numberOfCasesResolved);
            context.putVar("byDeadTime", byDeadTime);
            context.putVar("onTime", onTime);
            context.putVar("outOfDate", outOfDate);
            context.putVar("inDueDate", inDueDate);
            context.putVar("numberOfCasesBeingProcessed", numberOfCasesBeingProcessed);
            context.putVar("title", title);
            context.putVar("nameReport", nameReport);
            context.putVar("reportingUnit", reportingUnit);
            context.putVar("unitReceivingReport", unitReceivingReport);
            context.putVar("unitNumberOfDossiers", unitNumberOfDossiers);
            context.putVar("reportingPeriod", reportingPeriod);
            context.putVar("fromDayToDay", fromDayToDay);
            context.putVar("itemDtos", getFormProvince6bExcelDto(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict));
            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            return outputStream.toByteArray();
        }
    }

    // exportExcelFormProvince6b2020
    public byte[] exportExcelFormProvince6b2020( String fromDate, String toDate, String agencyId, String agencyName,
                                                 List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict
    ) throws IOException, ParseException {
        try (InputStream is = resourceTemplateProvince6B2020.getInputStream()) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            String nameReport = translator.toLocale("lang.word.form-province-6b.form-no");
            String title = translator.toLocale("lang.word.form-province-6b.title");
            String agencyReceivingReport = "";
            String reportingUnit = translator.toLocale("lang.word.form-province-6a.reporting-unit", new String[]{agencyName});
            String unitReceivingReport = translator.toLocale("lang.word.form-province-6a.unit-receiving-report", new String[]{agencyReceivingReport});
            String unitNumberOfDossiers = translator.toLocale("lang.word.form-province-6a.unit-number-of-dossiers", new String[]{"TTHC"});
            String reportingPeriod = translator.toLocale("lang.word.form-province-6a.reporting-period", new String[]{translator.toLocale("lang.word.year", new String[]{LocalDateTime.now().getYear()+""})});
            String fromDateStr = dateConverter.convertStringDateToDateFormatExcel(fromDate);
            String toDateStr = dateConverter.convertStringDateToDateFormatExcel(toDate);
            String fromDayToDay = translator.toLocale("lang.word.fromDayToDay", new String[]{fromDateStr, toDateStr});
            String no = translator.toLocale("lang.word.no");
            String fieldOfSettlemen = translator.toLocale("lang.word.field-of-settlement");
            String numberOfApplicationsReceived = translator.toLocale("lang.word.number-of-applications-received");
            String inThePeriod = translator.toLocale("lang.word.in-the-period");
            String total = translator.toLocale("lang.word.total");
            String online = translator.toLocale("lang.word.online");
            String direct = translator.toLocale("lang.word.direct");
//            String postalServices = translator.toLocale("lang.word.postal-services");
            String fromThePreviousPeriod = translator.toLocale("lang.word.from-the-previous-period");
            String numberOfCasesResolved = translator.toLocale("lang.word.number-of-cases-resolved");
            String byDeadTime = translator.toLocale("lang.word.by-dead-time");
            String onTime = translator.toLocale("lang.word.on-time");
            String outOfDate = translator.toLocale("lang.word.out-of-date");
            String inDueDate = translator.toLocale("lang.word.in-due-date");
            String numberOfCasesBeingProcessed = translator.toLocale("lang.word.number-of-cases-being-processed");
            context.putVar("no", no);
            context.putVar("fieldOfSettlemen", fieldOfSettlemen);
            context.putVar("numberOfApplicationsReceived", numberOfApplicationsReceived);
            context.putVar("inThePeriod", inThePeriod);
            context.putVar("total", total);
            context.putVar("online", online);
            context.putVar("direct", direct);
//            context.putVar("postalServices", postalServices);
            context.putVar("fromThePreviousPeriod", fromThePreviousPeriod);
            context.putVar("numberOfCasesResolved", numberOfCasesResolved);
            context.putVar("byDeadTime", byDeadTime);
            context.putVar("onTime", onTime);
            context.putVar("outOfDate", outOfDate);
            context.putVar("inDueDate", inDueDate);
            context.putVar("numberOfCasesBeingProcessed", numberOfCasesBeingProcessed);
            context.putVar("title", title);
            context.putVar("nameReport", nameReport);
            context.putVar("reportingUnit", reportingUnit);
            context.putVar("unitReceivingReport", unitReceivingReport);
            context.putVar("unitNumberOfDossiers", unitNumberOfDossiers);
            context.putVar("reportingPeriod", reportingPeriod);
            context.putVar("fromDayToDay", fromDayToDay);
            context.putVar("itemDtos", getFormProvince6bExcelDto(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict));
            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            return outputStream.toByteArray();
        }
    }

    public List<ExcelFormProvince6bDto> getFormProvince6bExcelDto(String fromDate, String toDate, String agencyId,
                                                                  List<String> tagIds, String agencyLevelCommune, String agencyLevelDistrict) throws ParseException {
        List<SimpleDossier> simpleDossiers = getFormProvince6b(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict);
        List<ExcelFormProvince6bDto> dtos = new ArrayList<>();
        List<String> agencyLevels =  Arrays.asList(agencyLevelDistrict, agencyLevelCommune);
        int i = 1;
        for(String agencyLevel: agencyLevels){
            ExcelFormProvince6bDto dto = new ExcelFormProvince6bDto();
            dto.setNoRoman(Converter.intToRoman(i));
            if(agencyLevelDistrict.equals(agencyLevel)){
                String agencyLevelName = translator.toLocale("lang.word.form-province-6b.sub-title-agency-level") + " "
                        + translator.toLocale("lang.word.agency-level-district");
                dto.setAgencyLevelName(agencyLevelName);
            }
            if(agencyLevelCommune.equals(agencyLevel)){
                String agencyLevelName = translator.toLocale("lang.word.form-province-6b.sub-title-agency-level") + " "
                        + translator.toLocale("lang.word.agency-level-commune");
                dto.setAgencyLevelName(agencyLevelName);
            }
            ObjectId agencyLevelId = new ObjectId(agencyLevel);
            List<SimpleDossier> simpleDossiersByAgencyLevel = simpleDossiers.stream()
                    .filter(Objects::nonNull)
                    .filter(item -> agencyLevelId.equals(item.getAgencyLevel().getId())).collect(Collectors.toList());
            AtomicLong atomicLong = new AtomicLong(1L);
            List<ExcelFormDetail> itemDetails = simpleDossiersByAgencyLevel.stream()
                    .map(item -> convertSimpleDossierToExcelFormDetail(atomicLong.getAndIncrement(), item)).collect(Collectors.toList());
            dto.setItemDetails(itemDetails);
            dtos.add(dto);
            i++;
        }
        return dtos;
    }

    private ExcelFormDetail convertSimpleDossierToExcelFormDetail(long no, SimpleDossier entity){
        entity.setSectorName(translator.getCurrentLocaleId());
        ExcelFormDetail dto = new ExcelFormDetail();
        Integer receivedOld = 0;
        dto.setNo(no);
        if(Objects.nonNull(entity.getSector())){
            dto.setSectorId(entity.getSector().getId());
        }
        dto.setSector(entity.getSectorName());
        dto.setSum1(entity.getReceived() + receivedOld);
        dto.setAcceptedOnl(entity.getReceivedOnline());
        dto.setReceivedDirect(entity.getReceivedDirect());
        dto.setAcceptedDirect(entity.getReceived() - entity.getReceivedOnline() - entity.getReceivedDirect());
        dto.setPastDossier(receivedOld);

        dto.setSum2(entity.getResolved());
        dto.setResolvedEarly(entity.getResolvedEarly());
        dto.setReturnedOnTime(entity.getReturnOnTime());
        dto.setResolvedOverdue(entity.getResolvedOverdue());

        dto.setSum3(entity.getUnresolved());
        Integer unresolvedHadTime = entity.getUnresolved() - entity.getUnresolvedOverdue();
        dto.setUnresolvedNoTime(unresolvedHadTime);
        dto.setUnresolvedOvertime(entity.getUnresolvedOverdue());
        return dto;
    }

    //end exportExcelFormProvince6b

    // exportExcelFormProvince6a
    public byte[] exportExcelFormProvince6a(Pageable pageable, String fromDate, String toDate, String agencyId, String agencyName, String spec) throws IOException, ParseException {
        try (InputStream is = resourceTemplateProvince6A.getInputStream()) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();
            Page<SimpleDossier> listDossierByDay = getFormProvince6a(pageable, fromDate, toDate, agencyId, spec);
            String nameReport = translator.toLocale("lang.word.form-province-6a.form-no");
            String title = translator.toLocale("lang.word.form-province-6a.title");
            String agencyReceivingReport = "";
            String reportingUnit = translator.toLocale("lang.word.form-province-6a.reporting-unit", new String[]{agencyName});
            String unitReceivingReport = translator.toLocale("lang.word.form-province-6a.unit-receiving-report", new String[]{agencyReceivingReport});
            String unitNumberOfDossiers = translator.toLocale("lang.word.form-province-6a.unit-number-of-dossiers", new String[]{"TTHC"});
            String reportingPeriod = translator.toLocale("lang.word.form-province-6a.reporting-period", new String[]{translator.toLocale("lang.word.year", new String[]{LocalDateTime.now().getYear()+""})});
            String fromDateStr = dateConverter.convertStringDateToDateFormatExcel(fromDate);
            String toDateStr = dateConverter.convertStringDateToDateFormatExcel(toDate);
            String fromDayToDay = translator.toLocale("lang.word.fromDayToDay", new String[]{fromDateStr, toDateStr});
            String no = translator.toLocale("lang.word.no");
            String fieldOfSettlemen = translator.toLocale("lang.word.field-of-settlement");
            String numberOfApplicationsReceived = translator.toLocale("lang.word.number-of-applications-received");
            String inThePeriod = translator.toLocale("lang.word.in-the-period");
            String total = translator.toLocale("lang.word.total");
            String online = translator.toLocale("lang.word.online");
            String direct = translator.toLocale("lang.word.direct");
            String postalServices = translator.toLocale("lang.word.postal-services");
            String fromThePreviousPeriod = translator.toLocale("lang.word.from-the-previous-period");
            String numberOfCasesResolved = translator.toLocale("lang.word.number-of-cases-resolved");
            String byDeadTime = translator.toLocale("lang.word.by-dead-time");
            String onTime = translator.toLocale("lang.word.on-time");
            String outOfDate = translator.toLocale("lang.word.out-of-date");
            String inDueDate = translator.toLocale("lang.word.in-due-date");
            String numberOfCasesBeingProcessed = translator.toLocale("lang.word.number-of-cases-being-processed");
            context.putVar("no", no);
            context.putVar("fieldOfSettlemen", fieldOfSettlemen);
            context.putVar("numberOfApplicationsReceived", numberOfApplicationsReceived);
            context.putVar("inThePeriod", inThePeriod);
            context.putVar("total", total);
            context.putVar("online", online);
            context.putVar("direct", direct);
            context.putVar("postalServices", postalServices);
            context.putVar("fromThePreviousPeriod", fromThePreviousPeriod);
            context.putVar("numberOfCasesResolved", numberOfCasesResolved);
            context.putVar("byDeadTime", byDeadTime);
            context.putVar("onTime", onTime);
            context.putVar("outOfDate", outOfDate);
            context.putVar("inDueDate", inDueDate);
            context.putVar("numberOfCasesBeingProcessed", numberOfCasesBeingProcessed);
            context.putVar("title", title);
            context.putVar("nameReport", nameReport);
            context.putVar("reportingUnit", reportingUnit);
            context.putVar("unitReceivingReport", unitReceivingReport);
            context.putVar("unitNumberOfDossiers", unitNumberOfDossiers);
            context.putVar("reportingPeriod", reportingPeriod);
            context.putVar("fromDayToDay", fromDayToDay);
            context.putVar("itemDtos", getFormProvince6aExcelDto(listDossierByDay));
            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            return outputStream.toByteArray();
        }
    }

    private List<ExcelFormProvince6aDto> getFormProvince6aExcelDto(Page<SimpleDossier> listDossierByDay) {
        AtomicLong i = new AtomicLong(1L);
        return listDossierByDay.getContent().stream()
                .map(item -> convertToProvince6aExcelDto(i.getAndIncrement(), item))
                .collect(Collectors.toList());
    }

    private ExcelFormProvince6aDto convertToProvince6aExcelDto(long no, SimpleDossier entity){
        entity.setSectorName(translator.getCurrentLocaleId());
        ExcelFormProvince6aDto dto = new ExcelFormProvince6aDto();
        Integer receivedOld = 0;
        dto.setNo(no);
        dto.setSector(entity.getSectorName());
        dto.setSum1(entity.getReceived() + receivedOld);
        dto.setAcceptedOnl(entity.getReceivedOnline());
        dto.setReceivedDirect(entity.getReceivedDirect());
        dto.setAcceptedDirect(entity.getReceived() - entity.getReceivedOnline() - entity.getReceivedDirect());
        dto.setPastDossier(receivedOld);

        dto.setSum2(entity.getResolved());
        dto.setResolvedEarly(entity.getResolvedEarly());
        dto.setReturnedOnTime(entity.getReturnOnTime());
        dto.setResolvedOverdue(entity.getResolvedOverdue());

        dto.setSum3(entity.getUnresolved());
        Integer unresolvedHadTime = entity.getUnresolved() - entity.getUnresolvedOverdue();
        dto.setUnresolvedNoTime(unresolvedHadTime);
        dto.setUnresolvedOvertime(entity.getUnresolvedOverdue());
        return dto;
    }

    //end exportExcelFormProvince6a

    public List<GetStatisticDossierBySectorDto> getStatisticDossierByAncestorId(String _fromDate, String _toDate, String _ancestorId, String _agencyId, String _sectorId, String _procedureId) throws ParseException {
        ArrayList<GetStatisticDossierBySectorDto> arrResult = new ArrayList<GetStatisticDossierBySectorDto>();
        ObjectId deploymentId = Context.getDeploymentId();
        List<GetDossierByDay> listResult = new ArrayList<GetDossierByDay>();
        Date fromDate = null;
        if (_fromDate != null && _fromDate != "") {
            fromDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_fromDate);
        }

        Date toDate = null;
        if (_toDate != null && _toDate != "") {
            toDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(_toDate);
        }
        ObjectId ancestorId = (_ancestorId != "" && _ancestorId != null) ? new ObjectId(_ancestorId) : null;
        ObjectId agencyId = (_agencyId != "" && _agencyId != null) ? new ObjectId(_agencyId) : null;
        ObjectId sectorId = (_sectorId != "" && _sectorId != null) ? new ObjectId(_sectorId) : null;
        ObjectId procedureId = (_procedureId != "" && _procedureId != null) ? new ObjectId(_procedureId) : null;

        listResult = dossierByDayRepository.getListDossierByAncestorId(deploymentId, fromDate, toDate, agencyId, sectorId, procedureId, ancestorId);
        if (listResult.size() > 0 && listResult != null) {
            for (GetDossierByDay d : listResult) {
                if (arrResult.size() == 0) {
                    GetStatisticDossierBySectorDto result = new GetStatisticDossierBySectorDto();

                    if (d.getAgency() != null) {
                        result.setAgency(d.getAgency());
                    }
                    if (d.getProcedure() != null) {
                        result.setProcedure(d.getProcedure());
                    }
                    if (d.getProcedureLevel() != null) {
                        result.setProcedureLevel(d.getProcedureLevel());
                    }
                    if (d.getSector() != null) {
                        result.setSector(d.getSector());
                    }
                    result.setAppliedOnline(d.getAppliedOnline());
                    result.setReceived(d.getReceived());
                    result.setReceivedOnline(d.getReceivedOnline());
                    result.setReceivedDirect(d.getReceivedDirect());
                    result.setResolved(d.getResolved());
                    result.setResolvedEarly(d.getResolvedEarly());
                    result.setResolvedOverdue(d.getResolvedOverdue());
                    result.setUnresolved(d.getUnresolved());
                    result.setUnresolvedOverdue(d.getUnresolvedOverdue());
                    result.setCancelled(d.getCancelled());
                    result.setSuspended(d.getSuspended());
                    result.setDeleted(d.getDeleted());
                    result.setReturnOnTime(d.getReturnOnTime());
                    result.setReturnOverdue(d.getReturnOverdue());
                    arrResult.add(result);
                } else {
                    var count = 0;
                    if (d.getAgency() != null && d.getSector() != null && d.getProcedure() != null) {
                        for (GetStatisticDossierBySectorDto r : arrResult) {
                            if (d.getAgency().getId().equals(r.getAgency().getId()) && d.getSector().getId().equals(r.getSector().getId()) && d.getProcedure().getId().equals(r.getProcedure().getId())) {
                                var appliedOnline = r.getAppliedOnline() + d.getAppliedOnline();
                                r.setAppliedOnline(appliedOnline);

                                var received = r.getReceived() + d.getReceived();
                                r.setReceived(received);

                                var receivedOnline = r.getReceivedOnline() + d.getReceivedOnline();
                                r.setReceivedOnline(receivedOnline);

                                var receivedDirect = r.getReceivedDirect() + d.getReceivedDirect();
                                r.setReceivedDirect(receivedDirect);

                                var resolved = r.getResolved() + d.getResolved();
                                r.setResolved(resolved);

                                var resolvedEarly = r.getResolvedEarly() + d.getResolvedEarly();
                                r.setResolvedEarly(resolvedEarly);

                                var resolvedOverdue = r.getResolvedOverdue() + d.getResolvedOverdue();
                                r.setResolvedOverdue(resolvedOverdue);

                                var unresolved = r.getUnresolved() + d.getUnresolved();
                                r.setUnresolved(unresolved);

                                var returnOnTime = r.getReturnOnTime() + d.getReturnOnTime();
                                r.setReturnOnTime(returnOnTime);

                                var unresolvedOverdue = r.getUnresolvedOverdue() + d.getUnresolvedOverdue();
                                r.setUnresolvedOverdue(unresolvedOverdue);
                                count++;
                            }
                        }
                        if (count == 0) {
                            GetStatisticDossierBySectorDto result = new GetStatisticDossierBySectorDto();

                            if (d.getAgency() != null) {
                                result.setAgency(d.getAgency());
                            }
                            if (d.getProcedure() != null) {
                                result.setProcedure(d.getProcedure());
                            }
                            if (d.getProcedureLevel() != null) {
                                result.setProcedureLevel(d.getProcedureLevel());
                            }
                            if (d.getSector() != null) {
                                result.setSector(d.getSector());
                            }
                            result.setAppliedOnline(d.getAppliedOnline());
                            result.setReceived(d.getReceived());
                            result.setReceivedOnline(d.getReceivedOnline());
                            result.setReceivedDirect(d.getReceivedDirect());
                            result.setResolved(d.getResolved());
                            result.setResolvedEarly(d.getResolvedEarly());
                            result.setResolvedOverdue(d.getResolvedOverdue());
                            result.setUnresolved(d.getUnresolved());
                            result.setUnresolvedOverdue(d.getUnresolvedOverdue());
                            result.setCancelled(d.getCancelled());
                            result.setSuspended(d.getSuspended());
                            result.setDeleted(d.getDeleted());
                            result.setReturnOnTime(d.getReturnOnTime());
                            result.setReturnOverdue(d.getReturnOverdue());
                            arrResult.add(result);
                        }
                    }
                }
            }
        }
        return arrResult;
    }

    public GetDossierStatisticalByAgencyCodeDto getDossierStatisticalByAgencyCode(String _month, String _year, String _agencyCode){

        ObjectId agencyId = null;
        Aggregation agg = null;
        Aggregation aggprev = null;
        GetDossierStatisticalByAgencyCodeDto result = new GetDossierStatisticalByAgencyCodeDto();

        String uriFindByCode = null;
        AgencyNameCodeParentAncestorDto newAgencyFromCode = null;
        if(_agencyCode.toString() != ""){
            uriFindByCode = "agency/name+code+parent+ancestor/--fully-by-code?code=" + _agencyCode;
        }
        try {
            UriComponentsBuilder uriBuilder = microservice.basedataUri(uriFindByCode);
            UriComponents uriComponents = uriBuilder.encode().build();
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            String response = digoRestTemplate.exchangeWithResponse(null,uriComponents.toUri(),body, HttpMethod.GET);
            ObjectMapper mapper = new ObjectMapper();
            newAgencyFromCode = mapper.readValue(response, AgencyNameCodeParentAncestorDto.class);
            result.setTenDonVi(newAgencyFromCode.getName().get(0).getName());
            result.setMadonvi(newAgencyFromCode.getCode());
        }catch (Exception e){

        }

        if (!isNullOrEmpty(newAgencyFromCode.getId()) && newAgencyFromCode.getId() != null) {
            agencyId = new ObjectId(newAgencyFromCode.getId());
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(
                                    Criteria.where("agency.id").is(agencyId),
                                    Criteria.where("agency.parent.id").is(agencyId),
                                    Criteria.where("agency.ancestors.id").is(agencyId)
                            ),
                            Criteria.where("month").is(Integer.parseInt(_month)),
                            Criteria.where("year").is(Integer.parseInt(_year))
                    )),
                    group("agency")
                            .first("agency").as("agency")
                            .sum("appliedOnline").as("appliedOnline")
                            .sum("receivedDirect").as("receivedDirect")
                            .sum("received").as("received")
                            .sum("receivedOnline").as("receivedOnline")
                            .sum("resolved").as("resolved")
                            .sum("resolvedEarly").as("resolvedEarly")
                            .sum("resolvedOverdue").as("resolvedOverdue")
                            .sum("cancelled").as("cancelled")
                            .sum("deleted").as("deleted")
                            .sum("suspended").as("suspended")
                            .sum("returnOnTime").as("returnOnTime")
                            .sum("returnOverdue").as("returnOverdue")
                            .sum("unresolved").as("unresolved")
                            .sum("unresolvedOverdue").as("unresolvedOverdue")
            );

            if(Integer.parseInt(_month) > 1){
                aggprev = (Aggregation) newAggregation(
                        match(Criteria.where("").andOperator(
                                Criteria.where("").orOperator(
                                        Criteria.where("agency.id").is(agencyId),
                                        Criteria.where("agency.parent.id").is(agencyId),
                                        Criteria.where("agency.ancestors.id").is(agencyId)
                                ),
                                Criteria.where("month").is(Integer.parseInt(_month) - 1),
                                Criteria.where("year").is(Integer.parseInt(_year))
                        )),
                        group("agency")
                                .first("agency").as("agency")
                                .sum("appliedOnline").as("appliedOnline")
                                .sum("receivedDirect").as("receivedDirect")
                                .sum("received").as("received")
                                .sum("receivedOnline").as("receivedOnline")
                                .sum("resolved").as("resolved")
                                .sum("resolvedEarly").as("resolvedEarly")
                                .sum("resolvedOverdue").as("resolvedOverdue")
                                .sum("cancelled").as("cancelled")
                                .sum("deleted").as("deleted")
                                .sum("suspended").as("suspended")
                                .sum("returnOnTime").as("returnOnTime")
                                .sum("returnOverdue").as("returnOverdue")
                                .sum("unresolved").as("unresolved")
                                .sum("unresolvedOverdue").as("unresolvedOverdue")
                );
            }else{
                aggprev = (Aggregation) newAggregation(
                        match(Criteria.where("").andOperator(
                                Criteria.where("").orOperator(
                                        Criteria.where("agency.id").is(agencyId),
                                        Criteria.where("agency.parent.id").is(agencyId),
                                        Criteria.where("agency.ancestors.id").is(agencyId)
                                ),
                                Criteria.where("month").is(12),
                                Criteria.where("year").is(Integer.parseInt(_year) -1)
                        )),
                        group("agency")
                                .first("agency").as("agency")
                                .sum("appliedOnline").as("appliedOnline")
                                .sum("receivedDirect").as("receivedDirect")
                                .sum("received").as("received")
                                .sum("receivedOnline").as("receivedOnline")
                                .sum("resolved").as("resolved")
                                .sum("resolvedEarly").as("resolvedEarly")
                                .sum("resolvedOverdue").as("resolvedOverdue")
                                .sum("cancelled").as("cancelled")
                                .sum("deleted").as("deleted")
                                .sum("suspended").as("suspended")
                                .sum("returnOnTime").as("returnOnTime")
                                .sum("returnOverdue").as("returnOverdue")
                                .sum("unresolved").as("unresolved")
                                .sum("unresolvedOverdue").as("unresolvedOverdue")
                );
            }
        }
        AggregationResults<SimpleDossier> results;
        results = mongoTemplate.aggregate(agg, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultList = results.getMappedResults();
        AggregationResults<SimpleDossier> resultsPrev;
        resultsPrev = mongoTemplate.aggregate(aggprev, "dossierByDay", SimpleDossier.class);
        List<SimpleDossier> resultListPrev = resultsPrev.getMappedResults();

        var SoNhanTrongKy = 0;
        var SoTonKyTruoc = 0;
        var TongSoXuLy = 0;
        var TongDaXuLy = 0;
        var TongXuLyDungHan = 0;
        var PhanTramXuLyDungHan = 0;
        var TongXuLyTreHan = 0;
        var PhanTramXuLyTreHan = 0;
        var TongChuaXuLy = 0;
        var TongChuaXuLyTrongHan = 0;
        var PhanTramChuaXuLyTrongHan = 0;
        var TongChuaXuLyTreHan = 0;
        var PhanTramChuaXuLyTreHan = 0;
        String agency_id = null;

        if (resultList.size() > 0 && resultList != null) {
            for (SimpleDossier d : resultList) {
                SoNhanTrongKy += d.getReceived();
                TongSoXuLy += d.getResolved() + d.getUnresolved();
                TongDaXuLy += d.getResolved();
                TongXuLyDungHan += d.getResolvedEarly();
                TongXuLyTreHan += d.getResolvedOverdue();
                TongChuaXuLy += d.getUnresolved();
                TongChuaXuLyTrongHan += d.getReturnOnTime();
                TongChuaXuLyTreHan += d.getReturnOverdue();
            }

            for (SimpleDossier d1 : resultListPrev) {
                SoTonKyTruoc += d1.getUnresolved();
            }
        }

        result.setSoNhanTrongKy(String.valueOf(SoNhanTrongKy));
        result.setSoTonKyTruoc(String.valueOf(SoTonKyTruoc));
        result.setTongSoXuLy(String.valueOf(TongSoXuLy));
        result.setTongDaXuLy(String.valueOf(TongDaXuLy));
        result.setTongXuLyDungHan(String.valueOf(TongXuLyDungHan));
        result.setPhanTramXuLyDungHan(String.valueOf(TongSoXuLy != 0 ? Math.round ((float) TongXuLyDungHan * 100 / TongSoXuLy *100.0) /100.0 : 0));
        result.setTongXuLyTreHan(String.valueOf(TongXuLyTreHan));
        result.setPhanTramXuLyTreHan(String.valueOf(TongSoXuLy != 0 ?  Math.round ((float) TongXuLyTreHan * 100 / TongSoXuLy *100.0) /100.0 : 0));
        result.setTongChuaXuLy(String.valueOf(TongChuaXuLy));
        result.setTongChuaXuLyTrongHan(String.valueOf(TongChuaXuLyTrongHan));
        result.setPhanTramChuaXuLyTrongHan(String.valueOf(TongChuaXuLy != 0 ? Math.round ((float)TongChuaXuLyTrongHan * 100 / TongChuaXuLy *100.0) /100.0 : 0));
        result.setTongChuaXuLyTreHan(String.valueOf(TongChuaXuLyTreHan));
        result.setPhanTramChuaXuLyTreHan(String.valueOf(TongChuaXuLy != 0 ? Math.round ((float) TongChuaXuLyTreHan * 100 / TongChuaXuLy *100.0) /100.0 : 0));
        result.setThang(String.valueOf(Integer.parseInt(_month)));
        result.setNam(String.valueOf(Integer.parseInt(_year)));
        result.setLoaiThongKe("1");

        return result;
    }

    public GetDossierStatisticalByProcedureIdDto getDossierStatisticalByProcedureId(String procedureCode, String fromDate, String toDate) throws ParseException {
        Date fromDateTemp = null;
        Date toDateTemp = null;
        if (fromDate != null && fromDate != "") {
            fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        }
        if (toDate != null && toDate != "") {
            toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        }
        Criteria aCriteria = new Criteria();
        GetDossierStatisticalByProcedureIdDto result = new GetDossierStatisticalByProcedureIdDto();

        Aggregation agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        procedureCode != null ? Criteria.where("procedure.code").is(procedureCode): aCriteria,
                        fromDateTemp != null ? Criteria.where("updatedDate").gte(fromDateTemp): aCriteria,
                        toDateTemp != null ? Criteria.where("updatedDate").lte(toDateTemp): aCriteria
                ))
        );
        List<GetDossierByDay> listResult = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class).getMappedResults();

        if (listResult.size() > 0 && listResult != null) {
            result.setTotalDossier(listResult.size());
        }
        else {
            result.setTotalDossier(0);
        }
        result.setId(procedureCode);
        return result;
    }

    public List<GetDossierByII8Dto> getDossierStatisticalQBH(ObjectId agencyLevelId, String fromDate, String toDate) throws ParseException, JSONException {

        GetDossierStatisticalByProcedureIdDto result = new GetDossierStatisticalByProcedureIdDto();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        List<String> listAgencyLevel = new ArrayList<>();
        var listSectorDto =  this.getListSectorByAgencyLevelQBH(agencyLevelId);
        List<ObjectId> listSectorId = new ArrayList<>();
        listSectorId = listSectorDto.stream().map(x->x.getId()).collect(Collectors.toList());
        for(int i= 0; i<listSectorId.size();i++ ){
            List<GetDossierByII8Dto> items = new ArrayList<>();
            items = this.getListDossierBySectorQBH(listSectorId.get(i),  fromDate,  toDate,agencyLevelId);
            listDossierByII8.addAll(items);
        }

        return listDossierByII8;
    }
    private List<GetDossierByII8Dto> mapNameForGetDossierByII8(List<GetDossierByDay> resultList, List<GetDossierByII8Dto> listNpad){
        List<GetDossierByII8Dto> resultListGetDossierDay = new ArrayList<>();
        try {

            for (GetDossierByII8Dto item : listNpad) {
                AtomicInteger totalRecevier = new AtomicInteger();
                List<GetDossierByDay> dto = resultList.stream().filter(e -> e.getProcedure().getTranslate().get(0).getName().equals(item.getProcedureName())).collect(Collectors.toList());
                if (Objects.nonNull(dto)) {
                    dto.forEach(x -> totalRecevier.addAndGet(x.getReceived()));
                    item.setReceived(totalRecevier.intValue());
                }
                if (resultListGetDossierDay.stream().filter(x -> x.getProcedureName().equals(item.getProcedureName())).collect(Collectors.toList()).size() == 0)
                    resultListGetDossierDay.add(item);

            }
            return resultListGetDossierDay;
        }catch(Exception e){
            return resultListGetDossierDay;
        }
    }

    private List<SectorByAgencyReturnDto> getListSectorByAgencyLevel(ObjectId agencyLevel) throws JSONException {
        String parentURL = "procedure/--get-sector-public-by-agency-level";
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONArray objProcedure = new JSONArray(parentJson);
        List<SectorByAgencyReturnDto> listSector = new ArrayList<>();
        for (int i = 0; i < objProcedure.length(); i++) {
            SectorByAgencyReturnDto sector = new SectorByAgencyReturnDto();
            var aa= objProcedure.getJSONObject(i).get("id").toString();
            if(Objects.nonNull(aa) && !aa.equals("null")) {
                sector.setId(new ObjectId(objProcedure.getJSONObject(i).get("id").toString()));
                sector.setName(objProcedure.getJSONObject(i).get("name").toString());
                listSector.add(sector);
            }
        }
        return listSector;
    }
    private List<SectorByAgencyReturnDto> getListSectorByAgencyLevelQBH(ObjectId agencyLevel) throws JSONException {
        //String parentURL = "procedure/--get-sector-public-by-agency-level?agency-level-id=" + agencyLevel;
        String parentURL = "procedure/--get-sector-public-by-agency-level?agency-level-id=5ff6b1a706d0e31c6bf13e09";
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONArray objProcedure = new JSONArray(parentJson);
        List<SectorByAgencyReturnDto> listSector = new ArrayList<>();
        for (int i = 0; i < objProcedure.length(); i++) {
            SectorByAgencyReturnDto sector = new SectorByAgencyReturnDto();
            var aa= objProcedure.getJSONObject(i).get("id").toString();
            if(Objects.nonNull(aa) && !aa.equals("null")) {
                sector.setId(new ObjectId(objProcedure.getJSONObject(i).get("id").toString()));
                sector.setName(objProcedure.getJSONObject(i).get("name").toString());
                listSector.add(sector);
            }
        }
        return listSector;
    }
    private List<GetDossierByII8Dto> getListDossierBySector(ObjectId sectorId, String fromDate, String toDate,ObjectId agencyLevelId) throws JSONException, ParseException {
        Date fromDateTemp = null;
        Date toDateTemp = null;
        if (fromDate != null && fromDate != "") {
            fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        }
        if (toDate != null && toDate != "") {
            toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        }
        Criteria aCriteria = new Criteria();
        String parentURL = "procedure/--by-sector-public?sector-id=" +sectorId;
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONObject objProcedure = new JSONObject(parentJson);
        var parentJsonObject = objProcedure.get("content").toString();
        List<ObjectId> listprod = new ArrayList<>();
        JSONArray parentJsonAr = new JSONArray(parentJsonObject);
        List<GetDossierByII8Dto> listNpad = new ArrayList<>();
        for (int i = 0; i < parentJsonAr.length(); i++) {
            GetDossierByII8Dto item = new GetDossierByII8Dto();
            listprod.add( new ObjectId(parentJsonAr.getJSONObject(i).get("id").toString()));
            if(parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("null")
                    || parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("false"))
                item.setIsNpad(0);
            else {
                item.setIsNpad(1);
            }
            item.setProcedureId(parentJsonAr.getJSONObject(i).get("id").toString());
            JSONObject levelObj = new JSONObject(parentJsonAr.getJSONObject(i).get("level").toString());
            var level =  levelObj.get("code").toString();
            if(level.equals("MUCDO_3"))
                item.setIsLevel3(1);
            else
                item.setIsLevel4(1);
            item.setReceived(0);

            item.setProcedureName(parentJsonAr.getJSONObject(i).get("name").toString());
            item.setSectorName(parentJsonAr.getJSONObject(i).get("sectorName").toString());
            item.setSectorId(sectorId.toString());
            listNpad.add(item);
        }
        Aggregation agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        listprod != null ? Criteria.where("procedure.id").in(listprod): aCriteria,
                        fromDateTemp != null ? Criteria.where("updatedDate").gte(fromDateTemp): aCriteria,
                        toDateTemp != null ? Criteria.where("updatedDate").lte(toDateTemp): aCriteria
                ))
        );
        List<GetDossierByDay> listResult = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class).getMappedResults();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        listDossierByII8 =  mapNameForGetDossierByII8(listResult,listNpad);
        return listDossierByII8;
    }
    private List<GetDossierByII8Dto> getListDossierBySectorQBH(ObjectId sectorId, String fromDate, String toDate,ObjectId agencyLevelId) throws JSONException, ParseException {
        Date fromDateTemp = null;
        Date toDateTemp = null;
        if (fromDate != null && fromDate != "") {
            fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        }
        if (toDate != null && toDate != "") {
            toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        }
        Criteria aCriteria = new Criteria();
        String parentURL = "procedure/--by-sector-public?sector-id=" +sectorId;
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONObject objProcedure = new JSONObject(parentJson);
        var parentJsonObject = objProcedure.get("content").toString();
        List<ObjectId> listprod = new ArrayList<>();
        JSONArray parentJsonAr = new JSONArray(parentJsonObject);
        List<GetDossierByII8Dto> listNpad = new ArrayList<>();
        for (int i = 0; i < parentJsonAr.length(); i++) {
            var listIdString = parentJsonAr.getJSONObject(i).get("agencyLevelId").toString();
            JSONArray listId = new JSONArray(listIdString);
            for(int j = 0; j <listId.length(); j++) {
                GetDossierByII8Dto item = new GetDossierByII8Dto();
                listprod.add(new ObjectId(parentJsonAr.getJSONObject(i).get("id").toString()));
                if (parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("null")
                        || parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("false"))
                    item.setIsNpad(0);
                else {
                    item.setIsNpad(1);
                }
                item.setProcedureId(parentJsonAr.getJSONObject(i).get("id").toString());
                JSONObject levelObj = new JSONObject(parentJsonAr.getJSONObject(i).get("level").toString());
                var level = levelObj.get("code").toString();
                if (level.equals("MUCDO_3"))
                    item.setIsLevel3(1);
                else
                    item.setIsLevel4(1);
                item.setReceived(0);

                item.setProcedureName(parentJsonAr.getJSONObject(i).get("name").toString());
                item.setSectorName(parentJsonAr.getJSONObject(i).get("sectorName").toString());
                item.setSectorId(sectorId.toString());
                item.setAgencyLevelId(listId.getString(j));
                listNpad.add(item);
            }
        }
        Aggregation agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        listprod != null ? Criteria.where("procedure.id").in(listprod): aCriteria,
                        fromDateTemp != null ? Criteria.where("updatedDate").gte(fromDateTemp): aCriteria,
                        toDateTemp != null ? Criteria.where("updatedDate").lte(toDateTemp): aCriteria
                ))
        );
        List<GetDossierByDay> listResult = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class).getMappedResults();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        if(listResult.size() >0 )
        {
            for(GetDossierByDay d : listResult) {
                for (GetDossierByII8Dto item : listNpad) {
                    if (d.getProcedure().getId().equals(new ObjectId(item.getProcedureId()))) {
                        listDossierByII8 = mapNameForGetDossierByII8(listResult, listNpad);
                    }
                }
            }
        }
        else {
            listDossierByII8 = mapNameForGetDossierByII8(listResult, listNpad);

        }
        return listDossierByII8;
    }



    public List<GetDossierByII8Dto> getDossierStatisticalCMU(ObjectId agencyLevelId, String fromDate, String toDate, List<String> levelCodes,String levelCode) throws ParseException, JSONException {

        GetDossierStatisticalByProcedureIdDto result = new GetDossierStatisticalByProcedureIdDto();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        List<String> listAgencyLevel = new ArrayList<>();
        var listSectorDto =  this.getListSectorByAgencyLevelCMU(agencyLevelId,levelCodes);
        List<ObjectId> listSectorId = new ArrayList<>();
        listSectorId = listSectorDto.stream().map(x->x.getId()).collect(Collectors.toList());
        for(int i= 0; i<listSectorId.size();i++ ){
            List<GetDossierByII8Dto> items = new ArrayList<>();
            System.out.println(listSectorId.get(i));
            items = this.getListDossierBySectorCMU(listSectorId.get(i),  fromDate,  toDate,agencyLevelId,levelCodes,levelCode);
            listDossierByII8.addAll(items);
        }

        return listDossierByII8;
    }

    private List<SectorByAgencyReturnDto> getListSectorByAgencyLevelCMU(ObjectId agencyLevel, List<String> levelCodes) throws JSONException
    {
        String parentURL = "procedure/--get-sector-public-by-agency-level-cmu?agency-level-id=" + agencyLevel;
        for (String levelCode : levelCodes) {
            parentURL += "&level-codes=" + levelCode;
        }
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        System.out.println(getParentUrl);
        //StringBuilder getParentUrlBuilder = new StringBuilder("http://localhost:8069/procedure/--get-sector-public-by-agency-level-cmu?agency-level-id=").append(agencyLevel);
        //levelCodes.forEach(el->getParentUrlBuilder.append("&level-codes="+el));
        //String getParentUrl = getParentUrlBuilder.toString();
        System.out.println(getParentUrl);
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONArray objProcedure = new JSONArray(parentJson);
        List<SectorByAgencyReturnDto> listSector = new ArrayList<>();
        for (int i = 0; i < objProcedure.length(); i++) {
            SectorByAgencyReturnDto sector = new SectorByAgencyReturnDto();
            var aa= objProcedure.getJSONObject(i).get("id").toString();
            if(Objects.nonNull(aa) && !aa.equals("null")) {
                sector.setId(new ObjectId(objProcedure.getJSONObject(i).get("id").toString()));
                sector.setName(objProcedure.getJSONObject(i).get("name").toString());
                listSector.add(sector);
            }
        }
        return listSector;
    }
    private List<GetDossierByII8Dto> getListDossierBySectorCMU(ObjectId sectorId, String fromDate, String toDate,ObjectId agencyLevelId,
                                                               List<String> levelCodes, String levelCode) throws JSONException, ParseException {
        Date fromDateTemp = null;
        Date toDateTemp = null;
        if (fromDate != null && fromDate != "") {
            fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        }
        if (toDate != null && toDate != "") {
            toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        }
        Criteria aCriteria = new Criteria();
        String parentURL = "procedure/--by-sector-public-cmu?sector-id=" +sectorId + "&agency-level-id=" + agencyLevelId;
        for (String el : levelCodes) {
            parentURL += "&level-codes=" + el;
        }
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        //StringBuilder getParentUrlBuilder = new StringBuilder("http://localhost:8069/procedure/--by-sector-public-cmu?sector-id=").append(sectorId);
        //levelCodes.forEach(el->getParentUrlBuilder. append("&level-codes="+el));
        //String getParentUrl = getParentUrlBuilder.toString();
        //System.out.println(getParentUrlBuilder.toString());
        System.out.println(getParentUrl);;
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONObject objProcedure = new JSONObject(parentJson);
        var parentJsonObject = objProcedure.get("content").toString();
        List<ObjectId> listprod = new ArrayList<>();
        JSONArray parentJsonAr = new JSONArray(parentJsonObject);
        List<GetDossierByII8Dto> listNpad = new ArrayList<>();
        for (int i = 0; i < parentJsonAr.length(); i++) {
            GetDossierByII8Dto item = new GetDossierByII8Dto();
            listprod.add( new ObjectId(parentJsonAr.getJSONObject(i).get("id").toString()));
            if(parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("null")
                    || parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("false"))
                item.setIsNpad(0);
            else {
                item.setIsNpad(1);
            }
            item.setProcedureId(parentJsonAr.getJSONObject(i).get("id").toString());
            JSONObject levelObj = new JSONObject(parentJsonAr.getJSONObject(i).get("level").toString());
            var level =  levelObj.get("code").toString();
            if(level.equals(levelCode))
                item.setIsLevel3(1);
            else
                item.setIsLevel4(1);
            item.setReceived(0);

            item.setProcedureName(parentJsonAr.getJSONObject(i).get("name").toString());
            item.setSectorName(parentJsonAr.getJSONObject(i).get("sectorName").toString());
            item.setSectorId(sectorId.toString());
            listNpad.add(item);
        }
        Aggregation agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        listprod != null ? Criteria.where("procedure.id").in(listprod): aCriteria,
                        fromDateTemp != null ? Criteria.where("updatedDate").gte(fromDateTemp): aCriteria,
                        toDateTemp != null ? Criteria.where("updatedDate").lte(toDateTemp): aCriteria,
                        agencyLevelId != null ? Criteria.where("agencyLevel.id").is(agencyLevelId): aCriteria
                ))
        );
        List<GetDossierByDay> listResult = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class).getMappedResults();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        listDossierByII8 =  mapNameForGetDossierByII8(listResult,listNpad);
        return listDossierByII8;
    }
    public List<ProcedureGenerateDossierDto> generatedDossierProcedure(Pageable pageable, String fromDate, String toDate, List<String> _agencyIds, String spec, List<String> levelCodes) throws ParseException {
        ObjectId agencyId = null;
        Aggregation agg = null;
        Date fromDateTemp = null;
        Date toDateTemp = null;
        List<ProcedureGenerateDossierDto> dtoList = new ArrayList<>();

        fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        for(String _agencyId : _agencyIds){
            agencyId = new ObjectId(_agencyId);
            agg = (Aggregation) newAggregation(
                    match(Criteria.where("").andOperator(
                            Criteria.where("").orOperator(Criteria.where("agency.id").is(agencyId), Criteria.where("agency.parent.id").is(agencyId), Criteria.where("agency.ancestors.id").is(agencyId)),
                            Criteria.where("updatedDate").gte(fromDateTemp),
                            Criteria.where("updatedDate").lte(toDateTemp),
                            Criteria.where("procedureLevel.id").in(levelCodes.stream().map(ObjectId::new).collect(Collectors.toList()))
                    ))
                    //group("agency.id").count().as("countProcedureGenerateDossier")
//                    group("sector.id", "procedure")
//                            .first("sector").as("sector")
//                            .first("procedure").as("procedure")
            );

            AggregationResults<ProcedureGenerateDossierDto> results;
            results = mongoTemplate.aggregate(agg, "dossierByDay", ProcedureGenerateDossierDto.class);
            List<ProcedureGenerateDossierDto> resultList = results.getMappedResults();

            ProcedureGenerateDossierDto procedureGenerateDossierDto = new ProcedureGenerateDossierDto();
            procedureGenerateDossierDto.setId(agencyId);
            procedureGenerateDossierDto.setCountProcedureGenerateDossier(resultList.size());
            dtoList.add(procedureGenerateDossierDto);

        }
        return dtoList.stream()
                .sorted(Comparator.comparing(ProcedureGenerateDossierDto::getId))
                .collect(Collectors.toList());
    }
    public List<GetDossierByII8Dto> getDossierStatisticalAGG(ObjectId agencyLevelId, String fromDate, String toDate, List<String> levelCodes, String levelCode) throws ParseException, JSONException
    {

        GetDossierStatisticalByProcedureIdDto result = new GetDossierStatisticalByProcedureIdDto();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        List<String> listAgencyLevel = new ArrayList<>();
        var listSectorDto =  this.getListSectorByAgencyLevelAGG(agencyLevelId,levelCodes);
        List<ObjectId> listSectorId = new ArrayList<>();
        listSectorId = listSectorDto.stream().map(x->x.getId()).collect(Collectors.toList());
        for(int i= 0; i<listSectorId.size();i++ ){
            List<GetDossierByII8Dto> items = new ArrayList<>();
            System.out.println(listSectorId.get(i));
            items = this.getListDossierBySectorAGG(listSectorId.get(i),  fromDate,  toDate,agencyLevelId,levelCodes,levelCode);
            listDossierByII8.addAll(items);
        }

        return listDossierByII8;
    }
    private List<GetDossierByII8Dto> getListDossierBySectorAGG(ObjectId sectorId, String fromDate, String toDate, ObjectId agencyLevelId,
                                                               List<String> levelCodes, String levelCode) throws JSONException, ParseException
    {
        Date fromDateTemp = null;
        Date toDateTemp = null;
        if (fromDate != null && fromDate != "") {
            fromDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(fromDate + ".000Z");
        }
        if (toDate != null && toDate != "") {
            toDateTemp = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(toDate + ".999Z");
        }
        Criteria aCriteria = new Criteria();
        String parentURL = "procedure/--by-sector-public-agg?sector-id=" +sectorId + "&agency-level-id=" + agencyLevelId;
        for (String el : levelCodes) {
            parentURL += "&level-codes=" + el;
        }
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        System.out.println(getParentUrl);
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONObject objProcedure = new JSONObject(parentJson);
        var parentJsonObject = objProcedure.get("content").toString();
        List<ObjectId> listprod = new ArrayList<>();
        JSONArray parentJsonAr = new JSONArray(parentJsonObject);
        List<GetDossierByII8Dto> listNpad = new ArrayList<>();
        for (int i = 0; i < parentJsonAr.length(); i++) {
            GetDossierByII8Dto item = new GetDossierByII8Dto();
            listprod.add( new ObjectId(parentJsonAr.getJSONObject(i).get("id").toString()));
            if(parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("null")
                    || parentJsonAr.getJSONObject(i).get("isNpad").toString().equals("false"))
                item.setIsNpad(0);
            else {
                item.setIsNpad(1);
            }
            item.setProcedureId(parentJsonAr.getJSONObject(i).get("id").toString());
            JSONObject levelObj = new JSONObject(parentJsonAr.getJSONObject(i).get("level").toString());
            var level =  levelObj.get("code").toString();
            if(level.equals(levelCode))
                item.setIsLevel3(1);
            else
                item.setIsLevel4(1);
            item.setReceived(0);
            item.setCode(parentJsonAr.getJSONObject(i).get("code").toString());

            item.setProcedureName(parentJsonAr.getJSONObject(i).get("name").toString());
            item.setSectorName(parentJsonAr.getJSONObject(i).get("sectorName").toString());
            item.setSectorId(sectorId.toString());
            listNpad.add(item);
        }
        Aggregation agg = (Aggregation) newAggregation(
                match(Criteria.where("").andOperator(
                        listprod != null ? Criteria.where("procedure.id").in(listprod): aCriteria,
                        fromDateTemp != null ? Criteria.where("updatedDate").gte(fromDateTemp): aCriteria,
                        toDateTemp != null ? Criteria.where("updatedDate").lte(toDateTemp): aCriteria,
                        agencyLevelId != null ? Criteria.where("agencyLevel.id").is(agencyLevelId): aCriteria
                ))
        );
        List<GetDossierByDay> listResult = mongoTemplate.aggregate(agg, "dossierByDay", GetDossierByDay.class).getMappedResults();
        List<GetDossierByII8Dto> listDossierByII8 = new ArrayList<>();
        listDossierByII8 =  mapNameForGetDossierByII8AGG(listResult,listNpad);
        return listDossierByII8;
    }
    private List<GetDossierByII8Dto> mapNameForGetDossierByII8AGG(List<GetDossierByDay> resultList, List<GetDossierByII8Dto> listNpad){
        List<GetDossierByII8Dto> resultListGetDossierDay = new ArrayList<>();
        try {

            for (GetDossierByII8Dto item : listNpad) {
                AtomicInteger totalRecevier = new AtomicInteger();
                List<GetDossierByDay> dto = resultList.stream().filter(e -> e.getProcedure().getTranslate().get(0).getName().equals(item.getProcedureName())).collect(Collectors.toList());
                if (Objects.nonNull(dto)) {
                    dto.forEach(x -> totalRecevier.addAndGet(x.getReceived()));
                    item.setReceived(totalRecevier.intValue());
                }
                if (resultListGetDossierDay.stream().filter(x -> x.getProcedureName().equals(item.getProcedureName())).collect(Collectors.toList()).size() == 0)
                    resultListGetDossierDay.add(item);
            }
            return resultListGetDossierDay;
        }catch(Exception e){
            return resultListGetDossierDay;
        }
    }
    private List<SectorByAgencyReturnDto> getListSectorByAgencyLevelAGG(ObjectId agencyLevel, List<String> levelCodes) throws JSONException
    {
        String parentURL = "procedure/--get-sector-public-by-agency-level-agg?agency-level-id=" + agencyLevel;
        for (String levelCode : levelCodes) {
            parentURL += "&level-codes=" + levelCode;
        }
        String getParentUrl = microservice.basepadUri(parentURL).toUriString();
        System.out.println(getParentUrl);
        String parentJson = MicroserviceExchange.get(restTemplate, getParentUrl, String.class);
        JSONArray objProcedure = new JSONArray(parentJson);
        List<SectorByAgencyReturnDto> listSector = new ArrayList<>();
        for (int i = 0; i < objProcedure.length(); i++) {
            SectorByAgencyReturnDto sector = new SectorByAgencyReturnDto();
            var aa= objProcedure.getJSONObject(i).get("id").toString();
            if(Objects.nonNull(aa) && !aa.equals("null")) {
                sector.setId(new ObjectId(objProcedure.getJSONObject(i).get("id").toString()));
                sector.setName(objProcedure.getJSONObject(i).get("name").toString());
                listSector.add(sector);
            }
        }
        return listSector;
    }
}
