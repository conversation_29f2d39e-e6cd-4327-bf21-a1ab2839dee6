/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DecodedAccessToken implements Serializable {
    private String deployment_id;
    
    public ObjectId getDeploymentId() {
        if (deployment_id != null) {
            return new ObjectId(deployment_id);
        }
        return null;
    }
}