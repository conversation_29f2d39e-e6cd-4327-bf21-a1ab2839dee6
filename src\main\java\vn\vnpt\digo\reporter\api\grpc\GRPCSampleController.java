package vn.vnpt.digo.reporter.api.grpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.service.GRPCSample.GRPCSampleService;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/grpc-sample")
@IcodeAuthorize("vnpt.permission.manageDigo")
public class GRPCSampleController {

    @Autowired
    GRPCSampleService grpcSampleService;

    @GetMapping("/--test-grpc-adapter")
    public ResponseEntity<Object> testGRPCAdapter(HttpServletRequest request,
                                                  @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCAdapter(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-basecat")
    public ResponseEntity<Object> testGRPCBasecat(HttpServletRequest request,
                                                  @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCBasecat(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-basedata")
    public ResponseEntity<Object> testGRPCBasedata(HttpServletRequest request,
                                                   @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCBasedata(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-bpm")
    public ResponseEntity<Object> testGRPCBpm(HttpServletRequest request,
                                              @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCBpm(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-eform")
    public ResponseEntity<Object> testGRPCEform(HttpServletRequest request,
                                                @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCEform(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-fileman")
    public ResponseEntity<Object> testGRPCFileman(HttpServletRequest request,
                                                  @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCFileman(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-human")
    public ResponseEntity<Object> testGRPCHuman(HttpServletRequest request,
                                                @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCHuman(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-logman")
    public ResponseEntity<Object> testGRPCLogman(HttpServletRequest request,
                                                 @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCLogman(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-messenger")
    public ResponseEntity<Object> testGRPCMessenger(HttpServletRequest request,
                                                    @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCMessenger(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-modeling")
    public ResponseEntity<Object> testGRPCModeling(HttpServletRequest request,
                                                   @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCModeling(input);
        return ResponseEntity.ok(result);
    }
//
//    @GetMapping("/--test-grpc-reporter")
//    public ResponseEntity<Object> testGRPCReporter(HttpServletRequest request,
//                                                   @RequestParam(value = "input", required = false) String input){
//        String result = grpcSampleService.testGRPCReporter(input);
//        return ResponseEntity.ok(result);
//    }

    @GetMapping("/--test-grpc-sysman")
    public ResponseEntity<Object> testGRPCSysman(HttpServletRequest request,
                                                 @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCSysman(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-system")
    public ResponseEntity<Object> testGRPCSystem(HttpServletRequest request,
                                                 @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCSystem(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-basepad")
    public ResponseEntity<Object> testGRPCBasepad(HttpServletRequest request,
                                                  @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCBasepad(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-padman")
    public ResponseEntity<Object> testGRPCPadman(HttpServletRequest request,
                                                 @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCPadman(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-rbonegate")
    public ResponseEntity<Object> testGRPCRbonegate(HttpServletRequest request,
                                                    @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCRbonegate(input);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/--test-grpc-storage")
    public ResponseEntity<Object> testGRPCStorage(HttpServletRequest request,
                                                  @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCStorage(input);
        return ResponseEntity.ok(result);
    }


    @GetMapping("/--test-grpc-surfeed")
    public ResponseEntity<Object> testGRPCSurfeed(HttpServletRequest request,
                                                  @RequestParam(value = "input", required = false) String input){
        String result = grpcSampleService.testGRPCSurfeed(input);
        return ResponseEntity.ok(result);
    }


}
