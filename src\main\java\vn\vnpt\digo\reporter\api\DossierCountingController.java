package vn.vnpt.digo.reporter.api;


import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.document.DossierCounting;
import vn.vnpt.digo.reporter.document.DossierCountingLog;
import vn.vnpt.digo.reporter.document.DossierSynthesis;
import vn.vnpt.digo.reporter.document.KTMETLDossier;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.properties.DossierCountingProperties;
import vn.vnpt.digo.reporter.service.DossierCountingService;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import static com.google.common.base.Strings.isNullOrEmpty;

@RestController
@RequestMapping("/dossiercounting")
@IcodeAuthorize("vnpt.permission.manageDigo")
public class DossierCountingController {

  @Autowired
  private DossierCountingService dossierCountingService;

  Logger logger = LoggerFactory.getLogger(DossierByDayController.class);

  @PostMapping("/--by-day")
  public ResponseEntity postReportDossierByDay(HttpServletRequest request,
                                               @RequestParam(value = "by-day") String byDay,
                                               @RequestParam(value = "list-tag-agency-id") String listTagAgencyId,
                                               @RequestParam(value = "list-is-dept") String listIsDept,
                                               @RequestParam(value = "milestone",required = false) String milestone

  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result = dossierCountingService.postReportDossierByDay(byDay, listTagAgencyId, listIsDept,milestone);
    return ResponseEntity.ok(result);
  }
  @PostMapping("/--by-day-agency")
  public ResponseEntity postReportDossierByDayAgency(HttpServletRequest request,
                                               @RequestParam(value = "by-day") String byDay,
                                               @RequestParam(value = "agency-id") String agencyId,
                                               @RequestParam(value = "tag-agency-id") String tagAgencyId,
                                               @RequestParam(value = "is-dept") boolean isDept,
                                               @RequestParam(value = "milestone",required = false) String milestone
  ) throws ParseException, IOException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result = dossierCountingService.postReportDossierByDayAgency( byDay,agencyId,isDept,tagAgencyId,milestone);
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--from-to-by-agency")
  public ResponseEntity getReportDossierFromTo(HttpServletRequest request,
                                               @RequestParam(value = "from-date") String fromDate,
                                               @RequestParam(value = "to-date") String toDate,
                                               @RequestParam(value = "agency-id", required = false) String agencyId,
                                               @RequestParam(value = "sector-id", required = false) String sectorId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    GetDossierCountingDto listDossierByDay = dossierCountingService.getReportDossierFromTo(fromDate, toDate, agencyId, null, sectorId, false);
    return ResponseEntity.ok(listDossierByDay);
  }
  @GetMapping("/--duplicate")
  public ResponseEntity getDuplicateInfo(HttpServletRequest request,
                                         @RequestParam(value = "by-day") String day,
                                         @RequestParam(value = "tag-agency-id", required = false) String tagAgencyId,
                                         @RequestParam(value = "agency-id", required = false) String agencyId,
                                         @RequestParam(value = "sector-id", required = false) String sectorId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    List<DuplicatesDataDto> result=  dossierCountingService.getDuplicateInfo(day, agencyId,tagAgencyId,sectorId);
    return ResponseEntity.ok(result);
  }
  //phuongktm
  @GetMapping("/--from-to-by-agency-sector")
  public ResponseEntity getReportDossierByAgencySector(HttpServletRequest request,
                                                       @RequestParam(value = "from-date") String fromDate,
                                                       @RequestParam(value = "to-date") String toDate,
                                                       @RequestParam(value = "agency-id", required = false) String agencyId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    List<GetDossierCountingSectorDto> listDossierByDay = dossierCountingService.getReportDossierFromToByAgency(fromDate, toDate, agencyId, null, false);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--from-to-bundle")
  public ResponseEntity getReportDossierFromToBundle(HttpServletRequest request,
                                                     @RequestParam(value = "from-date") String fromDate,
                                                     @RequestParam(value = "to-date") String toDate,
                                                     @RequestParam(value = "parent-id", required = false) String parentId,
                                                     @RequestParam(value = "tag-agency-id", required = false) String tagAgencyId
  ) throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    List<GetDossierCountingDto> listDossierByDay = dossierCountingService.getReportDossierFromToBundle(fromDate, toDate, parentId, tagAgencyId);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--by")
  public ResponseEntity getReportDossierFromTo(HttpServletRequest request,
                                               @RequestParam(value = "year") String year,
                                               @RequestParam(value = "tag-id", required = false) String listTagId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    GetDossierCountingDto listDossierByDay = dossierCountingService.getReportDossierByYear(year,listTagId);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--run")
  public ResponseEntity getReportDossierRun(HttpServletRequest request,
                                            @RequestParam(value = "from") String from,
                                            @RequestParam(value = "to") String to,
                                            @RequestParam(value = "list-tag-agency-id") String listTagAgencyId,
                                            @RequestParam(value = "list-is-dept") String listIsDept,
                                            @RequestParam(value = "milestone",required = false) String milestone
  ) throws ParseException, JSONException, IOException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

//    dossierCountingService.runReportDossier(from,to,listTagAgencyId,listIsDept);
    return ResponseEntity.ok(dossierCountingService.runReportDossier(from, to, listTagAgencyId, listIsDept,milestone));
  }

  @GetMapping("/--from-to-by-tag-agency")
  public ResponseEntity getReportDossierFromTo(HttpServletRequest request,
                                               @RequestParam(value = "from-date") String fromDate,
                                               @RequestParam(value = "to-date") String toDate,
                                               @RequestParam(value = "agency-tag-id") String agencyTagId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    GetDossierCountingDto listDossierByDay = dossierCountingService.getReportDossierFromTo(fromDate, toDate, null, agencyTagId, null, true);
    return ResponseEntity.ok(listDossierByDay);
  }
  @DeleteMapping("/--duplicate")
  public ResponseEntity deleteDuplicateInfo(HttpServletRequest request,
                                            @RequestParam(value = "by-day") String day,
                                            @RequestParam(value = "tag-agency-id", required = false) String tagAgencyId,
                                            @RequestParam(value = "agency-id", required = false) String agencyId,
                                            @RequestParam(value = "sector-id", required = false) String sectorId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result=  dossierCountingService.deleteDuplicate(day, agencyId,tagAgencyId,sectorId);
    return ResponseEntity.ok(result);
  }
  @DeleteMapping("/{id}/--by-day")
  public ResponseEntity deleteReportDossierByDay(HttpServletRequest request,
                                                 @PathVariable(value = "id", required = true) ObjectId id) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result = dossierCountingService.deleteReportDossierByDay(id);
    return ResponseEntity.ok(result);
  }

  @DeleteMapping("/--by-day")
  public ResponseEntity deleteReportDossierByDay(HttpServletRequest request,
                                                 @RequestParam(value = "by-day", required = false) String byDay,
                                                 @RequestParam(value = "by-tag", required = false) String byTag,
                                                 @RequestParam(value = "agency-id", required = false) String agencyId,
                                                 @RequestParam(value = "sector-id", required = false) String sectorId) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    AffectedRowsDto result = dossierCountingService.deleteReportDossierByDay(byDay, agencyId, sectorId,byTag);
    return ResponseEntity.ok(result);
  }

  @GetMapping("/{id}/--by-day")
  public ResponseEntity getReportDossierById(HttpServletRequest request,
                                             @PathVariable(value = "id", required = true) ObjectId id) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    DossierCounting result = dossierCountingService.getReportDossierByDay(id);
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--by-day")
  public Page<DossierCounting> getReportDossierByDay(HttpServletRequest request, Pageable pageable,
                                                     @RequestParam(value = "by-day", required = false) String byDay,
                                                     @RequestParam(value = "agency-id", required = false) String agencyId,
                                                     @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                                     @RequestParam(value = "sector-id", required = false) String sectorId,
                                                     @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    Page<DossierCounting> result = dossierCountingService.getReportDossierByDay(byDay, agencyId, agencyTagId, sectorId, spec, pageable);
    return result;
  }
  @GetMapping("/--monitoring")
  public DossierCountingMonitoringDto getMonitoring(HttpServletRequest request, Pageable pageable,
                                                     @RequestParam(value = "by-day", required = false) String byDay,
                                                     @RequestParam(value = "agency-id", required = false) String agencyId,
                                                     @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                                     @RequestParam(value = "sector-id", required = false) String sectorId) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    DossierCountingMonitoringDto result= dossierCountingService.getMonitoring(byDay, agencyId, agencyTagId, sectorId);
    return result;
  }
  @GetMapping("/--list-id-bycode")
  public DossierCountingIdDto getDossierIdByCode(HttpServletRequest request, Pageable pageable,
                                                    @RequestParam(value = "by-day", required = false) String byDay,
                                                    @RequestParam(value = "agency-id", required = false) String agencyId,
                                                    @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                                    @RequestParam(value = "sector-id", required = false) String sectorId,
                                                    @RequestParam(value = "code", required = false) int code

  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    DossierCountingIdDto result= dossierCountingService.getDossierIdByCode(byDay, agencyId, agencyTagId, sectorId,code);
    return result;
  }

  @GetMapping("/--counting-detail")
  // @IcodeAuthorize(permission = "manageDossier")
  public Slice<DossierCountingLog> getDossierCountingDetail (HttpServletRequest request,
                                                               @RequestParam(value = "from", required = false) String from,
                                                               @RequestParam(value = "to", required = false) String to,
                                                               @RequestParam(value = "list-agency-id", required = false) String lAgencyId,
                                                               @RequestParam(value = "list-sector-id", required = false) String lSectorId,
                                                               @RequestParam(value = "procedure-id", required = false) String procedureId,
                                                               @RequestParam(value = "list-code", required = false,defaultValue = "-1") String code,
                                                               @RequestParam(value = "is-dept", required = false) boolean isDept,
                                                               @RequestParam(value = "is-tax-nre-report", required = false,defaultValue = "false") boolean isTaxAndNREAgencyReport,
                                                               @RequestParam(value = "list-nre-agency", required = false) String lNREAgencyId,
                                                               @RequestParam(value = "is-out-of-date-code", required = false)Integer isOutOfDateCode,
                                                               Pageable pageable
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);
    Slice<DossierCountingLog> getDossierCountDto = dossierCountingService.getListDetailDossierCounting(
            from,
            to,
            lAgencyId,
            lNREAgencyId,
            lSectorId,
            procedureId,
            code,
            isOutOfDateCode,
            isTaxAndNREAgencyReport,
            // isDept,
            pageable
    );
    return getDossierCountDto;
  }
  @GetMapping("/--counting-detail-previous")
  public Slice<DossierCountingLog> getDossierCountingDetailPreviousPeriod (HttpServletRequest request,
                                                             @RequestParam(value = "from", required = false) String from,
                                                             @RequestParam(value = "to", required = false) String to,
                                                             @RequestParam(value = "list-agency-id", required = false) String lAgencyId,
                                                             @RequestParam(value = "list-sector-id", required = false) String lSectorId,
                                                             @RequestParam(value = "procedure-id", required = false) String procedureId,
                                                             @RequestParam(value = "is-tax-nre-report", required = false,defaultValue = "false") boolean isTaxAndNREAgencyReport,
                                                             @RequestParam(value = "is-out-of-date-code", required = false) Integer isOutOfDateCode,
                                                             Pageable pageable
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);
    Slice<DossierCountingLog> getDossierCountDto = dossierCountingService.getListDetailDossierCountingPrevPeriod(
            from,
            lAgencyId,
            lSectorId,
            procedureId,
            isOutOfDateCode,
            // isTaxAndNREAgencyReport,
            pageable
    );
    return getDossierCountDto;
  }
  @GetMapping("/--received-applymethod-detail")
  public Slice<DossierCountingLog> getDossierCountingDetailReceivedOrApplyMethod (
                                                                          HttpServletRequest request,
                                                                          @RequestParam(value = "from", required = false) String from,
                                                                          @RequestParam(value = "to", required = false) String to,
                                                                          @RequestParam(value = "apply-method-id", required = false) String applyMethodId,
                                                                          @RequestParam(value = "list-agency-id", required = false) String lAgencyId,
                                                                          @RequestParam(value = "list-sector-id", required = false) String lSectorId,
                                                                          @RequestParam(value = "is-tax-nre-report", required = false,defaultValue = "false") boolean isTaxAndNREAgencyReport,
                                                                          @RequestParam(value = "procedure-id", required = false) String procedureId,
                                                                          @RequestParam(value = "is-out-of-date-code", required = false) Integer isOutOfDateCode,
                                                                          Pageable pageable
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);
    Slice<DossierCountingLog> getDossierCountDto = dossierCountingService.getListDetailDossierCountingForApplyMethodOrReceived(
            from,
            to,
            lAgencyId,
            lSectorId,
            procedureId,
            applyMethodId,
            isOutOfDateCode,
            // isTaxAndNREAgencyReport,
            pageable
    );
    return getDossierCountDto;
  }
  @GetMapping("--export")
  public ResponseEntity<Object> exportExcel(HttpServletRequest request,Pageable pageable,
                                            @RequestParam(value = "from-date", required = false) String fromDate,
                                            @RequestParam(value = "to-date", required = false) String toDate,
                                            @RequestParam(value = "apply-method-id", required = false) String applyMethodId,
                                            @RequestParam(value = "list-agency-id", required = false) String lAgencyId,
                                            @RequestParam(value = "list-sector-id", required = false) String lSectorId,
                                            @RequestParam(value = "procedure-id", required = false) String procedureId,
                                            @RequestParam(value = "list-nre-agency", required = false) String lNREAgencyId,
                                            @RequestParam(value = "list-code", required = false,defaultValue = "-1") String code,
                                            @RequestParam(value = "is-dept", required = false) boolean isDept,
                                            @RequestParam(value = "is-show-late-at", required = false,defaultValue = "false") boolean isTaxAndNREAgencyReport,
                                            @RequestParam(value = "is-out-of-date-code", required = false) Integer isOutOfDateCode,
                                            @RequestParam(name  = "report-type", required = false) Integer reportType
  ) throws IOException, ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);

    ResponseEntity<Object> res = dossierCountingService.statisticExport(
            pageable,fromDate,toDate,lAgencyId,reportType,
            lSectorId,procedureId,code,applyMethodId,
            isOutOfDateCode,isTaxAndNREAgencyReport,lNREAgencyId,isDept
    );
    logger.info("DIGO-Request: " + res.getStatusCode());
    return res;
  }

  @GetMapping("--change-state")
  public String changeDossierCountingState(
          HttpServletRequest request) {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);
    if(DossierCountingProperties.DOSSIER_COUNTING_EVENT_AVAILABLE){
      DossierCountingProperties.DOSSIER_COUNTING_EVENT_AVAILABLE = false;
    } else {
      DossierCountingProperties.DOSSIER_COUNTING_EVENT_AVAILABLE = true;
    }

    return "Done!";
  }
  @GetMapping("--display-configuration-param")
  public String DisplayConfigurationParam(
          HttpServletRequest request) {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);
    String result = "DOSSIER_COUNTING_EVENT_AVAILABLE: "+ String.valueOf(DossierCountingProperties.DOSSIER_COUNTING_EVENT_AVAILABLE)
            + "\nDOSSIER_COUNTING_CNVP_ID: "+ DossierCountingProperties.DOSSIER_COUNTING_CNVP_ID
            + "\nDOSSIER_COUNTING_DEP_ID: "+ DossierCountingProperties.DOSSIER_COUNTING_DEPT_ID
            + "\nDOSSIER_COUNTING_COMMUNE_ID: "+ DossierCountingProperties.DOSSIER_COUNTING_COMMUNE_ID
            + "\nDOSSIER_COUNTING_DISTRICT_ID: "+ DossierCountingProperties.DOSSIER_COUNTING_DISTRICT_ID
            + "\nDOSSIER_COUNTING_MILESTONE "+ DossierCountingProperties.DOSSIER_COUNTING_MILESTONE;
    return result;
  }
  @GetMapping("--change-tag-id")
  public String changeDossierCountingState(
          HttpServletRequest request,
          @RequestParam(value = "cnvp", required = false) String cnvpId,
          @RequestParam(value = "dept", required = false) String deptId,
          @RequestParam(value = "commune", required = false) String communeId,
          @RequestParam(value = "district", required = false) String districtId,
          @RequestParam(value = "milestone", required = false) String milestone

  ) {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Request: " + requestPath);
    if (!isNullOrEmpty(cnvpId)) DossierCountingProperties.DOSSIER_COUNTING_CNVP_ID = cnvpId;
    if (!isNullOrEmpty(deptId)) DossierCountingProperties.DOSSIER_COUNTING_DEPT_ID = deptId;
    if (!isNullOrEmpty(communeId)) DossierCountingProperties.DOSSIER_COUNTING_COMMUNE_ID = communeId;
    if (!isNullOrEmpty(districtId)) DossierCountingProperties.DOSSIER_COUNTING_DISTRICT_ID = districtId;
    if (!isNullOrEmpty(milestone)) DossierCountingProperties.DOSSIER_COUNTING_MILESTONE = milestone;
    return "Done!!!";
  }

  @GetMapping("/--digitization")
  public ResponseEntity getDigitizationInfo (HttpServletRequest request,
                                                 @RequestParam(value = "from", required = false) String from,
                                                 @RequestParam(value = "to", required = false) String to,
                                                 @RequestParam(value = "is-dept", required = false) boolean isDept,
                                                 @RequestParam(value = "agency-id", required = false) String lAgencyId,
                                                 Pageable pageable
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    GetDigitizationDto result = dossierCountingService.getDigitizationInfo(
            from,
            to,
            lAgencyId,
            isDept,
            pageable
    );
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--synthesize-general")
  public ResponseEntity synthesizeGeneralReport(HttpServletRequest request,
                                         @RequestParam(value = "from-date", required = true) String fromDate,
                                         @RequestParam(value = "to-date", required = true) String toDate,
                                         @RequestParam(value = "agency-id", required = false) String strAgencyId,
                                         @RequestParam(value = "sector-id", required = false) String strSectorId,
//                                         @RequestParam(value = "is-dept", required = false) Boolean isDept,
                                         @RequestParam(value = "list-tag-agency-id", required = false) String listTagAgencyId,
                                         @RequestParam(value = "milestone", required = false) String milestone
  ) {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    dossierCountingService.synthesizeGeneralReport(fromDate, toDate, strAgencyId, strSectorId, listTagAgencyId, milestone);
    return ResponseEntity.ok(null);
  }

  @GetMapping("/--total-document")
  public ResponseEntity<String> calculateTotalDocumentByDay(HttpServletRequest request,
                                                            @RequestParam(value = "from-date", required = true) String fromDate,
                                                            @RequestParam(value = "to-date", required = true) String toDate,
                                                            @RequestParam(value = "tag-agency-id", required = false) String tagAgencyId
  ){
    String result = dossierCountingService.calculateTotalDocumentByDay(fromDate, toDate, tagAgencyId);
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--etl-dossier-id-list")
  public ResponseEntity<List<IdPojo>> getETLDossierIdList(HttpServletRequest request,
                                                          @RequestParam(value = "from-date", required = true) String fromDate,
                                                          @RequestParam(value = "to-date", required = true) String toDate)
  {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    List<IdPojo> result = dossierCountingService.getETLDossierIdList(fromDate, toDate);
    return  ResponseEntity.ok(result);
  }

  @GetMapping("/--etl-dossier")
  public ResponseEntity<KTMETLDossier> getETLDossier(HttpServletRequest request,
                                                          @RequestParam(value = "dossier-id", required = true) String id)
  {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    KTMETLDossier result = dossierCountingService.getETLDossier(id);
    return  ResponseEntity.ok(result);
  }

  @PutMapping("/--etl-dossier")
  public ResponseEntity<KTMETLDossier> updateETLDossier(HttpServletRequest request,
                                                        @RequestBody KTMETLDossier body)
  {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    KTMETLDossier result = dossierCountingService.updateETLDossier(body);
    return  ResponseEntity.ok(result);
  }

  @GetMapping("/--etl-dossier-list")
  public Slice<KTMETLDossier> getETLDossierList(HttpServletRequest request,
                                                          @RequestParam(value = "from-date", required = true) String fromDate,
                                                          @RequestParam(value = "to-date", required = true) String toDate,
                                                          Pageable pageable)
  {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    Slice<KTMETLDossier> result = dossierCountingService.getETLDossierList(fromDate, toDate, pageable);
    return result;
  }

  @GetMapping("/--synthesize-list")
  public Slice<DossierSynthesisDTO> getSynthesizeList(HttpServletRequest request,
                                                          @RequestParam(value = "from-date", required = true) String fromDate,
                                                          @RequestParam(value = "to-date", required = true) String toDate,
                                                          @RequestParam(value = "tag-agency-id", required = false) String tagAgencyId,
                                                          Pageable pageable)
  {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    Slice<DossierSynthesisDTO> result = dossierCountingService.getSynthesizeList(fromDate, toDate, tagAgencyId, pageable);
    return result;
  }

  @GetMapping("/--from-to-bundles")
  public ResponseEntity getReportDossierFromToBundles(HttpServletRequest request,
                                                      @RequestParam(value = "from-date") String fromDate,
                                                      @RequestParam(value = "to-date") String toDate,
                                                      @RequestParam(value = "parent-id", required = false) String parentId,
                                                      @RequestParam(value = "tag-agency-id", required = false) String tagAgencyId
  ) throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    List<DossierSumEtlDto> listDossierByDay = dossierCountingService.getReportDossierAgency(fromDate, toDate, parentId, tagAgencyId);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--from-to-by-agency-sectors")
  public ResponseEntity getReportDossierAgencySector(HttpServletRequest request,
                                                     @RequestParam(value = "from-date") String fromDate,
                                                     @RequestParam(value = "to-date") String toDate,
                                                     @RequestParam(value = "agency-id", required = false) String agencyId)
          throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    List<DossierCoutingEtlDto> listDossierByDay = dossierCountingService.getReportDossierAgencySector(fromDate, toDate, agencyId);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--get-id-dossier")
  public Slice getIdDossier(HttpServletRequest request,
                            @RequestParam(value = "from-date") String fromDate,
                            @RequestParam(value = "to-date") String toDate,
                            @RequestParam(value = "type", defaultValue = "0") int type,
                            @RequestParam(value = "list-agency-id", required = false) List<String> agencyId,
                            @RequestParam(value = "list-sector-id", required = false) List<String> sectorId,

                            Pageable pageable
  )
          throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    //  List<DossierEtlDetailDto>
    return  dossierCountingService.getIdDossier(fromDate, toDate, type, agencyId, sectorId, pageable);
  }

  @GetMapping("/--digitization-v2")
  public ResponseEntity getDigitizationInfoV2 (HttpServletRequest request,
                                               @RequestParam(value = "from-date", required = false) String from,
                                               @RequestParam(value = "to-date", required = false) String to,
                                               @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                               @RequestParam(value = "agency-id", required = false) String lAgencyId,
                                               @RequestParam(value = "procedure-id", required = false) String lProcedureId,
                                               Pageable pageable
  ) throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
        + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    List<GetDigitizationDto> result = dossierCountingService.getDigitizationInfoV2(
        from,
        to,
        agencyTagId,
        lAgencyId,
        lProcedureId,
        pageable
    );
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--digitization-v2-1")
  public ResponseEntity getDigitizationInfoV2_1 (HttpServletRequest request,
                                               @RequestParam(value = "from-date", required = false) String from,
                                               @RequestParam(value = "to-date", required = false) String to,
                                               @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                               @RequestParam(value = "agency-id", required = false) String lAgencyId,
                                               @RequestParam(value = "procedure-id", required = false) String lProcedureId,
                                               Pageable pageable
  ) throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    List<GetDigitizationDto> result = dossierCountingService.getDigitizationInfoV2_1(
            from,
            to,
            agencyTagId,
            lAgencyId,
            lProcedureId,
            pageable
    );
    return ResponseEntity.ok(result);
  }

  @GetMapping("/--digitization-v2-details")
  public Slice getDigitizationInfoV2Details (HttpServletRequest request,
                                               @RequestParam(value = "from-date", required = false) String from,
                                               @RequestParam(value = "to-date", required = false) String to,
                                               @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                               @RequestParam(value = "agency-id", required = false) String lAgencyId,
                                               @RequestParam(value = "procedure-id", required = false) String lProcedureId,
                                               @RequestParam(value = "type", required = false) int type,
                                               Pageable pageable
  ) throws ParseException, JSONException {
    String requestPath = request.getMethod() + " " + request.getRequestURI()
            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    return  dossierCountingService.getDigitizationInfoV2Details(
            from,
            to,
            agencyTagId,
            lAgencyId,
            lProcedureId,
            type,
            pageable
    );

  }
  
  @GetMapping("/--export-list-dossier-detail")
  public ResponseEntity<Object> getIdDossier(HttpServletRequest request,
                                             @RequestParam(value = "from-date") String fromDate,
                                             @RequestParam(value = "to-date") String toDate,
                                             @RequestParam(value = "type", defaultValue = "0") int type,
                                             @RequestParam(value = "list-agency-id", required = false) List<String> agencyId,
                                             @RequestParam(value = "list-sector-id", required = false) List<String> sectorId
  )
      throws ParseException, JSONException, IOException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    return dossierCountingService.exportListDossierDetail(fromDate, toDate, type, agencyId, sectorId);
  }

  @GetMapping("/--by-v2")
  public ResponseEntity getReportDossierFromToV2(HttpServletRequest request,
                                                 @RequestParam(value = "year") String year,
                                                 @RequestParam(value = "tag-id", required = false) String listTagId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    DossierSumEtlDto listDossierByDay = dossierCountingService.getReportDossierByYearV2(year,listTagId);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--from-to-by-agency-v2")
  public ResponseEntity getReportDossierFromToV2(HttpServletRequest request,
                                                 @RequestParam(value = "from-date") String fromDate,
                                                 @RequestParam(value = "to-date") String toDate,
                                                 @RequestParam(value = "agency-tag-id", required = false) String agencyTagId,
                                                 @RequestParam(value = "agency-id", required = false) String agencyId
  ) throws ParseException {
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);

    DossierSumEtlDto listDossierByDay = dossierCountingService.getReportDossierFromToV2(fromDate, toDate, agencyTagId, agencyId);
    return ResponseEntity.ok(listDossierByDay);
  }

  @GetMapping("/--delete-etl-dossier-by-range")
  public long deleteETLDossierByRange(HttpServletRequest request,
                                      @RequestParam(value = "from-date") String fromDate,
                                      @RequestParam(value = "to-date") String toDate){
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    long result = dossierCountingService.deleteETLDossierByRange(fromDate, toDate);
    return result;
  }

  @GetMapping("/--delete-synthesis-by-range")
  public long deleteSynthesisByRange(HttpServletRequest request,
                                     @RequestParam(value = "from-date") String fromDate,
                                     @RequestParam(value = "to-date") String toDate){
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    long result = dossierCountingService.deleteSynthesisByRange(fromDate, toDate);
    return result;
  }

  @GetMapping("/--delete-etl-dossier-by-range-agency")
  public long deleteETLDossierByRangeAndAgency(HttpServletRequest request,
                                      @RequestParam(value = "from-date") String fromDate,
                                      @RequestParam(value = "to-date") String toDate,
                                      @RequestParam(value = "agency") String agency){
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    long result = dossierCountingService.deleteETLDossierByRangeAndAgency(fromDate, toDate, agency);
    return result;
  }

  @GetMapping("/--delete-synthesis-by-range-agency")
  public long deleteSynthesisByRangeAndAgency(HttpServletRequest request,
                                     @RequestParam(value = "from-date") String fromDate,
                                     @RequestParam(value = "to-date") String toDate,
                                     @RequestParam(value = "agency") String agency){
    String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
    logger.info("DIGO-Info: " + requestPath);
    long result = dossierCountingService.deleteSynthesisByRangeAndAgency(fromDate, toDate, agency);
    return result;
  }

}
