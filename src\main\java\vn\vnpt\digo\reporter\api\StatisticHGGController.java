package vn.vnpt.digo.reporter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.service.StatisticHGGService;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistic-hgg")
@IcodeAuthorize("digo.permission.statistics.hgg")
public class StatisticHGGController {

    @Autowired
    private StatisticHGGService service;

    Logger logger = LoggerFactory.getLogger(StatisticHGGController.class);

    @GetMapping(value = "/--analytics-report")
    public Object syncReport(HttpServletRequest request,
                             @RequestParam(value = "propertyId") String propertyId,
                             @RequestParam(value = "metrics") String metrics,
                             @RequestParam(value = "startDate") String startDate,
                             @RequestParam(value = "endDate") String endDate,
                             @RequestParam(value = "wayDiff") boolean wayDiff) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var listSector = service.analyticsReport(propertyId, metrics, startDate, endDate,wayDiff);
        logger.info("DIGO-Info: " + listSector);
        return listSector;
    }

    @GetMapping(value = "/--analytics-report-day")
    public Object syncReportDay(HttpServletRequest request,
                             @RequestParam(value = "propertyId") String propertyId,
                             @RequestParam(value = "metrics") String metrics,
                             @RequestParam(value = "dimension") String dimension,
                             @RequestParam(value = "startDate") String startDate,
                             @RequestParam(value = "endDate") String endDate) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var listSector = service.analyticsReportDay(propertyId, dimension, metrics, startDate, endDate);
        logger.info("DIGO-Info: " + listSector);
        return listSector;
    }

}
