package vn.vnpt.digo.reporter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.DossierOutOfDate;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelFormDossierCountingDetailDto implements Serializable {
  private Long no;
  private String agency;
  private String sector;
  private String procedure;
  private String dossierCode;
  private String status;
  private String address;
  private String phone;
  private String owner;
  private String submitter;

  private String acceptedDate;

  private String appointmentDate;

  private String duration;

  private String completedDate;

  private String returnedDate;

  private String  dossierType;

  private String applyMethod;

  private String  dossierStatus;

  private String implementer;

//  private int outOfDateType; // 0: don;t care, 1 : late at tax agency, 2: another agency

  private String lateAt;

  private String note;

  private String dossierTaskStatus;
  private String cancelledDate;
  private String withdrawDate;
  private String procedureLevel;
}
