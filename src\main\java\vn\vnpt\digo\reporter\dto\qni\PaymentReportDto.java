package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.util.List;

@Data
public class PaymentReportDto {
    private String agencyIdAgency;
    private String agencyName;
    private List<ProcedurePaymentDto> procedurePayment;
    private int procedureUsingPayment;
    private double percentProcedureUsingPayment;
    private int procedureUsingPaymentOnline;

    public PaymentReportDto() {
    }

    public PaymentReportDto(String agencyIdAgency,
                            String agencyName,
                            List<ProcedurePaymentDto> procedurePayment,
                            int procedureUsingPayment,
                            double percentProcedureUsingPayment,
                            int procedureUsingPaymentOnline,
                            double percentPaymentOnline) {
        this.agencyIdAgency = agencyIdAgency;
        this.agencyName = agencyName;
        this.procedurePayment = procedurePayment;
        this.procedureUsingPayment = procedureUsingPayment;
        this.percentProcedureUsingPayment = percentProcedureUsingPayment;
        this.procedureUsingPaymentOnline = procedureUsingPaymentOnline;
    }

    public int getProcedureUsingPaymentOnline() {
        return procedureUsingPaymentOnline;
    }

    public void setProcedureUsingPaymentOnline(int procedureUsingPaymentOnline) {
        this.procedureUsingPaymentOnline = procedureUsingPaymentOnline;
    }

    public double getPercentProcedureUsingPayment() {
        return percentProcedureUsingPayment;
    }

    public void setPercentProcedureUsingPayment(double percentProcedureUsingPayment) {
        this.percentProcedureUsingPayment = percentProcedureUsingPayment;
    }

    public String getAgencyIdAgency() {
        return agencyIdAgency;
    }

    public void setAgencyIdAgency(String agencyIdAgency) {
        this.agencyIdAgency = agencyIdAgency;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public List<ProcedurePaymentDto> getProcedurePayment() {
        return procedurePayment;
    }

    public void setProcedurePayment(List<ProcedurePaymentDto> procedurePayment) {
        this.procedurePayment = procedurePayment;
    }

    public int getProcedureUsingPayment() {
        return procedureUsingPayment;
    }

    public void setProcedureUsingPayment(int procedureUsingPayment) {
        this.procedureUsingPayment = procedureUsingPayment;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcedurePaymentDto {
        private double financialObligationOnline;
        private double financialObligationDirect;
        private double paymentOnline;
        private String procedureName;
        private String procedureId;
        private double financialObligation;
        private double percentPaymentOnline;

        public ProcedurePaymentDto() {
        }

        // Constructors, getters, and setters
        public ProcedurePaymentDto(double financialObligationOnline, double financialObligationDirect, double paymentOnline,
                                   String procedureName, String procedureId,
                                   double financialObligation) {
            this.financialObligationOnline = financialObligationOnline;
            this.financialObligationDirect = financialObligationDirect;
            this.paymentOnline = paymentOnline;
            this.procedureName = procedureName;
            this.procedureId = procedureId;
            this.financialObligation = financialObligation;
        }
        // Getters and setters...


        public double getPercentPaymentOnline() {
            return percentPaymentOnline;
        }

        public void setPercentPaymentOnline(double percentPaymentOnline) {
            this.percentPaymentOnline = percentPaymentOnline;
        }

        public double getFinancialObligationOnline() {
            return financialObligationOnline;
        }

        public void setFinancialObligationOnline(double financialObligationOnline) {
            this.financialObligationOnline = financialObligationOnline;
        }

        public double getFinancialObligationDirect() {
            return financialObligationDirect;
        }

        public void setFinancialObligationDirect(double financialObligationDirect) {
            this.financialObligationDirect = financialObligationDirect;
        }

        public double getPaymentOnline() {
            return paymentOnline;
        }

        public void setPaymentOnline(double paymentOnline) {
            this.paymentOnline = paymentOnline;
        }

        public String getProcedureName() {
            return procedureName;
        }

        public void setProcedureName(String procedureName) {
            this.procedureName = procedureName;
        }

        public String getProcedureId() {
            return procedureId;
        }

        public void setProcedureId(String procedureId) {
            this.procedureId = procedureId;
        }

        public double getFinancialObligation() {
            return financialObligation;
        }

        public void setFinancialObligation(double financialObligation) {
            this.financialObligation = financialObligation;
        }
    }
}

