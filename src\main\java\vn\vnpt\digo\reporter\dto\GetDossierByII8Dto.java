package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.reporter.pojo.AgencyDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureDossierByDay;
import vn.vnpt.digo.reporter.pojo.ProcedureLevelDossierByDay;
import vn.vnpt.digo.reporter.pojo.SectorDossierByDay;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetDossierByII8Dto implements Serializable {
    @JsonProperty("agencyLevel")
    private String  agencyLevel;
    private String code;
    private String  agencyLevelId;
    private String sectorName;

    private String sectorId;

    private int isNpad = 0;
    private int isLevel3 = 0;
    private int isLevel4 = 0;

    private String procedureName;

    private String procedureId;

    @JsonProperty("received")
    private int received;
      
}
