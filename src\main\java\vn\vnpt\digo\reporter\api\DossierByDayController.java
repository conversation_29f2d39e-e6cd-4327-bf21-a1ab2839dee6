package vn.vnpt.digo.reporter.api;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.data.domain.Pageable;
import javax.servlet.http.HttpServletRequest;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.*;
import vn.vnpt.digo.reporter.service.DossierByDayService;

@RestController
@RequestMapping("/dossier/")
@IcodeAuthorize("vnpt.permission.dossier")
public class DossierByDayController {

    @Autowired
    private DossierByDayService dossierByDayService;

    Logger logger = LoggerFactory.getLogger(DossierByDayController.class);

    @GetMapping("--by-day")
    public Slice<GetDossierByDay> getReportDossierByDay(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "fromDate", required = false) String fromDate,
            @RequestParam(value = "toDate", required = false) String toDate,
            @RequestParam(value = "sector-id", required = false) String sectorId,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "procedure-level", required = false) String procedureLevel,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<GetDossierByDay> listDossierByDay = dossierByDayService.getReportDossierByDay(pageable, fromDate, toDate, sectorId, agencyId, procedureLevel, spec);
        logger.info(listDossierByDay.getSize() + "");
        return listDossierByDay;
    }

    @GetMapping("statistic/--by-procedure")
    public Slice<GetDossierByDay> getReportDossierByDayProcedure(HttpServletRequest request,
                                                                Pageable pageable,
                                                        @RequestParam(value = "accepted-from", required = false) String fromDate,
                                                        @RequestParam(value = "accepted-to", required = false) String toDate,
                                                        @RequestParam(value = "sector-id", required = false) String sectorId,
                                                        @RequestParam(value = "agency-id", required = false) String agencyId,
                                                        @RequestParam(value = "arr-agency-id", required = false) List<ObjectId> arrAgencyId,
                                                        @RequestParam(value = "procedure-level", required = false) String procedureLevel,
                                                                 @RequestParam(value = "keyword", required = false) String keyword,
                                                                 @RequestParam(value = "procedure-check", required = false) Boolean procedureCheck,
                                                                 @RequestParam(value = "status-procedure", required = false) String statusProcedure,
                                                        @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException,JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Slice<GetDossierByDay> listDossierByDay = dossierByDayService.getReportDossierByDayProcedure(pageable, fromDate, toDate, sectorId, agencyId, arrAgencyId, procedureLevel, keyword, procedureCheck, statusProcedure,  spec);
        logger.info(listDossierByDay.getNumberOfElements() + "");
        return listDossierByDay;
    }

    @GetMapping("statistic/--by-procedure-all")
    public List<GetDossierByDay> getReportDossierByDayProcedureAll(HttpServletRequest request,
                                                                 Pageable pageable,
                                                                 @RequestParam(value = "accepted-from", required = false) String fromDate,
                                                                 @RequestParam(value = "accepted-to", required = false) String toDate,
                                                                 @RequestParam(value = "sector-id", required = false) String sectorId,
                                                                 @RequestParam(value = "agency-id", required = false) String agencyId,
                                                                 @RequestParam(value = "arr-agency-id", required = false) List<ObjectId> arrAgencyId,
                                                                 @RequestParam(value = "procedure-level", required = false) String procedureLevel,
                                                                 @RequestParam(value = "keyword", required = false) String keyword,
                                                                 @RequestParam(value = "procedure-check", required = false) Boolean procedureCheck,
                                                                 @RequestParam(value = "status-procedure", required = false) String statusProcedure,
                                                                 @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException,JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetDossierByDay> listDossierByDay = dossierByDayService.getReportDossierByDayProcedureAll(fromDate, toDate, sectorId, agencyId,arrAgencyId, procedureLevel, keyword, procedureCheck, statusProcedure);
        logger.info(listDossierByDay.size() + "");
        return listDossierByDay;
    }

    @GetMapping("--by-6a")
    public Page<SimpleDossier> getReportDossierByDay(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "agency-parent-id", required = false) String agencyParentId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SimpleDossier> listDossierByDay = dossierByDayService.getForm6a(pageable, fromDate, toDate, agencyId, agencyParentId, spec);
        return listDossierByDay;
    }

    @GetMapping("--by-province-6a")
    public Page<SimpleDossier> getReportDossierByProvince6a(HttpServletRequest request, Pageable pageable,
                                                     @RequestParam(value = "from-date", required = false) String fromDate,
                                                     @RequestParam(value = "to-date", required = false) String toDate,
                                                     @RequestParam(value = "agency-id", required = false) String agencyId,
                                                     @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SimpleDossier> listDossierByDay = dossierByDayService.getFormProvince6a(pageable, fromDate, toDate, agencyId, spec);
        return listDossierByDay;
    }

    @GetMapping("--by-province-6b")
    public List<ExcelFormProvince6bDto> getReportDossierByProvince6b(HttpServletRequest request,
                                                                     @RequestParam(value = "from-date", required = false) String fromDate,
                                                                     @RequestParam(value = "to-date", required = false) String toDate,
                                                                     @RequestParam(value = "agency-id", required = false) String agencyId,
                                                                     @RequestParam(value = "tag-id", required = false) List<String> tagIds,
                                                                     @RequestParam(value = "agency-level-commune", required = false) String agencyLevelCommune,
                                                                     @RequestParam(value = "agency-level-district", required = false) String agencyLevelDistrict) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<ExcelFormProvince6bDto> dtos = dossierByDayService.getFormProvince6bExcelDto(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict);
        return dtos;
    }

    @GetMapping("--by-province-6c")
    public ExcelFormProvince6cDto getReportDossierByProvince6b(HttpServletRequest request,
                                                                     @RequestParam(value = "from-date", required = false) String fromDate,
                                                                     @RequestParam(value = "to-date", required = false) String toDate,
                                                                     @RequestParam(value = "agency-id", required = false) String agencyId,
                                                                     @RequestParam(value = "tag-id", required = false) List<String> tagIds,
                                                                     @RequestParam(value = "agency-level-commune", required = false) String agencyLevelCommune,
                                                                     @RequestParam(value = "agency-level-district", required = false) String agencyLevelDistrict,
                                                                    @RequestParam(value = "agency-level-province", required = false) String agencyLevelProvince) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        ExcelFormProvince6cDto dtos = dossierByDayService.getFormProvince6cExcelDto(fromDate, toDate, agencyId, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
        return dtos;
    }

    @GetMapping("--export")
    public ResponseEntity<Object> exportExcel(HttpServletRequest request, Pageable pageable,
                                               @RequestParam(value = "from-date", required = false) String fromDate,
                                               @RequestParam(value = "to-date", required = false) String toDate,
                                               @RequestParam(value = "agency-id", required = false) String agencyId,
                                               @RequestParam(value = "agency-name", required = false) String agencyName,
                                               @RequestParam(value = "spec", required = false, defaultValue = "page") String spec,
                                               @RequestParam(name = "report-type", required = false) Integer reportType,
                                               @RequestParam(value = "tag-id", required = false) List<String> tagIds,
                                              @RequestParam(value = "agency-level-commune", required = false) String agencyLevelCommune,
                                              @RequestParam(value = "agency-level-district", required = false) String agencyLevelDistrict,
                                              @RequestParam(value = "agency-level-province", required = false) String agencyLevelProvince
                                              ) throws IOException, ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        ResponseEntity<Object> res = dossierByDayService.statisticExport(pageable, fromDate, toDate, agencyId, agencyName, spec, reportType, tagIds, agencyLevelCommune, agencyLevelDistrict, agencyLevelProvince);
        logger.info("DIGO-Request: " + res.getStatusCode());
        return res;
    }

    @GetMapping("--by-6b")
    public Page<SimpleDossier> getReportDossierBy6b(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "agency-parent-id", required = false) String agencyParentId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SimpleDossier> listDossierByDay = dossierByDayService.getForm6b(pageable, fromDate, toDate, agencyId, agencyParentId, spec);
        return listDossierByDay;
    }

    @GetMapping("--by-6d")
    public Page<SimpleDossier> getReportDossierBy6d(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "agency-parent-id", required = false) String agencyParentId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SimpleDossier> listDossierByDay = dossierByDayService.getForm6d(pageable, fromDate, toDate, agencyId, agencyParentId, spec);
        return listDossierByDay;
    }

    @GetMapping("--by--unresolved-overdue")
    public Page<SectorOverdue> getSectorOverdue(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SectorOverdue> listDossierByDay = dossierByDayService.getSectorOverdue(pageable, fromDate, toDate, agencyId, spec);
        return listDossierByDay;
    }

    @GetMapping("--by-procedure")
    public Page<SectorOverdue> getProcedure(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SectorOverdue> listDossierByDay = dossierByDayService.getProcedure(pageable, fromDate, toDate, agencyId, spec);
        return listDossierByDay;
    }

    @GetMapping("--by-deployment")
    public Page<SimpleDossier> getReportForDeployment(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SimpleDossier> listDossierByDay = dossierByDayService.getReportForDeployment(pageable, deploymentId, spec);
        return listDossierByDay;
    }

    @GetMapping("--agency")
    public Page<SimpleDossier> getDossierByAgency(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "spec", required = false, defaultValue = "page") String spec) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Page<SimpleDossier> listDossierByDay = dossierByDayService.getDossierByAgency(pageable, agencyId, deploymentId, spec);
        return listDossierByDay;
    }

    @PutMapping("--by-day")
    public AffectedRowsDto updateReportDossierByDay(HttpServletRequest request,
            @RequestBody PutDossierByDayDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRows = dossierByDayService.updateReportDossierByDay(body);
        return affectedRows;
    }
    
    @GetMapping("--by-day/deployment")
    public GetTotalDossierByDeploymentDto getReportByDeployment(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "agency-id", required = false) String agencyId
            ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        GetTotalDossierByDeploymentDto listDossierByDay = dossierByDayService.getListDossierByDeploymentId(fromDate, toDate, deploymentId,agencyId);
        return listDossierByDay;
    }
    
    @GetMapping("statistic/--by-agency")
    public GetDossierStatisticalByAgencyIdDto getDossierStatisticalByAgencyId(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "agency-id", required = false) String agencyId
            ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        GetDossierStatisticalByAgencyIdDto results = dossierByDayService.getDossierStatisticalByAgencyId(fromDate, toDate, deploymentId,agencyId);
        return results;
    }
    
    @GetMapping("statistic/--by-level")
    public List<GetStatisticDossierByLevelDto> getStatisticDossierByLevel(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "ancestor-id", required = false) String ancestorId
            ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetStatisticDossierByLevelDto> results = dossierByDayService.getStatisticDossierByLevel(fromDate, toDate, deploymentId,agencyId,ancestorId);
        return results;
    }
    
    @GetMapping("statistic/--by-sector")
    public List<GetStatisticDossierBySectorDto> getStatisticDossierBySector(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "sector-id", required = false) String sectorId,
            @RequestParam(value = "procedure-id", required = false) String procedureId
            ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetStatisticDossierBySectorDto> results = dossierByDayService.getStatisticDossierBySector(fromDate, toDate, deploymentId,agencyId,sectorId,procedureId);
        return results;
    }
    
    @GetMapping("statistic/--by-ancestor")
    public List<GetStatisticDossierBySectorDto> getStatisticDossierByAncestorId(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate,
//            @RequestParam(value = "deployment-id", required = false) String deploymentId,
            @RequestParam(value = "ancestor-id", required = false) String ancestorId,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "sector-id", required = false) String sectorId,
            @RequestParam(value = "procedure-id", required = false) String procedureId
            ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetStatisticDossierBySectorDto> results = dossierByDayService.getStatisticDossierByAncestorId(fromDate, toDate, ancestorId,agencyId,sectorId,procedureId);
        return results;
    }

    @GetMapping("statistic/--by-agency-code")
    public GetDossierStatisticalByAgencyCodeDto getDossierStatisticalByUnitCodeSync(HttpServletRequest request, Pageable pageable,
                                                                                    @RequestParam(value = "month", required = true) String month,
                                                                                    @RequestParam(value = "year", required = true) String year,
                                                                                    @RequestParam(value = "agency-code", required = true) String agencyCode
    ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        GetDossierStatisticalByAgencyCodeDto results = dossierByDayService.getDossierStatisticalByAgencyCode(month, year, agencyCode);
        return results;
    }
    
    @GetMapping("statistic/--by-procedure-report-II08")
    public GetDossierStatisticalByProcedureIdDto getDossierStatisticalByProcedureId(HttpServletRequest request, Pageable pageable,
            @RequestParam(value = "id", required = true) String procedureId,
            @RequestParam(value = "from-date", required = false) String fromDate,
            @RequestParam(value = "to-date", required = false) String toDate
            ) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        GetDossierStatisticalByProcedureIdDto results = dossierByDayService.getDossierStatisticalByProcedureId(procedureId, fromDate, toDate);
        return results;
    }

    @GetMapping("statistic/--by-agency-report-II08-qbh")
    public List<GetDossierByII8Dto> getDossierStatisticalQBH(HttpServletRequest request, Pageable pageable,
                                                                                       @RequestParam(value = "agency-level-id", required = false) ObjectId agencyLevelId,
                                                                                       @RequestParam(value = "from-date", required = false) String fromDate,
                                                                                       @RequestParam(value = "to-date", required = false) String toDate
    ) throws ParseException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetDossierByII8Dto> results = dossierByDayService.getDossierStatisticalQBH(agencyLevelId, fromDate, toDate);
        return results;
    }

    @GetMapping("statistic/--by-agency-report-II08-cmu")
    public List<GetDossierByII8Dto> getDossierStatisticalCMU(HttpServletRequest request, Pageable pageable,
                                                             @RequestParam(value = "agency-level-id", required = false) ObjectId agencyLevelId,
                                                             @RequestParam(value = "from-date", required = false) String fromDate,
                                                             @RequestParam(value = "to-date", required = false) String toDate,
                                                             @RequestParam(value = "level-codes", required = false) List<String> levelCodes,
                                                             @RequestParam(value = "level-code", required = false) String levelCode
    ) throws ParseException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetDossierByII8Dto> results = dossierByDayService.getDossierStatisticalCMU(agencyLevelId, fromDate, toDate,levelCodes,levelCode);
        return results;
    }
    @GetMapping("--by-procedure-cmu")
    public List<ProcedureGenerateDossierDto> getProcedure(HttpServletRequest request, Pageable pageable,
                                                          @RequestParam(value = "from-date", required = false) String fromDate,
                                                          @RequestParam(value = "to-date", required = false) String toDate,
                                                          @RequestParam(value = "agency-ids", required = false) List<String> agencyIds,
                                                          @RequestParam(value = "spec", required = false, defaultValue = "page") String spec,
                                                          @RequestParam(value = "level-code-ids",required = false) List<String> levelCodeIds) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<ProcedureGenerateDossierDto> listDossierByDay = dossierByDayService.generatedDossierProcedure(pageable, fromDate, toDate, agencyIds, spec,levelCodeIds);
        return listDossierByDay;
    }
    @GetMapping("statistic/--by-agency-report-II08-agg")
    public List<GetDossierByII8Dto> getDossierStatisticalAGG(HttpServletRequest request, Pageable pageable,
                                                                @RequestParam(value = "agency-level-id", required = false) ObjectId agencyLevelId,
                                                                @RequestParam(value = "from-date", required = false) String fromDate,
                                                                @RequestParam(value = "to-date", required = false) String toDate,
                                                                @RequestParam(value = "level-codes", required = false) List<String> levelCodes,
                                                                @RequestParam(value = "level-code", required = false) String levelCode
    ) throws ParseException, JSONException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        List<GetDossierByII8Dto> results = dossierByDayService.getDossierStatisticalAGG(agencyLevelId, fromDate, toDate,levelCodes,levelCode);
        return results;
    }

}
