package vn.vnpt.digo.reporter.document;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "ssoToken")
public class SsoToken{
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String clientId;

    private String accessToken;

    private long expiresIn;

    private Date expiration;

    private Date createdDate;
}
