/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.document.Template;
import vn.vnpt.digo.reporter.util.Translator;
import vn.vnpt.digo.reporter.pojo.TemplateFile;
import vn.vnpt.digo.reporter.pojo.TemplateSubsystem;
import vn.vnpt.digo.reporter.pojo.TemplateType;
import vn.vnpt.digo.reporter.util.GsonHelper;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
public class GetTemplateDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private TemplateType type;

    private String name;

    private List<TemplateSubsystem> subsystem;

    private Object listVariable;

    private String listVariableString;

    private TemplateFile file;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    private Integer signEnable;

    private String code;

    @Autowired
    @JsonIgnore
    private Translator trans;

    private static Translator translator;

    @PostConstruct
    private void initStaticDao() {
        translator = this.trans;
    }

    public static GetTemplateDto fromDocument(Template template) {
        GetTemplateDto getTemplateDto = GsonHelper.copyObject(template, GetTemplateDto.class);
        return getTemplateDto;
    }

}
