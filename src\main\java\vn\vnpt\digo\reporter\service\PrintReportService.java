package vn.vnpt.digo.reporter.service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.reporter.api.PrintReportController;
import vn.vnpt.digo.reporter.document.SsoToken;
import vn.vnpt.digo.reporter.dto.PrintToBirtViewerDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.digo.reporter.util.Oauth2RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
@Service
public class PrintReportService {
     
    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;
    
    private static RestTemplate restTemplate = new RestTemplate();
    Logger logger = LoggerFactory.getLogger(PrintReportService.class);
    
    @Value(value = "${digo.report.birtviewer.url}")
    private String birtviewerURL;

    public ResponseEntity<String> printToBirtViewer(PrintToBirtViewerDto printToBirtViewerDto){
        try{ 
       if(birtviewerURL == null || birtviewerURL.isEmpty()){ 
            throw new DigoHttpException(11004, new String[]{"BirtviewerURL không hợp lệ"}, HttpServletResponse.SC_BAD_REQUEST);
        }
       String azpName = Context.getJwtParameterValue("azp");
       String token = Context.getJwtAuthenticationTokenValue();
       if(azpName.equals("web-onegate")){
           token = oauth2RestTemplate.getToken(false);
       }
        String path = "/output?__report=" + printToBirtViewerDto.getReport()
                + "&&displayNone=true&__dpi=96&__format=html&__pageoverflow=0&__overwrite=false"
                + "&token=" + token
                + "&apiGateway=" + printToBirtViewerDto.getApiGateway()
                + "&dossierId=" + printToBirtViewerDto.getDossierId().toString();
            if(Objects.nonNull(printToBirtViewerDto.getId())){
                path += "&id=" + printToBirtViewerDto.getId().toString();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getEndDate())){
                path += "&endDate=" + printToBirtViewerDto.getEndDate();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getReceiptCode())){
                path += "&receiptCode=" + printToBirtViewerDto.getReceiptCode();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getAgencyUser())){
                path += "&agencyUser=" + printToBirtViewerDto.getAgencyUser();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getTransferredUser())){
                path += "&transferredUser=" + printToBirtViewerDto.getTransferredUser();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getNumberUser())){
                path += "&numberUser=" + printToBirtViewerDto.getNumberUser();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getAddressUser())){
                path += "&addressUser=" + printToBirtViewerDto.getAddressUser();
            }
            if(StringUtils.hasText(printToBirtViewerDto.getSigningPlace())){
                path += "&signingPlace=" + printToBirtViewerDto.getSigningPlace();
            }
        String url = birtviewerURL + path;
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(url);
        UriComponents uriComponents = uriBuilder.build();
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<?> request = new HttpEntity<>( headers);
        ResponseEntity<byte[]> result = restTemplate.exchange(
                uriComponents.toUriString(),
                HttpMethod.GET,
                request,
                byte[].class
        );
        if (result == null || result.getBody() == null) {
            logger.info("No Content");
            return ResponseEntity.noContent().build();
        }
        // byte[] sang String charset UTF-8
        String html = new String(result.getBody(), StandardCharsets.UTF_8);
        return ResponseEntity
                .status(result.getStatusCode())
                .contentType(MediaType.valueOf("text/html; charset=UTF-8"))
                .body(html);
        }catch(Exception exception){
            logger.error("Lỗi in phiếu " + exception.getMessage());
            throw new DigoHttpException(11004, new String[]{"In phiếu failed"}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
