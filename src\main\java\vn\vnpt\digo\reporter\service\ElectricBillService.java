package vn.vnpt.digo.reporter.service;

import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.reporter.document.ElectricBill;
import vn.vnpt.digo.reporter.dto.ElectricBillInputDto;
import vn.vnpt.digo.reporter.dto.GetElectricBillDto;
import vn.vnpt.digo.reporter.dto.GetListElectricBillDto;
import vn.vnpt.digo.reporter.dto.PostResponseDto;
import vn.vnpt.digo.reporter.exception.DigoHttpException;
import vn.vnpt.digo.reporter.repository.ElectricBillRepository;
import vn.vnpt.digo.reporter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class ElectricBillService {

    @Autowired
    ElectricBillRepository electricBillRepository;

    @Autowired
    private Translator translator;

    public Slice<GetListElectricBillDto> getElectricBillByMonth(String customerId, Integer month, Integer year,
            Pageable pageable) {
        Slice<GetListElectricBillDto> electricBillDto;

        if (month != null && year != null) {
            electricBillDto = electricBillRepository.getElectricBillByMonth(customerId, month, year, pageable);
        } else {
            electricBillDto = electricBillRepository.getElectricBillByMonthAndYearDesc(customerId, pageable);
        }

        return electricBillDto;
    }

    public GetElectricBillDto getElectricBillById(ObjectId id) {
        GetElectricBillDto electricBillDto = electricBillRepository.getElectricBillById(id);

        if (Objects.isNull(electricBillDto)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("digo.word.bill")}, HttpServletResponse.SC_NOT_FOUND);
        }

        return electricBillDto;
    }

    public PostResponseDto addNewBillForCustomer(ElectricBillInputDto elecBillInputDto) {

        ElectricBill bill = new ElectricBill(elecBillInputDto);
        bill = electricBillRepository.save(bill);

        return new PostResponseDto(bill.getId());
    }
}
