package vn.vnpt.digo.reporter.api;

import com.google.gson.JsonObject;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import vn.vnpt.digo.reporter.config.IcodeAuthorize;
import vn.vnpt.digo.reporter.dto.OfficeTemplateReqDto;
import vn.vnpt.digo.reporter.pojo.IdPojo;
import vn.vnpt.digo.reporter.service.OfficeTemplateService;
import vn.vnpt.digo.reporter.util.OfficeUtils;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/office-template")
@IcodeAuthorize("vnpt.permission.office-template")
public class OfficeTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(OfficeTemplateController.class);

    @Autowired
    private OfficeTemplateService officeTemplateService;
    
    @GetMapping("/--generate")
    public IdPojo generate(HttpServletRequest request, @Valid @RequestParam OfficeTemplateReqDto params) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IdPojo res = officeTemplateService.generate(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--download")
    public ResponseEntity<Resource> download(HttpServletRequest request, @Valid @RequestParam OfficeTemplateReqDto params) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        Resource resource = officeTemplateService.download(params);
        logger.info("DIGO-Response: " + resource.getFilename());
        return ResponseEntity.ok()
                .contentType(OfficeUtils.getMediaType(params.getSaveFormat()))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
    
    @PostMapping("/--download-from-data")
    public ResponseEntity<Resource> downloadFromData(HttpServletRequest request, @Valid @RequestParam OfficeTemplateReqDto params, @RequestBody String body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        Resource resource = officeTemplateService.downloadFromData(params, body);
        logger.info("DIGO-Response: " + resource.getFilename());
        return ResponseEntity.ok()
                .contentType(OfficeUtils.getMediaType(params.getSaveFormat()))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }

}
