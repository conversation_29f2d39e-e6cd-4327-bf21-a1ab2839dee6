package vn.vnpt.digo.reporter.grpc.service;


import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.reporter.grpc.preprocess.GRPCSamplePreprocess;
import vn.vnpt.igate.Interact.InteracterGrpc;
import vn.vnpt.igate.Interact.Reply;
import vn.vnpt.igate.Interact.Request;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@GrpcService
public class InteractServiceImpl extends InteracterGrpc.InteracterImplBase {
    Logger logger = LoggerFactory.getLogger(InteractServiceImpl.class);

    @Autowired
    GRPCSamplePreprocess grpcSamplePreprocess;

    @Override
    public void sendRequest(Request req, StreamObserver<Reply> responseObserver) {
        String res = "";
        String url = req.getUrl();
        String pathVariable = req.getPathVariable();
        String requestBody = req.getRequestBody();
        String httpMethod = req.getHttpMethod();

        UriComponents uriComponents = this.buildURL(url, pathVariable);

        if (uriComponents.getPath().contains("/sample/test-grpc-reporter")) {
            res = grpcSamplePreprocess.handleInput(uriComponents, requestBody, httpMethod);
        }

        Reply reply = Reply.newBuilder().setMessage(res).build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }
    private UriComponents buildURL(String url, String pathVariable) {
        String URL = url;

        if (Objects.nonNull(pathVariable)) {
            URL = URL.replaceAll("\\{id\\}", pathVariable);
        }

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(URL);
        UriComponents uriComponents = uriBuilder.build();
        logger.info("sendRequest uriComponents: " + uriComponents.toUriString());

        return uriComponents;
    }

}
