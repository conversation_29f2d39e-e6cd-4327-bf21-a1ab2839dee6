/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AgencyProcedure implements Serializable {

    private Integer activeQuantity = 0;

    private Integer secondLevelQuantity = 0;

    private Integer thirdLevelQuantity = 0;

    private Integer fourthLevelQuantity = 0;
}
