/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.util;

import org.apache.poi.ss.usermodel.Cell;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ExcelHelper {
    public static String getStringCell(Cell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }
        cell.getCellType();
        switch (cell.getCellType()) {
            case STRING: {
                return cell.getStringCellValue().trim();
            }
            case NUMERIC: {
                return String.valueOf((int) cell.getNumericCellValue());
            }
            default: {
                try {
                    return cell.toString().trim();
                } catch (IllegalStateException ex) {
                    return "";
                }
            }
        }
    }


}
