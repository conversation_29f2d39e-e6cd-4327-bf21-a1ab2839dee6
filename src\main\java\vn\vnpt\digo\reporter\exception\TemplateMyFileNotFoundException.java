/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 *
 * <AUTHOR>
 */
@ResponseStatus(HttpStatus.NOT_FOUND)
public class TemplateMyFileNotFoundException extends RuntimeException {
    
    public TemplateMyFileNotFoundException(String message) {
        super(message);
    }

    public TemplateMyFileNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
}
