package vn.vnpt.digo.reporter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.reporter.util.ListObjectIdJsonSerializer;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgencySector  implements Serializable{

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private Short procedureQuantity;

    private List<AgencyTransSector> transSector;
    
    @JsonSerialize(using = ListObjectIdJsonSerializer.class)
    private List<ObjectId> agencyUsed;

    private String name;

    public void setName(short langId) {
        this.transSector.forEach(trans -> {
            if (trans.getLanguageId().equals(langId)) {
                this.name = trans.getName();
            }
        });
    }
}
