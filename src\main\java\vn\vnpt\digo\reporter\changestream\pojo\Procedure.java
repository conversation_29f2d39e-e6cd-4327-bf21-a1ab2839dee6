package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Procedure implements Serializable{

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @NotNull
    private String code;
    
    private String dvcltProcedureCode;

    private   Boolean sameDayPayProfile;

    @NotNull
    private ArrayList<TranslateName> translate;

    @JsonIgnore
    private String name;

    @NotNull
    private Tag sector;
    
}
