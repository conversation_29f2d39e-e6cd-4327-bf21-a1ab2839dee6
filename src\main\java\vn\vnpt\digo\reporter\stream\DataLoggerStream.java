package vn.vnpt.digo.reporter.stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.reporter.dto.PostDataLogToKafkaDto;
import vn.vnpt.digo.reporter.stream.messaging.DataLoggerRequest;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(DataLoggerRequest.class)
public class DataLoggerStream {

    @Autowired
    private DataLoggerRequest dataLoggerRequest;

    public void writeLog(PostDataLogToKafkaDto postDataLogToKafka) {
        Message<PostDataLogToKafkaDto> message = MessageBuilder.withPayload(postDataLogToKafka).build();
        boolean check = dataLoggerRequest.output().send(message);
    }

    public boolean push(PostDataLogToKafkaDto postDataLogToKafka){
        Message<PostDataLogToKafkaDto> message = MessageBuilder.withPayload(postDataLogToKafka).build();
        return dataLoggerRequest.output().send(message);
    }
}
