/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.repository;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.reporter.document.SubscriptionType;
import vn.vnpt.digo.reporter.dto.SubscriptionTypeDto;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface SubscriptionTypeRepository extends MongoRepository<SubscriptionType, ObjectId> {

    @Query(value = "{'name.languageId': ?0}", fields = "{'id': 1, 'name':1}")
    List<SubscriptionTypeDto> getListSubscriptionType(Short languageId);
}
