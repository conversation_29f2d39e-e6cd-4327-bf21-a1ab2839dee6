package vn.vnpt.digo.reporter.dto.qni;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;

public class DossierHavingFinancialObligationsDTO {
    @JsonProperty("id")
    private String id;

    @JsonProperty("assignedDate")
    private Instant assignedDate;

    public DossierHavingFinancialObligationsDTO() {
    }

    public DossierHavingFinancialObligationsDTO(String id, Instant assignedDate) {
        this.id = id;
        this.assignedDate = assignedDate;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getAssignedDate() {
        return assignedDate;
    }

    public void setAssignedDate(Instant assignedDate) {
        this.assignedDate = assignedDate;
    }
}
