/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KafkaAgencyStatisticDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId originId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    private Dossier dossier;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Dossier {

        private Integer year = 2020;
        private Integer month = 1;
        private DossierData data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierData {

        private Integer received = 0;
        private Integer resolved = 0;
        private Integer early = 0;
        private Integer onTime = 0;
        private Integer overdue = 0;
        private Integer canceled = 0;

    }
}
