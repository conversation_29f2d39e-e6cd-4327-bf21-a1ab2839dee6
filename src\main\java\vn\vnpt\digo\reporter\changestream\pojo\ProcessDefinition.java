/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changestream.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProcessDefinition implements Serializable {

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private Double processingTime;

    private String processingTimeUnit;

    private IdName timesheet;

    @Field("eForm")
    @JsonProperty("eForm")
    private Object eForm;

    private Object applicantEForm;

    private Object firstTask;
    
    @Field("dynamicVariable")
    @JsonProperty("dynamicVariable")
    private Object dynamicVariable;

    private List<IdName> appliedAgency;

    private Object activiti;

    private String sameDayPayProfile;

    private String morningDay;

    private String afternoonDay;
}
