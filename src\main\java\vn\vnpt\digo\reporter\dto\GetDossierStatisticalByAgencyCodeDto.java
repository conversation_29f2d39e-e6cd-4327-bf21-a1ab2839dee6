package vn.vnpt.digo.reporter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
public class GetDossierStatisticalByAgencyCodeDto {
    @JsonProperty("Thang")
    private String Thang;

    @JsonProperty("Nam")
    private String Nam;

    @JsonProperty("SoNhanTrongKy")
    private String SoNhanTrongKy;

    @JsonProperty("SoTonKyTruoc")
    private String SoTonKyTruoc;

    @JsonProperty("TongSoXuLy")
    private String TongSoXuLy;

    @JsonProperty("TongDaXuLy")
    private String TongDaXuLy;

    @JsonProperty("TongXuLyDungHan")
    private String TongXuLyDungHan;

    @JsonProperty("PhanTramXuLyDungHan")
    private String PhanTramXuLyDungHan;

    @JsonProperty("TongXuLyTreHan")
    private String TongXuLyTreHan;

    @JsonProperty("PhanTramXuLyTreHan")
    private String PhanTramXuLyTreHan;

    @JsonProperty("TongChuaXuLy")
    private String TongChuaXuLy;

    @JsonProperty("TongChuaXuLyTrongHan")
    private String TongChuaXuLyTrongHan;

    @JsonProperty("PhanTramChuaXuLyTrongHan")
    private String PhanTramChuaXuLyTrongHan;

    @JsonProperty("TongChuaXuLyTreHan")
    private String TongChuaXuLyTreHan;

    @JsonProperty("PhanTramChuaXuLyTreHan")
    private String PhanTramChuaXuLyTreHan;

    @JsonProperty("Madonvi")
    private String Madonvi;

    @JsonProperty("TenDonVi")
    private String TenDonVi;

    @JsonProperty("LoaiThongKe")
    private String LoaiThongKe;
}
