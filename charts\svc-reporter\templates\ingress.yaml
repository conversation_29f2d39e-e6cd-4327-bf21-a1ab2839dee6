kind: Ingress
apiVersion: extensions/v1beta1
metadata:
  name: svc-reporter
  namespace: {{namespace}}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/cors-allow-credentials: 'true'
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: 50m
spec:
  rules:
    - host: {{domain}}
      http:
        paths:
          - path: /re(/|$)(.*)
            backend:
              serviceName: svc-reporter
              servicePort: 8080
status:
  loadBalancer:
    ingress:
      - ip: {{nodeip}}
      - ip: {{nodeip}}
      - ip: {{nodeip}}
      - ip: {{nodeip}}
