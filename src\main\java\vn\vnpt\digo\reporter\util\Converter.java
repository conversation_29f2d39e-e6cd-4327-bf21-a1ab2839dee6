/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.util;

import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Blob;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.sql.rowset.serial.SerialBlob;

/**
 *
 * <AUTHOR>
 */
public class Converter {


    
    public static Blob byteArrayToBlob(byte[] byteArray) {
        try {
            return new SerialBlob(byteArray);
        } catch (SQLException ex) {
            throw new IllegalArgumentException(ex);
        }
    }
    
    public static byte[] blobToByteArray(Blob blob) {
        return blobToByteArray(blob, true);
    }
    
    public static byte[] blobToByteArray(Blob blob, boolean freeup) {
        try {
            byte[] bytes = blob.getBytes(1, (int) blob.length());
            if (freeup) {
                blob.free();
            }
            return bytes;
        } catch (SQLException ex) {
            throw new IllegalArgumentException(ex);
        }
    }

    public static String intToRoman(int num){
        String[] thousands = {"", "M", "MM", "MMM"};
        String[] hundreds =
                {"", "C", "CC", "CCC", "CD", "D", "DC", "DCC", "DCCC", "CM"};
        String[] tens =
                {"", "X", "XX", "XXX", "XL", "L", "LX", "LXX", "LXXX", "XC"};
        String[] units =
                {"", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX"};
        return thousands[num / 1000] +
                hundreds[(num % 1000) / 100] +
                tens[(num % 100) / 10] +
                units[num % 10];
    }


}
