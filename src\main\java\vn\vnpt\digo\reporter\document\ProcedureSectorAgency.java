package vn.vnpt.digo.reporter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import vn.vnpt.digo.reporter.pojo.AgencyDossier;
import vn.vnpt.digo.reporter.pojo.AgencyLevel;
import vn.vnpt.digo.reporter.pojo.AgencyName;
import vn.vnpt.digo.reporter.pojo.AgencyProcedure;
import vn.vnpt.digo.reporter.pojo.AgencySector;
import vn.vnpt.digo.reporter.pojo.Ancestor;
import vn.vnpt.digo.reporter.pojo.Tag;
import vn.vnpt.digo.reporter.pojo.TagName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "procedureSectorAgency")
//@CompoundIndex(name = "unique_origin_id_and deployment_id", def = "{'originId': 1, 'deploymentId': 1 }", unique = true)
public class ProcedureSectorAgency {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private ObjectId originId;
    
    private ObjectId parentId;
    
    private ObjectId placeId;
    
    private ArrayList<ObjectId> ancestorPlaceId;
     
    private ArrayList<Ancestor> ancestors;
    
    private ArrayList<ObjectId> tagAgency;
    
    private ArrayList<AgencyName> name;

    private ArrayList<AgencySector> sector;

    private AgencyProcedure procedure;
    
    private TagName procedureAgencyLevel;
    
//    private AgencyLevel level;
    
    private ObjectId deploymentId;

}
