/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-10-14-04-40")
public class ProcedureSectorAgencyChangeLogs {
    @ChangeSet(order = "2021-10-14-04-40", id = "ProcedureSectorAgency::create", author = "nvtoan")
    public void create(DB db) {
        db.createCollection("procedureSectorAgency", null);
    }
}

