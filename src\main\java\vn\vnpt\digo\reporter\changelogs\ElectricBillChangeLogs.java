package vn.vnpt.digo.reporter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.DB;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-05-15-10-50")
public class ElectricBillChangeLogs {

    @ChangeSet(order = "2020-05-15-10-50", id = "ElectricBillChangeLogs::create", author = "haimn")
    public void create(DB db) {
        db.createCollection("electricBill", null);
    }
}
