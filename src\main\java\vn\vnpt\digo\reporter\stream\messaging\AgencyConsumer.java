/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.stream.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;

/**
 *
 * <AUTHOR>
 */
public interface AgencyConsumer {
    
    public static final String INPUT = "agencyConsumerRequestIn";
    
    @Input(value = INPUT)
    public SubscribableChannel input();
}
