package vn.vnpt.digo.reporter.api;

import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cache")
public class CacheController {
    
    @Autowired
    private CacheManager cacheManager;

    private Logger logger = LoggerFactory.getLogger(CacheController.class);
    
    @Value("${digo.cache.security-key}")
    private String securityKey;

    @GetMapping("/--clear-all")
    public ResponseEntity<Object> clearAll(HttpServletRequest request,
            @RequestParam(name = "key", required = true) String key) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String result = "Security key isn't correct";
        if (key.equals(securityKey)) {
            cacheManager.getCacheNames().parallelStream().forEach(
                cacheName -> {
                    logger.info("DIGO-Info: Cache ID '" + cacheName + "' was clear");
                    Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
                }
            );
            result = "All cache is cleared";
        }
        logger.info("DIGO-Response: " + result);
        return ResponseEntity.ok(result);
    }
}
