/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.reporter.grpc.util;

import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.reporter.grpc.auth.BearerToken;
import vn.vnpt.digo.reporter.pojo.JWTGetTokenResponseBody;
import vn.vnpt.digo.reporter.util.Context;
import vn.vnpt.igate.Interact.InteracterGrpc;
import vn.vnpt.igate.Interact.Reply;
import vn.vnpt.igate.Interact.Request;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class GrpcMicroservice {

    @GrpcClient("svc-adapter")
    private InteracterGrpc.InteracterBlockingStub adapterStub;

    @GrpcClient("svc-basecat")
    private InteracterGrpc.InteracterBlockingStub basecatStub;

    @GrpcClient("svc-basedata")
    private InteracterGrpc.InteracterBlockingStub basedataStub;

    @GrpcClient("svc-bpm")
    private InteracterGrpc.InteracterBlockingStub bpmStub;

    @GrpcClient("svc-eform")
    private InteracterGrpc.InteracterBlockingStub eformStub;

    @GrpcClient("svc-fileman")
    private InteracterGrpc.InteracterBlockingStub filemanStub;

    @GrpcClient("svc-human")
    private InteracterGrpc.InteracterBlockingStub humanStub;

    @GrpcClient("svc-logman")
    private InteracterGrpc.InteracterBlockingStub logmanStub;

    @GrpcClient("svc-messenger")
    private InteracterGrpc.InteracterBlockingStub messengerStub;

    @GrpcClient("svc-modeling")
    private InteracterGrpc.InteracterBlockingStub modelingStub;

    @GrpcClient("svc-reporter")
    private InteracterGrpc.InteracterBlockingStub reporterStub;

    @GrpcClient("svc-sysman")
    private InteracterGrpc.InteracterBlockingStub sysmanStub;

    @GrpcClient("svc-system")
    private InteracterGrpc.InteracterBlockingStub systemStub;

    @GrpcClient("svc-basepad")
    private InteracterGrpc.InteracterBlockingStub basepadStub;

    @GrpcClient("svc-padman")
    public InteracterGrpc.InteracterBlockingStub padmanStub;

    @GrpcClient("svc-rbonegate")
    private InteracterGrpc.InteracterBlockingStub rbonegateStub;

    @GrpcClient("svc-storage")
    private InteracterGrpc.InteracterBlockingStub storageStub;

    @GrpcClient("svc-surfeed")
    private InteracterGrpc.InteracterBlockingStub surfeedStub;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String oidcPropertiesUrl;

    @Value("${digo.oidc.client-id}")
    private String client;

    @Value("${digo.oidc.client-secret}")
    private String secret;

    private static String oidcUrl;
    private static String clientId;
    private static String clientSecret;

    @PostConstruct
    void init() {
        clientId = client;
        clientSecret = secret;
        oidcUrl = oidcPropertiesUrl;
    }

    private static RestTemplate restTemplate = new RestTemplate();

    public static final String ADAPTER = "adapter";
    public static final String BASECAT = "basecat";
    public static final String BASEDATA = "basedata";
    public static final String BPM = "bpm";
    public static final String EFORM = "eform";
    public static final String FILEMAN = "fileman";
    public static final String HUMAN = "human";
    public static final String LOGMAN = "logman";
    public static final String MESSENGER = "messenger";
    public static final String MODELING = "modeling";
    public static final String REPORTER = "reporter";
    public static final String SYSMAN = "sysman";
    public static final String SYSTEM = "system";
    public static final String BASEPAD = "basepad";
    public static final String PADMAN = "padman";
    public static final String RBONEGATE = "rbonegate";
    public static final String STORAGE = "storage";
    public static final String SURFEED = "surfeed";

    public String getPathCode(String code) {
        switch (code) {
            //digo
            case ADAPTER:
                return "ad";
            case BASECAT:
                return "bt";
            case BASEDATA:
                return "ba";
            case BPM:
                return "bpm";
            case EFORM:
                return "eform";
            case FILEMAN:
                return "fi";
            case HUMAN:
                return "hu";
            case LOGMAN:
                return "lo";
            case MESSENGER:
                return "me";
            case MODELING:
                return "modeling-service";
            case REPORTER:
                return "re";
            case SYSMAN:
                return "sy";
            case SYSTEM:
                return "system";
            //igate
            case BASEPAD:
                return "bd";
            case PADMAN:
                return "pa";
            case RBONEGATE:
                return "rbonegate";
            case SURFEED:
                return "su";
            //vndrive
            case STORAGE:
                return "storage";
        }
        return null;
    }

    public String adapterUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(ADAPTER) + uriComponents.toUriString();

        Reply response = adapterStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String adapterUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(ADAPTER) + uriComponents.toUriString();

        Reply response = adapterStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String adapterUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(ADAPTER) + uriComponents.toUriString();

        Reply response = adapterStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String adapterUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(ADAPTER) + uriComponents.toUriString();

        Reply response = adapterStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basecatUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASECAT) + uriComponents.toUriString();

        Reply response = basecatStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basecatUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASECAT) + uriComponents.toUriString();

        Reply response = basecatStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basecatUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASECAT) + uriComponents.toUriString();

        Reply response = basecatStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basecatUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASECAT) + uriComponents.toUriString();

        Reply response = basecatStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basedataUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEDATA) + uriComponents.toUriString();

        Reply response = basedataStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basedataUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEDATA) + uriComponents.toUriString();

        Reply response = basedataStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basedataUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEDATA) + uriComponents.toUriString();

        Reply response = basedataStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basedataUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEDATA) + uriComponents.toUriString();

        Reply response = basedataStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String bpmUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BPM) + uriComponents.toUriString();

        Reply response = bpmStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String bpmUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BPM) + uriComponents.toUriString();

        Reply response = bpmStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String bpmUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BPM) + uriComponents.toUriString();

        Reply response = bpmStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String bpmUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BPM) + uriComponents.toUriString();

        Reply response = bpmStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String eformUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(EFORM) + uriComponents.toUriString();

        Reply response = eformStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String eformUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(EFORM) + uriComponents.toUriString();

        Reply response = eformStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String eformUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(EFORM) + uriComponents.toUriString();

        Reply response = eformStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String eformUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(EFORM) + uriComponents.toUriString();

        Reply response = eformStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String filemanUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(FILEMAN) + uriComponents.toUriString();

        Reply response = filemanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String filemanUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(FILEMAN) + uriComponents.toUriString();

        Reply response = filemanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String filemanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(FILEMAN) + uriComponents.toUriString();

        Reply response = filemanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String filemanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(FILEMAN) + uriComponents.toUriString();

        Reply response = filemanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String humanUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(HUMAN) + uriComponents.toUriString();

        Reply response = humanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String humanUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(HUMAN) + uriComponents.toUriString();

        Reply response = humanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String humanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(HUMAN) + uriComponents.toUriString();

        Reply response = humanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String humanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(HUMAN) + uriComponents.toUriString();

        Reply response = humanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String logmanUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(LOGMAN) + uriComponents.toUriString();

        Reply response = logmanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String logmanUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(LOGMAN) + uriComponents.toUriString();

        Reply response = logmanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String logmanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(LOGMAN) + uriComponents.toUriString();

        Reply response = logmanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String logmanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(LOGMAN) + uriComponents.toUriString();

        Reply response = logmanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String messengerUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MESSENGER) + uriComponents.toUriString();

        Reply response = messengerStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String messengerUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MESSENGER) + uriComponents.toUriString();

        Reply response = messengerStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String messengerUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MESSENGER) + uriComponents.toUriString();

        Reply response = messengerStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String messengerUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MESSENGER) + uriComponents.toUriString();

        Reply response = messengerStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String modelingUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MODELING) + uriComponents.toUriString();

        Reply response = modelingStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String modelingUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MODELING) + uriComponents.toUriString();

        Reply response = modelingStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String modelingUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MODELING) + uriComponents.toUriString();

        Reply response = modelingStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String modelingUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(MODELING) + uriComponents.toUriString();

        Reply response = modelingStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String reporterUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(REPORTER) + uriComponents.toUriString();

        Reply response = reporterStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String reporterUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(REPORTER) + uriComponents.toUriString();

        Reply response = reporterStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String reporterUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(REPORTER) + uriComponents.toUriString();

        Reply response = reporterStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String reporterUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(REPORTER) + uriComponents.toUriString();

        Reply response = reporterStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String sysmanUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSMAN) + uriComponents.toUriString();

        Reply response = sysmanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String sysmanUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSMAN) + uriComponents.toUriString();

        Reply response = sysmanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String sysmanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSMAN) + uriComponents.toUriString();

        Reply response = sysmanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String sysmanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSMAN) + uriComponents.toUriString();

        Reply response = sysmanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String systemUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSTEM) + uriComponents.toUriString();

        Reply response = systemStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String systemUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSTEM) + uriComponents.toUriString();

        Reply response = systemStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String systemUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSTEM) + uriComponents.toUriString();

        Reply response = systemStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String systemUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SYSTEM) + uriComponents.toUriString();

        Reply response = systemStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basepadUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEPAD) + uriComponents.toUriString();

        Reply response = basepadStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basepadUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEPAD) + uriComponents.toUriString();

        Reply response = basepadStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basepadUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEPAD) + uriComponents.toUriString();

        Reply response = basepadStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String basepadUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(BASEPAD) + uriComponents.toUriString();

        Reply response = basepadStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String padmanUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(PADMAN) + uriComponents.toUriString();

        Reply response = padmanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String padmanUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(PADMAN) + uriComponents.toUriString();

        Reply response = padmanStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String padmanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(PADMAN) + uriComponents.toUriString();

        Reply response = padmanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String padmanUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(PADMAN) + uriComponents.toUriString();

        Reply response = padmanStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String rbonegateUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(RBONEGATE) + uriComponents.toUriString();

        Reply response = rbonegateStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String rbonegateUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(RBONEGATE) + uriComponents.toUriString();

        Reply response = rbonegateStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String rbonegateUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(RBONEGATE) + uriComponents.toUriString();

        Reply response = rbonegateStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String rbonegateUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(RBONEGATE) + uriComponents.toUriString();

        Reply response = rbonegateStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String storageUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(STORAGE) + uriComponents.toUriString();

        Reply response = storageStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String storageUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(STORAGE) + uriComponents.toUriString();

        Reply response = storageStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String storageUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(STORAGE) + uriComponents.toUriString();

        Reply response = storageStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String storageUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(STORAGE) + uriComponents.toUriString();

        Reply response = storageStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String surfeedUri(UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SURFEED) + uriComponents.toUriString();

        Reply response = surfeedStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String surfeedUri(UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken token = new BearerToken(this.getToken());
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SURFEED) + uriComponents.toUriString();

        Reply response = surfeedStub.withCallCredentials(token).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String surfeedUri(String token, UriComponentsBuilder uriBuilder, String httpMethod) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SURFEED) + uriComponents.toUriString();

        Reply response = surfeedStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String surfeedUri(String token, UriComponentsBuilder uriBuilder, String httpMethod, String requestBody) {
        BearerToken bearerToken = new BearerToken(token);
        UriComponents uriComponents = uriBuilder.encode().build();
        String url = getPathCode(SURFEED) + uriComponents.toUriString();

        Reply response = surfeedStub.withCallCredentials(bearerToken).sendRequest(Request.newBuilder()
                .setUrl(url)
                .setRequestBody(requestBody)
                .setHttpMethod(httpMethod)
                .build());
        return response.getMessage();
    }

    public String getToken() {
        String token = null;
        try{
            JwtAuthenticationToken claims = Context.getJwtAuthenticationToken();
            token = claims.getToken().getTokenValue();
        } catch (Exception ex) {}

        if(Objects.isNull(token)){
            try{
                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("grant_type", "client_credentials");
                map.add("client_id", clientId);
                map.add("client_secret", clientSecret);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
                String url = oidcUrl + "/protocol/openid-connect/token";
                System.out.println(url);
                ResponseEntity<JWTGetTokenResponseBody> result = restTemplate.exchange(url, HttpMethod.POST, request, JWTGetTokenResponseBody.class);
                token = result.getBody().getAccessToken();
            } catch (Exception ex) {}
        }
        return token;
    }

}
