package vn.vnpt.digo.reporter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.google.common.base.Strings.isNullOrEmpty;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierEtlDetailDto {
    private String id;
    private String dossierId;
    private String sectorName;
    private String procedureLevel;
    private String procedureName;
    private String dossierCode;
    private String dossierStatus;
    private String dossierTaskStatus;
    private String phone;
    private String fullName;
    private String organization;
    private Date acceptedDate;
    private String address;
    private double totalImplementDate;
    private String agency;
    private String agencyId;
    private int outOfDateCode;
    private List<DossierOutOfDateDTO> listDossierOutOfDate;
    private Date appointmentDate;
    private Date completedDate;
    private Date returnedDate;
    private Date withdrawDate;
    private Date cancelledDate;
    private String applyMethod;
    private int applyMethodId;
    private int dossierDetailStatus;
    private String implementer;
    private Date createdDate;
    private Date updatedDate;
    private List<AttachmentDTO> attachment;
    public ExcelFormDossierCountingDetailDto convertToExcelForm(long no, DossierEtlDetailDto input){
        String df2 = "dd/MM/yyyy HH:mm:ss";
        String timeZone= "Asia/Ho_Chi_Minh";
        ExcelFormDossierCountingDetailDto ret = new ExcelFormDossierCountingDetailDto();
        ret.setNo(no);
        ret.setAgency(input.getAgency());
        ret.setSector(input.getSectorName());
        ret.setProcedure(input.getProcedureName());
        ret.setProcedureLevel(input.getProcedureLevel());
        ret.setDossierCode(input.getDossierCode());
        ret.setStatus(input.getDossierStatus());
        ret.setAddress(input.getAddress());
        ret.setPhone(input.getPhone());
        ret.setOwner(input.getFullName());
        ret.setSubmitter(input.getOrganization());
        ret.setDossierType(input.getApplyMethod());

        if(input.getAcceptedDate()!=null){
            String date = formatDateToString(input.getAcceptedDate(), df2, timeZone);
            ret.setAcceptedDate(date);

        }
        if(input.getAppointmentDate()!=null){
            String date = formatDateToString(input.getAppointmentDate(), df2, timeZone);
            ret.setAppointmentDate(date);
        }

        ret.setDuration(input.getTotalImplementDate() > 0 ? input.getTotalImplementDate() + " ngày" : "Không xác định thời gian");

        if(input.getCompletedDate()!=null ){
            String date = formatDateToString(input.getCompletedDate(), df2, timeZone);
            ret.setCompletedDate(date);
        }
        if(input.getReturnedDate()!=null ){
            String date = formatDateToString(input.getReturnedDate(), df2, timeZone);
            ret.setReturnedDate(date);
        }

        if(input.getCancelledDate()!=null ){
            String date = formatDateToString(input.getCancelledDate(), df2, timeZone);
            ret.setCancelledDate(date);
        }
        if(input.getWithdrawDate()!=null ){
            String date = formatDateToString(input.getWithdrawDate(), df2, timeZone);
            ret.setWithdrawDate(date);
        }

        String impTemp = input.getImplementer();
        List<String> listImp=new ArrayList<>();
        String imp= "";
        if (!isNullOrEmpty(impTemp)){
            listImp = List.of(impTemp.split(","));
            imp= listImp.get(listImp.size()-1).equals("null")?"":listImp.get(listImp.size()-1);
        }
        ret.setImplementer(imp);
//    dto.setOutOfDateType(entity.getOutOfDateCode());
        String strTemp = "";
        //
        if (input.getListDossierOutOfDate()!=null){
            for (int i=0; i<input.getListDossierOutOfDate().size();++i){
               // if(input.getOutOfDateCode()!=0){
                    if (input.getListDossierOutOfDate().get(i).getAccountName()!=null){
                        strTemp+= (i+1) + ". " + input.getListDossierOutOfDate().get(i).getAgency() +
                          //  +input.getListDossierOutOfDate().get(i).getStep() +" - "
                                (input.getListDossierOutOfDate().get(i).getUserName() != null ?
                                        " - " + input.getListDossierOutOfDate().get(i).getUserName()  :
                                        "") + "\n";
                    }
             //   }
            }
        }

        ret.setLateAt(strTemp);
        ret.setNote("");

        ret.setDossierTaskStatus(input.getDossierTaskStatus());

        String processStatus = "";
        switch (input.getDossierDetailStatus()){
            case 1 : {
                processStatus = "Đang giải quyết - Trong hạn";
                break;
            }
            case 2 : {
                processStatus = "Đang giải quyết - Quá hạn";
                break;
            }
            case 3 : {
                processStatus = "Đã giải quyết - Trước hạn";
                break;
            }
            case 4 : {
                processStatus = "Đã giải quyết - Quá hạn";
                break;
            }
            case 5 : {
                processStatus = "Đã giải quyết - Đúng hạn";
                break;
            }
        }
        ret.setDossierStatus(processStatus);

        return ret;
    }

    private String formatDateToString(
        Date date, String format,
        String timeZone
    ) {
        // null check
        if (date == null) return null;
        // create SimpleDateFormat object with input format
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // default system timezone if passed null or empty
        if (timeZone == null || "".equalsIgnoreCase(timeZone.trim())) {
            timeZone = Calendar.getInstance().getTimeZone().getID();
        }
        // set timezone to SimpleDateFormat
        sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        // return Date in required format with timezone as String
        return sdf.format(date);
    }

    @Data
    @NoArgsConstructor
    public static class DossierOutOfDateDTO {

        private String userName;
        private String step;
        private String accountName;
        private String agency;
        private Date completedDate;
        private String dueDate;

    }

    @Data
    @NoArgsConstructor
    public static class AttachmentDTO {
        private String Id;
        private String filename;
        private int size;
        private String extension;
        private String uuid;
        private String group;
    }


}

