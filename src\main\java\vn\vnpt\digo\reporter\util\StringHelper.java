package vn.vnpt.digo.reporter.util;

import org.apache.poi.ss.usermodel.Cell;
import org.bson.types.ObjectId;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringHelper {
    public static boolean hasValue(String str){
        return Objects.nonNull(str) && !str.isEmpty() && !str.isBlank();
    }

    public static ObjectId tryToBojectId(String str){
        try {
            return new ObjectId(str);
        }catch (Exception ex){
            return null;
        }
    }
    public static String getStringCell(Cell cell){
        if(Objects.isNull(cell)){
            return "";
        }
        cell.getCellType();
        switch(cell.getCellType()){
            case STRING: {
                return cell.getStringCellValue().trim();
            }
            case NUMERIC:{
                return String.valueOf((int) cell.getNumericCellValue());
            }
            default:{
                try{
                    return cell.toString().trim();
                }catch(IllegalStateException ex){
                    return "";
                }
            }
        }
    }

    public static String toDDMMYYYY(String isoDate){
        try{
            //Điều chỉnh timezone về ETC+7
            isoDate = convertToStandardISODate(isoDate);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX");

            ZonedDateTime originalDateTime = ZonedDateTime.parse(isoDate, formatter);

            ZonedDateTime convertedDateTime = originalDateTime.withZoneSameInstant(java.time.ZoneOffset.ofHours(7));

            String convertedIsoDate = convertedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);

            convertedIsoDate = convertedIsoDate.substring(0,19)+".000Z";
            TemporalAccessor ta = DateTimeFormatter.ISO_INSTANT.parse(convertedIsoDate);
            Instant instant = Instant.from(ta);
            Date date = Date.from(instant);
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            return df.format(date);
        }catch (Exception ex){
            //Thực hiện như cũ
            isoDate = isoDate.substring(0,19)+".000Z";
            TemporalAccessor ta = DateTimeFormatter.ISO_INSTANT.parse(isoDate);
            Instant instant = Instant.from(ta);
            Date date = Date.from(instant);
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
            return df.format(date);
        }
    }

    public static String convertToStandardISODate(String inputDate){
        try {
            // Parse the input date
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(inputDate, DateTimeFormatter.ISO_OFFSET_DATE_TIME);

            // Format the date with milliseconds and the desired pattern
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
            String convertedDate = zonedDateTime.format(outputFormatter);

            System.out.println("Converted Date: " + convertedDate);
            return convertedDate;
        } catch (Exception ex) {
            // Define a custom formatter to handle the input format
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX");

            // Parse the input date string with the custom formatter
            OffsetDateTime dateTime = OffsetDateTime.parse(inputDate, inputFormatter);

            // Convert to the desired format
            String formattedDate = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX"));

            return formattedDate;
        }

    }

    public static String encodeUTF8(String input) {
        byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
        String output = new String(bytes, StandardCharsets.UTF_8);

        return output;
    }

    public static String replaceSpecialCharacter(String str) {
        String regex = "[\\-\\_\\.\\!\\~\\*\\'\\(\\)\\[\\]\\{\\}\\|\\`\\!`\\@`\\#\\$\\%\\^\\&\\+\\=\\\\\\;\\:\\\"\\<\\.\\>\\,\\?\\/]";
        return str.replaceAll(regex, "\\\\$0");
    }

    public static Integer tryToInteger(String str){
        try{
            return Integer.valueOf(str);
        }catch (Exception ex){
            return null;
        }
    }

    public static String tryToString(Object obj){
        try{
            return obj.toString();
        }catch (Exception ex){
            return null;
        }
    }

    public static String filterSpecialCharacters(String input) {
        if (input == null) {
            return null;
        }
        String regex = "[^\\p{L}\\p{N}\\p{Z}\\-\\.'\"]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String result = matcher.replaceAll("");
        if (result.matches("^[\\-.'\"]+$")) {
            result = null;
        }
        return result;
    }

    public static boolean hasValidSpecialCharacters(String input) {
        if (input == null) {
            return false;
        }
        String validCharacters = "\\p{L}\\p{N}\\p{Z}\\-\\.'";
        String regex = "^[\\-.'\"]+$";
        for (char c : input.toCharArray()) {
            if (!Character.toString(c).matches("[" + validCharacters + "]")) {
                return false;
            }
        }
        if (input.matches(regex)) {
            return false;
        }
        return true;
    }

}
